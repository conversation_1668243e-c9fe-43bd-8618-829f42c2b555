/**
 * Tests for PerformanceOverview component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import PerformanceOverview from '../PerformanceOverview';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock Recharts
vi.mock('recharts', () => ({
  ResponsiveContainer: ({ children }) => <div data-testid="responsive-container">{children}</div>,
  LineChart: ({ children }) => <div data-testid="line-chart">{children}</div>,
  Line: () => <div data-testid="line" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="recharts-tooltip" />,
  Legend: () => <div data-testid="legend" />,
  BarChart: ({ children }) => <div data-testid="bar-chart">{children}</div>,
  Bar: () => <div data-testid="bar" />,
}));

describe('PerformanceOverview', () => {
  const mockData = {
    total_impressions: 150000,
    total_engagements: 12500,
    total_followers: 8500,
    engagement_rate: 8.33,
    previous_period: {
      total_impressions: 125000,
      total_engagements: 10000,
      total_followers: 8000,
      engagement_rate: 8.0,
    },
    time_series: [
      {
        date: '2023-01-01',
        impressions: 25000,
        engagements: 2000,
        followers: 8000,
        engagement_rate: 8.0,
      },
      {
        date: '2023-01-02',
        impressions: 28000,
        engagements: 2300,
        followers: 8100,
        engagement_rate: 8.2,
      },
      {
        date: '2023-01-03',
        impressions: 30000,
        engagements: 2500,
        followers: 8200,
        engagement_rate: 8.33,
      },
    ],
    platforms: [
      {
        name: 'LinkedIn',
        impressions: 75000,
        engagements: 6500,
        followers: 4500,
      },
      {
        name: 'Twitter',
        impressions: 45000,
        engagements: 3500,
        followers: 2500,
      },
      {
        name: 'Instagram',
        impressions: 30000,
        engagements: 2500,
        followers: 1500,
      },
    ],
  };

  const mockCallbacks = {
    onRefresh: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock document.createElement for export functionality
    global.document.createElement = vi.fn(() => ({
      setAttribute: vi.fn(),
      click: vi.fn(),
      style: {}
    }));

    global.document.body.appendChild = vi.fn();
    global.document.body.removeChild = vi.fn();
  });

  test('renders performance overview with metrics', () => {
    render(
      <TestWrapper>
        <PerformanceOverview data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    expect(screen.getByText('Performance Overview')).toBeInTheDocument();
    expect(screen.getByText('150,000')).toBeInTheDocument(); // Total impressions
    expect(screen.getByText('12,500')).toBeInTheDocument(); // Total engagements
    expect(screen.getByText('8,500')).toBeInTheDocument(); // Total followers
    expect(screen.getByText('8.33%')).toBeInTheDocument(); // Engagement rate
  });

  test('displays percentage changes correctly', () => {
    render(
      <TestWrapper>
        <PerformanceOverview data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    // Should show percentage increases
    expect(screen.getByText('20.0%')).toBeInTheDocument(); // Impressions increase
    expect(screen.getByText('25.0%')).toBeInTheDocument(); // Engagements increase
    expect(screen.getByText('6.3%')).toBeInTheDocument(); // Followers increase
    expect(screen.getByText('4.1%')).toBeInTheDocument(); // Engagement rate increase

    // Should show "increase" text
    const increaseTexts = screen.getAllByText('increase');
    expect(increaseTexts).toHaveLength(4);
  });

  test('shows loading state with skeletons', () => {
    render(
      <TestWrapper>
        <PerformanceOverview data={null} loading={true} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    // Should show skeleton loaders
    const skeletons = screen.getAllByTestId(/skeleton/i);
    expect(skeletons.length).toBeGreaterThan(0);
  });

  test('shows error state correctly', () => {
    const errorMessage = 'Failed to load performance data';
    render(
      <TestWrapper>
        <PerformanceOverview error={errorMessage} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
    expect(screen.getByText('Retry')).toBeInTheDocument();
  });

  test('shows no data state correctly', () => {
    render(
      <TestWrapper>
        <PerformanceOverview data={null} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    expect(screen.getByText('No performance data available. Please check your data source or try refreshing.')).toBeInTheDocument();
  });

  test('handles refresh functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PerformanceOverview data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    const refreshButton = screen.getByLabelText('Refresh performance data');
    await user.click(refreshButton);

    expect(mockCallbacks.onRefresh).toHaveBeenCalled();
  });

  test('handles export functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PerformanceOverview data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    const exportButton = screen.getByLabelText('Export performance data');
    await user.click(exportButton);

    // Should create download link
    expect(global.document.createElement).toHaveBeenCalledWith('a');
  });

  test('disables export when no data available', () => {
    render(
      <TestWrapper>
        <PerformanceOverview data={null} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    const exportButton = screen.getByLabelText('Export performance data');
    expect(exportButton).toBeDisabled();
  });

  test('disables refresh during loading', () => {
    render(
      <TestWrapper>
        <PerformanceOverview data={mockData} loading={true} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    const refreshButton = screen.getByLabelText('Refresh performance data');
    expect(refreshButton).toBeDisabled();
  });

  test('renders charts when data is available', () => {
    render(
      <TestWrapper>
        <PerformanceOverview data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    expect(screen.getByText('Performance Over Time')).toBeInTheDocument();
    expect(screen.getByText('Platform Comparison')).toBeInTheDocument();
    expect(screen.getAllByTestId('responsive-container')).toHaveLength(2);
    expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
  });

  test('handles retry functionality in error state', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PerformanceOverview error="Network error" loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    const retryButton = screen.getByText('Retry');
    await user.click(retryButton);

    expect(mockCallbacks.onRefresh).toHaveBeenCalled();
  });

  test('hides actions when showRefresh and showExport are false', () => {
    render(
      <TestWrapper>
        <PerformanceOverview 
          data={mockData} 
          loading={false} 
          onRefresh={mockCallbacks.onRefresh}
          showRefresh={false}
          showExport={false}
        />
      </TestWrapper>
    );

    expect(screen.queryByLabelText('Refresh performance data')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('Export performance data')).not.toBeInTheDocument();
    expect(screen.queryByText('Performance Overview')).not.toBeInTheDocument(); // Header should be hidden
  });

  test('handles missing previous period data gracefully', () => {
    const dataWithoutPrevious = {
      ...mockData,
      previous_period: null
    };

    render(
      <TestWrapper>
        <PerformanceOverview data={dataWithoutPrevious} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    expect(screen.getByText('150,000')).toBeInTheDocument(); // Should still show current data
    expect(screen.queryByText('increase')).not.toBeInTheDocument(); // Should not show change indicators
    expect(screen.queryByText('decrease')).not.toBeInTheDocument();
  });

  test('handles negative changes correctly', () => {
    const dataWithDecline = {
      ...mockData,
      total_impressions: 100000,
      previous_period: {
        ...mockData.previous_period,
        total_impressions: 125000,
      }
    };

    render(
      <TestWrapper>
        <PerformanceOverview data={dataWithDecline} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    expect(screen.getByText('20.0%')).toBeInTheDocument(); // Should show decline percentage
    expect(screen.getByText('decrease')).toBeInTheDocument(); // Should show "decrease" text
  });

  test('handles missing time series data gracefully', () => {
    const dataWithoutTimeSeries = {
      ...mockData,
      time_series: null
    };

    render(
      <TestWrapper>
        <PerformanceOverview data={dataWithoutTimeSeries} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    expect(screen.getByText('Performance Over Time')).toBeInTheDocument();
    expect(screen.getByTestId('line-chart')).toBeInTheDocument(); // Chart should still render
  });

  test('handles missing platform data gracefully', () => {
    const dataWithoutPlatforms = {
      ...mockData,
      platforms: null
    };

    render(
      <TestWrapper>
        <PerformanceOverview data={dataWithoutPlatforms} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    expect(screen.getByText('Platform Comparison')).toBeInTheDocument();
    expect(screen.getByTestId('bar-chart')).toBeInTheDocument(); // Chart should still render
  });

  test('applies correct accessibility attributes', () => {
    render(
      <TestWrapper>
        <PerformanceOverview data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    // Check ARIA labels
    expect(screen.getByLabelText('Export performance data')).toBeInTheDocument();
    expect(screen.getByLabelText('Refresh performance data')).toBeInTheDocument();

    // Check tooltips
    expect(screen.getByTitle('Export Performance Data')).toBeInTheDocument();
    expect(screen.getByTitle('Refresh Data')).toBeInTheDocument();
  });

  test('shows loading spinner during export', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PerformanceOverview data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    const exportButton = screen.getByLabelText('Export performance data');
    await user.click(exportButton);

    // Should briefly show loading spinner (this test might be flaky due to timing)
    // In a real scenario, you might want to mock the async behavior
  });
});
