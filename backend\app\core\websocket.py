"""
WebSocket Manager for Real-time Communication.
Provides real-time updates for inventory, sync status, and other e-commerce events.
@since 2024-1-1 to 2025-25-7
"""

import logging
import json
import asyncio
from typing import Dict, Set, List, Any, Optional
from datetime import datetime, timezone
from fastapi import WebSocket, WebSocketDisconnect
from collections import defaultdict

from app.core.redis import get_redis_client
from app.core.monitoring import monitor_performance, log_audit_event, OperationType

logger = logging.getLogger(__name__)

# Redis keys for WebSocket management
WEBSOCKET_CONNECTIONS_KEY = "websocket_connections:{user_id}"
WEBSOCKET_ROOMS_KEY = "websocket_rooms:{room_id}"


class ConnectionManager:
    """
    WebSocket connection manager with Redis backing for scalability.
    """
    
    def __init__(self):
        # Local connections for this instance
        self.active_connections: Dict[str, Set[WebSocket]] = defaultdict(set)
        self.connection_metadata: Dict[WebSocket, Dict[str, Any]] = {}
        self.user_connections: Dict[str, Set[WebSocket]] = defaultdict(set)
        self.room_connections: Dict[str, Set[WebSocket]] = defaultdict(set)
        self.redis_client = None
    
    async def _get_redis_client(self):
        """Get Redis client for distributed WebSocket management."""
        if not self.redis_client:
            self.redis_client = await get_redis_client()
        return self.redis_client
    
    @monitor_performance("websocket_connect")
    async def connect(
        self,
        websocket: WebSocket,
        user_id: str,
        connection_type: str = "general",
        room_id: Optional[str] = None
    ):
        """
        Accept WebSocket connection and register it.
        
        Args:
            websocket: WebSocket connection
            user_id: User ID
            connection_type: Type of connection (inventory, sync, general)
            room_id: Optional room ID for grouped connections
        """
        try:
            await websocket.accept()
            
            # Store connection metadata
            self.connection_metadata[websocket] = {
                "user_id": user_id,
                "connection_type": connection_type,
                "room_id": room_id,
                "connected_at": datetime.now(timezone.utc),
                "last_ping": datetime.now(timezone.utc)
            }
            
            # Add to local tracking
            self.active_connections[user_id].add(websocket)
            self.user_connections[user_id].add(websocket)
            
            if room_id:
                self.room_connections[room_id].add(websocket)
            
            # Register in Redis for distributed tracking
            redis = await self._get_redis_client()
            if redis:
                connection_key = WEBSOCKET_CONNECTIONS_KEY.format(user_id=user_id)
                connection_data = {
                    "connection_type": connection_type,
                    "room_id": room_id or "",
                    "connected_at": datetime.now(timezone.utc).isoformat(),
                    "instance_id": id(self)  # Instance identifier
                }
                redis.hset(connection_key, str(id(websocket)), json.dumps(connection_data))
                redis.expire(connection_key, 3600)  # 1 hour expiry
                
                if room_id:
                    room_key = WEBSOCKET_ROOMS_KEY.format(room_id=room_id)
                    redis.sadd(room_key, user_id)
                    redis.expire(room_key, 3600)
            
            # Log connection
            log_audit_event(
                operation_type=OperationType.CREATE,
                resource_type="websocket_connection",
                resource_id=str(id(websocket)),
                user_id=user_id,
                details={
                    "connection_type": connection_type,
                    "room_id": room_id
                }
            )
            
            logger.info(f"WebSocket connected: user={user_id}, type={connection_type}, room={room_id}")
            
        except Exception as e:
            logger.error(f"Error connecting WebSocket for user {user_id}: {str(e)}")
            raise
    
    async def disconnect(self, websocket: WebSocket):
        """
        Disconnect WebSocket and clean up.
        
        Args:
            websocket: WebSocket connection to disconnect
        """
        try:
            metadata = self.connection_metadata.get(websocket, {})
            user_id = metadata.get("user_id")
            room_id = metadata.get("room_id")
            
            # Remove from local tracking
            if user_id:
                self.active_connections[user_id].discard(websocket)
                self.user_connections[user_id].discard(websocket)
                
                # Clean up empty sets
                if not self.active_connections[user_id]:
                    del self.active_connections[user_id]
                if not self.user_connections[user_id]:
                    del self.user_connections[user_id]
            
            if room_id:
                self.room_connections[room_id].discard(websocket)
                if not self.room_connections[room_id]:
                    del self.room_connections[room_id]
            
            # Remove metadata
            if websocket in self.connection_metadata:
                del self.connection_metadata[websocket]
            
            # Clean up Redis
            if user_id:
                redis = await self._get_redis_client()
                if redis:
                    connection_key = WEBSOCKET_CONNECTIONS_KEY.format(user_id=user_id)
                    redis.hdel(connection_key, [str(id(websocket))])
                    
                    if room_id:
                        # Check if user has other connections in the room
                        user_connections_in_room = [
                            ws for ws in self.user_connections.get(user_id, set())
                            if self.connection_metadata.get(ws, {}).get("room_id") == room_id
                        ]
                        
                        if not user_connections_in_room:
                            room_key = WEBSOCKET_ROOMS_KEY.format(room_id=room_id)
                            redis.srem(room_key, user_id)
            
            # Log disconnection
            if user_id:
                log_audit_event(
                    operation_type=OperationType.DELETE,
                    resource_type="websocket_connection",
                    resource_id=str(id(websocket)),
                    user_id=user_id,
                    details=metadata
                )
            
            logger.info(f"WebSocket disconnected: user={user_id}, room={room_id}")
            
        except Exception as e:
            logger.error(f"Error disconnecting WebSocket: {str(e)}")
    
    @monitor_performance("websocket_send_personal")
    async def send_personal_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """
        Send message to specific WebSocket connection.
        
        Args:
            websocket: Target WebSocket connection
            message: Message to send
        """
        try:
            await websocket.send_text(json.dumps(message))
        except WebSocketDisconnect:
            await self.disconnect(websocket)
        except Exception as e:
            logger.error(f"Error sending personal message: {str(e)}")
            await self.disconnect(websocket)
    
    @monitor_performance("websocket_send_to_user")
    async def send_to_user(self, user_id: str, message: Dict[str, Any]):
        """
        Send message to all connections for a specific user.
        
        Args:
            user_id: Target user ID
            message: Message to send
        """
        try:
            user_connections = self.user_connections.get(user_id, set()).copy()
            
            if not user_connections:
                logger.debug(f"No active connections for user {user_id}")
                return
            
            # Add timestamp to message
            message["timestamp"] = datetime.now(timezone.utc).isoformat()
            
            # Send to all user connections
            disconnected_connections = []
            for websocket in user_connections:
                try:
                    await websocket.send_text(json.dumps(message))
                except WebSocketDisconnect:
                    disconnected_connections.append(websocket)
                except Exception as e:
                    logger.error(f"Error sending message to user {user_id}: {str(e)}")
                    disconnected_connections.append(websocket)
            
            # Clean up disconnected connections
            for websocket in disconnected_connections:
                await self.disconnect(websocket)
                
        except Exception as e:
            logger.error(f"Error sending message to user {user_id}: {str(e)}")
    
    @monitor_performance("websocket_send_to_room")
    async def send_to_room(self, room_id: str, message: Dict[str, Any], exclude_user: Optional[str] = None):
        """
        Send message to all connections in a room.
        
        Args:
            room_id: Target room ID
            message: Message to send
            exclude_user: Optional user ID to exclude from broadcast
        """
        try:
            room_connections = self.room_connections.get(room_id, set()).copy()
            
            if not room_connections:
                logger.debug(f"No active connections in room {room_id}")
                return
            
            # Add timestamp to message
            message["timestamp"] = datetime.now(timezone.utc).isoformat()
            
            # Send to all room connections
            disconnected_connections = []
            for websocket in room_connections:
                try:
                    metadata = self.connection_metadata.get(websocket, {})
                    if exclude_user and metadata.get("user_id") == exclude_user:
                        continue
                    
                    await websocket.send_text(json.dumps(message))
                except WebSocketDisconnect:
                    disconnected_connections.append(websocket)
                except Exception as e:
                    logger.error(f"Error sending message to room {room_id}: {str(e)}")
                    disconnected_connections.append(websocket)
            
            # Clean up disconnected connections
            for websocket in disconnected_connections:
                await self.disconnect(websocket)
                
        except Exception as e:
            logger.error(f"Error sending message to room {room_id}: {str(e)}")
    
    async def broadcast(self, message: Dict[str, Any], connection_type: Optional[str] = None):
        """
        Broadcast message to all connections or specific connection type.
        
        Args:
            message: Message to broadcast
            connection_type: Optional connection type filter
        """
        try:
            # Add timestamp to message
            message["timestamp"] = datetime.now(timezone.utc).isoformat()
            
            # Get all connections to broadcast to
            target_connections = set()
            for user_connections in self.user_connections.values():
                for websocket in user_connections:
                    if connection_type:
                        metadata = self.connection_metadata.get(websocket, {})
                        if metadata.get("connection_type") == connection_type:
                            target_connections.add(websocket)
                    else:
                        target_connections.add(websocket)
            
            # Send to all target connections
            disconnected_connections = []
            for websocket in target_connections:
                try:
                    await websocket.send_text(json.dumps(message))
                except WebSocketDisconnect:
                    disconnected_connections.append(websocket)
                except Exception as e:
                    logger.error(f"Error broadcasting message: {str(e)}")
                    disconnected_connections.append(websocket)
            
            # Clean up disconnected connections
            for websocket in disconnected_connections:
                await self.disconnect(websocket)
                
        except Exception as e:
            logger.error(f"Error broadcasting message: {str(e)}")
    
    async def get_connection_stats(self) -> Dict[str, Any]:
        """Get WebSocket connection statistics."""
        try:
            total_connections = sum(len(connections) for connections in self.user_connections.values())
            
            connection_types = defaultdict(int)
            rooms = defaultdict(int)
            
            for websocket, metadata in self.connection_metadata.items():
                connection_types[metadata.get("connection_type", "unknown")] += 1
                room_id = metadata.get("room_id")
                if room_id:
                    rooms[room_id] += 1
            
            return {
                "total_connections": total_connections,
                "unique_users": len(self.user_connections),
                "connection_types": dict(connection_types),
                "active_rooms": dict(rooms),
                "instance_id": id(self)
            }
            
        except Exception as e:
            logger.error(f"Error getting connection stats: {str(e)}")
            return {}

    async def broadcast_to_channel(self, channel: str, message: Dict[str, Any]):
        """
        Broadcast message to a specific channel.

        Args:
            channel: Channel name
            message: Message to broadcast
        """
        try:
            # For now, use room-based broadcasting
            await self.send_to_room(channel, message)
        except Exception as e:
            logger.error(f"Error broadcasting to channel {channel}: {str(e)}")

    async def subscribe_to_channel(self, connection_id: str, channel: str):
        """
        Subscribe a connection to a channel.

        Args:
            connection_id: Connection identifier
            channel: Channel name
        """
        try:
            # For now, use room joining
            # In a real implementation, this would manage channel subscriptions
            logger.info(f"Connection {connection_id} subscribed to channel {channel}")
        except Exception as e:
            logger.error(f"Error subscribing to channel {channel}: {str(e)}")

    async def unsubscribe_from_channel(self, connection_id: str, channel: str):
        """
        Unsubscribe a connection from a channel.

        Args:
            connection_id: Connection identifier
            channel: Channel name
        """
        try:
            # For now, use room leaving
            # In a real implementation, this would manage channel subscriptions
            logger.info(f"Connection {connection_id} unsubscribed from channel {channel}")
        except Exception as e:
            logger.error(f"Error unsubscribing from channel {channel}: {str(e)}")

    async def send_to_connection(self, connection_id: str, message: Dict[str, Any]):
        """
        Send message to a specific connection.

        Args:
            connection_id: Connection identifier
            message: Message to send
        """
        try:
            # For now, extract user_id from connection_id and send to user
            # In a real implementation, this would track individual connections
            if "_" in connection_id:
                user_id = connection_id.split("_")[1]
                await self.send_to_user(user_id, message)
        except Exception as e:
            logger.error(f"Error sending to connection {connection_id}: {str(e)}")


# Create singleton instance
websocket_manager = ConnectionManager()
