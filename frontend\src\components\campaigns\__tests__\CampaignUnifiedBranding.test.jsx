// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import CampaignUnifiedBranding from '../CampaignUnifiedBranding';

// Mock the notification hook
const mockShowSuccessNotification = vi.fn();
const mockShowErrorNotification = vi.fn();

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showSuccessNotification: mockShowSuccessNotification,
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Mock the branding hook
const mockBrandingData = {
  colorSystem: {
    primary: '#4E40C5',
    secondary: '#EBAE1B',
    accent: '#FF5733',
    background: '#FFFFFF',
    text: '#15110E'
  },
  visualStyle: {
    photographyStyle: 'lifestyle',
    lighting: 'bright-airy',
    saturation: 0,
    contrast: 0,
    brightness: 0,
    warmth: 0
  },
  fonts: ['Inter', 'Roboto'],
  style: 'professional',
  logo_url: 'https://example.com/logo.png',
  logo_settings: {
    size: 30,
    position: 'bottom-right',
    opacity: 100
  }
};

vi.mock('../../../hooks/useBranding', () => ({
  default: () => ({
    brandingData: mockBrandingData,
    loading: false,
  }),
}));

// Mock the VisualBrandEditor component
vi.mock('../../branding/VisualBrandEditor', () => ({
  default: ({ brandingData, onChange }) => (
    <div data-testid="visual-brand-editor">
      <span>Visual Brand Editor Component</span>
      <button onClick={() => onChange({ colorSystem: { primary: '#FF0000' } })}>
        Change Branding
      </button>
    </div>
  ),
}));

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('CampaignUnifiedBranding', () => {
  const mockCampaignBranding = {
    colorSystem: {
      primary: '#FF0000',
      secondary: '#00FF00'
    },
    fonts: ['Helvetica'],
    style: 'modern'
  };

  const mockProps = {
    campaignBranding: mockCampaignBranding,
    useCustomBranding: false,
    onBrandingChange: vi.fn(),
    onUseCustomBrandingChange: vi.fn(),
    campaignName: 'Test Campaign'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders campaign unified branding correctly', () => {
    render(
      <TestWrapper>
        <CampaignUnifiedBranding {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Campaign Branding')).toBeInTheDocument();
    expect(screen.getByText('Test Campaign')).toBeInTheDocument();
  });

  test('shows global branding alert when custom branding is disabled', () => {
    render(
      <TestWrapper>
        <CampaignUnifiedBranding {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText(/This campaign will use your global branding settings/)).toBeInTheDocument();
  });

  test('shows branding editor when custom branding is enabled', () => {
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignUnifiedBranding {...customBrandingProps} />
      </TestWrapper>
    );

    expect(screen.getByTestId('visual-brand-editor')).toBeInTheDocument();
    expect(screen.getByText('Visual Brand Editor Component')).toBeInTheDocument();
  });

  test('handles custom branding toggle', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignUnifiedBranding {...mockProps} />
      </TestWrapper>
    );

    const toggle = screen.getByRole('checkbox');
    await user.click(toggle);

    expect(mockProps.onUseCustomBrandingChange).toHaveBeenCalledWith(true);
  });

  test('handles copying global branding', async () => {
    const user = userEvent.setup();
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignUnifiedBranding {...customBrandingProps} />
      </TestWrapper>
    );

    const copyButton = screen.getByText('Copy from Global Branding');
    await user.click(copyButton);

    expect(mockProps.onBrandingChange).toHaveBeenCalledWith(mockBrandingData);
    expect(mockShowSuccessNotification).toHaveBeenCalledWith('Global branding copied to campaign successfully!');
  });

  test('handles saving campaign branding', async () => {
    const user = userEvent.setup();
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignUnifiedBranding {...customBrandingProps} />
      </TestWrapper>
    );

    const saveButton = screen.getByText('Save Campaign Branding');
    await user.click(saveButton);

    expect(mockShowSuccessNotification).toHaveBeenCalledWith('Campaign branding saved successfully!');
  });

  test('handles branding changes from editor', async () => {
    const user = userEvent.setup();
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignUnifiedBranding {...customBrandingProps} />
      </TestWrapper>
    );

    const changeBrandingButton = screen.getByText('Change Branding');
    await user.click(changeBrandingButton);

    await waitFor(() => {
      expect(mockProps.onBrandingChange).toHaveBeenCalledWith(
        expect.objectContaining({
          colorSystem: { primary: '#FF0000' }
        })
      );
    });
  });

  test('shows loading state during operations', async () => {
    const user = userEvent.setup();
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignUnifiedBranding {...customBrandingProps} />
      </TestWrapper>
    );

    const saveButton = screen.getByText('Save Campaign Branding');
    await user.click(saveButton);

    // Check for loading state (this would be more complex in a real implementation)
    expect(mockShowSuccessNotification).toHaveBeenCalled();
  });

  test('handles comparison between global and campaign branding', async () => {
    const user = userEvent.setup();
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignUnifiedBranding {...customBrandingProps} />
      </TestWrapper>
    );

    const compareButton = screen.getByText('Compare with Global');
    await user.click(compareButton);

    // In a real implementation, this would show a comparison dialog
    expect(compareButton).toBeInTheDocument();
  });

  test('initializes with campaign branding when custom branding is enabled', () => {
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignUnifiedBranding {...customBrandingProps} />
      </TestWrapper>
    );

    // Component should initialize with campaign branding data
    expect(screen.getByTestId('visual-brand-editor')).toBeInTheDocument();
  });

  test('initializes with global branding when custom branding is disabled', () => {
    render(
      <TestWrapper>
        <CampaignUnifiedBranding {...mockProps} />
      </TestWrapper>
    );

    // Should show global branding alert
    expect(screen.getByText(/This campaign will use your global branding settings/)).toBeInTheDocument();
  });

  test('renders with default props when no campaign branding provided', () => {
    const propsWithoutCampaignBranding = {
      ...mockProps,
      campaignBranding: null
    };

    render(
      <TestWrapper>
        <CampaignUnifiedBranding {...propsWithoutCampaignBranding} />
      </TestWrapper>
    );

    expect(screen.getByText('Campaign Branding')).toBeInTheDocument();
  });

  test('renders with default campaign name when not provided', () => {
    const propsWithoutCampaignName = {
      ...mockProps,
      campaignName: ''
    };

    render(
      <TestWrapper>
        <CampaignUnifiedBranding {...propsWithoutCampaignName} />
      </TestWrapper>
    );

    expect(screen.getByText('Campaign Branding')).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignUnifiedBranding {...customBrandingProps} />
      </TestWrapper>
    );

    // Check for proper form controls
    expect(screen.getByRole('checkbox')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /copy from global branding/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /save campaign branding/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /compare with global/i })).toBeInTheDocument();
  });

  test('handles switching from custom to global branding', async () => {
    const user = userEvent.setup();
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignUnifiedBranding {...customBrandingProps} />
      </TestWrapper>
    );

    // Toggle off custom branding
    const toggle = screen.getByRole('checkbox');
    await user.click(toggle);

    expect(mockProps.onUseCustomBrandingChange).toHaveBeenCalledWith(false);
  });

  test('shows proper branding status messages', () => {
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignUnifiedBranding {...customBrandingProps} />
      </TestWrapper>
    );

    expect(screen.getByText(/Configure custom branding settings for this campaign/)).toBeInTheDocument();
  });

  test('handles branding data merging correctly', async () => {
    const user = userEvent.setup();
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true,
      campaignBranding: null // No existing campaign branding
    };

    render(
      <TestWrapper>
        <CampaignUnifiedBranding {...customBrandingProps} />
      </TestWrapper>
    );

    const copyButton = screen.getByText('Copy from Global Branding');
    await user.click(copyButton);

    // Should copy global branding when no campaign branding exists
    expect(mockProps.onBrandingChange).toHaveBeenCalledWith(mockBrandingData);
  });

  test('preserves existing campaign branding when toggling', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignUnifiedBranding {...mockProps} />
      </TestWrapper>
    );

    // Toggle to custom branding
    const toggle = screen.getByRole('checkbox');
    await user.click(toggle);

    expect(mockProps.onUseCustomBrandingChange).toHaveBeenCalledWith(true);
    // Campaign branding should be preserved
  });

  test('shows appropriate buttons based on branding state', () => {
    const { rerender } = render(
      <TestWrapper>
        <CampaignUnifiedBranding {...mockProps} />
      </TestWrapper>
    );

    // When using global branding, should not show editor buttons
    expect(screen.queryByText('Copy from Global Branding')).not.toBeInTheDocument();
    expect(screen.queryByText('Save Campaign Branding')).not.toBeInTheDocument();

    // When using custom branding, should show editor buttons
    rerender(
      <TestWrapper>
        <CampaignUnifiedBranding {...mockProps} useCustomBranding={true} />
      </TestWrapper>
    );

    expect(screen.getByText('Copy from Global Branding')).toBeInTheDocument();
    expect(screen.getByText('Save Campaign Branding')).toBeInTheDocument();
    expect(screen.getByText('Compare with Global')).toBeInTheDocument();
  });
});
