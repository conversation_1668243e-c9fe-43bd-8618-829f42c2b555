"""
Multi-currency Support API Routes.
Provides endpoints for currency conversion, user preferences, and exchange rates.
@since 2024-1-1 to 2025-25-7
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
from decimal import Decimal
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import JSONResponse

from app.models.user import User
from app.middleware.auth import get_current_active_user
from app.api.dependencies.rate_limiter import rate_limit
from app.api.dependencies.feature_access import require_feature_access
from app.services.ecommerce.currency_service import currency_service
from app.schemas.ecommerce import (
    CurrencyConversionRequest, CurrencyConversionResponse,
    CurrencyPreferencesRequest
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/exchange-rates")
@rate_limit("get_exchange_rates", limit=100, window=3600)
async def get_exchange_rates(
    base_currency: str = Query("USD", description="Base currency code"),
    target_currencies: Optional[List[str]] = Query(None, description="Target currencies"),
    force_refresh: bool = Query(False, description="Force refresh from API"),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(require_feature_access("ecommerce_integration"))
):
    """
    Get current exchange rates for specified currencies.
    
    Args:
        base_currency: Base currency code (default: USD)
        target_currencies: List of target currencies (optional)
        force_refresh: Force refresh from external APIs
        current_user: Current authenticated user
        
    Returns:
        Current exchange rates with metadata
    """
    try:
        # Validate currency codes
        if len(base_currency) != 3 or not base_currency.isupper():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid base currency code. Must be 3-letter uppercase code."
            )
        
        if target_currencies:
            invalid_currencies = [
                c for c in target_currencies 
                if len(c) != 3 or not c.isupper()
            ]
            if invalid_currencies:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid currency codes: {invalid_currencies}"
                )
        
        result = await currency_service.fetch_exchange_rates(
            base_currency=base_currency,
            target_currencies=target_currencies,
            force_refresh=force_refresh
        )
        
        if not result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Unable to fetch exchange rates. Please try again later."
            )
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting exchange rates: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get exchange rates"
        )


@router.post("/convert", response_model=CurrencyConversionResponse)
@rate_limit("convert_currency", limit=200, window=3600)
async def convert_currency(
    request: CurrencyConversionRequest,
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(require_feature_access("ecommerce_integration"))
):
    """
    Convert amount from one currency to another.
    
    Args:
        request: Currency conversion request
        current_user: Current authenticated user
        
    Returns:
        Conversion result with exchange rate and metadata
    """
    try:
        result = await currency_service.convert_currency(
            amount=request.amount,
            from_currency=request.from_currency,
            to_currency=request.to_currency,
            user_id=str(current_user.id)
        )
        
        if not result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "Currency conversion failed")
            )
        
        return CurrencyConversionResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error converting currency: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to convert currency"
        )


@router.post("/preferences")
@rate_limit("set_currency_preferences", limit=50, window=3600)
async def set_currency_preferences(
    request: CurrencyPreferencesRequest,
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(require_feature_access("ecommerce_integration"))
):
    """
    Set user currency preferences.
    
    Args:
        request: Currency preferences request
        current_user: Current authenticated user
        
    Returns:
        Updated preferences
    """
    try:
        result = await currency_service.set_user_currency_preferences(
            user_id=str(current_user.id),
            default_currency=request.default_currency,
            display_currencies=request.display_currencies,
            auto_convert=request.auto_convert
        )
        
        if not result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "Failed to set currency preferences")
            )
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error setting currency preferences: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to set currency preferences"
        )


@router.get("/preferences")
@rate_limit("get_currency_preferences", limit=100, window=3600)
async def get_currency_preferences(
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(require_feature_access("ecommerce_integration"))
):
    """
    Get user currency preferences.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        User currency preferences
    """
    try:
        result = await currency_service.get_user_currency_preferences(
            user_id=str(current_user.id)
        )
        
        if not result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "Failed to get currency preferences")
            )
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting currency preferences: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get currency preferences"
        )


@router.get("/supported")
@rate_limit("get_supported_currencies", limit=50, window=3600)
async def get_supported_currencies(
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(require_feature_access("ecommerce_integration"))
):
    """
    Get list of supported currencies with metadata.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Supported currencies with names and symbols
    """
    try:
        result = await currency_service.get_supported_currencies()
        
        if not result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get supported currencies"
            )
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=result
        )
        
    except Exception as e:
        logger.error(f"Error getting supported currencies: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get supported currencies"
        )


@router.post("/bulk-convert")
@rate_limit("bulk_convert_currency", limit=50, window=3600)
async def bulk_convert_currency(
    amounts: List[Dict[str, Any]],
    to_currency: str = Query(..., description="Target currency for all conversions"),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(require_feature_access("ecommerce_integration"))
):
    """
    Convert multiple amounts to a target currency.
    
    Args:
        amounts: List of amounts with currency codes
        to_currency: Target currency for all conversions
        current_user: Current authenticated user
        
    Returns:
        Bulk conversion results
    """
    try:
        if len(amounts) > 100:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Maximum 100 conversions allowed per request"
            )
        
        results = []
        for item in amounts:
            try:
                amount = Decimal(str(item.get("amount", 0)))
                from_currency = item.get("currency", "USD")
                
                conversion_result = await currency_service.convert_currency(
                    amount=amount,
                    from_currency=from_currency,
                    to_currency=to_currency,
                    user_id=str(current_user.id)
                )
                
                results.append({
                    "original": item,
                    "conversion": conversion_result
                })
                
            except Exception as e:
                results.append({
                    "original": item,
                    "conversion": {
                        "success": False,
                        "error": str(e)
                    }
                })
        
        successful_conversions = sum(1 for r in results if r["conversion"].get("success"))
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "total_conversions": len(amounts),
                "successful_conversions": successful_conversions,
                "failed_conversions": len(amounts) - successful_conversions,
                "target_currency": to_currency,
                "results": results,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in bulk currency conversion: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform bulk currency conversion"
        )
