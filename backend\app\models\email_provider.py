"""
Email Provider Configuration Models

This module defines the MongoDB models for email provider configurations,
supporting multiple email providers with encryption and failover capabilities.

@since 2024-1-1 to 2025-25-7
"""
from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field, validator
from bson import ObjectId
from enum import Enum


class EmailProviderType(str, Enum):
    """Supported email provider types."""
    SMTP = "smtp"
    SENDGRID = "sendgrid"
    MAILGUN = "mailgun"
    AWS_SES = "aws_ses"


class EmailType(str, Enum):
    """Email categorization types."""
    TRANSACTIONAL = "transactional"
    MARKETING = "marketing"
    SYSTEM = "system"


class ProviderStatus(str, Enum):
    """Provider status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    FAILED = "failed"
    TESTING = "testing"


class RateLimits(BaseModel):
    """Rate limiting configuration for email providers."""
    per_minute: int = Field(default=60, ge=1, le=10000)
    per_hour: int = Field(default=1000, ge=1, le=100000)
    per_day: int = Field(default=10000, ge=1, le=1000000)


class BounceHandling(BaseModel):
    """Bounce handling configuration."""
    webhook_url: Optional[str] = None
    max_bounce_rate: float = Field(default=5.0, ge=0.0, le=100.0)
    auto_disable: bool = Field(default=True)


class SMTPSettings(BaseModel):
    """SMTP-specific configuration."""
    host: str
    port: int = Field(default=587, ge=1, le=65535)
    username: str
    password: str  # Will be encrypted
    use_tls: bool = Field(default=True)
    use_ssl: bool = Field(default=False)
    
    @validator('port')
    def validate_port(cls, v):
        if v not in [25, 465, 587, 2525]:
            raise ValueError('Port must be a valid SMTP port (25, 465, 587, 2525)')
        return v


class SendGridSettings(BaseModel):
    """SendGrid-specific configuration."""
    api_key: str  # Will be encrypted


class MailgunSettings(BaseModel):
    """Mailgun-specific configuration."""
    api_key: str  # Will be encrypted
    domain: str
    region: str = Field(default="us")  # us, eu


class AWSSESSettings(BaseModel):
    """AWS SES-specific configuration."""
    access_key_id: str  # Will be encrypted
    secret_access_key: str  # Will be encrypted
    region: str = Field(default="us-east-1")


class EmailProviderSettings(BaseModel):
    """Union of all provider-specific settings."""
    smtp: Optional[SMTPSettings] = None
    sendgrid: Optional[SendGridSettings] = None
    mailgun: Optional[MailgunSettings] = None
    aws_ses: Optional[AWSSESSettings] = None


class EmailProviderConfig(BaseModel):
    """Email provider configuration model."""
    id: Optional[str] = Field(default=None, alias="_id")
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(default="", max_length=500)
    provider_type: EmailProviderType
    is_active: bool = Field(default=True)
    priority: int = Field(default=1, ge=1, le=100)  # Lower number = higher priority
    
    # Email configuration
    from_email: str = Field(..., pattern=r'^[^@]+@[^@]+\.[^@]+$')
    from_name: str = Field(..., min_length=1, max_length=100)
    reply_to: Optional[str] = Field(default=None, pattern=r'^[^@]+@[^@]+\.[^@]+$')
    
    # Provider-specific settings
    settings: EmailProviderSettings
    
    # Rate limiting and bounce handling
    rate_limits: RateLimits = Field(default_factory=RateLimits)
    bounce_handling: BounceHandling = Field(default_factory=BounceHandling)
    
    # Email type assignments
    email_types: List[EmailType] = Field(default_factory=lambda: [EmailType.TRANSACTIONAL])
    
    # Status and monitoring
    status: ProviderStatus = Field(default=ProviderStatus.ACTIVE)
    last_test_date: Optional[datetime] = None
    last_test_result: Optional[Dict[str, Any]] = None
    
    # Health monitoring
    success_count: int = Field(default=0, ge=0)
    failure_count: int = Field(default=0, ge=0)
    last_success_date: Optional[datetime] = None
    last_failure_date: Optional[datetime] = None
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: str  # Admin user ID
    updated_by: Optional[str] = None  # Admin user ID
    
    class Config:
        allow_population_by_field_name = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }
    
    @validator('priority')
    def validate_priority_uniqueness(cls, v, values):
        # Note: Uniqueness validation will be handled at the service level
        return v
    
    @validator('email_types')
    def validate_email_types_not_empty(cls, v):
        if not v:
            raise ValueError('At least one email type must be assigned')
        return v


class EmailProviderTest(BaseModel):
    """Model for email provider testing."""
    provider_id: str
    test_email: str = Field(..., pattern=r'^[^@]+@[^@]+\.[^@]+$')
    test_subject: str = Field(default="ACE Social Email Configuration Test")
    test_message: str = Field(default="This is a test email to verify your email provider configuration.")


class EmailProviderStats(BaseModel):
    """Email provider statistics model."""
    provider_id: str
    provider_name: str
    total_sent: int = Field(default=0, ge=0)
    total_delivered: int = Field(default=0, ge=0)
    total_bounced: int = Field(default=0, ge=0)
    total_failed: int = Field(default=0, ge=0)
    bounce_rate: float = Field(default=0.0, ge=0.0, le=100.0)
    delivery_rate: float = Field(default=0.0, ge=0.0, le=100.0)
    last_24h_sent: int = Field(default=0, ge=0)
    last_24h_failed: int = Field(default=0, ge=0)
    avg_response_time: float = Field(default=0.0, ge=0.0)  # in milliseconds
    status: ProviderStatus
    last_updated: datetime = Field(default_factory=datetime.utcnow)


class EmailDeliveryLog(BaseModel):
    """Email delivery log model for tracking."""
    id: Optional[str] = Field(default=None, alias="_id")
    provider_id: str
    provider_name: str
    email_type: EmailType
    recipient_email: str
    subject: str
    template_name: str
    status: str  # sent, delivered, bounced, failed
    response_time: Optional[float] = None  # in milliseconds
    error_message: Optional[str] = None
    external_id: Optional[str] = None  # Provider's message ID
    sent_at: datetime = Field(default_factory=datetime.utcnow)
    delivered_at: Optional[datetime] = None
    
    class Config:
        allow_population_by_field_name = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }


class EmailProviderFailover(BaseModel):
    """Failover configuration for email providers."""
    id: Optional[str] = Field(default=None, alias="_id")
    email_type: EmailType
    primary_provider_id: str
    fallback_providers: List[str] = Field(default_factory=list)  # Ordered by priority
    max_retries: int = Field(default=3, ge=1, le=10)
    retry_delay: int = Field(default=300, ge=60, le=3600)  # seconds
    circuit_breaker_threshold: int = Field(default=5, ge=1, le=50)
    circuit_breaker_timeout: int = Field(default=300, ge=60, le=3600)  # seconds
    is_active: bool = Field(default=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        allow_population_by_field_name = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }
