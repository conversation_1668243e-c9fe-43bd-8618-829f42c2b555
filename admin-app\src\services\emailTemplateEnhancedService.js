// @since 2024-1-1 to 2025-25-7
import axios from 'axios';
import { API_BASE_URL } from '../config';

/**
 * Enhanced Email Template Service
 * Handles all API communications for comprehensive email management
 */
class EmailTemplateEnhancedService {
  constructor() {
    this.baseURL = `${API_BASE_URL}/admin/email-management`;
    this.setupInterceptors();
  }

  /**
   * Setup axios interceptors for authentication and error handling
   */
  setupInterceptors() {
    // Request interceptor to add auth token
    axios.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('adminToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    axios.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Handle unauthorized access
          localStorage.removeItem('adminToken');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Template Management Methods

  /**
   * Get all email templates with optional filtering
   */
  async getTemplates(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.skip !== undefined) params.append('skip', filters.skip);
      if (filters.limit !== undefined) params.append('limit', filters.limit);
      if (filters.category) params.append('category', filters.category);
      if (filters.status) params.append('status', filters.status);
      if (filters.search) params.append('search', filters.search);
      if (filters.tags) filters.tags.forEach(tag => params.append('tags', tag));
      if (filters.language) params.append('language', filters.language);
      if (filters.created_by) params.append('created_by', filters.created_by);

      const response = await axios.get(`${this.baseURL}/templates?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching email templates:', error);
      throw error;
    }
  }

  /**
   * Get a specific email template by ID
   */
  async getTemplate(templateId) {
    try {
      const response = await axios.get(`${this.baseURL}/templates/${templateId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching email template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new email template
   */
  async createTemplate(templateData) {
    try {
      const response = await axios.post(`${this.baseURL}/templates`, templateData);
      return response.data;
    } catch (error) {
      console.error('Error creating email template:', error);
      throw error;
    }
  }

  /**
   * Update an existing email template
   */
  async updateTemplate(templateId, templateData) {
    try {
      const response = await axios.put(`${this.baseURL}/templates/${templateId}`, templateData);
      return response.data;
    } catch (error) {
      console.error(`Error updating email template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Delete an email template
   */
  async deleteTemplate(templateId) {
    try {
      const response = await axios.delete(`${this.baseURL}/templates/${templateId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting email template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Duplicate an email template
   */
  async duplicateTemplate(templateId, duplicateData) {
    try {
      const response = await axios.post(`${this.baseURL}/templates/${templateId}/duplicate`, duplicateData);
      return response.data;
    } catch (error) {
      console.error(`Error duplicating email template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Preview an email template with sample data
   */
  async previewTemplate(templateId, templateData) {
    try {
      const response = await axios.post(`${this.baseURL}/templates/${templateId}/preview`, {
        template_data: templateData
      });
      return response.data;
    } catch (error) {
      console.error(`Error previewing email template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Validate an email template
   */
  async validateTemplate(templateData) {
    try {
      const response = await axios.post(`${this.baseURL}/templates/validate`, templateData);
      return response.data;
    } catch (error) {
      console.error('Error validating email template:', error);
      throw error;
    }
  }

  /**
   * Import HTML template from external source
   */
  async importHtmlTemplate(htmlData) {
    try {
      const response = await axios.post(`${this.baseURL}/templates/import-html`, htmlData);
      return response.data;
    } catch (error) {
      console.error('Error importing HTML template:', error);
      throw error;
    }
  }

  // Campaign Management Methods

  /**
   * Get all email campaigns with optional filtering
   */
  async getCampaigns(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.skip !== undefined) params.append('skip', filters.skip);
      if (filters.limit !== undefined) params.append('limit', filters.limit);
      if (filters.status) params.append('status', filters.status);
      if (filters.template_id) params.append('template_id', filters.template_id);
      if (filters.search) params.append('search', filters.search);
      if (filters.created_by) params.append('created_by', filters.created_by);

      const response = await axios.get(`${this.baseURL}/campaigns?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching email campaigns:', error);
      throw error;
    }
  }

  /**
   * Get a specific email campaign by ID
   */
  async getCampaign(campaignId) {
    try {
      const response = await axios.get(`${this.baseURL}/campaigns/${campaignId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching email campaign ${campaignId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new email campaign
   */
  async createCampaign(campaignData) {
    try {
      const response = await axios.post(`${this.baseURL}/campaigns`, campaignData);
      return response.data;
    } catch (error) {
      console.error('Error creating email campaign:', error);
      throw error;
    }
  }

  /**
   * Update an existing email campaign
   */
  async updateCampaign(campaignId, campaignData) {
    try {
      const response = await axios.put(`${this.baseURL}/campaigns/${campaignId}`, campaignData);
      return response.data;
    } catch (error) {
      console.error(`Error updating email campaign ${campaignId}:`, error);
      throw error;
    }
  }

  /**
   * Delete an email campaign
   */
  async deleteCampaign(campaignId) {
    try {
      const response = await axios.delete(`${this.baseURL}/campaigns/${campaignId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting email campaign ${campaignId}:`, error);
      throw error;
    }
  }

  /**
   * Test an email campaign
   */
  async testCampaign(campaignId, testData) {
    try {
      const response = await axios.post(`${this.baseURL}/campaigns/${campaignId}/test`, testData);
      return response.data;
    } catch (error) {
      console.error(`Error testing email campaign ${campaignId}:`, error);
      throw error;
    }
  }

  // Trigger Management Methods

  /**
   * Get all email triggers with optional filtering
   */
  async getTriggers(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.skip !== undefined) params.append('skip', filters.skip);
      if (filters.limit !== undefined) params.append('limit', filters.limit);
      if (filters.trigger_type) params.append('trigger_type', filters.trigger_type);
      if (filters.event_name) params.append('event_name', filters.event_name);
      if (filters.template_id) params.append('template_id', filters.template_id);
      if (filters.is_active !== undefined) params.append('is_active', filters.is_active);
      if (filters.search) params.append('search', filters.search);

      const response = await axios.get(`${this.baseURL}/triggers?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching email triggers:', error);
      throw error;
    }
  }

  /**
   * Create a new email trigger
   */
  async createTrigger(triggerData) {
    try {
      const response = await axios.post(`${this.baseURL}/triggers`, triggerData);
      return response.data;
    } catch (error) {
      console.error('Error creating email trigger:', error);
      throw error;
    }
  }

  /**
   * Update an existing email trigger
   */
  async updateTrigger(triggerId, triggerData) {
    try {
      const response = await axios.put(`${this.baseURL}/triggers/${triggerId}`, triggerData);
      return response.data;
    } catch (error) {
      console.error(`Error updating email trigger ${triggerId}:`, error);
      throw error;
    }
  }

  /**
   * Delete an email trigger
   */
  async deleteTrigger(triggerId) {
    try {
      const response = await axios.delete(`${this.baseURL}/triggers/${triggerId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting email trigger ${triggerId}:`, error);
      throw error;
    }
  }

  // Dashboard and Analytics Methods

  /**
   * Get email management dashboard overview
   */
  async getDashboard() {
    try {
      const response = await axios.get(`${this.baseURL}/dashboard`);
      return response.data;
    } catch (error) {
      console.error('Error fetching email dashboard:', error);
      throw error;
    }
  }

  /**
   * Get template analytics
   */
  async getTemplateAnalytics(templateId, filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.start_date) params.append('start_date', filters.start_date);
      if (filters.end_date) params.append('end_date', filters.end_date);

      const response = await axios.get(`${this.baseURL}/templates/${templateId}/analytics?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching template analytics ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Get campaign statistics
   */
  async getCampaignStats(campaignId) {
    try {
      const response = await axios.get(`${this.baseURL}/campaigns/${campaignId}/stats`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching campaign stats ${campaignId}:`, error);
      throw error;
    }
  }

  // Bulk Operations

  /**
   * Perform bulk operations on templates
   */
  async bulkTemplateOperation(operation, itemIds, confirm = false) {
    try {
      const response = await axios.post(`${this.baseURL}/templates/bulk`, {
        operation,
        item_ids: itemIds,
        confirm
      });
      return response.data;
    } catch (error) {
      console.error('Error performing bulk template operation:', error);
      throw error;
    }
  }

  // Utility Methods

  /**
   * Get template categories
   */
  getTemplateCategories() {
    return [
      {
        value: 'transactional',
        label: 'Transactional',
        description: 'User-triggered emails (welcome, password reset, etc.)'
      },
      {
        value: 'marketing',
        label: 'Marketing',
        description: 'Promotional and campaign emails'
      },
      {
        value: 'system',
        label: 'System',
        description: 'Administrative and monitoring notifications'
      }
    ];
  }

  /**
   * Get template statuses
   */
  getTemplateStatuses() {
    return [
      { value: 'draft', label: 'Draft', color: 'default' },
      { value: 'active', label: 'Active', color: 'success' },
      { value: 'archived', label: 'Archived', color: 'warning' },
      { value: 'testing', label: 'Testing', color: 'info' }
    ];
  }

  /**
   * Get campaign statuses
   */
  getCampaignStatuses() {
    return [
      { value: 'draft', label: 'Draft', color: 'default' },
      { value: 'scheduled', label: 'Scheduled', color: 'info' },
      { value: 'running', label: 'Running', color: 'success' },
      { value: 'paused', label: 'Paused', color: 'warning' },
      { value: 'completed', label: 'Completed', color: 'primary' },
      { value: 'cancelled', label: 'Cancelled', color: 'error' }
    ];
  }

  /**
   * Get trigger types
   */
  getTriggerTypes() {
    return [
      {
        value: 'user_lifecycle',
        label: 'User Lifecycle',
        description: 'Registration, trial, subscription events'
      },
      {
        value: 'behavioral',
        label: 'Behavioral',
        description: 'User activity and engagement triggers'
      },
      {
        value: 'business_event',
        label: 'Business Event',
        description: 'Feature announcements, maintenance alerts'
      },
      {
        value: 'custom_event',
        label: 'Custom Event',
        description: 'API-triggered custom events'
      },
      {
        value: 'scheduled',
        label: 'Scheduled',
        description: 'Time-based recurring emails'
      }
    ];
  }
}

// Create and export a singleton instance
export const emailTemplateEnhancedService = new EmailTemplateEnhancedService();
export default emailTemplateEnhancedService;
