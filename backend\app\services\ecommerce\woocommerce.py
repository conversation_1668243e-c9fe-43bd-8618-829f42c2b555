"""
WooCommerce e-commerce integration service.
@since 2024-1-1 to 2025-25-7
"""

import secrets
import hmac
import hashlib
import base64
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone
from decimal import Decimal
import logging
import aiohttp
from urllib.parse import urlencode

from app.core.config import settings
from app.models.ecommerce import (
    EcommerceStore, 
    SyncedProduct, 
    ProductVariant, 
    ProductImage,
    ProductStatusEnum
)
from app.models.common import EcommercePlatformEnum, SyncStatusEnum
from app.models.user import PyObjectId, utc_now
from app.services.ecommerce.base import (
    BaseEcommerceIntegration,
    AuthenticationError,
    SyncError
)

logger = logging.getLogger(__name__)


class WooCommerceIntegration(BaseEcommerceIntegration):
    """
    WooCommerce integration service using REST API with consumer key/secret.
    """
    
    platform_name: str = "woocommerce"
    
    async def get_authorization_url(self, redirect_uri: str, store_url: Optional[str] = None) -> <PERSON>ple[str, str]:
        """
        Get the authorization URL for WooCommerce OAuth flow.
        
        Args:
            redirect_uri: The redirect URI for the OAuth callback
            store_url: WooCommerce store URL (required)
            
        Returns:
            Tuple of (authorization_url, state)
        """
        if not store_url:
            raise ValueError("Store URL is required for WooCommerce integration")
        
        # Generate a random state parameter to prevent CSRF
        state = secrets.token_urlsafe(32)
        
        # WooCommerce OAuth parameters
        params = {
            "app_name": "ACEO Social Media Platform",
            "scope": "read",
            "user_id": "aceo_user",
            "return_url": redirect_uri,
            "callback_url": redirect_uri
        }
        
        # Construct the authorization URL
        auth_url = f"{store_url.rstrip('/')}/wc-auth/v1/authorize?" + urlencode(params)
        
        # Include store URL in state for callback processing
        state_with_store = f"{state}:{store_url}"
        
        return auth_url, state_with_store
    
    async def handle_oauth_callback(
        self, 
        code: str, 
        state: str, 
        redirect_uri: str,
        store_url: Optional[str] = None
    ) -> EcommerceStore:
        """
        Handle the OAuth callback and get access token.
        
        Args:
            code: The authorization code from the callback (consumer_key for WooCommerce)
            state: The state parameter from the callback
            redirect_uri: The redirect URI used in the authorization request
            store_url: Store URL (can be extracted from state)
            
        Returns:
            EcommerceStore object with tokens and store info
        """
        try:
            # Extract store URL from state
            if ":" in state:
                _, extracted_store_url = state.rsplit(":", 1)
                store_url = store_url or extracted_store_url
                # Log redirect_uri for debugging
                logger.debug(f"WooCommerce OAuth callback for store: {store_url}, redirect_uri: {redirect_uri}")
            else:
                raise AuthenticationError("Invalid state parameter")
            
            if not store_url:
                raise AuthenticationError("Store URL is required")
            
            # For WooCommerce, the 'code' parameter contains the consumer_key
            # and we need to extract consumer_secret from the callback
            # This is a simplified implementation - in practice, you'd get both from the callback
            consumer_key = code
            
            # Test the connection with the provided credentials
            store_info = await self._get_store_info_with_credentials(store_url, consumer_key, "")
            
            # Create store object
            store = EcommerceStore(
                user_id=PyObjectId(),  # Will be set by the calling service
                platform=EcommercePlatformEnum.WOOCOMMERCE,
                store_name=store_info.get("name", "WooCommerce Store"),
                store_url=store_url,
                store_id=store_url,  # Use URL as ID for WooCommerce
                access_token=consumer_key,  # Store consumer key as access token
                refresh_token=None,  # WooCommerce doesn't use refresh tokens
                api_key=consumer_key,  # Same as access_token for WooCommerce
                api_secret="",  # Consumer secret will be provided separately
                webhook_secret=None,  # Will be set when webhooks are configured
                status=SyncStatusEnum.CONNECTED,
                last_sync_at=None,
                last_error=None,
                webhook_url=None,  # Will be set when webhooks are configured
                created_at=utc_now(),
                updated_at=utc_now(),
                connected_at=utc_now(),
                metadata={
                    "store_url": store_url,
                    "wc_version": store_info.get("version"),
                    "wp_version": store_info.get("wp_version"),
                    "currency": store_info.get("currency"),
                    "timezone": store_info.get("timezone")
                }
            )
            
            return store
            
        except Exception as e:
            logger.error(f"WooCommerce OAuth callback error: {str(e)}")
            raise AuthenticationError(f"OAuth callback failed: {str(e)}")
    
    async def refresh_access_token(self, store: EcommerceStore) -> EcommerceStore:
        """
        Refresh the access token for a store.
        Note: WooCommerce uses consumer key/secret which don't expire.
        
        Args:
            store: The store with token
            
        Returns:
            Same store (WooCommerce credentials don't expire)
        """
        # WooCommerce consumer key/secret don't expire
        return store
    
    async def test_connection(self, store: EcommerceStore) -> Dict[str, Any]:
        """
        Test the connection to the WooCommerce store.
        
        Args:
            store: The store to test
            
        Returns:
            Connection test results
        """
        try:
            store_info = await self._get_store_info_with_credentials(
                store.store_url, 
                store.access_token, 
                store.api_secret or ""
            )
            
            return {
                "success": True,
                "store_name": store_info.get("name"),
                "version": store_info.get("version"),
                "wp_version": store_info.get("wp_version"),
                "currency": store_info.get("currency"),
                "timezone": store_info.get("timezone")
            }
            
        except Exception as e:
            logger.error(f"WooCommerce connection test failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_store_info(self, store: EcommerceStore) -> Dict[str, Any]:
        """
        Get basic store information.
        
        Args:
            store: The store to get info for
            
        Returns:
            Store information
        """
        return await self._get_store_info_with_credentials(
            store.store_url, 
            store.access_token, 
            store.api_secret or ""
        )
    
    async def sync_products(
        self, 
        store: EcommerceStore, 
        limit: int = 100,
        since_id: Optional[str] = None
    ) -> List[SyncedProduct]:
        """
        Sync products from the WooCommerce store.
        
        Args:
            store: The store to sync from
            limit: Maximum number of products to sync (max 100 for WooCommerce)
            since_id: Sync products created after this ID
            
        Returns:
            List of synced products
        """
        try:
            # Build API URL
            api_url = f"{store.store_url.rstrip('/')}/wp-json/wc/v3/products"
            params: Dict[str, Any] = {"per_page": min(limit, 100)}  # WooCommerce max is 100

            if since_id:
                # WooCommerce uses ISO 8601 date format for 'after' parameter
                # If since_id is a string, use it as-is (assuming it's a date)
                # If it's numeric, convert to string
                params["after"] = str(since_id)
            
            # Make authenticated request
            auth = aiohttp.BasicAuth(store.access_token, store.api_secret or "")
            
            async with aiohttp.ClientSession() as session:
                async with session.get(api_url, params=params, auth=auth) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise SyncError(f"Failed to fetch products: {error_text}")
                    
                    products_data = await response.json()
            
            # Convert to SyncedProduct objects
            synced_products = []
            for product_data in products_data:
                if isinstance(product_data, dict):
                    synced_product = self._convert_woocommerce_product(product_data, store)
                    synced_products.append(synced_product)
            
            return synced_products
            
        except Exception as e:
            logger.error(f"WooCommerce product sync error: {str(e)}")
            raise SyncError(f"Product sync failed: {str(e)}")
    
    async def get_product(self, store: EcommerceStore, product_id: str) -> Optional[SyncedProduct]:
        """
        Get a specific product from the WooCommerce store.
        
        Args:
            store: The store to get product from
            product_id: WooCommerce product ID
            
        Returns:
            Product if found, None otherwise
        """
        try:
            api_url = f"{store.store_url.rstrip('/')}/wp-json/wc/v3/products/{product_id}"
            auth = aiohttp.BasicAuth(store.access_token, store.api_secret or "")
            
            async with aiohttp.ClientSession() as session:
                async with session.get(api_url, auth=auth) as response:
                    if response.status == 404:
                        return None
                    elif response.status != 200:
                        error_text = await response.text()
                        raise SyncError(f"Failed to fetch product: {error_text}")
                    
                    product_data = await response.json()
                    return self._convert_woocommerce_product(product_data, store)
                    
        except Exception as e:
            logger.error(f"WooCommerce get product error: {str(e)}")
            return None
    
    async def setup_webhooks(self, store: EcommerceStore) -> Dict[str, Any]:
        """
        Set up webhooks for real-time sync.
        
        Args:
            store: The store to setup webhooks for
            
        Returns:
            Webhook setup results
        """
        try:
            # Use frontend URL as base, but replace with backend URL for webhooks
            base_url = settings.FRONTEND_URL.replace(":3000", ":8000").replace("localhost", "localhost")
            webhook_url = f"{base_url}/api/ecommerce/webhooks/woocommerce"
            
            # Webhook topics to subscribe to
            webhook_topics = [
                "product.created",
                "product.updated", 
                "product.deleted"
            ]
            
            created_webhooks = []
            auth = aiohttp.BasicAuth(store.access_token, store.api_secret or "")
            
            async with aiohttp.ClientSession() as session:
                for topic in webhook_topics:
                    webhook_data = {
                        "name": f"ACEO {topic}",
                        "topic": topic,
                        "delivery_url": webhook_url,
                        "status": "active"
                    }
                    
                    api_url = f"{store.store_url.rstrip('/')}/wp-json/wc/v3/webhooks"
                    
                    async with session.post(api_url, json=webhook_data, auth=auth) as response:
                        if response.status == 201:
                            webhook_response = await response.json()
                            created_webhooks.append(webhook_response)
                        else:
                            error_text = await response.text()
                            logger.warning(f"Failed to create webhook for {topic}: {error_text}")
            
            return {
                "success": len(created_webhooks) > 0,
                "webhooks_created": len(created_webhooks),
                "webhooks": created_webhooks
            }
            
        except Exception as e:
            logger.error(f"WooCommerce webhook setup error: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def verify_webhook(self, payload: bytes, signature: str, secret: str) -> bool:
        """
        Verify WooCommerce webhook signature.
        
        Args:
            payload: Raw webhook payload
            signature: Webhook signature
            secret: Webhook secret
            
        Returns:
            True if signature is valid
        """
        try:
            # WooCommerce uses HMAC-SHA256
            expected_signature = base64.b64encode(
                hmac.new(secret.encode(), payload, hashlib.sha256).digest()
            ).decode()
            
            return hmac.compare_digest(signature, expected_signature)
            
        except Exception as e:
            logger.error(f"WooCommerce webhook verification error: {str(e)}")
            return False

    async def _get_store_info_with_credentials(self, store_url: str, consumer_key: str, consumer_secret: str) -> Dict[str, Any]:
        """Get store information from WooCommerce API."""
        api_url = f"{store_url.rstrip('/')}/wp-json/wc/v3/system_status"
        auth = aiohttp.BasicAuth(consumer_key, consumer_secret)

        async with aiohttp.ClientSession() as session:
            async with session.get(api_url, auth=auth) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise AuthenticationError(f"Failed to get store info: {error_text}")

                data = await response.json()

                # Extract relevant information
                environment = data.get("environment", {})
                settings_data = data.get("settings", {})

                # Ensure environment and settings_data are dictionaries
                if not isinstance(environment, dict):
                    environment = {}
                if not isinstance(settings_data, dict):
                    settings_data = {}

                return {
                    "name": environment.get("site_url", store_url),
                    "version": environment.get("version"),
                    "wp_version": environment.get("wp_version"),
                    "currency": settings_data.get("currency"),
                    "timezone": environment.get("timezone")
                }

    def _convert_woocommerce_product(self, product_data: Dict[str, Any], store: EcommerceStore) -> SyncedProduct:
        """Convert WooCommerce product data to SyncedProduct."""
        # Extract basic product info
        product_id = str(product_data.get("id"))
        title = product_data.get("name", "")
        description = product_data.get("description", "")
        short_description = product_data.get("short_description", "")
        sku = product_data.get("sku", "")
        price = Decimal(str(product_data.get("price", "0")))
        regular_price = Decimal(str(product_data.get("regular_price", "0")))
        sale_price = product_data.get("sale_price")

        # Get categories
        categories = product_data.get("categories", [])
        category = categories[0].get("name") if categories else None

        # Get tags
        tags_data = product_data.get("tags", [])
        tags = [tag.get("name", "") for tag in tags_data]

        # Get images
        images_data = product_data.get("images", [])
        images = []
        featured_image = None

        for i, img_data in enumerate(images_data):
            image = ProductImage(
                image_id=str(img_data.get("id")),
                url=img_data.get("src", ""),
                alt_text=img_data.get("alt"),
                width=None,  # WooCommerce doesn't provide image dimensions in product API
                height=None,
                position=i + 1
            )
            images.append(image)

            if not featured_image:
                featured_image = image.url

        # Handle variations (WooCommerce variations are separate API calls)
        variants = []
        has_variants = product_data.get("type") == "variable"

        # Create a single variant for simple products
        if not has_variants:
            variant = ProductVariant(
                variant_id=product_id,
                title="Default",
                price=price,
                compare_at_price=regular_price if sale_price else None,
                sku=sku,
                inventory_quantity=product_data.get("stock_quantity", 0),
                weight=product_data.get("weight"),
                option1=None,
                option2=None,
                option3=None,
                image_url=featured_image,
                available=product_data.get("in_stock", True)
            )
            variants.append(variant)

        # Determine status
        status = ProductStatusEnum.ACTIVE
        if product_data.get("status") == "draft":
            status = ProductStatusEnum.DRAFT
        elif not product_data.get("in_stock", True):
            status = ProductStatusEnum.OUT_OF_STOCK

        return SyncedProduct(
            user_id=store.user_id,
            store_id=store.id,
            external_product_id=product_id,
            platform=EcommercePlatformEnum.WOOCOMMERCE,
            title=title,
            description=description or short_description,
            vendor=None,  # WooCommerce doesn't have vendor field by default
            product_type=product_data.get("type", "simple"),
            price=price,
            compare_at_price=regular_price if sale_price else None,
            sku=sku,
            inventory_quantity=product_data.get("stock_quantity", 0),
            tags=tags,
            category=category,
            images=images,
            featured_image=featured_image,
            variants=variants,
            has_variants=has_variants,
            status=status,
            published=product_data.get("status") == "publish",
            seo_title=None,  # Would need SEO plugin data
            seo_description=None,  # Would need SEO plugin data
            handle=product_data.get("slug", ""),
            last_synced_at=utc_now(),
            external_updated_at=datetime.fromisoformat(product_data.get("date_modified", "").replace("Z", "+00:00")) if product_data.get("date_modified") else None,
            created_at=utc_now(),
            updated_at=utc_now(),
            last_content_generation=None,
            metadata={
                "woocommerce_id": product_id,
                "type": product_data.get("type"),
                "featured": product_data.get("featured"),
                "catalog_visibility": product_data.get("catalog_visibility"),
                "date_created": product_data.get("date_created"),
                "date_modified": product_data.get("date_modified"),
                "permalink": product_data.get("permalink")
            }
        )
