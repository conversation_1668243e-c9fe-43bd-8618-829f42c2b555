// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import BrandVoiceView from '../BrandVoiceView';

// Mock the notification hook
const mockShowSuccessNotification = vi.fn();
const mockShowErrorNotification = vi.fn();

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showSuccessNotification: mockShowSuccessNotification,
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn().mockResolvedValue(undefined),
  },
});

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#1976d2',
      },
      secondary: {
        main: '#dc004e',
      },
      error: {
        main: '#d32f2f',
      },
      warning: {
        main: '#ed6c02',
      },
      success: {
        main: '#2e7d32',
      },
      grey: {
        300: '#e0e0e0',
        400: '#bdbdbd',
        500: '#9e9e9e',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('BrandVoiceView', () => {
  const mockBrandVoice = {
    tone: 'professional',
    personality_traits: ['confident', 'helpful', 'innovative'],
    writing_style: 'clear and concise',
    vocabulary: ['solution', 'partnership', 'excellence'],
    phrases_to_use: ['We\'re here to help', 'Let\'s solve this together'],
    phrases_to_avoid: ['We can\'t', 'That\'s not possible']
  };

  const mockProps = {
    brandVoice: mockBrandVoice,
    onError: vi.fn(),
    onExport: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders brand voice view correctly', () => {
    render(
      <TestWrapper>
        <BrandVoiceView {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Brand Voice Profile')).toBeInTheDocument();
    expect(screen.getByText('Tone and Style')).toBeInTheDocument();
    expect(screen.getByText('Personality Traits')).toBeInTheDocument();
    expect(screen.getByText('Brand Vocabulary & Phrases')).toBeInTheDocument();
  });

  test('displays voice completeness indicator', () => {
    render(
      <TestWrapper>
        <BrandVoiceView {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Voice Profile Completeness')).toBeInTheDocument();
    expect(screen.getByText('100%')).toBeInTheDocument(); // All fields are filled
  });

  test('shows placeholder when no brand voice provided', () => {
    render(
      <TestWrapper>
        <BrandVoiceView brandVoice={null} />
      </TestWrapper>
    );

    expect(screen.getByText('No brand voice defined')).toBeInTheDocument();
    expect(screen.getByText('Configure brand voice settings to see comprehensive voice analysis and examples')).toBeInTheDocument();
  });

  test('displays tone configuration with enhanced details', () => {
    render(
      <TestWrapper>
        <BrandVoiceView {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Professional')).toBeInTheDocument();
    expect(screen.getByText('Clear and Concise')).toBeInTheDocument();
  });

  test('displays personality traits correctly', () => {
    render(
      <TestWrapper>
        <BrandVoiceView {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('confident')).toBeInTheDocument();
    expect(screen.getByText('helpful')).toBeInTheDocument();
    expect(screen.getByText('innovative')).toBeInTheDocument();
  });

  test('handles vocabulary tab navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVoiceView {...mockProps} />
      </TestWrapper>
    );

    // Should start on vocabulary tab
    expect(screen.getByText('Key Words and Terms')).toBeInTheDocument();
    expect(screen.getByText('solution')).toBeInTheDocument();
    expect(screen.getByText('partnership')).toBeInTheDocument();
    expect(screen.getByText('excellence')).toBeInTheDocument();
  });

  test('handles phrases to use tab navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVoiceView {...mockProps} />
      </TestWrapper>
    );

    // Switch to phrases to use tab
    const phrasesToUseTab = screen.getByText('Phrases to Use');
    await user.click(phrasesToUseTab);

    expect(screen.getByText('Recommended Phrases')).toBeInTheDocument();
    expect(screen.getByText('"We\'re here to help"')).toBeInTheDocument();
    expect(screen.getByText('"Let\'s solve this together"')).toBeInTheDocument();
  });

  test('handles phrases to avoid tab navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVoiceView {...mockProps} />
      </TestWrapper>
    );

    // Switch to phrases to avoid tab
    const phrasesToAvoidTab = screen.getByText('Phrases to Avoid');
    await user.click(phrasesToAvoidTab);

    expect(screen.getByText('Phrases to Avoid')).toBeInTheDocument();
    expect(screen.getByText('"We can\'t"')).toBeInTheDocument();
    expect(screen.getByText('"That\'s not possible"')).toBeInTheDocument();
  });

  test('handles copy to clipboard functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVoiceView {...mockProps} />
      </TestWrapper>
    );

    // Click on a vocabulary word to copy it
    const vocabularyWord = screen.getByText('solution');
    await user.click(vocabularyWord);

    await waitFor(() => {
      expect(navigator.clipboard.writeText).toHaveBeenCalledWith('solution');
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Content copied to clipboard');
    });
  });

  test('displays voice showcase when showPreview is true', () => {
    render(
      <TestWrapper>
        <BrandVoiceView {...mockProps} showPreview={true} />
      </TestWrapper>
    );

    expect(screen.getByText('Voice in Action')).toBeInTheDocument();
  });

  test('hides voice showcase when showPreview is false', () => {
    render(
      <TestWrapper>
        <BrandVoiceView {...mockProps} showPreview={false} />
      </TestWrapper>
    );

    expect(screen.queryByText('Voice in Action')).not.toBeInTheDocument();
  });

  test('handles export functionality', async () => {
    // Mock URL.createObjectURL and related APIs
    global.URL.createObjectURL = vi.fn(() => 'mock-url');
    global.URL.revokeObjectURL = vi.fn();
    
    const mockLink = {
      href: '',
      download: '',
      click: vi.fn(),
    };
    
    vi.spyOn(document, 'createElement').mockReturnValue(mockLink);
    vi.spyOn(document.body, 'appendChild').mockImplementation(() => {});
    vi.spyOn(document.body, 'removeChild').mockImplementation(() => {});

    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVoiceView {...mockProps} />
      </TestWrapper>
    );

    const exportButton = screen.getByLabelText('Export brand voice profile');
    await user.click(exportButton);

    await waitFor(() => {
      expect(mockProps.onExport).toHaveBeenCalled();
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Brand voice view exported successfully');
    });
  });

  test('opens fullscreen preview dialog', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVoiceView {...mockProps} />
      </TestWrapper>
    );

    const fullscreenButton = screen.getByLabelText('Open fullscreen view');
    await user.click(fullscreenButton);

    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('Complete Voice Showcase')).toBeInTheDocument();
    });
  });

  test('opens voice guidelines dialog', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVoiceView {...mockProps} />
      </TestWrapper>
    );

    const guidelinesButton = screen.getByLabelText('Open voice guidelines');
    await user.click(guidelinesButton);

    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('Brand Voice Guidelines')).toBeInTheDocument();
    });
  });

  test('opens interactive demo dialog when enabled', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVoiceView {...mockProps} showInteractiveDemo={true} />
      </TestWrapper>
    );

    const demoButton = screen.getByLabelText('Open interactive voice demo');
    await user.click(demoButton);

    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('Interactive Voice Demo')).toBeInTheDocument();
    });
  });

  test('handles disabled state correctly', () => {
    render(
      <TestWrapper>
        <BrandVoiceView {...mockProps} disabled />
      </TestWrapper>
    );

    const exportButton = screen.getByLabelText('Export brand voice profile');
    const fullscreenButton = screen.getByLabelText('Open fullscreen view');

    expect(exportButton).toBeDisabled();
    expect(fullscreenButton).toBeDisabled();
  });

  test('displays consistency score when enabled', () => {
    render(
      <TestWrapper>
        <BrandVoiceView {...mockProps} showConsistencyScore={true} />
      </TestWrapper>
    );

    expect(screen.getByText('Voice Consistency Analysis')).toBeInTheDocument();
    expect(screen.getByText('Tone Consistency')).toBeInTheDocument();
    expect(screen.getByText('Vocabulary Usage')).toBeInTheDocument();
  });

  test('hides consistency score when disabled', () => {
    render(
      <TestWrapper>
        <BrandVoiceView {...mockProps} showConsistencyScore={false} />
      </TestWrapper>
    );

    expect(screen.queryByText('Voice Consistency Analysis')).not.toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <BrandVoiceView {...mockProps} />
      </TestWrapper>
    );

    // Check for proper ARIA labels
    expect(screen.getByLabelText('Export brand voice profile')).toBeInTheDocument();
    expect(screen.getByLabelText('Open fullscreen view')).toBeInTheDocument();
    expect(screen.getByLabelText('Open voice guidelines')).toBeInTheDocument();

    // Check for proper headings
    const headings = screen.getAllByRole('heading');
    expect(headings.length).toBeGreaterThan(0);
  });

  test('handles incomplete voice data gracefully', () => {
    const incompleteVoice = {
      tone: 'professional',
      personality_traits: [],
      writing_style: '',
      vocabulary: [],
      phrases_to_use: [],
      phrases_to_avoid: []
    };

    render(
      <TestWrapper>
        <BrandVoiceView brandVoice={incompleteVoice} />
      </TestWrapper>
    );

    expect(screen.getByText('No personality traits defined yet')).toBeInTheDocument();
    expect(screen.getByText('No vocabulary words defined yet')).toBeInTheDocument();
  });

  test('calculates voice completeness correctly', () => {
    const partialVoice = {
      tone: 'professional',
      personality_traits: ['confident'],
      writing_style: '',
      vocabulary: [],
      phrases_to_use: [],
      phrases_to_avoid: []
    };

    render(
      <TestWrapper>
        <BrandVoiceView brandVoice={partialVoice} />
      </TestWrapper>
    );

    // Should show lower completeness score since many fields are empty
    expect(screen.getByText('Voice Profile Completeness')).toBeInTheDocument();
    // Score should be less than 100% since many fields are empty
  });
});
