// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Typography,
  Box,
  Chip,
  FormControlLabel,
  Switch,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  InputAdornment,
  IconButton
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { emailProviderService } from '../../services/emailProviderService';

const EmailProviderForm = ({ 
  open, 
  mode = 'create', 
  provider = null, 
  onClose, 
  onSubmit 
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    provider_type: 'smtp',
    is_active: true,
    priority: 1,
    from_email: '',
    from_name: '',
    reply_to: '',
    email_types: ['transactional'],
    settings: {
      smtp: {
        host: '',
        port: 587,
        username: '',
        password: '',
        use_tls: true,
        use_ssl: false
      },
      sendgrid: {
        api_key: ''
      },
      mailgun: {
        api_key: '',
        domain: '',
        region: 'us'
      },
      aws_ses: {
        access_key_id: '',
        secret_access_key: '',
        region: 'us-east-1'
      }
    },
    rate_limits: {
      per_minute: 60,
      per_hour: 1000,
      per_day: 10000
    },
    bounce_handling: {
      webhook_url: '',
      max_bounce_rate: 5.0,
      auto_disable: true
    }
  });

  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const providerTypes = emailProviderService.getProviderTypes();
  const emailTypes = emailProviderService.getEmailTypes();

  useEffect(() => {
    if (mode === 'edit' && provider) {
      setFormData({
        ...provider,
        settings: provider.settings || formData.settings
      });
    } else if (mode === 'create') {
      setFormData({
        ...formData,
        name: '',
        description: '',
        from_email: '',
        from_name: '',
        reply_to: ''
      });
    }
  }, [mode, provider, open]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const handleSettingsChange = (providerType, field, value) => {
    setFormData(prev => ({
      ...prev,
      settings: {
        ...prev.settings,
        [providerType]: {
          ...prev.settings[providerType],
          [field]: value
        }
      }
    }));
  };

  const handleRateLimitsChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      rate_limits: {
        ...prev.rate_limits,
        [field]: parseInt(value) || 0
      }
    }));
  };

  const handleBounceHandlingChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      bounce_handling: {
        ...prev.bounce_handling,
        [field]: field === 'max_bounce_rate' ? parseFloat(value) || 0 : value
      }
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Provider name is required';
    }

    if (!formData.from_email.trim()) {
      newErrors.from_email = 'From email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.from_email)) {
      newErrors.from_email = 'Invalid email format';
    }

    if (!formData.from_name.trim()) {
      newErrors.from_name = 'From name is required';
    }

    if (formData.reply_to && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.reply_to)) {
      newErrors.reply_to = 'Invalid email format';
    }

    if (formData.email_types.length === 0) {
      newErrors.email_types = 'At least one email type must be selected';
    }

    // Provider-specific validation
    const settings = formData.settings[formData.provider_type];
    if (formData.provider_type === 'smtp') {
      if (!settings.host.trim()) {
        newErrors.smtp_host = 'SMTP host is required';
      }
      if (!settings.username.trim()) {
        newErrors.smtp_username = 'SMTP username is required';
      }
      if (!settings.password.trim()) {
        newErrors.smtp_password = 'SMTP password is required';
      }
    } else if (formData.provider_type === 'sendgrid') {
      if (!settings.api_key.trim()) {
        newErrors.sendgrid_api_key = 'SendGrid API key is required';
      }
    } else if (formData.provider_type === 'mailgun') {
      if (!settings.api_key.trim()) {
        newErrors.mailgun_api_key = 'Mailgun API key is required';
      }
      if (!settings.domain.trim()) {
        newErrors.mailgun_domain = 'Mailgun domain is required';
      }
    } else if (formData.provider_type === 'aws_ses') {
      if (!settings.access_key_id.trim()) {
        newErrors.aws_access_key = 'AWS Access Key ID is required';
      }
      if (!settings.secret_access_key.trim()) {
        newErrors.aws_secret_key = 'AWS Secret Access Key is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      // Prepare submission data
      const submissionData = {
        ...formData,
        settings: {
          [formData.provider_type]: formData.settings[formData.provider_type]
        }
      };

      await onSubmit(submissionData);
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderProviderSettings = () => {
    const settings = formData.settings[formData.provider_type];

    switch (formData.provider_type) {
      case 'smtp':
        return (
          <Grid container spacing={2}>
            <Grid item xs={12} md={8}>
              <TextField
                fullWidth
                label="SMTP Host"
                value={settings.host}
                onChange={(e) => handleSettingsChange('smtp', 'host', e.target.value)}
                error={!!errors.smtp_host}
                helperText={errors.smtp_host}
                placeholder="smtp.gmail.com"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Port"
                type="number"
                value={settings.port}
                onChange={(e) => handleSettingsChange('smtp', 'port', parseInt(e.target.value))}
                placeholder="587"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Username"
                value={settings.username}
                onChange={(e) => handleSettingsChange('smtp', 'username', e.target.value)}
                error={!!errors.smtp_username}
                helperText={errors.smtp_username}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Password"
                type={showPassword ? 'text' : 'password'}
                value={settings.password}
                onChange={(e) => handleSettingsChange('smtp', 'password', e.target.value)}
                error={!!errors.smtp_password}
                helperText={errors.smtp_password}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowPassword(!showPassword)}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.use_tls}
                    onChange={(e) => handleSettingsChange('smtp', 'use_tls', e.target.checked)}
                  />
                }
                label="Use TLS"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.use_ssl}
                    onChange={(e) => handleSettingsChange('smtp', 'use_ssl', e.target.checked)}
                  />
                }
                label="Use SSL"
              />
            </Grid>
          </Grid>
        );

      case 'sendgrid':
        return (
          <TextField
            fullWidth
            label="SendGrid API Key"
            type={showPassword ? 'text' : 'password'}
            value={settings.api_key}
            onChange={(e) => handleSettingsChange('sendgrid', 'api_key', e.target.value)}
            error={!!errors.sendgrid_api_key}
            helperText={errors.sendgrid_api_key}
            placeholder="SG.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowPassword(!showPassword)}
                    edge="end"
                  >
                    {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        );

      case 'mailgun':
        return (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Mailgun API Key"
                type={showPassword ? 'text' : 'password'}
                value={settings.api_key}
                onChange={(e) => handleSettingsChange('mailgun', 'api_key', e.target.value)}
                error={!!errors.mailgun_api_key}
                helperText={errors.mailgun_api_key}
                placeholder="key-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowPassword(!showPassword)}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={8}>
              <TextField
                fullWidth
                label="Domain"
                value={settings.domain}
                onChange={(e) => handleSettingsChange('mailgun', 'domain', e.target.value)}
                error={!!errors.mailgun_domain}
                helperText={errors.mailgun_domain}
                placeholder="mg.yourdomain.com"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Region</InputLabel>
                <Select
                  value={settings.region}
                  label="Region"
                  onChange={(e) => handleSettingsChange('mailgun', 'region', e.target.value)}
                >
                  <MenuItem value="us">US</MenuItem>
                  <MenuItem value="eu">EU</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        );

      case 'aws_ses':
        return (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="AWS Access Key ID"
                value={settings.access_key_id}
                onChange={(e) => handleSettingsChange('aws_ses', 'access_key_id', e.target.value)}
                error={!!errors.aws_access_key}
                helperText={errors.aws_access_key}
                placeholder="AKIAIOSFODNN7EXAMPLE"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="AWS Secret Access Key"
                type={showPassword ? 'text' : 'password'}
                value={settings.secret_access_key}
                onChange={(e) => handleSettingsChange('aws_ses', 'secret_access_key', e.target.value)}
                error={!!errors.aws_secret_key}
                helperText={errors.aws_secret_key}
                placeholder="wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowPassword(!showPassword)}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>AWS Region</InputLabel>
                <Select
                  value={settings.region}
                  label="AWS Region"
                  onChange={(e) => handleSettingsChange('aws_ses', 'region', e.target.value)}
                >
                  <MenuItem value="us-east-1">US East (N. Virginia)</MenuItem>
                  <MenuItem value="us-west-2">US West (Oregon)</MenuItem>
                  <MenuItem value="eu-west-1">Europe (Ireland)</MenuItem>
                  <MenuItem value="ap-southeast-1">Asia Pacific (Singapore)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: { minHeight: '80vh' }
      }}
    >
      <DialogTitle>
        {mode === 'create' ? 'Add Email Provider' : 'Edit Email Provider'}
      </DialogTitle>
      
      <DialogContent dividers>
        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              Basic Information
            </Typography>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Provider Name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              error={!!errors.name}
              helperText={errors.name}
              placeholder="My SMTP Provider"
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Provider Type</InputLabel>
              <Select
                value={formData.provider_type}
                label="Provider Type"
                onChange={(e) => handleInputChange('provider_type', e.target.value)}
              >
                {providerTypes.map((type) => (
                  <MenuItem key={type.type} value={type.type}>
                    {type.name} - {type.description}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Description"
              multiline
              rows={2}
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Optional description for this email provider"
            />
          </Grid>

          {/* Email Configuration */}
          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
            <Typography variant="h6" gutterBottom>
              Email Configuration
            </Typography>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="From Email"
              value={formData.from_email}
              onChange={(e) => handleInputChange('from_email', e.target.value)}
              error={!!errors.from_email}
              helperText={errors.from_email}
              placeholder="<EMAIL>"
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="From Name"
              value={formData.from_name}
              onChange={(e) => handleInputChange('from_name', e.target.value)}
              error={!!errors.from_name}
              helperText={errors.from_name}
              placeholder="ACE Social"
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Reply To (Optional)"
              value={formData.reply_to}
              onChange={(e) => handleInputChange('reply_to', e.target.value)}
              error={!!errors.reply_to}
              helperText={errors.reply_to}
              placeholder="<EMAIL>"
            />
          </Grid>
          
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              label="Priority"
              type="number"
              value={formData.priority}
              onChange={(e) => handleInputChange('priority', parseInt(e.target.value))}
              inputProps={{ min: 1, max: 100 }}
              helperText="Lower number = higher priority"
            />
          </Grid>
          
          <Grid item xs={12} md={3}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.is_active}
                  onChange={(e) => handleInputChange('is_active', e.target.checked)}
                />
              }
              label="Active"
            />
          </Grid>

          {/* Email Types */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>
              Email Types
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {emailTypes.map((type) => (
                <Chip
                  key={type.type}
                  label={type.name}
                  clickable
                  color={formData.email_types.includes(type.type) ? 'primary' : 'default'}
                  onClick={() => {
                    const newTypes = formData.email_types.includes(type.type)
                      ? formData.email_types.filter(t => t !== type.type)
                      : [...formData.email_types, type.type];
                    handleInputChange('email_types', newTypes);
                  }}
                />
              ))}
            </Box>
            {errors.email_types && (
              <Typography variant="caption" color="error" sx={{ mt: 1, display: 'block' }}>
                {errors.email_types}
              </Typography>
            )}
          </Grid>

          {/* Provider Settings */}
          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
            <Typography variant="h6" gutterBottom>
              Provider Settings
            </Typography>
            {renderProviderSettings()}
          </Grid>

          {/* Advanced Settings */}
          <Grid item xs={12}>
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="subtitle1">Advanced Settings</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom>
                      Rate Limits
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Per Minute"
                      type="number"
                      value={formData.rate_limits.per_minute}
                      onChange={(e) => handleRateLimitsChange('per_minute', e.target.value)}
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Per Hour"
                      type="number"
                      value={formData.rate_limits.per_hour}
                      onChange={(e) => handleRateLimitsChange('per_hour', e.target.value)}
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Per Day"
                      type="number"
                      value={formData.rate_limits.per_day}
                      onChange={(e) => handleRateLimitsChange('per_day', e.target.value)}
                    />
                  </Grid>
                  
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                      Bounce Handling
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={8}>
                    <TextField
                      fullWidth
                      label="Webhook URL (Optional)"
                      value={formData.bounce_handling.webhook_url}
                      onChange={(e) => handleBounceHandlingChange('webhook_url', e.target.value)}
                      placeholder="https://yourdomain.com/webhooks/bounces"
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Max Bounce Rate (%)"
                      type="number"
                      value={formData.bounce_handling.max_bounce_rate}
                      onChange={(e) => handleBounceHandlingChange('max_bounce_rate', e.target.value)}
                      inputProps={{ min: 0, max: 100, step: 0.1 }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={formData.bounce_handling.auto_disable}
                          onChange={(e) => handleBounceHandlingChange('auto_disable', e.target.checked)}
                        />
                      }
                      label="Auto-disable provider when bounce rate exceeds limit"
                    />
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          </Grid>
        </Grid>

        {Object.keys(errors).length > 0 && (
          <Alert severity="error" sx={{ mt: 2 }}>
            Please fix the errors above before submitting.
          </Alert>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained"
          disabled={loading}
        >
          {loading ? 'Saving...' : (mode === 'create' ? 'Create Provider' : 'Update Provider')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EmailProviderForm;
