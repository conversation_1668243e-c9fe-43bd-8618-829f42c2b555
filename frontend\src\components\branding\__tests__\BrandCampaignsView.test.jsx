/**
 * Tests for BrandCampaignsView component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import BrandCampaignsView from '../BrandCampaignsView';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the API
vi.mock('../../../api', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn()
  }
}));

// Mock hooks
vi.mock('../../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showSuccessNotification: vi.fn(),
    showErrorNotification: vi.fn()
  }))
}));

describe('BrandCampaignsView', () => {
  const mockCampaigns = [
    {
      id: 'campaign-1',
      name: 'Summer Sale Campaign',
      description: 'Summer promotional campaign',
      status: 'active',
      platforms: ['facebook', 'instagram'],
      created_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'campaign-2',
      name: 'Holiday Campaign',
      description: 'Holiday promotional campaign',
      status: 'draft',
      platforms: ['twitter', 'linkedin'],
      created_at: '2024-01-02T00:00:00Z'
    }
  ];

  const mockAvailableCampaigns = [
    {
      id: 'campaign-3',
      name: 'New Product Launch',
      description: 'Product launch campaign',
      status: 'draft',
      created_at: '2024-01-03T00:00:00Z'
    }
  ];

  const mockProps = {
    campaignIds: ['campaign-1', 'campaign-2'],
    profileId: 'profile-123',
    onCampaignAssociated: vi.fn(),
    onError: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders brand campaigns view correctly', async () => {
    const api = await import('../../../api');
    
    // Mock API responses
    api.default.get.mockImplementation((url) => {
      if (url.includes('/api/campaigns/campaign-1')) {
        return Promise.resolve({ data: mockCampaigns[0] });
      }
      if (url.includes('/api/campaigns/campaign-2')) {
        return Promise.resolve({ data: mockCampaigns[1] });
      }
      if (url === '/api/campaigns') {
        return Promise.resolve({ data: [...mockCampaigns, ...mockAvailableCampaigns] });
      }
      return Promise.reject(new Error('Not found'));
    });

    render(
      <TestWrapper>
        <BrandCampaignsView {...mockProps} />
      </TestWrapper>
    );

    // Wait for campaigns to load
    await waitFor(() => {
      expect(screen.getByText('Summer Sale Campaign')).toBeInTheDocument();
      expect(screen.getByText('Holiday Campaign')).toBeInTheDocument();
    });

    expect(screen.getByText('Associate Campaign')).toBeInTheDocument();
  });

  test('shows loading state while fetching campaigns', () => {
    const api = import('../../../api');
    
    // Mock API to never resolve
    api.then(module => {
      module.default.get.mockImplementation(() => new Promise(() => {}));
    });

    render(
      <TestWrapper>
        <BrandCampaignsView {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('shows empty state when no campaigns are associated', async () => {
    const api = await import('../../../api');
    
    api.default.get.mockImplementation((url) => {
      if (url === '/api/campaigns') {
        return Promise.resolve({ data: mockAvailableCampaigns });
      }
      return Promise.reject(new Error('Not found'));
    });

    render(
      <TestWrapper>
        <BrandCampaignsView {...mockProps} campaignIds={[]} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('No campaigns associated')).toBeInTheDocument();
      expect(screen.getByText('Associate this brand profile with campaigns to maintain consistent branding across your marketing efforts.')).toBeInTheDocument();
    });
  });

  test('opens associate dialog when associate button is clicked', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.get.mockImplementation((url) => {
      if (url === '/api/campaigns') {
        return Promise.resolve({ data: mockAvailableCampaigns });
      }
      return Promise.resolve({ data: [] });
    });

    render(
      <TestWrapper>
        <BrandCampaignsView {...mockProps} campaignIds={[]} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getAllByLabelText('Associate campaign with brand profile')).toHaveLength(2);
    });

    const associateButtons = screen.getAllByLabelText('Associate campaign with brand profile');
    const associateButton = associateButtons[0]; // Use the first button
    await user.click(associateButton);

    expect(screen.getByText('Associate Campaign')).toBeInTheDocument();
    expect(screen.getByText('Select Campaign')).toBeInTheDocument();
  });

  test('associates campaign successfully', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.get.mockImplementation((url) => {
      if (url === '/api/campaigns') {
        return Promise.resolve({ data: mockAvailableCampaigns });
      }
      return Promise.resolve({ data: [] });
    });

    api.default.post.mockResolvedValue({
      data: { id: 'association-1' }
    });

    api.default.put.mockResolvedValue({});

    render(
      <TestWrapper>
        <BrandCampaignsView {...mockProps} campaignIds={[]} />
      </TestWrapper>
    );

    // Wait for component to load and open dialog
    await waitFor(() => {
      expect(screen.getAllByLabelText('Associate campaign with brand profile')).toHaveLength(2);
    });

    const associateButtons = screen.getAllByLabelText('Associate campaign with brand profile');
    const associateButton = associateButtons[0]; // Use the first button
    await user.click(associateButton);

    // Select a campaign
    const campaignSelect = screen.getByLabelText('Select campaign to associate');
    await user.click(campaignSelect);
    await user.click(screen.getByText('New Product Launch'));

    // Submit association
    const submitButton = screen.getByLabelText('Associate campaign');
    await user.click(submitButton);

    await waitFor(() => {
      expect(api.default.post).toHaveBeenCalledWith('/api/brand-profiles/campaigns', {
        brand_profile_id: 'profile-123',
        campaign_id: 'campaign-3'
      });
    });

    expect(api.default.put).toHaveBeenCalledWith('/api/campaigns/campaign-3', {
      use_custom_branding: true
    });

    expect(mockProps.onCampaignAssociated).toHaveBeenCalledWith('campaign-3', { id: 'association-1' });
  });

  test('handles association errors gracefully', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.get.mockImplementation((url) => {
      if (url === '/api/campaigns') {
        return Promise.resolve({ data: mockAvailableCampaigns });
      }
      return Promise.resolve({ data: [] });
    });

    api.default.post.mockRejectedValue(new Error('Association failed'));

    render(
      <TestWrapper>
        <BrandCampaignsView {...mockProps} campaignIds={[]} />
      </TestWrapper>
    );

    // Open dialog and select campaign
    await waitFor(() => {
      expect(screen.getByLabelText('Associate campaign with brand profile')).toBeInTheDocument();
    });

    const associateButton = screen.getByLabelText('Associate campaign with brand profile');
    await user.click(associateButton);

    const campaignSelect = screen.getByLabelText('Select campaign to associate');
    await user.click(campaignSelect);
    await user.click(screen.getByText('New Product Launch'));

    const submitButton = screen.getByLabelText('Associate campaign');
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockProps.onError).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  test('validates campaign selection before association', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    const { useNotification } = await import('../../../hooks/useNotification');
    
    const mockShowError = vi.fn();
    useNotification.mockReturnValue({
      showSuccessNotification: vi.fn(),
      showErrorNotification: mockShowError
    });

    api.default.get.mockImplementation((url) => {
      if (url === '/api/campaigns') {
        return Promise.resolve({ data: mockAvailableCampaigns });
      }
      return Promise.resolve({ data: [] });
    });

    render(
      <TestWrapper>
        <BrandCampaignsView {...mockProps} campaignIds={[]} />
      </TestWrapper>
    );

    // Open dialog
    await waitFor(() => {
      expect(screen.getAllByLabelText('Associate campaign with brand profile')).toHaveLength(2);
    });

    const associateButtons = screen.getAllByLabelText('Associate campaign with brand profile');
    const associateButton = associateButtons[0]; // Use the first button
    await user.click(associateButton);

    // Try to submit without selecting campaign
    const submitButton = screen.getByLabelText('Associate campaign');
    await user.click(submitButton);

    expect(mockShowError).toHaveBeenCalledWith('Please select a campaign');
  });

  test('displays campaign status correctly', async () => {
    const api = await import('../../../api');
    
    api.default.get.mockImplementation((url) => {
      if (url.includes('/api/campaigns/campaign-1')) {
        return Promise.resolve({ data: mockCampaigns[0] });
      }
      if (url.includes('/api/campaigns/campaign-2')) {
        return Promise.resolve({ data: mockCampaigns[1] });
      }
      if (url === '/api/campaigns') {
        return Promise.resolve({ data: mockCampaigns });
      }
      return Promise.reject(new Error('Not found'));
    });

    render(
      <TestWrapper>
        <BrandCampaignsView {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('active')).toBeInTheDocument();
      expect(screen.getByText('draft')).toBeInTheDocument();
    });
  });

  test('hides associate button when readOnly is true', async () => {
    const api = await import('../../../api');
    
    api.default.get.mockResolvedValue({ data: [] });

    render(
      <TestWrapper>
        <BrandCampaignsView {...mockProps} campaignIds={[]} readOnly={true} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByLabelText('Associate campaign with brand profile')).not.toBeInTheDocument();
    });
  });

  test('disables controls when disabled prop is true', async () => {
    const api = await import('../../../api');
    
    api.default.get.mockResolvedValue({ data: mockAvailableCampaigns });

    render(
      <TestWrapper>
        <BrandCampaignsView {...mockProps} campaignIds={[]} disabled={true} />
      </TestWrapper>
    );

    await waitFor(() => {
      const associateButtons = screen.getAllByLabelText('Associate campaign with brand profile');
      expect(associateButtons[0]).toBeDisabled();
    });
  });

  test('handles API errors gracefully', async () => {
    const api = await import('../../../api');
    
    api.default.get.mockRejectedValue(new Error('API Error'));

    render(
      <TestWrapper>
        <BrandCampaignsView {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockProps.onError).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  test('applies correct accessibility attributes', async () => {
    const api = await import('../../../api');
    
    api.default.get.mockResolvedValue({ data: mockAvailableCampaigns });

    render(
      <TestWrapper>
        <BrandCampaignsView {...mockProps} campaignIds={[]} />
      </TestWrapper>
    );

    await waitFor(() => {
      const associateButtons = screen.getAllByLabelText('Associate campaign with brand profile');
      expect(associateButtons[0]).toHaveAttribute('aria-label', 'Associate campaign with brand profile');
    });
  });

  test('passes through additional props', async () => {
    const api = await import('../../../api');
    
    api.default.get.mockResolvedValue({ data: [] });

    render(
      <TestWrapper>
        <BrandCampaignsView 
          {...mockProps} 
          campaignIds={[]}
          data-testid="test-brand-campaigns"
          className="custom-class"
        />
      </TestWrapper>
    );

    await waitFor(() => {
      const component = screen.getByTestId('test-brand-campaigns');
      expect(component).toHaveClass('custom-class');
    });
  });

  test('shows loading state during association', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.get.mockResolvedValue({ data: mockAvailableCampaigns });
    
    // Mock API to never resolve
    api.default.post.mockImplementation(() => new Promise(() => {}));

    render(
      <TestWrapper>
        <BrandCampaignsView {...mockProps} campaignIds={[]} />
      </TestWrapper>
    );

    // Open dialog and select campaign
    await waitFor(() => {
      expect(screen.getAllByLabelText('Associate campaign with brand profile')).toHaveLength(2);
    });

    const associateButtons = screen.getAllByLabelText('Associate campaign with brand profile');
    const associateButton = associateButtons[0]; // Use the first button
    await user.click(associateButton);

    const campaignSelect = screen.getByLabelText('Select campaign to associate');
    await user.click(campaignSelect);
    await user.click(screen.getByText('New Product Launch'));

    const submitButton = screen.getByLabelText('Associate campaign');
    await user.click(submitButton);

    expect(screen.getByText('Associating...')).toBeInTheDocument();
    expect(screen.getByLabelText('Associating campaign')).toBeInTheDocument();
  });
});
