"""
Real-time usage tracking service for ACEO add-ons.
Tracks consumption, provides analytics, and manages credit rollover.
@since 2024-1-1 to 2025-25-7
"""
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from enum import Enum

from app.db.mongodb import get_database
# Models are now handled as dictionaries for MongoDB
from app.services.addon_catalog import addon_catalog, AddonType
# Note: These functions don't exist yet, using placeholders
# from app.services.notification import send_usage_alert
# from app.core.monitoring import record_usage_metrics
from app.core.redis import (
    redis_manager, redis_get, redis_set, redis_delete, redis_setex,
    redis_incr, redis_expire, redis_lpush, redis_llen, redis_lrange
)
from bson import ObjectId

logger = logging.getLogger(__name__)


class UsageType(str, Enum):
    """Types of usage that can be tracked."""
    REGENERATION_CREDITS = "regeneration_credits"
    IMAGE_GENERATION = "image_generation"
    SENTIMENT_ANALYSIS = "sentiment_analysis"
    AUTO_REPLIES = "auto_replies"
    TEAM_SEATS = "team_seats"
    PRIORITY_SUPPORT = "priority_support"


@dataclass
class UsageAlert:
    """Usage alert configuration."""
    threshold_percentage: float
    message: str
    alert_type: str
    is_critical: bool = False


class AddonUsageTracker:
    """Real-time add-on usage tracking and analytics."""
    
    def __init__(self):
        self.usage_alerts = {
            75: UsageAlert(0.75, "You've used 75% of your {addon_name} credits", "warning"),
            90: UsageAlert(0.90, "You've used 90% of your {addon_name} credits", "warning"),
            95: UsageAlert(0.95, "You're running low on {addon_name} credits", "critical", True),
            100: UsageAlert(1.0, "You've exhausted your {addon_name} credits", "critical", True)
        }
    
    async def track_usage(self, user_id: str, usage_type: UsageType, amount: int = 1,
                         metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Track usage of an add-on feature and update credits."""
        try:
            db = await get_database()
            users_collection = db["users"]
            user_addons_collection = db["user_addons"]
            usage_collection = db["addon_usage"]

            # Convert user_id to ObjectId if it's a string
            user_object_id = ObjectId(user_id) if isinstance(user_id, str) else user_id

            # Find user
            user = await users_collection.find_one({"_id": user_object_id})
            if not user:
                return {"success": False, "error": "User not found"}

            # Find applicable add-on for this usage type
            applicable_addon = await self._find_applicable_addon(user, usage_type, user_addons_collection)

            if not applicable_addon:
                return {"success": False, "error": "No applicable add-on found for usage type"}

            # Check if user has enough credits
            available_credits = applicable_addon["credits_remaining"]

            if available_credits < amount:
                return {
                    "success": False,
                    "error": "Insufficient credits",
                    "available": available_credits,
                    "requested": amount
                }

            # Record usage
            usage_record = {
                "user_id": user_id,
                "user_addon_id": applicable_addon["_id"],
                "addon_id": applicable_addon["addon_id"],
                "usage_type": usage_type.value,
                "amount": amount,
                "metadata": metadata or {},
                "timestamp": datetime.now(timezone.utc),
                "created_at": datetime.now(timezone.utc)
            }

            await usage_collection.insert_one(usage_record)

            # Update addon credits
            new_credits_used = applicable_addon["credits_used"] + amount
            new_credits_remaining = applicable_addon["credits_remaining"] - amount

            await user_addons_collection.update_one(
                {"_id": applicable_addon["_id"]},
                {
                    "$set": {
                        "credits_used": new_credits_used,
                        "credits_remaining": new_credits_remaining,
                        "last_used_at": datetime.now(timezone.utc),
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )

            # Update real-time cache
            await self._update_usage_cache(str(user_id), applicable_addon["addon_id"], usage_type, amount)

            # Check for usage alerts
            applicable_addon["credits_used"] = new_credits_used
            applicable_addon["credits_remaining"] = new_credits_remaining
            await self._check_usage_alerts(user, applicable_addon, usage_type)

            # Record metrics
            plan_id = user.get("subscription", {}).get("plan_id", "free")
            record_usage_metrics(usage_type.value, plan_id, amount)

            return {
                "success": True,
                "credits_remaining": new_credits_remaining,
                "usage_percentage": (new_credits_used / applicable_addon["total_credits"]) * 100,
                "addon_id": applicable_addon["addon_id"]
            }
            
        except Exception as e:
            logger.error(f"Error tracking usage for user {user_id}: {str(e)}")
            return {"success": False, "error": "Internal error tracking usage"}
    
    async def get_usage_analytics(self, user_id: str, addon_id: Optional[str] = None,
                                 days: int = 30) -> Dict[str, Any]:
        """Get detailed usage analytics for a user's add-ons."""
        try:
            db = await get_database()
            usage_collection = db["addon_usage"]

            user_obj_id = ObjectId(user_id) if isinstance(user_id, str) else user_id

            # Build query filter
            query_filter = {
                "user_id": user_obj_id,
                "timestamp": {"$gte": datetime.now(timezone.utc) - timedelta(days=days)}
            }

            if addon_id:
                query_filter["addon_id"] = addon_id

            usage_records = await usage_collection.find(query_filter).to_list(length=None)

            # Aggregate usage by type and date
            analytics = {
                "total_usage": len(usage_records),
                "usage_by_type": {},
                "usage_by_date": {},
                "peak_usage_hours": {},
                "efficiency_metrics": {}
            }

            for record in usage_records:
                # Usage by type
                usage_type = record["usage_type"]
                if usage_type not in analytics["usage_by_type"]:
                    analytics["usage_by_type"][usage_type] = {"count": 0, "total_amount": 0}

                analytics["usage_by_type"][usage_type]["count"] += 1
                analytics["usage_by_type"][usage_type]["total_amount"] += record["amount"]

                # Usage by date
                date_key = record["timestamp"].date().isoformat()
                if date_key not in analytics["usage_by_date"]:
                    analytics["usage_by_date"][date_key] = 0
                analytics["usage_by_date"][date_key] += record["amount"]

                # Peak usage hours
                hour_key = record["timestamp"].hour
                if hour_key not in analytics["peak_usage_hours"]:
                    analytics["peak_usage_hours"][hour_key] = 0
                analytics["peak_usage_hours"][hour_key] += record["amount"]

            # Calculate efficiency metrics
            analytics["efficiency_metrics"] = await self._calculate_efficiency_metrics(user_id, usage_records)

            return analytics
            
        except Exception as e:
            logger.error(f"Error getting usage analytics for user {user_id}: {str(e)}")
            return {}
    
    async def get_addon_status(self, user_id: str) -> List[Dict[str, Any]]:
        """Get current status of all user's add-ons."""
        try:
            db = await get_database()
            user_addons_collection = db["user_addons"]

            user_obj_id = ObjectId(user_id) if isinstance(user_id, str) else user_id

            user_addons = await user_addons_collection.find({
                "user_id": user_obj_id,
                "is_active": True,
                "is_refunded": False
            }).to_list(length=None)

            addon_statuses = []

            for addon in user_addons:
                addon_config = addon_catalog.catalog.get(addon["addon_id"])
                if not addon_config:
                    continue

                # Calculate usage percentage
                usage_percentage = 0
                if addon["total_credits"] > 0:
                    usage_percentage = (addon["credits_used"] / addon["total_credits"]) * 100

                # Check expiry
                days_until_expiry = None
                if addon.get("expires_at"):
                    days_until_expiry = (addon["expires_at"] - datetime.now(timezone.utc)).days

                # Get recent usage trend
                recent_usage = await self._get_recent_usage_trend(user_id, addon["addon_id"])

                status = {
                    "addon_id": addon["addon_id"],
                    "name": addon_config["name"],
                    "type": addon_config["type"],
                    "status": "active" if addon["is_active"] else "inactive",
                    "credits_total": addon["total_credits"],
                    "credits_used": addon["credits_used"],
                    "credits_remaining": addon["credits_remaining"],
                    "usage_percentage": round(usage_percentage, 2),
                    "expires_at": addon["expires_at"].isoformat() if addon.get("expires_at") else None,
                    "days_until_expiry": days_until_expiry,
                    "recent_usage_trend": recent_usage,
                    "rollover_eligible": self._is_rollover_eligible(addon, addon_config),
                    "auto_renewal": addon.get("auto_renewal_enabled", False)
                }

                addon_statuses.append(status)

            return addon_statuses
            
        except Exception as e:
            logger.error(f"Error getting addon status for user {user_id}: {str(e)}")
            return []
    
    async def process_credit_rollover(self, user_id: str) -> Dict[str, Any]:
        """Process credit rollover for eligible add-ons."""
        try:
            db = await get_database()
            user_addons_collection = db["user_addons"]

            user_obj_id = ObjectId(user_id) if isinstance(user_id, str) else user_id

            # Find expiring add-ons
            user_addons = await user_addons_collection.find({
                "user_id": user_obj_id,
                "is_active": True,
                "expires_at": {"$lte": datetime.now(timezone.utc) + timedelta(days=1)}  # Expiring soon
            }).to_list(length=None)

            rollover_results = []

            for addon in user_addons:
                addon_config = addon_catalog.catalog.get(addon["addon_id"])
                if not addon_config or addon_config["type"] != AddonType.CONSUMABLE:
                    continue

                rollover_percentage = addon_config.get("rollover_percentage", 0)
                if rollover_percentage <= 0:
                    continue

                # Calculate rollover credits
                unused_credits = addon["credits_remaining"]
                rollover_credits = int(unused_credits * rollover_percentage)

                if rollover_credits > 0:
                    # Create new addon entry for rollover credits
                    rollover_addon = {
                        "user_id": user_obj_id,
                        "addon_id": addon["addon_id"],
                        "total_credits": rollover_credits,
                        "credits_remaining": rollover_credits,
                        "credits_used": 0,
                        "purchased_at": datetime.now(timezone.utc),
                        "expires_at": datetime.now(timezone.utc) + timedelta(days=addon_config.get("expiry_days", 30)),
                        "is_rollover": True,
                        "original_addon_id": addon["_id"],
                        "is_active": True,
                        "is_refunded": False,
                        "created_at": datetime.now(timezone.utc),
                        "updated_at": datetime.now(timezone.utc)
                    }

                    await user_addons_collection.insert_one(rollover_addon)

                    rollover_results.append({
                        "addon_id": addon["addon_id"],
                        "original_credits": unused_credits,
                        "rollover_credits": rollover_credits,
                        "rollover_percentage": rollover_percentage * 100
                    })

                # Mark original addon as expired
                await user_addons_collection.update_one(
                    {"_id": addon["_id"]},
                    {
                        "$set": {
                            "is_active": False,
                            "expired_at": datetime.now(timezone.utc),
                            "updated_at": datetime.now(timezone.utc)
                        }
                    }
                )

            return {"success": True, "rollovers": rollover_results}
            
        except Exception as e:
            logger.error(f"Error processing credit rollover for user {user_id}: {str(e)}")
            return {"success": False, "error": "Failed to process rollover"}
    
    async def _find_applicable_addon(self, user: Dict[str, Any], usage_type: UsageType, user_addons_collection) -> Optional[Dict[str, Any]]:
        """Find the most applicable add-on for a usage type."""
        # Find active add-ons for this user
        user_addons = await user_addons_collection.find({
            "user_id": user["_id"],
            "is_active": True,
            "is_refunded": False,
            "credits_remaining": {"$gt": 0}
        }).to_list(length=None)

        if not user_addons:
            return None

        # Filter add-ons that support this usage type
        applicable_addons = [
            addon for addon in user_addons
            if self._addon_supports_usage_type(addon["addon_id"], usage_type)
        ]

        if not applicable_addons:
            return None

        # Prioritize by expiry date (use expiring credits first)
        applicable_addons.sort(key=lambda x: x.get("expires_at") or datetime.max.replace(tzinfo=timezone.utc))

        return applicable_addons[0]
    
    def _addon_supports_usage_type(self, addon_id: str, usage_type: UsageType) -> bool:
        """Check if an add-on supports a specific usage type."""
        addon_config = addon_catalog.catalog.get(addon_id)
        if not addon_config:
            return False
        
        usage_increase = addon_config.get("usage_increase", {})
        
        # Map usage types to addon capabilities
        usage_mapping = {
            UsageType.REGENERATION_CREDITS: "regeneration_credits",
            UsageType.IMAGE_GENERATION: "image_generation_credits",
            UsageType.SENTIMENT_ANALYSIS: "sentiment_comments",
            UsageType.AUTO_REPLIES: "auto_replies",
            UsageType.TEAM_SEATS: "user_accounts",
            UsageType.PRIORITY_SUPPORT: "priority_support"
        }
        
        mapped_key = usage_mapping.get(usage_type)
        return mapped_key in usage_increase
    
    async def _get_current_usage(self, user_id: str, addon_id: str, usage_type: UsageType) -> int:
        """Get current usage for an add-on."""
        cache_key = f"addon_usage:{user_id}:{addon_id}:{usage_type.value}"
        cached_usage = await redis_get(cache_key)

        if cached_usage:
            return int(cached_usage)

        # Fallback to database
        db = await get_database()
        usage_collection = db["addon_usage"]

        total_usage = await usage_collection.count_documents({
            "user_id": ObjectId(user_id),
            "addon_id": addon_id,
            "usage_type": usage_type.value
        })

        # Cache for 5 minutes
        await redis_setex(cache_key, 300, str(total_usage))

        return total_usage

    async def _update_usage_cache(self, user_id: str, addon_id: str, usage_type: UsageType, amount: int):
        """Update real-time usage cache."""
        cache_key = f"addon_usage:{user_id}:{addon_id}:{usage_type.value}"
        current_usage = await self._get_current_usage(user_id, addon_id, usage_type)
        new_usage = current_usage + amount

        await redis_setex(cache_key, 300, str(new_usage))

    async def _check_usage_alerts(self, user: Dict[str, Any], addon: Dict[str, Any], usage_type: UsageType):
        """Check if usage alerts should be triggered."""
        if addon["total_credits"] <= 0:
            return

        usage_percentage = (addon["credits_used"] / addon["total_credits"]) * 100

        for threshold_pct, alert in self.usage_alerts.items():
            if usage_percentage >= threshold_pct:
                # Check if we've already sent this alert
                alert_key = f"usage_alert:{user['_id']}:{addon['_id']}:{threshold_pct}"
                if await redis_get(alert_key):
                    continue

                # Send alert
                addon_config = addon_catalog.catalog.get(addon["addon_id"])
                addon_name = addon_config["name"] if addon_config else "Add-on"

                message = alert.message.format(addon_name=addon_name)

                await send_usage_alert(
                    user_id=str(user["_id"]),
                    message=message,
                    alert_type=alert.alert_type,
                    is_critical=alert.is_critical,
                    addon_id=addon["addon_id"],
                    usage_percentage=usage_percentage
                )

                # Mark alert as sent (expires in 24 hours)
                await redis_setex(alert_key, 86400, "sent")
    
    async def _get_recent_usage_trend(self, user_id: str, addon_id: str, days: int = 7) -> str:
        """Get recent usage trend for an add-on."""
        try:
            db = await get_database()
            usage_collection = db["addon_usage"]

            user_obj_id = ObjectId(user_id) if isinstance(user_id, str) else user_id

            recent_usage = await usage_collection.count_documents({
                "user_id": user_obj_id,
                "addon_id": addon_id,
                "timestamp": {"$gte": datetime.now(timezone.utc) - timedelta(days=days)}
            })

            previous_usage = await usage_collection.count_documents({
                "user_id": user_obj_id,
                "addon_id": addon_id,
                "timestamp": {
                    "$gte": datetime.now(timezone.utc) - timedelta(days=days*2),
                    "$lt": datetime.now(timezone.utc) - timedelta(days=days)
                }
            })

            if previous_usage == 0:
                return "increasing" if recent_usage > 0 else "stable"

            change_percentage = ((recent_usage - previous_usage) / previous_usage) * 100

            if change_percentage > 20:
                return "increasing"
            elif change_percentage < -20:
                return "decreasing"
            else:
                return "stable"

        except Exception:
            return "unknown"

    def _is_rollover_eligible(self, addon: Dict[str, Any], addon_config: Dict[str, Any]) -> bool:
        """Check if an add-on is eligible for credit rollover."""
        if addon_config["type"] != AddonType.CONSUMABLE:
            return False

        rollover_percentage = addon_config.get("rollover_percentage", 0)
        return rollover_percentage > 0 and addon["credits_remaining"] > 0

    async def _calculate_efficiency_metrics(self, user_id: str, usage_records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate efficiency metrics for usage."""
        if not usage_records:
            return {}

        # Calculate average usage per day
        timestamps = [r["timestamp"] for r in usage_records]
        date_range = (max(timestamps) - min(timestamps)).days
        avg_daily_usage = len(usage_records) / max(date_range, 1)

        # Calculate peak usage day
        usage_by_date = {}
        for record in usage_records:
            date_key = record["timestamp"].date()
            usage_by_date[date_key] = usage_by_date.get(date_key, 0) + record["amount"]

        peak_usage_date = max(usage_by_date.items(), key=lambda x: x[1]) if usage_by_date else None

        return {
            "avg_daily_usage": round(avg_daily_usage, 2),
            "peak_usage_date": peak_usage_date[0].isoformat() if peak_usage_date else None,
            "peak_usage_amount": peak_usage_date[1] if peak_usage_date else 0,
            "total_days_active": len(usage_by_date),
            "usage_consistency": round(len(usage_by_date) / max(date_range, 1), 2)
        }


# Global usage tracker instance
usage_tracker = AddonUsageTracker()


async def track_addon_usage(user_id, usage_type: str, amount: int = 1,
                           metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Track add-on usage for a user."""
    usage_type_enum = UsageType(usage_type)
    # Convert user_id to string if it's an ObjectId or int
    user_id_str = str(user_id)
    return await usage_tracker.track_usage(user_id_str, usage_type_enum, amount, metadata)


async def get_user_addon_analytics(user_id, addon_id: Optional[str] = None,
                                  days: int = 30) -> Dict[str, Any]:
    """Get usage analytics for a user's add-ons."""
    # Convert user_id to string if it's an ObjectId or int
    user_id_str = str(user_id)
    return await usage_tracker.get_usage_analytics(user_id_str, addon_id, days)


async def get_user_addon_status(user_id) -> List[Dict[str, Any]]:
    """Get status of all user's add-ons."""
    # Convert user_id to string if it's an ObjectId or int
    user_id_str = str(user_id)
    return await usage_tracker.get_addon_status(user_id_str)


# Placeholder functions for missing notification and monitoring services
# TODO: Implement these functions in the appropriate services

async def send_usage_alert(user_id: str, message: str, alert_type: str, **kwargs):
    """
    Placeholder for usage alert function.
    TODO: Implement this in app.services.notification
    """
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"PLACEHOLDER: Would send usage alert to user {user_id}: {message} (type: {alert_type})")
    return True

def record_usage_metrics(usage_type: str, plan_id: str, amount: int, **kwargs):
    """
    Placeholder for usage metrics recording function.
    TODO: Implement this in app.core.monitoring
    """
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"PLACEHOLDER: Would record usage metrics - type: {usage_type}, plan: {plan_id}, amount: {amount}")
    return True
