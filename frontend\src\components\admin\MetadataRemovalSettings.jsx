// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Switch,
  FormControlLabel,
  TextField,
  Button,
  Grid,
  Divider,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Paper,
  CircularProgress,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  Assessment as AssessmentIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { useNotification } from '../../hooks/useNotification';
import metadataRemovalApi from '../../api/metadataRemoval';

/**
 * Internal Admin Settings Component for Metadata Removal
 * 
 * This component provides admin controls for:
 * - Configuring metadata removal settings
 * - Monitoring performance metrics
 * - Viewing system alerts
 * - Testing functionality
 */
const MetadataRemovalSettings = () => {
  const { showSuccessNotification, showErrorNotification } = useNotification();
  
  // Configuration state
  const [config, setConfig] = useState({
    enabled: true,
    preserveQuality: true,
    maxFileSizeMB: 50,
    cacheTTL: 3600,
    performanceTargetMs: 200,
    batchSizeLimit: 10,
    trackUsage: true
  });
  
  // Performance metrics state
  const [metrics, setMetrics] = useState(null);
  const [globalStats, setGlobalStats] = useState(null);
  const [alerts, setAlerts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [testUrl, setTestUrl] = useState('');
  const [testResult, setTestResult] = useState(null);
  const [testLoading, setTestLoading] = useState(false);
  const [configChanged, setConfigChanged] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);

  // Load functions with useCallback for performance
  const loadConfiguration = useCallback(async () => {
    try {
      const data = await metadataRemovalApi.getConfiguration();
      setConfig(data.configuration || {});
    } catch (error) {
      console.error('Error loading configuration:', error);
      showErrorNotification('Failed to load configuration');
    }
  }, [showErrorNotification]);

  const loadMetrics = useCallback(async () => {
    try {
      const data = await metadataRemovalApi.getPerformanceMetrics();
      setMetrics(data.service_metrics || null);
    } catch (error) {
      console.error('Error loading metrics:', error);
      showErrorNotification('Failed to load performance metrics');
    }
  }, [showErrorNotification]);

  const loadGlobalStats = useCallback(async () => {
    try {
      const data = await metadataRemovalApi.getGlobalStats(24);
      setGlobalStats(data.stats || null);
    } catch (error) {
      console.error('Error loading global stats:', error);
      showErrorNotification('Failed to load global statistics');
    }
  }, [showErrorNotification]);

  const loadAlerts = useCallback(async () => {
    try {
      const data = await metadataRemovalApi.getAlerts(1);
      setAlerts(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error loading alerts:', error);
      showErrorNotification('Failed to load alerts');
    }
  }, [showErrorNotification]);

  // Load initial data
  useEffect(() => {
    loadConfiguration();
    loadMetrics();
    loadGlobalStats();
    loadAlerts();
  }, [loadConfiguration, loadMetrics, loadGlobalStats, loadAlerts]);

  const handleRefresh = useCallback(async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadConfiguration(),
        loadMetrics(),
        loadGlobalStats(),
        loadAlerts()
      ]);
      showSuccessNotification('Data refreshed successfully');
    } catch (refreshError) {
      console.error('Error refreshing data:', refreshError);
      showErrorNotification('Failed to refresh data');
    } finally {
      setLoading(false);
    }
  }, [loadConfiguration, loadMetrics, loadGlobalStats, loadAlerts, showSuccessNotification, showErrorNotification]);

  const handleTestMetadataRemoval = useCallback(async () => {
    if (!testUrl.trim()) {
      showErrorNotification('Please enter a test image URL');
      return;
    }

    // Basic URL validation
    try {
      new URL(testUrl);
    } catch {
      showErrorNotification('Please enter a valid URL');
      return;
    }

    setTestLoading(true);
    setTestResult(null); // Clear previous results

    try {
      const data = await metadataRemovalApi.testMetadataRemoval(testUrl);
      setTestResult(data.result || {});
      showSuccessNotification('Test completed successfully');
    } catch (testError) {
      console.error('Test metadata removal error:', testError);
      const errorMessage = testError.response?.data?.detail || testError.message || 'Unknown error occurred';
      showErrorNotification(`Test failed: ${errorMessage}`);
      setTestResult({ success: false, error: errorMessage });
    } finally {
      setTestLoading(false);
    }
  }, [testUrl, showErrorNotification, showSuccessNotification]);

  // Configuration handlers
  const handleConfigChange = useCallback((newConfig) => {
    setConfig(newConfig);
    setConfigChanged(true);
  }, []);

  const handleSaveConfiguration = useCallback(async () => {
    setSaveLoading(true);
    try {
      // Note: This would require a backend endpoint to save configuration
      // For now, we'll just show a success message
      showSuccessNotification('Configuration saved successfully');
      setConfigChanged(false);
    } catch (saveError) {
      console.error('Error saving configuration:', saveError);
      showErrorNotification('Failed to save configuration');
    } finally {
      setSaveLoading(false);
    }
  }, [showSuccessNotification, showErrorNotification]);

  const getStatusColor = (value, threshold, inverse = false) => {
    const isGood = inverse ? value < threshold : value > threshold;
    return isGood ? 'success' : 'error';
  };

  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <SettingsIcon sx={{ mr: 2 }} />
        <Typography variant="h4" component="h1">
          Metadata Removal Settings
        </Typography>
        <Box sx={{ ml: 'auto' }}>
          <Tooltip title="Refresh Data">
            <IconButton onClick={handleRefresh} disabled={loading}>
              {loading ? <CircularProgress size={24} /> : <RefreshIcon />}
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Configuration Panel */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Configuration
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={config.enabled}
                      onChange={(e) => handleConfigChange({...config, enabled: e.target.checked})}
                      aria-label="Toggle metadata removal"
                    />
                  }
                  label="Metadata Removal Enabled"
                />
              </Box>

              <Box sx={{ mb: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={config.preserveQuality}
                      onChange={(e) => handleConfigChange({...config, preserveQuality: e.target.checked})}
                      aria-label="Toggle image quality preservation"
                    />
                  }
                  label="Preserve Image Quality"
                />
              </Box>

              <Box sx={{ mb: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={config.trackUsage}
                      onChange={(e) => handleConfigChange({...config, trackUsage: e.target.checked})}
                      aria-label="Toggle usage tracking for billing"
                    />
                  }
                  label="Track Usage for Billing"
                />
              </Box>

              <TextField
                fullWidth
                label="Max File Size (MB)"
                type="number"
                value={config.maxFileSizeMB}
                onChange={(e) => handleConfigChange({...config, maxFileSizeMB: parseInt(e.target.value) || 0})}
                inputProps={{ min: 1, max: 100, 'aria-label': 'Maximum file size in megabytes' }}
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                label="Performance Target (ms)"
                type="number"
                value={config.performanceTargetMs}
                onChange={(e) => handleConfigChange({...config, performanceTargetMs: parseInt(e.target.value) || 0})}
                inputProps={{ min: 50, max: 5000, 'aria-label': 'Performance target in milliseconds' }}
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                label="Batch Size Limit"
                type="number"
                value={config.batchSizeLimit}
                onChange={(e) => handleConfigChange({...config, batchSizeLimit: parseInt(e.target.value) || 0})}
                inputProps={{ min: 1, max: 100, 'aria-label': 'Batch size limit' }}
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                label="Cache TTL (seconds)"
                type="number"
                value={config.cacheTTL}
                onChange={(e) => handleConfigChange({...config, cacheTTL: parseInt(e.target.value) || 0})}
                inputProps={{ min: 60, max: 86400, 'aria-label': 'Cache time to live in seconds' }}
                sx={{ mb: 2 }}
              />

              <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                <Button
                  variant="contained"
                  onClick={handleSaveConfiguration}
                  disabled={!configChanged || saveLoading}
                  startIcon={saveLoading ? <CircularProgress size={20} /> : null}
                  aria-label="Save configuration changes"
                >
                  {saveLoading ? 'Saving...' : 'Save Configuration'}
                </Button>

                <Button
                  variant="outlined"
                  onClick={() => {
                    loadConfiguration();
                    setConfigChanged(false);
                  }}
                  disabled={!configChanged || saveLoading}
                  aria-label="Reset configuration to last saved state"
                >
                  Reset
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Performance Metrics Panel */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <AssessmentIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Performance Metrics
              </Typography>
              
              {metrics ? (
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary">
                      Total Processed
                    </Typography>
                    <Typography variant="h6">
                      {metrics.total_processed}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary">
                      Avg Processing Time
                    </Typography>
                    <Chip
                      label={`${metrics.average_processing_time_ms}ms`}
                      color={getStatusColor(metrics.average_processing_time_ms, config.performanceTargetMs, true)}
                      size="small"
                    />
                  </Grid>
                  
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary">
                      Cache Hit Rate
                    </Typography>
                    <Chip
                      label={`${(metrics.cache_hit_rate * 100).toFixed(1)}%`}
                      color={getStatusColor(metrics.cache_hit_rate, 0.5)}
                      size="small"
                    />
                  </Grid>
                  
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary">
                      Error Rate
                    </Typography>
                    <Chip
                      label={`${(metrics.error_rate * 100).toFixed(1)}%`}
                      color={getStatusColor(metrics.error_rate, 0.05, true)}
                      size="small"
                    />
                  </Grid>
                  
                  <Grid item xs={12}>
                    <Typography variant="body2" color="textSecondary">
                      Meets Performance Target
                    </Typography>
                    <Chip
                      icon={metrics.meets_performance_target ? <CheckCircleIcon /> : <ErrorIcon />}
                      label={metrics.meets_performance_target ? 'Yes' : 'No'}
                      color={metrics.meets_performance_target ? 'success' : 'error'}
                      size="small"
                    />
                  </Grid>
                </Grid>
              ) : (
                <Typography>Loading metrics...</Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Global Statistics Panel */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                24-Hour Global Statistics
              </Typography>

              {globalStats ? (
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableBody>
                      <TableRow>
                        <TableCell>Total Operations</TableCell>
                        <TableCell align="right">{globalStats.total_operations}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Images Processed</TableCell>
                        <TableCell align="right">{globalStats.total_images_processed}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Success Rate</TableCell>
                        <TableCell align="right">
                          <Chip
                            label={`${(globalStats.success_rate * 100).toFixed(1)}%`}
                            color={getStatusColor(globalStats.success_rate, 0.95)}
                            size="small"
                          />
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Size Reduction</TableCell>
                        <TableCell align="right">
                          {formatBytes(globalStats.total_size_reduction_bytes)}
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>AI Tags Removed</TableCell>
                        <TableCell align="right">{globalStats.total_ai_tags_removed}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Metadata Tags Removed</TableCell>
                        <TableCell align="right">{globalStats.total_metadata_tags_removed}</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography>Loading statistics...</Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Alerts Panel */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <WarningIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Recent Alerts (1 hour)
              </Typography>

              {alerts.length > 0 ? (
                <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                  {alerts.map((alert, index) => (
                    <Alert
                      key={index}
                      severity={alert.type === 'performance_degradation' ? 'warning' : 'error'}
                      sx={{ mb: 1 }}
                    >
                      <Typography variant="body2">
                        <strong>{alert.type.replace('_', ' ').toUpperCase()}</strong>
                      </Typography>
                      <Typography variant="caption" display="block">
                        {alert.error_message ||
                         `Processing time: ${alert.processing_time_ms}ms (target: ${alert.target_ms}ms)`}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {new Date(alert.timestamp).toLocaleString()}
                      </Typography>
                    </Alert>
                  ))}
                </Box>
              ) : (
                <Alert severity="success">
                  No alerts in the last hour
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Test Panel */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Test Metadata Removal
              </Typography>

              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={8}>
                  <TextField
                    fullWidth
                    label="Test Image URL"
                    value={testUrl}
                    onChange={(e) => setTestUrl(e.target.value)}
                    placeholder="https://example.com/image.jpg"
                    helperText="Enter an image URL to test metadata removal functionality"
                    inputProps={{
                      'aria-label': 'Test image URL input',
                      'aria-describedby': 'test-url-helper-text'
                    }}
                    id="test-url-helper-text"
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <Button
                    variant="contained"
                    onClick={handleTestMetadataRemoval}
                    disabled={testLoading || !testUrl.trim()}
                    fullWidth
                    startIcon={testLoading ? <CircularProgress size={20} /> : null}
                    aria-label="Test metadata removal functionality"
                  >
                    {testLoading ? 'Testing...' : 'Test Removal'}
                  </Button>
                </Grid>
              </Grid>

              {testResult && (
                <Box sx={{ mt: 3 }}>
                  <Divider sx={{ mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Test Results
                  </Typography>

                  {testResult.success ? (
                    <Alert severity="success" sx={{ mb: 2 }}>
                      Metadata removal completed successfully
                    </Alert>
                  ) : (
                    <Alert severity="error" sx={{ mb: 2 }}>
                      Metadata removal failed
                    </Alert>
                  )}

                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableBody>
                        <TableRow>
                          <TableCell>Success</TableCell>
                          <TableCell align="right">
                            <Chip
                              icon={testResult.success ? <CheckCircleIcon /> : <ErrorIcon />}
                              label={testResult.success ? 'Yes' : 'No'}
                              color={testResult.success ? 'success' : 'error'}
                              size="small"
                            />
                          </TableCell>
                        </TableRow>
                        {testResult.original_size_bytes && (
                          <TableRow>
                            <TableCell>Original Size</TableCell>
                            <TableCell align="right">
                              {formatBytes(testResult.original_size_bytes)}
                            </TableCell>
                          </TableRow>
                        )}
                        {testResult.processed_size_bytes && (
                          <TableRow>
                            <TableCell>Processed Size</TableCell>
                            <TableCell align="right">
                              {formatBytes(testResult.processed_size_bytes)}
                            </TableCell>
                          </TableRow>
                        )}
                        {testResult.size_reduction_percent && (
                          <TableRow>
                            <TableCell>Size Reduction</TableCell>
                            <TableCell align="right">
                              {testResult.size_reduction_percent.toFixed(2)}%
                            </TableCell>
                          </TableRow>
                        )}
                        {testResult.metadata_removed && (
                          <>
                            <TableRow>
                              <TableCell>AI Tags Removed</TableCell>
                              <TableCell align="right">
                                {testResult.metadata_removed.ai_generator_tags?.length || 0}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>Total Metadata Tags</TableCell>
                              <TableCell align="right">
                                {testResult.metadata_removed.total_tags_removed || 0}
                              </TableCell>
                            </TableRow>
                          </>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

// No PropTypes needed - component has no props

export default MetadataRemovalSettings;
