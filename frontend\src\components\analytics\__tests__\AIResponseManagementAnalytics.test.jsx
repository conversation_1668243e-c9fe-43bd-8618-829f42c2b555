/**
 * Tests for AIResponseManagementAnalytics component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import AIResponseManagementAnalytics from '../AIResponseManagementAnalytics';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the hooks and API
vi.mock('../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showErrorNotification: vi.fn()
  }))
}));

vi.mock('../../api', () => ({
  default: {
    get: vi.fn()
  }
}));

// Mock GlassmorphicCard
vi.mock('../common', () => ({
  GlassmorphicCard: ({ children, ...props }) => <div {...props}>{children}</div>
}));

// Mock Recharts components
vi.mock('recharts', () => ({
  ResponsiveContainer: ({ children }) => <div data-testid="responsive-container">{children}</div>,
  LineChart: ({ children }) => <div data-testid="line-chart">{children}</div>,
  Line: () => <div data-testid="line" />,
  BarChart: ({ children }) => <div data-testid="bar-chart">{children}</div>,
  Bar: () => <div data-testid="bar" />,
  PieChart: ({ children }) => <div data-testid="pie-chart">{children}</div>,
  Pie: () => <div data-testid="pie" />,
  Cell: () => <div data-testid="cell" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  Legend: () => <div data-testid="legend" />
}));

describe('AIResponseManagementAnalytics', () => {
  const mockApi = require('../../api').default;
  const mockNotification = vi.fn();
  const mockData = {
    overall_metrics: {
      total_suggestions: 1000,
      approved_suggestions: 800,
      rejected_suggestions: 150,
      edited_suggestions: 50,
      average_rating: 4.2,
      average_response_time: 180
    },
    platform_metrics: {
      facebook: {
        total_suggestions: 300,
        approved_suggestions: 240,
        average_rating: 4.1,
        average_response_time: 160
      },
      twitter: {
        total_suggestions: 400,
        approved_suggestions: 320,
        average_rating: 4.3,
        average_response_time: 200
      }
    },
    time_series_data: {
      dates: ['2023-01-01', '2023-01-02', '2023-01-03'],
      approval_rate: [75, 80, 85],
      average_rating: [4.0, 4.1, 4.2],
      response_time: [200, 180, 160]
    },
    sentiment_shift: {
      before: 0.2,
      after: 0.7,
      improvement: 25.5
    },
    improvement_suggestions: [
      'Improve response personalization',
      'Reduce response time for better engagement',
      'Focus on platform-specific content optimization'
    ]
  };

  const defaultProps = {
    data: mockData,
    loading: false,
    onRefresh: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockApi.get.mockResolvedValue({ data: mockData });

    const { useNotification } = require('../../hooks/useNotification');
    useNotification.mockReturnValue({
      showErrorNotification: mockNotification
    });
  });

  test('renders AI response management analytics dashboard', () => {
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByText('AI Response Management Analytics')).toBeInTheDocument();
    expect(screen.getByText('80%')).toBeInTheDocument(); // Approval rate
    expect(screen.getByText('4.2/5')).toBeInTheDocument(); // Average rating
    expect(screen.getByText('3.0 min')).toBeInTheDocument(); // Response time
  });

  test('shows loading state when loading prop is true', () => {
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics {...defaultProps} loading={true} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('displays time range and platform filters', () => {
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Select time range for analytics')).toBeInTheDocument();
    expect(screen.getByLabelText('Select platform for analytics')).toBeInTheDocument();
  });

  test('handles time range change', async () => {
    const user = userEvent.setup();
    const mockOnRefresh = vi.fn();
    
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics {...defaultProps} onRefresh={mockOnRefresh} />
      </TestWrapper>
    );

    const timeRangeSelect = screen.getByLabelText('Select time range for analytics');
    await user.click(timeRangeSelect);
    
    await waitFor(() => {
      expect(screen.getByText('Last 7 days')).toBeInTheDocument();
    });
    
    await user.click(screen.getByText('Last 7 days'));

    expect(mockOnRefresh).toHaveBeenCalledWith(7, 'all');
  });

  test('handles platform filter change', async () => {
    const user = userEvent.setup();
    const mockOnRefresh = vi.fn();
    
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics {...defaultProps} onRefresh={mockOnRefresh} />
      </TestWrapper>
    );

    const platformSelect = screen.getByLabelText('Select platform for analytics');
    await user.click(platformSelect);
    
    await waitFor(() => {
      expect(screen.getByText('Facebook')).toBeInTheDocument();
    });
    
    await user.click(screen.getByText('Facebook'));

    expect(mockOnRefresh).toHaveBeenCalledWith(30, 'facebook');
  });

  test('displays charts correctly in overview tab', () => {
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByTestId('pie-chart')).toBeInTheDocument();
    expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
  });

  test('shows sentiment improvement data', () => {
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByText('+25.5%')).toBeInTheDocument(); // Sentiment improvement
    expect(screen.getByText('Sentiment Improvement')).toBeInTheDocument();
  });

  test('handles tab navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics {...defaultProps} />
      </TestWrapper>
    );

    // Click on Performance Metrics tab
    await user.click(screen.getByText('Performance Metrics'));

    expect(screen.getByText('Response Time Trend')).toBeInTheDocument();
    expect(screen.getByText('Approval Rate Trend')).toBeInTheDocument();

    // Click on Platform Comparison tab
    await user.click(screen.getByText('Platform Comparison'));

    expect(screen.getByText('Platform Performance Comparison')).toBeInTheDocument();
    expect(screen.getByText('Platform Statistics')).toBeInTheDocument();

    // Click on Response Quality tab
    await user.click(screen.getByText('Response Quality'));

    expect(screen.getByText('Quality Analysis')).toBeInTheDocument();
    expect(screen.getByText('Improvement Suggestions')).toBeInTheDocument();
  });

  test('displays platform statistics correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics {...defaultProps} />
      </TestWrapper>
    );

    // Navigate to Platform Comparison tab
    await user.click(screen.getByText('Platform Comparison'));

    expect(screen.getByText('Facebook')).toBeInTheDocument();
    expect(screen.getByText('Twitter')).toBeInTheDocument();
    expect(screen.getByText('Total: 300')).toBeInTheDocument(); // Facebook total
    expect(screen.getByText('Total: 400')).toBeInTheDocument(); // Twitter total
  });

  test('shows improvement suggestions', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics {...defaultProps} />
      </TestWrapper>
    );

    // Navigate to Response Quality tab
    await user.click(screen.getByText('Response Quality'));

    expect(screen.getByText('Improve response personalization')).toBeInTheDocument();
    expect(screen.getByText('Reduce response time for better engagement')).toBeInTheDocument();
    expect(screen.getByText('Focus on platform-specific content optimization')).toBeInTheDocument();
  });

  test('handles missing data gracefully', () => {
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics data={null} loading={false} />
      </TestWrapper>
    );

    expect(screen.getByText('No data available')).toBeInTheDocument();
    expect(screen.getByText('Please check your data source or try refreshing the page.')).toBeInTheDocument();
  });

  test('handles missing overall_metrics gracefully', () => {
    const incompleteData = { ...mockData };
    delete incompleteData.overall_metrics;

    render(
      <TestWrapper>
        <AIResponseManagementAnalytics data={incompleteData} loading={false} />
      </TestWrapper>
    );

    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  test('displays custom tooltip correctly', () => {
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics {...defaultProps} />
      </TestWrapper>
    );

    // The CustomTooltip component should be rendered within charts
    expect(screen.getByTestId('pie-chart')).toBeInTheDocument();
    expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
  });

  test('calculates approval rate correctly', () => {
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics {...defaultProps} />
      </TestWrapper>
    );

    // 800 approved out of 1000 total = 80%
    expect(screen.getByText('80%')).toBeInTheDocument();
    expect(screen.getByText('800 of 1000 suggestions')).toBeInTheDocument();
  });

  test('formats response time correctly', () => {
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics {...defaultProps} />
      </TestWrapper>
    );

    // 180 seconds = 3.0 minutes
    expect(screen.getByText('3.0 min')).toBeInTheDocument();
  });

  test('works without onRefresh callback', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics data={mockData} loading={false} />
      </TestWrapper>
    );

    const timeRangeSelect = screen.getByLabelText('Select time range for analytics');
    await user.click(timeRangeSelect);
    
    await waitFor(() => {
      expect(screen.getByText('Last 7 days')).toBeInTheDocument();
    });
    
    // Should not throw error when onRefresh is not provided
    await user.click(screen.getByText('Last 7 days'));
  });

  test('displays all tab labels correctly', () => {
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Overview')).toBeInTheDocument();
    expect(screen.getByText('Performance Metrics')).toBeInTheDocument();
    expect(screen.getByText('Platform Comparison')).toBeInTheDocument();
    expect(screen.getByText('Response Quality')).toBeInTheDocument();
  });

  test('handles missing time_series_data in performance tab', async () => {
    const user = userEvent.setup();
    const dataWithoutTimeSeries = { ...mockData };
    delete dataWithoutTimeSeries.time_series_data;
    
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics data={dataWithoutTimeSeries} loading={false} />
      </TestWrapper>
    );

    await user.click(screen.getByText('Performance Metrics'));

    // Should handle missing data gracefully without crashing
    expect(screen.getByText('Performance Metrics')).toBeInTheDocument();
  });

  test('handles missing platform_metrics in platform tab', async () => {
    const user = userEvent.setup();
    const dataWithoutPlatformMetrics = { ...mockData };
    delete dataWithoutPlatformMetrics.platform_metrics;
    
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics data={dataWithoutPlatformMetrics} loading={false} />
      </TestWrapper>
    );

    await user.click(screen.getByText('Platform Comparison'));

    // Should handle missing data gracefully without crashing
    expect(screen.getByText('Platform Comparison')).toBeInTheDocument();
  });

  test('handles missing improvement_suggestions in quality tab', async () => {
    const user = userEvent.setup();
    const dataWithoutSuggestions = { ...mockData };
    delete dataWithoutSuggestions.improvement_suggestions;
    
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics data={dataWithoutSuggestions} loading={false} />
      </TestWrapper>
    );

    await user.click(screen.getByText('Response Quality'));

    // Should handle missing data gracefully without crashing
    expect(screen.getByText('Response Quality')).toBeInTheDocument();
  });

  test('fetches data automatically when autoFetch is true', async () => {
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics autoFetch={true} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/ai-feedback/analytics', {
        params: { days: 30, platform: undefined }
      });
    });
  });

  test('does not fetch data when autoFetch is false', () => {
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics autoFetch={false} />
      </TestWrapper>
    );

    expect(mockApi.get).not.toHaveBeenCalled();
  });

  test('handles API error gracefully', async () => {
    mockApi.get.mockRejectedValue(new Error('API Error'));

    render(
      <TestWrapper>
        <AIResponseManagementAnalytics autoFetch={true} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Failed to load analytics data. Please try again.')).toBeInTheDocument();
      expect(mockNotification).toHaveBeenCalledWith('Failed to load analytics data. Please try again.');
    });
  });

  test('refresh button triggers data refetch', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <AIResponseManagementAnalytics autoFetch={true} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledTimes(1);
    });

    // Clear previous calls
    vi.clearAllMocks();

    // Click refresh button
    const refreshButton = screen.getByLabelText('Refresh analytics data');
    await user.click(refreshButton);

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/ai-feedback/analytics', {
        params: { days: 30, platform: undefined }
      });
    });
  });

  test('disables controls during loading', async () => {
    render(
      <TestWrapper>
        <AIResponseManagementAnalytics loading={true} />
      </TestWrapper>
    );

    const timeRangeSelect = screen.getByLabelText('Select time range for analytics');
    const platformSelect = screen.getByLabelText('Select platform for analytics');
    const refreshButton = screen.getByLabelText('Refresh analytics data');

    expect(timeRangeSelect).toBeDisabled();
    expect(platformSelect).toBeDisabled();
    expect(refreshButton).toBeDisabled();
  });

  test('updates data when external data prop changes', () => {
    const { rerender } = render(
      <TestWrapper>
        <AIResponseManagementAnalytics data={mockData} />
      </TestWrapper>
    );

    expect(screen.getByText('80%')).toBeInTheDocument();

    const newData = {
      ...mockData,
      overall_metrics: {
        ...mockData.overall_metrics,
        approved_suggestions: 900
      }
    };

    rerender(
      <TestWrapper>
        <AIResponseManagementAnalytics data={newData} />
      </TestWrapper>
    );

    expect(screen.getByText('90%')).toBeInTheDocument(); // 900/1000 = 90%
  });

  test('filters are included in API calls', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <AIResponseManagementAnalytics autoFetch={true} />
      </TestWrapper>
    );

    // Wait for initial load
    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/ai-feedback/analytics', {
        params: { days: 30, platform: undefined }
      });
    });

    // Clear previous calls
    vi.clearAllMocks();

    // Change time range
    const timeRangeSelect = screen.getByLabelText('Select time range for analytics');
    await user.click(timeRangeSelect);
    await user.click(screen.getByText('Last 7 days'));

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/ai-feedback/analytics', {
        params: { days: 7, platform: undefined }
      });
    });

    // Clear previous calls
    vi.clearAllMocks();

    // Change platform
    const platformSelect = screen.getByLabelText('Select platform for analytics');
    await user.click(platformSelect);
    await user.click(screen.getByText('Facebook'));

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/ai-feedback/analytics', {
        params: { days: 7, platform: 'facebook' }
      });
    });
  });
});
