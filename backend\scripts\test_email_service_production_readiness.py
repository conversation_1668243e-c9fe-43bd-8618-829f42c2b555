#!/usr/bin/env python3
"""
Email Service Production Readiness Test

This script performs comprehensive production readiness testing for the email service,
including error handling, fallback behavior, performance, and integration stability.

Usage:
    python scripts/test_email_service_production_readiness.py

@since 2024-1-1 to 2025-25-7
"""
import asyncio
import sys
import os
import time
from datetime import datetime
from unittest.mock import Mock, patch

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.services.email_service import EmailService, email_service
from app.services.email_service import (
    send_welcome_email,
    send_trial_started_email,
    send_team_invitation_email,
    send_admin_notification_email
)


class EmailServiceProductionTester:
    """Production readiness testing for email service."""
    
    def __init__(self):
        self.results = []
    
    def log_result(self, test_name: str, status: str, message: str, details: dict = None):
        """Log test result."""
        result = {
            "test": test_name,
            "status": status,
            "message": message,
            "details": details or {},
            "timestamp": datetime.now().isoformat()
        }
        self.results.append(result)
        
        color = {
            "PASS": "\033[92m",    # Green
            "FAIL": "\033[91m",    # Red
            "WARNING": "\033[93m", # Yellow
        }.get(status, "\033[0m")
        
        reset_color = "\033[0m"
        print(f"[{color}{status}{reset_color}] {test_name}: {message}")
        
        if details:
            for key, value in details.items():
                print(f"  {key}: {value}")
    
    async def test_email_service_resilience(self):
        """Test email service resilience and error handling."""
        try:
            # Test with mock SMTP failure
            with patch('app.services.email_service.EmailService.send_email') as mock_send:
                mock_send.side_effect = Exception("SMTP server unavailable")
                
                # Test that application continues to function
                try:
                    result = await send_welcome_email("<EMAIL>", "Test User")
                    # Should return False but not crash
                    if result is False:
                        self.log_result(
                            "Email Service Resilience",
                            "PASS",
                            "Email service handles SMTP failures gracefully"
                        )
                    else:
                        self.log_result(
                            "Email Service Resilience",
                            "WARNING",
                            "Email service may not handle failures properly"
                        )
                except Exception as e:
                    self.log_result(
                        "Email Service Resilience",
                        "FAIL",
                        f"Email service crashes on SMTP failure: {str(e)}"
                    )
                    
        except Exception as e:
            self.log_result(
                "Email Service Resilience",
                "FAIL",
                f"Failed to test email service resilience: {str(e)}"
            )
    
    async def test_email_performance(self):
        """Test email service performance."""
        try:
            # Test multiple concurrent email sends
            start_time = time.time()
            
            tasks = []
            for i in range(10):
                task = send_welcome_email(f"test{i}@example.com", f"Test User {i}")
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            end_time = time.time()
            duration = end_time - start_time
            
            successful_sends = sum(1 for r in results if r is True)
            failed_sends = len(results) - successful_sends
            
            self.log_result(
                "Email Performance",
                "PASS" if failed_sends == 0 else "WARNING",
                f"Processed {len(tasks)} emails in {duration:.2f}s",
                {
                    "successful_sends": successful_sends,
                    "failed_sends": failed_sends,
                    "avg_time_per_email": f"{duration/len(tasks):.3f}s"
                }
            )
            
        except Exception as e:
            self.log_result(
                "Email Performance",
                "FAIL",
                f"Email performance test failed: {str(e)}"
            )
    
    def test_email_configuration_validation(self):
        """Test email configuration validation."""
        try:
            service = EmailService()
            
            # Check required configuration
            config_checks = {
                "smtp_host": service.smtp_host,
                "smtp_port": service.smtp_port,
                "from_email": service.from_email,
                "from_name": service.from_name
            }
            
            missing_config = [key for key, value in config_checks.items() if not value]
            
            if missing_config:
                self.log_result(
                    "Email Configuration",
                    "WARNING",
                    f"Missing configuration: {', '.join(missing_config)}",
                    {"note": "Email service will use mock mode in development"}
                )
            else:
                self.log_result(
                    "Email Configuration",
                    "PASS",
                    "Email service properly configured"
                )
                
        except Exception as e:
            self.log_result(
                "Email Configuration",
                "FAIL",
                f"Email configuration test failed: {str(e)}"
            )
    
    async def test_email_function_completeness(self):
        """Test that all email functions are implemented and working."""
        email_functions = [
            ("Welcome Email", send_welcome_email, ["<EMAIL>", "Test User"]),
            ("Trial Started", send_trial_started_email, ["<EMAIL>", "Test User", "2025-08-01"]),
            ("Team Invitation", send_team_invitation_email, ["<EMAIL>", "Inviter", "Team", "token123"]),
            ("Admin Notification", send_admin_notification_email, ["Test Subject", "Test Message"])
        ]
        
        working_functions = []
        broken_functions = []
        
        for name, func, args in email_functions:
            try:
                result = await func(*args)
                if result:
                    working_functions.append(name)
                else:
                    broken_functions.append(f"{name}: Returned False")
            except Exception as e:
                broken_functions.append(f"{name}: {str(e)}")
        
        if broken_functions:
            self.log_result(
                "Email Function Completeness",
                "FAIL",
                f"Broken functions: {', '.join(broken_functions)}",
                {"working": len(working_functions), "broken": len(broken_functions)}
            )
        else:
            self.log_result(
                "Email Function Completeness",
                "PASS",
                f"All {len(working_functions)} email functions working",
                {"tested_functions": working_functions}
            )
    
    def test_template_integrity(self):
        """Test email template integrity."""
        template_path = os.path.join(os.path.dirname(__file__), '..', 'app', 'templates', 'emails')
        
        critical_templates = [
            'welcome.html',
            'password_reset.html',
            'email_verification.html',
            'trial_started.html',
            'trial_ending_soon.html',
            'trial_ended.html',
            'team_invitation.html',
            'admin_notification.html',
            'subscription_updated.html',
            'payment_failed.html',
            'payment_succeeded.html'
        ]
        
        existing_templates = []
        missing_templates = []
        corrupted_templates = []
        
        for template in critical_templates:
            template_file = os.path.join(template_path, template)
            if os.path.exists(template_file):
                try:
                    with open(template_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if len(content) > 100 and '<html' in content:  # Basic validation
                            existing_templates.append(template)
                        else:
                            corrupted_templates.append(template)
                except Exception:
                    corrupted_templates.append(template)
            else:
                missing_templates.append(template)
        
        if missing_templates or corrupted_templates:
            status = "WARNING" if not missing_templates else "FAIL"
            issues = []
            if missing_templates:
                issues.append(f"Missing: {', '.join(missing_templates)}")
            if corrupted_templates:
                issues.append(f"Corrupted: {', '.join(corrupted_templates)}")
            
            self.log_result(
                "Template Integrity",
                status,
                "; ".join(issues),
                {
                    "existing": len(existing_templates),
                    "missing": len(missing_templates),
                    "corrupted": len(corrupted_templates)
                }
            )
        else:
            self.log_result(
                "Template Integrity",
                "PASS",
                f"All {len(existing_templates)} critical templates are valid"
            )
    
    def test_oauth_integration_stability(self):
        """Test OAuth integration stability."""
        try:
            # Test that OAuth routes can import email functions without issues
            from app.api.routes.auth import send_welcome_email as oauth_welcome
            
            # Test that the import doesn't cause circular dependencies
            self.log_result(
                "OAuth Integration Stability",
                "PASS",
                "OAuth routes import email functions without circular dependencies"
            )
            
        except ImportError as e:
            self.log_result(
                "OAuth Integration Stability",
                "FAIL",
                f"OAuth integration import error: {str(e)}"
            )
        except Exception as e:
            self.log_result(
                "OAuth Integration Stability",
                "WARNING",
                f"OAuth integration stability issue: {str(e)}"
            )
    
    def generate_report(self):
        """Generate production readiness report."""
        total_tests = len(self.results)
        passed_tests = len([r for r in self.results if r["status"] == "PASS"])
        failed_tests = len([r for r in self.results if r["status"] == "FAIL"])
        warning_tests = len([r for r in self.results if r["status"] == "WARNING"])
        
        print("\n" + "="*60)
        print("EMAIL SERVICE PRODUCTION READINESS REPORT")
        print("="*60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Warnings: {warning_tests}")
        print()
        
        # Calculate readiness score
        score = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        if failed_tests == 0:
            print(f"✅ EMAIL SERVICE PRODUCTION READINESS: {score:.1f}%")
            print("Email service is ready for production deployment.")
        else:
            print(f"❌ EMAIL SERVICE PRODUCTION READINESS: {score:.1f}%")
            print("Please fix the failed tests before deploying to production.")
        
        return failed_tests == 0


async def main():
    """Main test function."""
    print("Starting Email Service Production Readiness Tests...")
    print(f"Timestamp: {datetime.now().isoformat()}")
    print()
    
    tester = EmailServiceProductionTester()
    
    # Run all production readiness tests
    print("=== Resilience Tests ===")
    await tester.test_email_service_resilience()
    
    print("\n=== Performance Tests ===")
    await tester.test_email_performance()
    
    print("\n=== Configuration Tests ===")
    tester.test_email_configuration_validation()
    
    print("\n=== Function Completeness Tests ===")
    await tester.test_email_function_completeness()
    
    print("\n=== Template Integrity Tests ===")
    tester.test_template_integrity()
    
    print("\n=== OAuth Integration Tests ===")
    tester.test_oauth_integration_stability()
    
    # Generate final report
    success = tester.generate_report()
    
    return 0 if success else 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error during testing: {str(e)}")
        sys.exit(1)
