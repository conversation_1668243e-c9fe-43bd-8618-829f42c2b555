"""
Email template management API routes.
@since 2024-1-1 to 2025-25-7
"""

import logging
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from fastapi.responses import J<PERSON><PERSON>esponse

from app.middleware.auth import get_current_admin_user
from app.services.advanced_rate_limiter import rate_limit
from app.utils.correlation_id import get_correlation_id, set_correlation_id
from app.models.user import User
from app.schemas.email_template import (
    EmailTemplateCreate, EmailTemplateUpdate, EmailTemplateResponse,
    EmailTemplateListResponse, TemplatePreviewRequest, TemplatePreviewResponse,
    TemplateTestRequest, BulkTemplateOperation, BulkOperationResponse
)
from app.schemas.email_template_enhanced import (
    TemplateCreateRequest, TemplateUpdateRequest, TemplateListRequest,
    TemplateResponse, TemplatePreviewRequest as EnhancedPreviewRequest
)
from app.services.email_template_enhanced_service import EmailTemplateEnhancedService

# Initialize the enhanced service
email_template_service = EmailTemplateEnhancedService()
from app.services.app_logging import get_logger

logger = get_logger(__name__)
router = APIRouter()

# Initialize the enhanced service on startup
async def initialize_service():
    """Initialize the enhanced email template service."""
    await email_template_service.initialize()


@router.get("", response_model=dict)
async def email_templates_root():
    """
    Root endpoint for Email Templates API with system information.
    """
    return {
        "message": "Email Templates & Campaign Management API",
        "version": "1.0.0",
        "features": [
            "template_management",
            "campaign_creation",
            "ab_testing",
            "template_versioning",
            "preview_functionality",
            "drag_drop_designer",
            "analytics_tracking",
            "bulk_operations",
            "audit_logging",
            "encryption_support"
        ],
        "endpoints": [
            "/",
            "/templates",
            "/templates/{template_id}",
            "/templates/{template_id}/preview",
            "/templates/{template_id}/test",
            "/templates/{template_id}/versions",
            "/campaigns",
            "/campaigns/{campaign_id}",
            "/bulk-operations"
        ],
        "template_types": [
            "transactional",
            "marketing", 
            "system",
            "notification"
        ],
        "supported_features": [
            "jinja2_templating",
            "responsive_design",
            "device_preview",
            "variable_validation",
            "content_encryption",
            "version_control",
            "a_b_testing",
            "analytics_integration"
        ]
    }


@router.post("/templates", response_model=EmailTemplateResponse, status_code=status.HTTP_201_CREATED)
@rate_limit(calls=20, period=60)  # 20 template creations per minute
async def create_template(
    request: Request,
    template_data: EmailTemplateCreate,
    current_admin: User = Depends(get_current_admin_user)
):
    """
    Create a new email template.
    
    **Features:**
    - Template content validation
    - Variable extraction and validation
    - Content encryption for sensitive templates
    - Audit logging with correlation IDs
    - Version control initialization
    
    **Rate Limit:** 20 requests per minute per admin
    """
    correlation_id = get_correlation_id()
    
    try:
        # Ensure service is initialized
        if email_template_service.db is None:
            await email_template_service.initialize()

        # Convert basic schema to enhanced schema
        from app.models.email_template import TemplateVariable

        # Convert string variables to TemplateVariable objects
        template_variables = []
        for var_name in template_data.variables:
            template_variables.append(TemplateVariable(
                name=var_name,
                description=f"Variable: {var_name}",
                type="string",
                required=False,
                example_value=template_data.default_values.get(var_name, f"sample_{var_name}")
            ))

        enhanced_request = TemplateCreateRequest(
            name=template_data.name,
            description=template_data.description or "",
            category=template_data.template_type,  # Use template_type as category
            subject=template_data.subject,
            html_content=template_data.html_content,
            text_content=template_data.text_content,
            variables=template_variables,
            tags=template_data.tags,
            language="en"  # Default language
        )

        template = await email_template_service.create_template(
            request=enhanced_request,
            created_by=str(current_admin.id)
        )
        
        logger.info(f"Created email template: {template.name} (ID: {template.id}) by admin {current_admin.id}")
        
        return EmailTemplateResponse(**template.model_dump())
        
    except ValueError as e:
        logger.warning(f"Template creation validation error: {e} (correlation_id: {correlation_id})")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating template: {e} (correlation_id: {correlation_id})")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create template"
        )


@router.get("/templates", response_model=EmailTemplateListResponse)
@rate_limit(calls=100, period=60)  # 100 requests per minute
async def list_templates(
    request: Request,
    skip: int = Query(0, ge=0, description="Number of templates to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of templates to return"),
    template_type: Optional[str] = Query(None, description="Filter by template type"),
    template_status: Optional[str] = Query(None, alias="status", description="Filter by template status"),
    category: Optional[str] = Query(None, description="Filter by template category"),
    search: Optional[str] = Query(None, description="Search in name, description, and tags"),
    sort_by: str = Query("created_at", description="Sort field"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="Sort order"),
    current_admin: User = Depends(get_current_admin_user)
):
    """
    List email templates with filtering, searching, and pagination.
    
    **Features:**
    - Advanced filtering by type, status, category
    - Full-text search across name, description, tags
    - Flexible sorting options
    - Pagination with metadata
    - Performance optimized with database indexes
    
    **Rate Limit:** 100 requests per minute per admin
    """
    try:
        # Ensure service is initialized
        if email_template_service.db is None:
            await email_template_service.initialize()

        # Convert parameters to enhanced request
        list_request = TemplateListRequest(
            skip=skip,
            limit=limit,
            category=template_type,  # Map template_type to category
            status=template_status,
            search=search
        )

        templates, total = await email_template_service.list_templates(list_request)
        
        # Convert templates to response format
        template_responses = [
            EmailTemplateResponse(**template.model_dump())
            for template in templates
        ]

        # Calculate pagination metadata
        page = (skip // limit) + 1 if limit > 0 else 1
        pages = (total + limit - 1) // limit if limit > 0 else 1

        return EmailTemplateListResponse(
            templates=template_responses,
            total=total,
            page=page,
            page_size=limit,
            pages=pages
        )
        
    except Exception as e:
        logger.error(f"Error listing templates: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list templates"
        )


@router.get("/templates/{template_id}", response_model=EmailTemplateResponse)
@rate_limit(calls=200, period=60)  # 200 requests per minute
async def get_template(
    request: Request,
    template_id: str,
    current_admin: User = Depends(get_current_admin_user)
):
    """
    Get email template by ID.
    
    **Features:**
    - Content decryption for encrypted templates
    - Complete template metadata
    - Usage statistics
    - Version information
    
    **Rate Limit:** 200 requests per minute per admin
    """
    try:
        template = await email_template_service.get_template(template_id)
        
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Template not found"
            )
        
        return EmailTemplateResponse(**template.model_dump())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting template {template_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get template"
        )


@router.put("/templates/{template_id}", response_model=EmailTemplateResponse)
@rate_limit(calls=50, period=60)  # 50 updates per minute
async def update_template(
    request: Request,
    template_id: str,
    update_data: EmailTemplateUpdate,
    current_admin: User = Depends(get_current_admin_user)
):
    """
    Update email template.
    
    **Features:**
    - Content validation and variable checking
    - Automatic version creation for content changes
    - Content encryption for sensitive templates
    - Audit logging with change tracking
    - Rollback capability through versioning
    
    **Rate Limit:** 50 requests per minute per admin
    """
    correlation_id = get_correlation_id()
    
    try:
        # Ensure service is initialized
        if email_template_service.db is None:
            await email_template_service.initialize()

        # Convert basic update schema to enhanced schema
        enhanced_update = TemplateUpdateRequest(
            name=update_data.name,
            description=update_data.description,
            category=update_data.template_type,
            subject=update_data.subject,
            html_content=update_data.html_content,
            text_content=update_data.text_content,
            status=update_data.status,
            tags=update_data.tags
        )

        template = await email_template_service.update_template(
            template_id=template_id,
            request=enhanced_update,
            updated_by=str(current_admin.id)
        )
        
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Template not found"
            )
        
        logger.info(f"Updated email template: {template_id} by admin {current_admin.id}")
        
        return EmailTemplateResponse(**template.model_dump())
        
    except ValueError as e:
        logger.warning(f"Template update validation error: {e} (correlation_id: {correlation_id})")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating template {template_id}: {e} (correlation_id: {correlation_id})")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update template"
        )


@router.delete("/templates/{template_id}", status_code=status.HTTP_204_NO_CONTENT)
@rate_limit(calls=20, period=60)  # 20 deletions per minute
async def delete_template(
    request: Request,
    template_id: str,
    current_admin: User = Depends(get_current_admin_user)
):
    """
    Delete email template.
    
    **Features:**
    - Campaign usage validation
    - Cascade deletion of template versions
    - Audit logging for compliance
    - Soft delete option for data retention
    
    **Rate Limit:** 20 requests per minute per admin
    """
    correlation_id = get_correlation_id()
    
    try:
        # Ensure service is initialized
        if email_template_service.db is None:
            await email_template_service.initialize()

        success = await email_template_service.delete_template(template_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Template not found"
            )
        
        logger.info(f"Deleted email template: {template_id} by admin {current_admin.id}")
        
    except ValueError as e:
        logger.warning(f"Template deletion validation error: {e} (correlation_id: {correlation_id})")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting template {template_id}: {e} (correlation_id: {correlation_id})")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete template"
        )


@router.post("/templates/{template_id}/preview", response_model=TemplatePreviewResponse)
@rate_limit(calls=100, period=60)  # 100 previews per minute
async def preview_template(
    request: Request,
    template_id: str,
    preview_request: TemplatePreviewRequest,
    current_admin: User = Depends(get_current_admin_user)
):
    """
    Preview email template with sample data.
    
    **Features:**
    - Real-time template rendering
    - Variable substitution
    - Device-specific preview (desktop, mobile, tablet)
    - Error handling for invalid templates
    - Performance optimized rendering
    
    **Rate Limit:** 100 requests per minute per admin
    """
    try:
        # Ensure service is initialized
        if email_template_service.db is None:
            await email_template_service.initialize()

        # Use render_template method from enhanced service
        rendered = await email_template_service.render_template(
            template_id=template_id,
            template_data=preview_request.template_data
        )

        return TemplatePreviewResponse(
            rendered_subject=rendered.get("subject") or "",
            rendered_html=rendered.get("html_content") or "",
            rendered_preview_text=rendered.get("text_content")
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error previewing template {template_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to preview template"
        )


@router.post("/templates/{template_id}/test", status_code=status.HTTP_200_OK)
@rate_limit(calls=10, period=60)  # 10 test emails per minute
async def test_template(
    request: Request,
    template_id: str,
    test_request: TemplateTestRequest,
    current_admin: User = Depends(get_current_admin_user)
):
    """
    Send test email using template.
    
    **Features:**
    - Real email delivery testing
    - Template rendering validation
    - Audit logging for test sends
    - Rate limiting to prevent abuse
    - Error handling and reporting
    
    **Rate Limit:** 10 requests per minute per admin
    """
    correlation_id = get_correlation_id()
    
    try:
        # Ensure service is initialized
        if email_template_service.db is None:
            await email_template_service.initialize()

        # Get the template
        template = await email_template_service.get_template(template_id)
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Template not found"
            )

        # Render the template with test data
        rendered = await email_template_service.render_template(
            template_id=template_id,
            template_data=test_request.template_data
        )

        # Send test email using the basic email service
        from app.services.email_service import email_service as basic_email_service
        success = await basic_email_service.send_email(
            recipient_email=test_request.test_email,
            subject=f"[TEST] {rendered.get('subject', template.subject)}",
            template_name="notification",  # Use a basic template
            template_data={
                "message": rendered.get('html_content', template.html_content),
                "title": "Test Email"
            },
            recipient_name="Test User"
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send test email"
            )

        return {"message": "Test email sent successfully"}
        
    except ValueError as e:
        logger.warning(f"Template test validation error: {e} (correlation_id: {correlation_id})")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing template {template_id}: {e} (correlation_id: {correlation_id})")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send test email"
        )


@router.post("/templates/{template_id}/duplicate", response_model=EmailTemplateResponse)
@rate_limit(calls=20, period=60)  # 20 duplications per minute
async def duplicate_template(
    request: Request,
    template_id: str,
    current_admin: User = Depends(get_current_admin_user)
):
    """
    Duplicate an existing email template.

    **Features:**
    - Creates a copy of the template with a new name
    - Preserves all content and settings
    - Resets usage statistics
    - Audit logging for template duplication

    **Rate Limit:** 20 requests per minute per admin
    """
    correlation_id = get_correlation_id()

    try:
        # Get original template
        original_template = await email_template_service.get_template(template_id)
        if not original_template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Template not found"
            )

        # Create enhanced request directly
        enhanced_request = TemplateCreateRequest(
            name=f"{original_template.name} (Copy)",
            description=original_template.description or "",
            category=original_template.category,
            subject=original_template.subject,
            html_content=original_template.html_content,
            text_content=original_template.text_content,
            variables=original_template.variables,  # Use original TemplateVariable objects
            tags=original_template.tags,
            language=original_template.language
        )

        # Create the duplicate
        duplicate_template = await email_template_service.create_template(
            request=enhanced_request,
            created_by=str(current_admin.id)
        )

        logger.info(f"Duplicated email template: {template_id} -> {duplicate_template.id} by admin {current_admin.id}")

        return EmailTemplateResponse(**duplicate_template.model_dump())

    except ValueError as e:
        logger.warning(f"Template duplication validation error: {e} (correlation_id: {correlation_id})")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error duplicating template {template_id}: {e} (correlation_id: {correlation_id})")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to duplicate template"
        )


@router.get("/analytics", response_model=dict)
@rate_limit(calls=50, period=60)  # 50 requests per minute
async def get_template_analytics(
    request: Request,
    time_range: str = Query("30d", description="Time range for analytics"),
    template_type: Optional[str] = Query(None, description="Filter by template type"),
    current_admin: User = Depends(get_current_admin_user)
):
    """
    Get email template analytics and performance metrics.

    **Features:**
    - Overview statistics (sent, opened, clicked, bounced)
    - Top performing templates
    - Performance trends over time
    - Template type breakdown
    - Engagement metrics

    **Rate Limit:** 50 requests per minute per admin
    """
    try:
        # Mock analytics data for now - in production this would query actual data
        analytics_data = {
            "overview": {
                "totalSent": 15420,
                "totalDelivered": 14890,
                "totalOpened": 8934,
                "totalClicked": 2145,
                "totalBounced": 530,
                "totalUnsubscribed": 89,
                "openRate": 60.0,
                "clickRate": 14.4,
                "bounceRate": 3.4,
                "unsubscribeRate": 0.6
            },
            "topTemplates": [
                {
                    "id": "1",
                    "name": "Welcome Email",
                    "type": "transactional",
                    "sent": 3420,
                    "opened": 2890,
                    "clicked": 1245,
                    "openRate": 84.5,
                    "clickRate": 36.4
                },
                {
                    "id": "2",
                    "name": "Weekly Newsletter",
                    "type": "marketing",
                    "sent": 2890,
                    "opened": 1734,
                    "clicked": 456,
                    "openRate": 60.0,
                    "clickRate": 15.8
                }
            ],
            "timeRange": time_range,
            "templateType": template_type
        }

        return analytics_data

    except Exception as e:
        logger.error(f"Error getting template analytics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get analytics data"
        )


@router.get("/campaigns", response_model=dict)
@rate_limit(calls=100, period=60)  # 100 requests per minute
async def get_campaigns(
    request: Request,
    skip: int = Query(0, ge=0, description="Number of campaigns to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of campaigns to return"),
    campaign_status: Optional[str] = Query(None, alias="status", description="Filter by campaign status"),
    current_admin: User = Depends(get_current_admin_user)
):
    """
    Get email campaigns with filtering and pagination.

    **Features:**
    - Campaign list with metadata
    - Status filtering
    - Performance metrics
    - Pagination support

    **Rate Limit:** 100 requests per minute per admin
    """
    try:
        # Suppress unused parameter warning - will be implemented when real data is added
        _ = campaign_status

        # Mock campaign data for now - in production this would query actual data
        campaigns_data = {
            "campaigns": [
                {
                    "id": "1",
                    "name": "Welcome Series",
                    "description": "Automated welcome email series for new users",
                    "template_id": "1",
                    "status": "running",
                    "scheduled_at": None,
                    "sent_at": "2024-01-15T10:00:00Z",
                    "recipient_count": 1500,
                    "sent_count": 1450,
                    "delivered_count": 1420,
                    "opened_count": 890,
                    "clicked_count": 245,
                    "bounced_count": 30,
                    "unsubscribed_count": 5
                },
                {
                    "id": "2",
                    "name": "Monthly Newsletter",
                    "description": "Monthly product updates and news",
                    "template_id": "2",
                    "status": "scheduled",
                    "scheduled_at": "2024-02-01T09:00:00Z",
                    "sent_at": None,
                    "recipient_count": 5000,
                    "sent_count": 0,
                    "delivered_count": 0,
                    "opened_count": 0,
                    "clicked_count": 0,
                    "bounced_count": 0,
                    "unsubscribed_count": 0
                }
            ],
            "total": 2,
            "page": skip // limit + 1,
            "page_size": limit,
            "pages": 1
        }

        return campaigns_data

    except Exception as e:
        logger.error(f"Error getting campaigns: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get campaigns"
        )
