/**
 * Tests for AppSumoBadge component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import AppSumoBadge from '../AppSumoBadge';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

describe('AppSumoBadge', () => {
  const mockProps = {
    variant: 'chip',
    tierName: 'Double',
    size: 'medium',
    onClick: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Chip Variant', () => {
    test('renders chip variant correctly', () => {
      render(
        <TestWrapper>
          <AppSumoBadge variant="chip" />
        </TestWrapper>
      );

      expect(screen.getByText('AppSumo Lifetime')).toBeInTheDocument();
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    test('renders chip variant with tier name', () => {
      render(
        <TestWrapper>
          <AppSumoBadge variant="chip" tierName="Double" />
        </TestWrapper>
      );

      expect(screen.getByText('AppSumo Lifetime - Double')).toBeInTheDocument();
    });

    test('hides tier info when showTierInfo is false', () => {
      render(
        <TestWrapper>
          <AppSumoBadge variant="chip" tierName="Double" showTierInfo={false} />
        </TestWrapper>
      );

      expect(screen.getByText('AppSumo Lifetime')).toBeInTheDocument();
      expect(screen.queryByText('AppSumo Lifetime - Double')).not.toBeInTheDocument();
    });

    test('handles click events', async () => {
      const user = userEvent.setup();
      const handleClick = vi.fn();

      render(
        <TestWrapper>
          <AppSumoBadge variant="chip" onClick={handleClick} />
        </TestWrapper>
      );

      const badge = screen.getByRole('button');
      await user.click(badge);

      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    test('handles keyboard events', async () => {
      const user = userEvent.setup();
      const handleClick = vi.fn();

      render(
        <TestWrapper>
          <AppSumoBadge variant="chip" onClick={handleClick} />
        </TestWrapper>
      );

      const badge = screen.getByRole('button');
      badge.focus();
      await user.keyboard('{Enter}');

      expect(handleClick).toHaveBeenCalledTimes(1);

      await user.keyboard(' ');
      expect(handleClick).toHaveBeenCalledTimes(2);
    });

    test('applies different sizes correctly', () => {
      const { rerender } = render(
        <TestWrapper>
          <AppSumoBadge variant="chip" size="small" />
        </TestWrapper>
      );

      let badge = screen.getByRole('button');
      expect(badge).toHaveClass('MuiChip-sizeSmall');

      rerender(
        <TestWrapper>
          <AppSumoBadge variant="chip" size="medium" />
        </TestWrapper>
      );

      badge = screen.getByRole('button');
      expect(badge).toHaveClass('MuiChip-sizeMedium');
    });
  });

  describe('Inline Variant', () => {
    test('renders inline variant correctly', () => {
      render(
        <TestWrapper>
          <AppSumoBadge variant="inline" />
        </TestWrapper>
      );

      expect(screen.getByText('AppSumo Lifetime')).toBeInTheDocument();
      // Should not be a button by default
      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });

    test('renders inline variant with tier name', () => {
      render(
        <TestWrapper>
          <AppSumoBadge variant="inline" tierName="Triple" />
        </TestWrapper>
      );

      expect(screen.getByText('AppSumo Lifetime')).toBeInTheDocument();
      expect(screen.getByText('- Triple')).toBeInTheDocument();
    });

    test('becomes interactive when onClick is provided', async () => {
      const user = userEvent.setup();
      const handleClick = vi.fn();

      render(
        <TestWrapper>
          <AppSumoBadge variant="inline" onClick={handleClick} />
        </TestWrapper>
      );

      const badge = screen.getByRole('button');
      await user.click(badge);

      expect(handleClick).toHaveBeenCalledTimes(1);
    });
  });

  describe('Full Variant', () => {
    test('renders full variant correctly', () => {
      render(
        <TestWrapper>
          <AppSumoBadge variant="full" />
        </TestWrapper>
      );

      expect(screen.getByText('AppSumo Lifetime Deal')).toBeInTheDocument();
    });

    test('renders full variant with tier name', () => {
      render(
        <TestWrapper>
          <AppSumoBadge variant="full" tierName="Single" />
        </TestWrapper>
      );

      expect(screen.getByText('AppSumo Lifetime Deal')).toBeInTheDocument();
      expect(screen.getByText('Single Tier')).toBeInTheDocument();
    });

    test('hides tier info when showTierInfo is false', () => {
      render(
        <TestWrapper>
          <AppSumoBadge variant="full" tierName="Single" showTierInfo={false} />
        </TestWrapper>
      );

      expect(screen.getByText('AppSumo Lifetime Deal')).toBeInTheDocument();
      expect(screen.queryByText('Single Tier')).not.toBeInTheDocument();
    });

    test('becomes interactive when onClick is provided', async () => {
      const user = userEvent.setup();
      const handleClick = vi.fn();

      render(
        <TestWrapper>
          <AppSumoBadge variant="full" onClick={handleClick} />
        </TestWrapper>
      );

      const badge = screen.getByRole('button');
      await user.click(badge);

      expect(handleClick).toHaveBeenCalledTimes(1);
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA attributes', () => {
      render(
        <TestWrapper>
          <AppSumoBadge variant="chip" onClick={vi.fn()} />
        </TestWrapper>
      );

      const badge = screen.getByRole('button');
      expect(badge).toHaveAttribute('tabIndex', '0');
    });

    test('has proper tooltip', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <AppSumoBadge variant="chip" tierName="Double" />
        </TestWrapper>
      );

      const badge = screen.getByRole('button');
      await user.hover(badge);

      expect(screen.getByText('AppSumo Lifetime Deal - Double Tier')).toBeInTheDocument();
    });

    test('tooltip works without tier name', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <AppSumoBadge variant="chip" />
        </TestWrapper>
      );

      const badge = screen.getByRole('button');
      await user.hover(badge);

      expect(screen.getByText('AppSumo Lifetime Deal')).toBeInTheDocument();
    });
  });

  describe('Styling and Theming', () => {
    test('applies custom styles', () => {
      render(
        <TestWrapper>
          <AppSumoBadge 
            variant="chip" 
            sx={{ backgroundColor: 'red' }}
            data-testid="custom-badge"
          />
        </TestWrapper>
      );

      const badge = screen.getByTestId('custom-badge');
      expect(badge).toHaveStyle({ backgroundColor: 'red' });
    });

    test('applies custom className', () => {
      render(
        <TestWrapper>
          <AppSumoBadge 
            variant="chip" 
            className="custom-class"
            data-testid="custom-badge"
          />
        </TestWrapper>
      );

      const badge = screen.getByTestId('custom-badge');
      expect(badge).toHaveClass('custom-class');
    });

    test('applies AppSumo brand colors', () => {
      render(
        <TestWrapper>
          <AppSumoBadge variant="chip" data-testid="brand-badge" />
        </TestWrapper>
      );

      const badge = screen.getByTestId('brand-badge');
      // Check for AppSumo orange background
      expect(badge).toHaveStyle({ backgroundColor: '#FF8C00' });
    });
  });

  describe('Animation', () => {
    test('applies animation when animated prop is true', () => {
      render(
        <TestWrapper>
          <AppSumoBadge 
            variant="chip" 
            animated={true}
            data-testid="animated-badge"
          />
        </TestWrapper>
      );

      const badge = screen.getByTestId('animated-badge');
      // Animation styles should be applied
      expect(badge).toHaveStyle({ animation: 'pulse 2s infinite' });
    });

    test('does not apply animation by default', () => {
      render(
        <TestWrapper>
          <AppSumoBadge 
            variant="chip" 
            data-testid="static-badge"
          />
        </TestWrapper>
      );

      const badge = screen.getByTestId('static-badge');
      // No animation should be applied
      expect(badge).not.toHaveStyle({ animation: 'pulse 2s infinite' });
    });
  });

  describe('Size Configurations', () => {
    test('applies correct size configurations for small', () => {
      render(
        <TestWrapper>
          <AppSumoBadge variant="full" size="small" tierName="Test" />
        </TestWrapper>
      );

      // Small size should use caption typography for tier
      expect(screen.getByText('Test Tier')).toBeInTheDocument();
    });

    test('applies correct size configurations for large', () => {
      render(
        <TestWrapper>
          <AppSumoBadge variant="full" size="large" tierName="Test" />
        </TestWrapper>
      );

      // Large size should use body2 typography for tier
      expect(screen.getByText('Test Tier')).toBeInTheDocument();
    });
  });

  describe('Props Validation', () => {
    test('handles missing props gracefully', () => {
      render(
        <TestWrapper>
          <AppSumoBadge />
        </TestWrapper>
      );

      // Should render with default props
      expect(screen.getByText('AppSumo Lifetime')).toBeInTheDocument();
    });

    test('handles empty tierName', () => {
      render(
        <TestWrapper>
          <AppSumoBadge variant="full" tierName="" />
        </TestWrapper>
      );

      expect(screen.getByText('AppSumo Lifetime Deal')).toBeInTheDocument();
      expect(screen.queryByText('Tier')).not.toBeInTheDocument();
    });

    test('passes through additional props', () => {
      render(
        <TestWrapper>
          <AppSumoBadge 
            variant="chip" 
            data-testid="test-badge"
            aria-label="Custom AppSumo Badge"
          />
        </TestWrapper>
      );

      const badge = screen.getByTestId('test-badge');
      expect(badge).toHaveAttribute('aria-label', 'Custom AppSumo Badge');
    });
  });

  describe('Integration with AppSumo Tiers', () => {
    test('renders different tier names correctly', () => {
      const tiers = ['Single', 'Double', 'Triple'];
      
      tiers.forEach(tier => {
        const { unmount } = render(
          <TestWrapper>
            <AppSumoBadge variant="full" tierName={tier} />
          </TestWrapper>
        );

        expect(screen.getByText(`${tier} Tier`)).toBeInTheDocument();
        unmount();
      });
    });

    test('handles special tier names', () => {
      render(
        <TestWrapper>
          <AppSumoBadge variant="chip" tierName="Premium Plus" />
        </TestWrapper>
      );

      expect(screen.getByText('AppSumo Lifetime - Premium Plus')).toBeInTheDocument();
    });
  });
});
