/**
 * AI Insights Integration Test Suite
 * 
 * Tests the complete AI insights enhancement including:
 * - Data aggregation service
 * - OpenAI service integration
 * - Caching mechanisms
 * - QuickInsights component
 * - Subscription-based features
 @since 2024-1-1 to 2025-25-7
*/

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import { aiInsightsDataService } from '../services/aiInsightsDataService';
import { openAIInsightsService } from '../services/openAIInsightsService';
import { aiInsightsCacheService } from '../services/aiInsightsCacheService';
import QuickInsights from '../components/analytics/QuickInsights';

// Mock dependencies
jest.mock('../services/api');
jest.mock('../contexts/SubscriptionContext');
jest.mock('../hooks/useNotification');

// Mock data
const mockAnalyticsData = {
  overview: {
    engagement_rate: 5.2,
    total_followers: 1250,
    total_engagements: 450,
    total_impressions: 8500,
    engagement_trend: 'up',
    previous_period: {
      engagement_rate: 4.8,
      total_followers: 1200,
      total_engagements: 400,
      total_impressions: 8000
    }
  },
  content: {
    content_type_distribution: {
      image: 45,
      video: 30,
      text: 25
    },
    content_performance: [
      { id: 1, type: 'image', engagement: 8.5, reach: 1200 },
      { id: 2, type: 'video', engagement: 12.3, reach: 2000 }
    ],
    best_content_type: 'video'
  },
  audience: {
    demographics: {
      age_distribution: { '18-24': 30, '25-34': 45, '35-44': 25 },
      gender_distribution: { male: 55, female: 45 },
      location_distribution: { 'US': 60, 'UK': 20, 'CA': 20 }
    },
    behavior: {
      active_hours: { morning: 25, afternoon: 35, evening: 40 },
      engagement_patterns: { weekday: 70, weekend: 30 }
    }
  },
  sentiment: {
    overall_sentiment: 0.75,
    sentiment_distribution: { positive: 60, neutral: 30, negative: 10 },
    confidence_score: 0.85
  }
};

const mockSubscription = {
  plan_id: 'accelerator',
  plan_name: 'Accelerator',
  user_id: 'test-user-123'
};

const mockAIResponse = {
  insights: [
    {
      id: 'insight-1',
      title: 'Video Content Performing Well',
      description: 'Your video content has 12.3% engagement rate, significantly higher than other content types.',
      category: 'content',
      type: 'positive',
      confidence: 0.92,
      impact: 'high',
      actionable: true,
      actions: [
        {
          title: 'Create More Videos',
          description: 'Increase video content production by 20%',
          priority: 'high',
          effort: 'medium',
          timeline: 'short-term'
        }
      ],
      metrics: {
        currentValue: '12.3% engagement',
        expectedImprovement: '15-20%',
        dataSource: 'content_analytics'
      }
    },
    {
      id: 'insight-2',
      title: 'Optimal Posting Time Opportunity',
      description: 'Your audience is most active in the evening (40%), but you post mostly during afternoon.',
      category: 'timing',
      type: 'opportunity',
      confidence: 0.78,
      impact: 'medium',
      actionable: true,
      actions: [
        {
          title: 'Adjust Posting Schedule',
          description: 'Shift 30% of posts to evening hours',
          priority: 'medium',
          effort: 'low',
          timeline: 'immediate'
        }
      ],
      metrics: {
        currentValue: 'Afternoon posting',
        expectedImprovement: '10-15%',
        dataSource: 'audience_behavior'
      }
    }
  ],
  summary: {
    overallPerformance: 'Your content strategy is performing well with strong video engagement and positive sentiment.',
    keyOpportunities: ['Increase video content', 'Optimize posting times'],
    priorityActions: ['Create more videos', 'Adjust posting schedule'],
    nextSteps: 'Focus on video content creation and evening posting schedule optimization.'
  },
  metadata: {
    planId: 'accelerator',
    tokensUsed: 1250,
    generatedAt: new Date().toISOString(),
    dataSourcesCount: 4,
    confidence: 0.85
  }
};

describe('AI Insights Integration Tests', () => {
  beforeEach(() => {
    // Clear all caches before each test
    aiInsightsCacheService.clearAll();
    jest.clearAllMocks();
  });

  describe('Data Aggregation Service', () => {
    test('should aggregate comprehensive metrics data', async () => {
      // Mock API responses
      jest.spyOn(global, 'fetch').mockImplementation((url) => {
        if (url.includes('/api/analytics/overview')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockAnalyticsData.overview)
          });
        }
        if (url.includes('/api/analytics/content')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockAnalyticsData.content)
          });
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({})
        });
      });

      const aggregatedData = await aiInsightsDataService.aggregateMetricsData(
        mockSubscription,
        { forceRefresh: true }
      );

      expect(aggregatedData).toHaveProperty('performance');
      expect(aggregatedData).toHaveProperty('content');
      expect(aggregatedData).toHaveProperty('audience');
      expect(aggregatedData).toHaveProperty('sentiment');
      expect(aggregatedData).toHaveProperty('metadata');
      
      expect(aggregatedData.performance.engagement.rate).toBe(5.2);
      expect(aggregatedData.content.distribution).toEqual(mockAnalyticsData.content.content_type_distribution);
      expect(aggregatedData.metadata.planId).toBe('accelerator');
    });

    test('should apply subscription-based data filtering', async () => {
      const creatorSubscription = { ...mockSubscription, plan_id: 'creator' };
      
      jest.spyOn(global, 'fetch').mockImplementation(() => 
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockAnalyticsData.overview)
        })
      );

      const aggregatedData = await aiInsightsDataService.aggregateMetricsData(creatorSubscription);

      expect(aggregatedData.planCapabilities.aiComplexity).toBe('basic');
      expect(aggregatedData.planCapabilities.includeCompetitive).toBe(false);
      expect(aggregatedData.planCapabilities.includePredictive).toBe(false);
    });
  });

  describe('OpenAI Service Integration', () => {
    test('should generate AI insights with proper structure', async () => {
      // Mock OpenAI API response
      jest.spyOn(global, 'fetch').mockImplementation((url) => {
        if (url.includes('openai.com')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              choices: [{
                message: {
                  content: JSON.stringify(mockAIResponse)
                }
              }],
              usage: {
                total_tokens: 1250
              }
            })
          });
        }
        return Promise.resolve({ ok: false });
      });

      const insights = await openAIInsightsService.generateInsights(
        mockAnalyticsData,
        mockSubscription,
        { maxInsights: 5 }
      );

      expect(insights).toHaveProperty('insights');
      expect(insights).toHaveProperty('summary');
      expect(insights).toHaveProperty('metadata');
      expect(insights.insights).toHaveLength(2);
      expect(insights.metadata.confidence).toBe(0.85);
      expect(insights.metadata.planId).toBe('accelerator');
    });

    test('should handle rate limiting correctly', async () => {
      // Simulate rate limit exceeded
      const rateLimitStatus = openAIInsightsService.getRateLimitStatus('test-user', 'creator');
      expect(rateLimitStatus).toHaveProperty('hourlyRemaining');
      expect(rateLimitStatus).toHaveProperty('dailyRemaining');
    });

    test('should provide fallback insights on API failure', async () => {
      // Mock API failure
      jest.spyOn(global, 'fetch').mockImplementation(() => 
        Promise.reject(new Error('API Error'))
      );

      const insights = await openAIInsightsService.generateInsights(
        mockAnalyticsData,
        mockSubscription
      );

      expect(insights).toHaveProperty('insights');
      expect(insights.metadata.fallback).toBe(true);
      expect(insights.insights.length).toBeGreaterThan(0);
    });
  });

  describe('Caching Service', () => {
    test('should cache and retrieve AI insights', async () => {
      const cacheKey = 'test-insights-key';
      const testData = { insights: mockAIResponse.insights };

      // Set cache
      await aiInsightsCacheService.set(cacheKey, testData);

      // Get from cache
      const cachedData = await aiInsightsCacheService.get(cacheKey);

      expect(cachedData).toEqual(testData);
    });

    test('should handle cache expiration', async () => {
      const cacheKey = 'test-expiry-key';
      const testData = { insights: [] };

      // Set cache with very short TTL
      await aiInsightsCacheService.set(cacheKey, testData, { ttl: 1 });

      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 10));

      // Should return null for expired cache
      const cachedData = await aiInsightsCacheService.get(cacheKey);
      expect(cachedData).toBeNull();
    });

    test('should provide cache statistics', () => {
      const stats = aiInsightsCacheService.getStats();
      
      expect(stats).toHaveProperty('hits');
      expect(stats).toHaveProperty('misses');
      expect(stats).toHaveProperty('hitRate');
      expect(stats).toHaveProperty('memorySize');
    });
  });

  describe('QuickInsights Component Integration', () => {
    const mockProps = {
      data: mockAnalyticsData,
      onRefresh: jest.fn(),
      loading: false,
      planFeatures: {
        maxInsights: 5,
        hasAdvancedAnalytics: true,
        hasCompetitiveIntelligence: true
      },
      enableAccessibility: true
    };

    test('should render AI insights with proper structure', async () => {
      // Mock the AI insights generation
      jest.spyOn(openAIInsightsService, 'generateInsights').mockResolvedValue(mockAIResponse);

      render(<QuickInsights {...mockProps} />);

      // Check for component elements
      expect(screen.getByText('AI Insights & Recommendations')).toBeInTheDocument();
      expect(screen.getByText('accelerator plan')).toBeInTheDocument();

      // Wait for insights to load
      await waitFor(() => {
        expect(screen.getByText('Video Content Performing Well')).toBeInTheDocument();
        expect(screen.getByText('Optimal Posting Time Opportunity')).toBeInTheDocument();
      });
    });

    test('should display confidence scores and action buttons', async () => {
      jest.spyOn(openAIInsightsService, 'generateInsights').mockResolvedValue(mockAIResponse);

      render(<QuickInsights {...mockProps} />);

      await waitFor(() => {
        // Check for confidence indicators
        expect(screen.getByText('92%')).toBeInTheDocument();
        expect(screen.getByText('78%')).toBeInTheDocument();

        // Check for action buttons
        expect(screen.getByText('Create More Videos')).toBeInTheDocument();
        expect(screen.getByText('Adjust Posting Schedule')).toBeInTheDocument();
      });
    });

    test('should handle category filtering', async () => {
      jest.spyOn(openAIInsightsService, 'generateInsights').mockResolvedValue(mockAIResponse);

      render(<QuickInsights {...mockProps} />);

      await waitFor(() => {
        // Click on content category filter
        const contentFilter = screen.getByText('Content Strategy');
        fireEvent.click(contentFilter);

        // Should show only content-related insights
        expect(screen.getByText('Video Content Performing Well')).toBeInTheDocument();
        expect(screen.queryByText('Optimal Posting Time Opportunity')).not.toBeInTheDocument();
      });
    });

    test('should show upgrade prompt for creator plan', async () => {
      const creatorProps = {
        ...mockProps,
        planFeatures: { ...mockProps.planFeatures, maxInsights: 3 }
      };

      // Mock subscription context to return creator plan
      jest.spyOn(require('../contexts/SubscriptionContext'), 'useSubscription').mockReturnValue({
        subscription: { plan_id: 'creator' }
      });

      render(<QuickInsights {...creatorProps} />);

      await waitFor(() => {
        expect(screen.getByText('🚀 Unlock Advanced AI Insights')).toBeInTheDocument();
        expect(screen.getByText('Upgrade Plan')).toBeInTheDocument();
      });
    });
  });

  describe('Performance and Error Handling', () => {
    test('should handle service errors gracefully', async () => {
      const errorProps = {
        ...mockProps,
        data: null
      };

      render(<QuickInsights {...errorProps} />);

      // Should show appropriate empty state
      expect(screen.getByText('No AI insights available')).toBeInTheDocument();
    });

    test('should track analytics events', async () => {
      const mockGtag = jest.fn();
      window.gtag = mockGtag;

      jest.spyOn(openAIInsightsService, 'generateInsights').mockResolvedValue(mockAIResponse);

      render(<QuickInsights {...mockProps} />);

      await waitFor(() => {
        expect(mockGtag).toHaveBeenCalledWith('event', 'ai_insights_generated', expect.any(Object));
      });
    });
  });
});

describe('Subscription-Based Feature Tests', () => {
  test('should limit insights based on subscription plan', async () => {
    const creatorData = await aiInsightsDataService.aggregateMetricsData(
      { plan_id: 'creator' }
    );
    
    expect(creatorData.planCapabilities.maxDataPoints).toBe(30);
    expect(creatorData.planCapabilities.aiComplexity).toBe('basic');
  });

  test('should provide advanced features for dominator plan', async () => {
    const dominatorData = await aiInsightsDataService.aggregateMetricsData(
      { plan_id: 'dominator' }
    );
    
    expect(dominatorData.planCapabilities.maxDataPoints).toBe(-1); // unlimited
    expect(dominatorData.planCapabilities.aiComplexity).toBe('premium');
    expect(dominatorData.planCapabilities.includeCompetitive).toBe(true);
    expect(dominatorData.planCapabilities.includePredictive).toBe(true);
  });
});

export default {};
