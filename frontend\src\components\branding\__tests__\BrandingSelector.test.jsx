/**
 * Tests for BrandingSelector component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import BrandingSelector from '../BrandingSelector';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the hooks
const mockShowErrorNotification = vi.fn();

vi.mock('../../hooks/useNotification', () => ({
  useNotification: () => ({
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Mock API
vi.mock('../../api', () => ({
  default: {
    get: vi.fn(),
  },
}));

// Mock fetch
global.fetch = vi.fn();

describe('BrandingSelector', () => {
  const mockBrandingOptions = [
    {
      id: 'brand1',
      name: 'Corporate Brand',
      colors: ['#0052CC', '#00B8D9', '#36B37E'],
      style: 'professional',
      tone: 'formal',
      description: 'Professional corporate branding for business content'
    },
    {
      id: 'brand2',
      name: 'Creative Brand',
      colors: ['#FF5630', '#FFAB00', '#6554C0'],
      style: 'creative',
      tone: 'casual',
      description: 'Creative and colorful branding for marketing content'
    }
  ];

  const mockProps = {
    selectedBranding: null,
    onSelectBranding: vi.fn(),
    onError: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockShowErrorNotification.mockClear();
    
    // Mock successful API response
    const mockApi = require('../../api').default;
    mockApi.get.mockResolvedValue({
      data: mockBrandingOptions
    });
  });

  test('renders branding selector correctly', async () => {
    render(
      <TestWrapper>
        <BrandingSelector {...mockProps} />
      </TestWrapper>
    );

    // Should show loading initially
    expect(screen.getByText('Loading branding options...')).toBeInTheDocument();

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Loading branding options...')).not.toBeInTheDocument();
    });

    expect(screen.getByLabelText('Brand Settings')).toBeInTheDocument();
    expect(screen.getByText('Manage brand settings')).toBeInTheDocument();
  });

  test('displays branding options in select dropdown', async () => {
    render(
      <TestWrapper>
        <BrandingSelector {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading branding options...')).not.toBeInTheDocument();
    });

    const selectElement = screen.getByLabelText('Brand Settings');
    const user = userEvent.setup();
    
    await user.click(selectElement);

    expect(screen.getByText('No branding')).toBeInTheDocument();
    expect(screen.getByText('Corporate Brand')).toBeInTheDocument();
    expect(screen.getByText('Creative Brand')).toBeInTheDocument();
  });

  test('calls onSelectBranding when option is selected', async () => {
    render(
      <TestWrapper>
        <BrandingSelector {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading branding options...')).not.toBeInTheDocument();
    });

    const selectElement = screen.getByLabelText('Brand Settings');
    const user = userEvent.setup();
    
    await user.click(selectElement);
    await user.click(screen.getByText('Corporate Brand'));

    expect(mockProps.onSelectBranding).toHaveBeenCalledWith(mockBrandingOptions[0]);
  });

  test('calls onSelectBranding with null when "No branding" is selected', async () => {
    render(
      <TestWrapper>
        <BrandingSelector {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading branding options...')).not.toBeInTheDocument();
    });

    const selectElement = screen.getByLabelText('Brand Settings');
    const user = userEvent.setup();
    
    await user.click(selectElement);
    await user.click(screen.getByText('No branding'));

    expect(mockProps.onSelectBranding).toHaveBeenCalledWith(null);
  });

  test('shows branding details when option is selected', async () => {
    const selectedBranding = mockBrandingOptions[0];
    
    render(
      <TestWrapper>
        <BrandingSelector 
          {...mockProps} 
          selectedBranding={selectedBranding}
        />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading branding options...')).not.toBeInTheDocument();
    });

    expect(screen.getByText('Show details')).toBeInTheDocument();
  });

  test('toggles details visibility when button is clicked', async () => {
    const selectedBranding = mockBrandingOptions[0];
    
    render(
      <TestWrapper>
        <BrandingSelector 
          {...mockProps} 
          selectedBranding={selectedBranding}
        />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading branding options...')).not.toBeInTheDocument();
    });

    const user = userEvent.setup();
    const detailsButton = screen.getByText('Show details');
    
    await user.click(detailsButton);

    expect(screen.getByText('Hide details')).toBeInTheDocument();
    expect(screen.getByText(selectedBranding.description)).toBeInTheDocument();
    expect(screen.getByText(`Style: ${selectedBranding.style}`)).toBeInTheDocument();
    expect(screen.getByText(`Tone: ${selectedBranding.tone}`)).toBeInTheDocument();
  });

  test('displays color swatches when branding has colors', async () => {
    const selectedBranding = mockBrandingOptions[0];
    
    render(
      <TestWrapper>
        <BrandingSelector 
          {...mockProps} 
          selectedBranding={selectedBranding}
        />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading branding options...')).not.toBeInTheDocument();
    });

    const user = userEvent.setup();
    const detailsButton = screen.getByText('Show details');
    
    await user.click(detailsButton);

    // Should show color swatches (3 colors for Corporate Brand)
    const colorSwatches = screen.getAllByRole('generic').filter(el => 
      el.style.backgroundColor && el.style.borderRadius === '50%'
    );
    expect(colorSwatches.length).toBeGreaterThan(0);
  });

  test('handles API error gracefully', async () => {
    const mockApi = require('../../api').default;
    mockApi.get.mockRejectedValue(new Error('API Error'));

    render(
      <TestWrapper>
        <BrandingSelector {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading branding options...')).not.toBeInTheDocument();
    });

    expect(mockShowErrorNotification).toHaveBeenCalledWith('Failed to load branding options');
    expect(mockProps.onError).toHaveBeenCalledWith(expect.any(Error));

    // Should still show the component with mock data
    expect(screen.getByLabelText('Brand Settings')).toBeInTheDocument();
  });

  test('disables interactions when disabled prop is true', async () => {
    render(
      <TestWrapper>
        <BrandingSelector {...mockProps} disabled={true} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading branding options...')).not.toBeInTheDocument();
    });

    const selectElement = screen.getByLabelText('Brand Settings');
    const manageButton = screen.getByText('Manage brand settings');

    expect(selectElement).toBeDisabled();
    expect(manageButton).toBeDisabled();
  });

  test('hides manage button when showManageButton is false', async () => {
    render(
      <TestWrapper>
        <BrandingSelector {...mockProps} showManageButton={false} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading branding options...')).not.toBeInTheDocument();
    });

    expect(screen.queryByText('Manage brand settings')).not.toBeInTheDocument();
  });

  test('uses correct size prop for form control', async () => {
    render(
      <TestWrapper>
        <BrandingSelector {...mockProps} size="medium" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading branding options...')).not.toBeInTheDocument();
    });

    const formControl = screen.getByLabelText('Brand Settings').closest('.MuiFormControl-root');
    expect(formControl).toHaveClass('MuiFormControl-sizeMedium');
  });

  test('passes through additional props', async () => {
    render(
      <TestWrapper>
        <BrandingSelector 
          {...mockProps} 
          data-testid="test-branding-selector"
          className="custom-class"
        />
      </TestWrapper>
    );

    const component = screen.getByTestId('test-branding-selector');
    expect(component).toHaveClass('custom-class');
  });

  test('does not call onSelectBranding when disabled', async () => {
    render(
      <TestWrapper>
        <BrandingSelector {...mockProps} disabled={true} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading branding options...')).not.toBeInTheDocument();
    });

    // The select should be disabled, so no interaction should occur
    expect(mockProps.onSelectBranding).not.toHaveBeenCalled();
  });

  test('shows correct selected value', async () => {
    const selectedBranding = mockBrandingOptions[1];
    
    render(
      <TestWrapper>
        <BrandingSelector 
          {...mockProps} 
          selectedBranding={selectedBranding}
        />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading branding options...')).not.toBeInTheDocument();
    });

    const selectElement = screen.getByDisplayValue('Creative Brand');
    expect(selectElement).toBeInTheDocument();
  });

  test('handles branding without colors gracefully', async () => {
    const brandingWithoutColors = {
      id: 'brand3',
      name: 'Simple Brand',
      style: 'minimal',
      tone: 'neutral',
      description: 'Simple branding without colors'
    };
    
    render(
      <TestWrapper>
        <BrandingSelector 
          {...mockProps} 
          selectedBranding={brandingWithoutColors}
        />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading branding options...')).not.toBeInTheDocument();
    });

    const user = userEvent.setup();
    const detailsButton = screen.getByText('Show details');
    
    await user.click(detailsButton);

    expect(screen.getByText(brandingWithoutColors.description)).toBeInTheDocument();
    expect(screen.getByText(`Style: ${brandingWithoutColors.style}`)).toBeInTheDocument();
    expect(screen.getByText(`Tone: ${brandingWithoutColors.tone}`)).toBeInTheDocument();
  });

  test('handles empty branding options array', async () => {
    const mockApi = require('../../api').default;
    mockApi.get.mockResolvedValue({ data: [] });

    render(
      <TestWrapper>
        <BrandingSelector {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading branding options...')).not.toBeInTheDocument();
    });

    const selectElement = screen.getByLabelText('Brand Settings');
    const user = userEvent.setup();
    
    await user.click(selectElement);

    // Should only show "No branding" option
    expect(screen.getByText('No branding')).toBeInTheDocument();
  });
});
