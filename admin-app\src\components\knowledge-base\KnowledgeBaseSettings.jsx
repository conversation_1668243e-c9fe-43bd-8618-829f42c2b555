// @since 2024-1-1 to 2025-25-7
import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Button,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';

const KnowledgeBaseSettings = ({ onRefresh }) => {
  const [settings, setSettings] = useState({
    site_title: 'ACE Social Knowledge Base',
    site_description: 'Help center and documentation for ACE Social platform',
    contact_email: '<EMAIL>',
    articles_per_page: 20,
    show_article_ratings: true,
    show_view_counts: true,
    enable_comments: true,
    require_comment_approval: true,
    enable_search_suggestions: true,
    search_results_per_page: 10,
    include_kb_links_in_emails: true,
    auto_suggest_articles: true,
    track_user_behavior: true,
    anonymize_analytics: true
  });

  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState({ text: '', type: 'success' });

  const handleInputChange = (field, value) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setMessage({ text: 'Settings saved successfully', type: 'success' });
      setTimeout(() => setMessage({ text: '', type: 'success' }), 3000);
    } catch (error) {
      setMessage({ text: 'Failed to save settings', type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box>
      {message.text && (
        <Alert severity={message.type} sx={{ mb: 3 }}>
          {message.text}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* General Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <SettingsIcon />
                General Settings
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Site Title"
                    value={settings.site_title}
                    onChange={(e) => handleInputChange('site_title', e.target.value)}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Site Description"
                    multiline
                    rows={3}
                    value={settings.site_description}
                    onChange={(e) => handleInputChange('site_description', e.target.value)}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Contact Email"
                    type="email"
                    value={settings.contact_email}
                    onChange={(e) => handleInputChange('contact_email', e.target.value)}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Articles Per Page</InputLabel>
                    <Select
                      value={settings.articles_per_page}
                      label="Articles Per Page"
                      onChange={(e) => handleInputChange('articles_per_page', e.target.value)}
                    >
                      <MenuItem value={10}>10</MenuItem>
                      <MenuItem value={20}>20</MenuItem>
                      <MenuItem value={50}>50</MenuItem>
                      <MenuItem value={100}>100</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Display Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Display Settings
              </Typography>
              
              <List>
                <ListItem>
                  <ListItemText
                    primary="Show Article Ratings"
                    secondary="Display helpfulness ratings on articles"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.show_article_ratings}
                      onChange={(e) => handleInputChange('show_article_ratings', e.target.checked)}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
                
                <ListItem>
                  <ListItemText
                    primary="Show View Counts"
                    secondary="Display how many times articles have been viewed"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.show_view_counts}
                      onChange={(e) => handleInputChange('show_view_counts', e.target.checked)}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
                
                <ListItem>
                  <ListItemText
                    primary="Enable Comments"
                    secondary="Allow users to comment on articles"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.enable_comments}
                      onChange={(e) => handleInputChange('enable_comments', e.target.checked)}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
                
                <ListItem>
                  <ListItemText
                    primary="Require Comment Approval"
                    secondary="Comments must be approved before being visible"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.require_comment_approval}
                      onChange={(e) => handleInputChange('require_comment_approval', e.target.checked)}
                      disabled={!settings.enable_comments}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Search Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Search Settings
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Search Results Per Page</InputLabel>
                    <Select
                      value={settings.search_results_per_page}
                      label="Search Results Per Page"
                      onChange={(e) => handleInputChange('search_results_per_page', e.target.value)}
                    >
                      <MenuItem value={5}>5</MenuItem>
                      <MenuItem value={10}>10</MenuItem>
                      <MenuItem value={20}>20</MenuItem>
                      <MenuItem value={50}>50</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
              
              <List>
                <ListItem>
                  <ListItemText
                    primary="Enable Search Suggestions"
                    secondary="Show search suggestions as users type"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.enable_search_suggestions}
                      onChange={(e) => handleInputChange('enable_search_suggestions', e.target.checked)}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Email Integration Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Email Integration
              </Typography>
              
              <List>
                <ListItem>
                  <ListItemText
                    primary="Include KB Links in Emails"
                    secondary="Automatically include relevant knowledge base links in emails"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.include_kb_links_in_emails}
                      onChange={(e) => handleInputChange('include_kb_links_in_emails', e.target.checked)}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
                
                <ListItem>
                  <ListItemText
                    primary="Auto-Suggest Articles"
                    secondary="Automatically suggest relevant articles based on user context"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.auto_suggest_articles}
                      onChange={(e) => handleInputChange('auto_suggest_articles', e.target.checked)}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Analytics Settings */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Analytics & Privacy
              </Typography>
              
              <List>
                <ListItem>
                  <ListItemText
                    primary="Track User Behavior"
                    secondary="Collect analytics data to improve content and user experience"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.track_user_behavior}
                      onChange={(e) => handleInputChange('track_user_behavior', e.target.checked)}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
                
                <ListItem>
                  <ListItemText
                    primary="Anonymize Analytics"
                    secondary="Remove personally identifiable information from analytics data"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.anonymize_analytics}
                      onChange={(e) => handleInputChange('anonymize_analytics', e.target.checked)}
                      disabled={!settings.track_user_behavior}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Action Buttons */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={onRefresh}
            >
              Reset to Defaults
            </Button>
            <Button
              variant="contained"
              startIcon={<SaveIcon />}
              onClick={handleSave}
              disabled={loading}
            >
              {loading ? 'Saving...' : 'Save Settings'}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default KnowledgeBaseSettings;
