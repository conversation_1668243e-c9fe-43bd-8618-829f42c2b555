#!/usr/bin/env python3
"""
Quick script to fix email import issues across the codebase.
"""
import os
import re

# Files that need to be fixed
files_to_fix = [
    "backend/app/services/appsumo.py",
    "backend/app/services/collaboration.py", 
    "backend/app/services/email_verification_reminder.py",
    "backend/app/services/lemon_squeezy.py",
    "backend/app/services/message_retry.py",
    "backend/app/services/reconciliation.py",
    "backend/app/services/subscription_state_machine.py",
    "backend/app/services/team.py",
    "backend/app/services/trial.py"
]

def fix_email_imports():
    """Fix email imports in all affected files."""
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            print(f"Fixing {file_path}...")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Replace the import
            content = content.replace(
                "from app.services.email import",
                "from app.services.email_service import"
            )
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
                
            print(f"Fixed {file_path}")
        else:
            print(f"File not found: {file_path}")

if __name__ == "__main__":
    fix_email_imports()
    print("All email imports fixed!")
