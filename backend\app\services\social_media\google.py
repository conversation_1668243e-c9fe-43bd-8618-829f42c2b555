"""
Google OAuth 2.0 integration service for ACE Social platform.

This module provides Google OAuth 2.0 authentication integration following
the existing social media integration patterns with enhanced security features:
- <PERSON><PERSON><PERSON> (Proof Key for Code Exchange) implementation
- State parameter validation for CSRF protection
- Secure token storage with AES-256 encryption
- Production-ready error handling and logging

@since 2024-1-1 to 2025-25-7
"""
import base64
import hashlib
import logging
import secrets
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Optional, List, Tuple
from urllib.parse import urlencode

import httpx
from bson import ObjectId

from app.core.config import settings
from app.models.user import SocialMediaAccount
from app.services.social_media.base import BaseSocialMediaIntegration
from app.utils.circuit_breaker import circuit_breaker, CircuitOpenError
from app.services.file_upload import FileMetadata

logger = logging.getLogger(__name__)


class GoogleIntegration(BaseSocialMediaIntegration):
    """
    Google OAuth 2.0 integration service for ACE Social platform.
    
    Provides secure authentication and user profile data extraction
    following OAuth 2.0 best practices with PKCE implementation.
    """
    platform_name: str = "google"

    # Google OAuth 2.0 endpoints
    API_URL = "https://www.googleapis.com/oauth2/v2"
    OAUTH_URL = "https://accounts.google.com/o/oauth2/v2/auth"
    TOKEN_URL = "https://oauth2.googleapis.com/token"
    USERINFO_URL = "https://www.googleapis.com/oauth2/v2/userinfo"
    REVOKE_URL = "https://oauth2.googleapis.com/revoke"

    # Required OAuth 2.0 scopes for user authentication and profile access
    SCOPES = [
        "openid",
        "email", 
        "profile",
        "https://www.googleapis.com/auth/userinfo.email",
        "https://www.googleapis.com/auth/userinfo.profile"
    ]

    def __init__(self):
        """Initialize Google OAuth integration with circuit breaker protection."""
        super().__init__()
        self.client_id = getattr(settings, 'GOOGLE_CLIENT_ID', None)
        self.client_secret = getattr(settings, 'GOOGLE_CLIENT_SECRET', None)
        
        if not self.client_id or not self.client_secret:
            logger.warning("Google OAuth credentials not configured. Set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET environment variables.")

    async def get_authorization_url(self, redirect_uri: str) -> Tuple[str, str]:
        """
        Generate Google OAuth 2.0 authorization URL with PKCE for enhanced security.

        Args:
            redirect_uri: The redirect URI for the OAuth callback

        Returns:
            Tuple of (authorization_url, state_with_verifier)
            
        Raises:
            ValueError: If Google OAuth credentials are not configured
        """
        if not self.client_id:
            raise ValueError("Google OAuth client ID not configured")

        # Generate cryptographically secure state parameter to prevent CSRF attacks
        state = secrets.token_urlsafe(32)

        # Generate PKCE code challenge for enhanced security
        code_verifier = secrets.token_urlsafe(64)
        code_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(code_verifier.encode()).digest()
        ).decode().rstrip("=")

        # Store the code verifier in the state for callback processing
        state_with_verifier = f"{state}:{code_verifier}"

        # Construct OAuth 2.0 authorization URL with all required parameters
        auth_params = {
            "client_id": self.client_id,
            "redirect_uri": redirect_uri,
            "state": state,
            "scope": " ".join(self.SCOPES),
            "response_type": "code",
            "code_challenge": code_challenge,
            "code_challenge_method": "S256",
            "access_type": "offline",  # Request refresh token
            "prompt": "consent",       # Force consent screen for refresh token
            "include_granted_scopes": "true"
        }

        auth_url = f"{self.OAUTH_URL}?{urlencode(auth_params)}"

        logger.info(f"Generated Google OAuth authorization URL for redirect_uri: {redirect_uri}")
        return auth_url, state_with_verifier

    async def handle_oauth_callback(self, code: str, state: str, redirect_uri: str) -> SocialMediaAccount:
        """
        Handle Google OAuth callback and exchange authorization code for access token.

        Args:
            code: The authorization code from Google OAuth callback
            state: The state parameter containing code verifier
            redirect_uri: The redirect URI used in the authorization request

        Returns:
            SocialMediaAccount object with tokens and Google account info
            
        Raises:
            ValueError: If state parameter is invalid or token exchange fails
            httpx.HTTPError: If API requests fail
        """
        try:
            # Extract code verifier from state parameter
            if ":" not in state:
                raise ValueError("Invalid state parameter: missing code verifier")
            
            original_state, code_verifier = state.rsplit(":", 1)
            
            if not code_verifier or len(code_verifier) < 32:
                raise ValueError("Invalid code verifier in state parameter")

            # Exchange authorization code for access token using PKCE
            token_data = {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "code": code,
                "grant_type": "authorization_code",
                "redirect_uri": redirect_uri,
                "code_verifier": code_verifier
            }

            async with httpx.AsyncClient(timeout=30.0) as client:
                # Exchange code for tokens
                token_response = await client.post(
                    self.TOKEN_URL,
                    data=token_data,
                    headers={"Content-Type": "application/x-www-form-urlencoded"}
                )
                
                if token_response.status_code != 200:
                    error_detail = token_response.text
                    logger.error(f"Google token exchange failed: {error_detail}")
                    raise ValueError(f"Token exchange failed: {error_detail}")

                token_info = token_response.json()
                access_token = token_info.get("access_token")
                refresh_token = token_info.get("refresh_token")
                expires_in = token_info.get("expires_in", 3600)
                
                if not access_token:
                    raise ValueError("No access token received from Google")

                # Get user profile information
                user_response = await client.get(
                    self.USERINFO_URL,
                    headers={"Authorization": f"Bearer {access_token}"}
                )
                
                if user_response.status_code != 200:
                    logger.error(f"Failed to get Google user info: {user_response.text}")
                    raise ValueError("Failed to retrieve user profile information")

                user_info = user_response.json()

            # Calculate token expiration time
            token_expires_at = datetime.now(timezone.utc) + timedelta(seconds=expires_in)

            # Create SocialMediaAccount object with Google account data
            account = SocialMediaAccount(
                platform="google",
                account_id=user_info.get("id", ""),
                account_name=user_info.get("name", user_info.get("email", "Google User")),
                access_token=access_token,
                refresh_token=refresh_token,
                token_expires_at=token_expires_at,
                connected_at=datetime.now(timezone.utc),
                is_encrypted=False  # Will be encrypted by credential manager
            )

            logger.info(f"Successfully created Google account for user: {user_info.get('email', 'unknown')}")
            return account

        except httpx.HTTPError as e:
            logger.error(f"HTTP error during Google OAuth callback: {str(e)}")
            raise ValueError(f"Network error during authentication: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error during Google OAuth callback: {str(e)}")
            raise ValueError(f"Authentication failed: {str(e)}")

    async def refresh_access_token(self, account: SocialMediaAccount) -> SocialMediaAccount:
        """
        Refresh the Google OAuth access token using the refresh token.

        Args:
            account: The social media account with expired token

        Returns:
            Updated SocialMediaAccount with new access token

        Raises:
            ValueError: If refresh token is missing or refresh fails
        """
        if not account.refresh_token:
            raise ValueError("No refresh token available for Google account")

        try:
            refresh_data = {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "refresh_token": account.refresh_token,
                "grant_type": "refresh_token"
            }

            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    self.TOKEN_URL,
                    data=refresh_data,
                    headers={"Content-Type": "application/x-www-form-urlencoded"}
                )

                if response.status_code != 200:
                    error_detail = response.text
                    logger.error(f"Google token refresh failed: {error_detail}")
                    raise ValueError(f"Token refresh failed: {error_detail}")

                token_info = response.json()
                new_access_token = token_info.get("access_token")
                expires_in = token_info.get("expires_in", 3600)

                if not new_access_token:
                    raise ValueError("No access token received from refresh")

                # Update account with new token
                updated_account = account.model_copy()
                updated_account.access_token = new_access_token
                updated_account.token_expires_at = datetime.now(timezone.utc) + timedelta(seconds=expires_in)

                # Refresh token might be rotated
                new_refresh_token = token_info.get("refresh_token")
                if new_refresh_token:
                    updated_account.refresh_token = new_refresh_token

                logger.info(f"Successfully refreshed Google access token for account: {account.account_id}")
                return updated_account

        except httpx.HTTPError as e:
            logger.error(f"HTTP error during Google token refresh: {str(e)}")
            raise ValueError(f"Network error during token refresh: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error during Google token refresh: {str(e)}")
            raise ValueError(f"Token refresh failed: {str(e)}")

    @circuit_breaker(name="google_oauth_account_info", failure_threshold=5, recovery_timeout=300)
    async def get_account_info(self, account: SocialMediaAccount) -> Dict[str, Any]:
        """
        Get detailed information about the Google account.

        Args:
            account: The Google social media account

        Returns:
            Dictionary with Google account information
        """
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(
                    self.USERINFO_URL,
                    headers={"Authorization": f"Bearer {account.access_token}"}
                )

                if response.status_code == 401:
                    # Token might be expired, try to refresh
                    if account.refresh_token:
                        refreshed_account = await self.refresh_access_token(account)
                        return await self.get_account_info(refreshed_account)
                    else:
                        raise ValueError("Access token expired and no refresh token available")

                if response.status_code != 200:
                    logger.error(f"Failed to get Google account info: {response.text}")
                    raise ValueError(f"Failed to retrieve account information: {response.status_code}")

                user_info = response.json()

                return {
                    "id": user_info.get("id"),
                    "name": user_info.get("name"),
                    "email": user_info.get("email"),
                    "picture": user_info.get("picture"),
                    "verified_email": user_info.get("verified_email", False),
                    "locale": user_info.get("locale"),
                    "family_name": user_info.get("family_name"),
                    "given_name": user_info.get("given_name"),
                    "platform": "google"
                }

        except CircuitOpenError:
            self.handle_circuit_open("Google", "get_account_info")
            raise ValueError("Google service is temporarily unavailable")
        except httpx.HTTPError as e:
            logger.error(f"HTTP error getting Google account info: {str(e)}")
            raise ValueError(f"Network error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error getting Google account info: {str(e)}")
            raise ValueError(f"Failed to get account information: {str(e)}")

    async def revoke_access(self, account: SocialMediaAccount) -> bool:
        """
        Revoke Google OAuth access token and refresh token.

        Args:
            account: The Google social media account to revoke

        Returns:
            True if revocation was successful, False otherwise
        """
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Revoke the access token
                revoke_response = await client.post(
                    self.REVOKE_URL,
                    data={"token": account.access_token},
                    headers={"Content-Type": "application/x-www-form-urlencoded"}
                )

                # Google returns 200 for successful revocation
                success = revoke_response.status_code == 200

                if success:
                    logger.info(f"Successfully revoked Google access for account: {account.account_id}")
                else:
                    logger.warning(f"Failed to revoke Google access: {revoke_response.text}")

                return success

        except Exception as e:
            logger.error(f"Error revoking Google access: {str(e)}")
            return False

    # Note: The following methods are required by the base class but not applicable
    # for Google OAuth authentication service. Google is used for user authentication
    # and profile data, not for social media posting or analytics.

    async def get_post_analytics(self, account: SocialMediaAccount, post_id: str) -> Dict[str, Any]:
        """
        Google OAuth is for authentication only, not social media posting.

        Raises:
            NotImplementedError: Google OAuth doesn't support post analytics
        """
        raise NotImplementedError("Google OAuth integration is for authentication only, not social media posting")

    async def get_account_analytics(self, account: SocialMediaAccount, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        Google OAuth is for authentication only, not social media analytics.

        Raises:
            NotImplementedError: Google OAuth doesn't support account analytics
        """
        raise NotImplementedError("Google OAuth integration is for authentication only, not social media analytics")

    async def get_post_comments(self, account: SocialMediaAccount, post_id: str) -> List[Dict[str, Any]]:
        """
        Google OAuth is for authentication only, not social media interaction.

        Raises:
            NotImplementedError: Google OAuth doesn't support comment retrieval
        """
        raise NotImplementedError("Google OAuth integration is for authentication only, not social media interaction")

    async def get_direct_messages(self, account: SocialMediaAccount, since: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        Google OAuth is for authentication only, not messaging.

        Raises:
            NotImplementedError: Google OAuth doesn't support direct messages
        """
        raise NotImplementedError("Google OAuth integration is for authentication only, not messaging")

    async def get_conversations(self, account: SocialMediaAccount) -> List[Dict[str, Any]]:
        """
        Google OAuth is for authentication only, not messaging.

        Raises:
            NotImplementedError: Google OAuth doesn't support conversations
        """
        raise NotImplementedError("Google OAuth integration is for authentication only, not messaging")

    async def get_conversation_messages(self, account: SocialMediaAccount, conversation_id: str, since: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        Google OAuth is for authentication only, not messaging.

        Raises:
            NotImplementedError: Google OAuth doesn't support conversation messages
        """
        raise NotImplementedError("Google OAuth integration is for authentication only, not messaging")

    async def send_direct_message(self, account: SocialMediaAccount, conversation_id: str, message: str, attachments: Optional[List[FileMetadata]] = None) -> Dict[str, Any]:
        """
        Google OAuth is for authentication only, not messaging.

        Raises:
            NotImplementedError: Google OAuth doesn't support sending messages
        """
        raise NotImplementedError("Google OAuth integration is for authentication only, not messaging")

    async def mark_message_as_read(self, account: SocialMediaAccount, message_id: str) -> bool:
        """
        Google OAuth is for authentication only, not messaging.

        Raises:
            NotImplementedError: Google OAuth doesn't support message management
        """
        raise NotImplementedError("Google OAuth integration is for authentication only, not messaging")

    async def reply_to_comment(self, account: SocialMediaAccount, post_id: str, comment_id: str, reply_text: str) -> Dict[str, Any]:
        """
        Google OAuth is for authentication only, not social media interaction.

        Raises:
            NotImplementedError: Google OAuth doesn't support comment replies
        """
        raise NotImplementedError("Google OAuth integration is for authentication only, not social media interaction")
