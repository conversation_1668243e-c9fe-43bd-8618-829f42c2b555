// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import CampaignBrandingSettings from '../CampaignBrandingSettings';

// Mock the notification hook
const mockShowSuccessNotification = vi.fn();
const mockShowErrorNotification = vi.fn();

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showSuccessNotification: mockShowSuccessNotification,
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Mock the branding hook
const mockBrandingData = {
  colorSystem: {
    primary: '#4E40C5',
    secondary: '#EBAE1B',
    accent: '#FF5733',
    background: '#FFFFFF',
    text: '#15110E'
  },
  visualStyle: {
    photographyStyle: 'lifestyle',
    lighting: 'bright-airy',
    saturation: 0,
    contrast: 0,
    brightness: 0,
    warmth: 0
  },
  fonts: ['Inter', 'Roboto'],
  style: 'professional',
  logo_url: 'https://example.com/logo.png',
  logo_settings: {
    size: 30,
    position: 'bottom-right',
    opacity: 100
  },
  imageComposition: {
    layout: 'rule-of-thirds',
    aspectRatio: '16:9'
  },
  visualElements: {
    patterns: [],
    textures: []
  }
};

vi.mock('../../../hooks/useBranding', () => ({
  default: () => ({
    brandingData: mockBrandingData,
    loading: false,
  }),
}));

// Mock the branding components
vi.mock('../../branding/ColorSystem', () => ({
  default: ({ colorSystem, onChange }) => (
    <div data-testid="color-system">
      <span>Color System Component</span>
      <button onClick={() => onChange({ primary: '#FF0000' })}>
        Change Color
      </button>
    </div>
  ),
}));

vi.mock('../../branding/VisualStyle', () => ({
  default: ({ visualStyle, onChange }) => (
    <div data-testid="visual-style">
      <span>Visual Style Component</span>
      <button onClick={() => onChange({ photographyStyle: 'modern' })}>
        Change Style
      </button>
    </div>
  ),
}));

vi.mock('../../branding/Typography', () => ({
  default: ({ typography, onChange }) => (
    <div data-testid="typography">
      <span>Typography Component</span>
      <button onClick={() => onChange({ fonts: ['Arial'], style: 'modern' })}>
        Change Typography
      </button>
    </div>
  ),
}));

vi.mock('../../branding/EnhancedLogoUpload', () => ({
  default: ({ logoData, onChange }) => (
    <div data-testid="logo-upload">
      <span>Logo Upload Component</span>
      <button onClick={() => onChange({ logo_url: 'new-logo.png' })}>
        Change Logo
      </button>
    </div>
  ),
}));

vi.mock('../../branding/ImageComposition', () => ({
  default: ({ imageComposition, onChange }) => (
    <div data-testid="image-composition">
      <span>Image Composition Component</span>
      <button onClick={() => onChange({ layout: 'centered' })}>
        Change Composition
      </button>
    </div>
  ),
}));

vi.mock('../../branding/PatternLibrary', () => ({
  default: ({ visualElements, onChange }) => (
    <div data-testid="pattern-library">
      <span>Pattern Library Component</span>
      <button onClick={() => onChange({ patterns: ['new-pattern'] })}>
        Change Patterns
      </button>
    </div>
  ),
}));

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('CampaignBrandingSettings', () => {
  const mockCampaignBranding = {
    colorSystem: {
      primary: '#FF0000',
      secondary: '#00FF00'
    },
    fonts: ['Helvetica'],
    style: 'modern'
  };

  const mockProps = {
    campaignBranding: mockCampaignBranding,
    useCustomBranding: false,
    onBrandingChange: vi.fn(),
    onUseCustomBrandingChange: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders campaign branding settings correctly', () => {
    render(
      <TestWrapper>
        <CampaignBrandingSettings {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Campaign Branding Settings')).toBeInTheDocument();
    expect(screen.getByText('Use Custom Branding for Campaign')).toBeInTheDocument();
  });

  test('shows global branding alert when custom branding is disabled', () => {
    render(
      <TestWrapper>
        <CampaignBrandingSettings {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText(/This campaign will use your global branding settings/)).toBeInTheDocument();
    expect(screen.getByText(/Toggle the switch above to create campaign-specific branding/)).toBeInTheDocument();
  });

  test('shows branding tabs when custom branding is enabled', () => {
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignBrandingSettings {...customBrandingProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Colors')).toBeInTheDocument();
    expect(screen.getByText('Visual Style')).toBeInTheDocument();
    expect(screen.getByText('Typography')).toBeInTheDocument();
    expect(screen.getByText('Logo & Assets')).toBeInTheDocument();
    expect(screen.getByText('Composition')).toBeInTheDocument();
    expect(screen.getByText('Patterns')).toBeInTheDocument();
  });

  test('shows copy global branding button when custom branding is enabled', () => {
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignBrandingSettings {...customBrandingProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Copy from Global Branding')).toBeInTheDocument();
  });

  test('handles custom branding toggle', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignBrandingSettings {...mockProps} />
      </TestWrapper>
    );

    const toggle = screen.getByRole('checkbox');
    await user.click(toggle);

    expect(mockProps.onUseCustomBrandingChange).toHaveBeenCalledWith(true);
  });

  test('handles copying global branding', async () => {
    const user = userEvent.setup();
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignBrandingSettings {...customBrandingProps} />
      </TestWrapper>
    );

    const copyButton = screen.getByText('Copy from Global Branding');
    await user.click(copyButton);

    expect(mockProps.onBrandingChange).toHaveBeenCalledWith(mockBrandingData);
    expect(mockShowSuccessNotification).toHaveBeenCalledWith('Global branding copied to campaign');
  });

  test('shows color system tab by default', () => {
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignBrandingSettings {...customBrandingProps} />
      </TestWrapper>
    );

    expect(screen.getByTestId('color-system')).toBeInTheDocument();
    expect(screen.getByText('Color System Component')).toBeInTheDocument();
  });

  test('handles tab navigation', async () => {
    const user = userEvent.setup();
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignBrandingSettings {...customBrandingProps} />
      </TestWrapper>
    );

    // Click on Visual Style tab
    await user.click(screen.getByText('Visual Style'));
    expect(screen.getByTestId('visual-style')).toBeInTheDocument();

    // Click on Typography tab
    await user.click(screen.getByText('Typography'));
    expect(screen.getByTestId('typography')).toBeInTheDocument();

    // Click on Logo & Assets tab
    await user.click(screen.getByText('Logo & Assets'));
    expect(screen.getByTestId('logo-upload')).toBeInTheDocument();

    // Click on Composition tab
    await user.click(screen.getByText('Composition'));
    expect(screen.getByTestId('image-composition')).toBeInTheDocument();

    // Click on Patterns tab
    await user.click(screen.getByText('Patterns'));
    expect(screen.getByTestId('pattern-library')).toBeInTheDocument();
  });

  test('handles color system changes', async () => {
    const user = userEvent.setup();
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignBrandingSettings {...customBrandingProps} />
      </TestWrapper>
    );

    const changeColorButton = screen.getByText('Change Color');
    await user.click(changeColorButton);

    await waitFor(() => {
      expect(mockProps.onBrandingChange).toHaveBeenCalledWith(
        expect.objectContaining({
          colorSystem: { primary: '#FF0000' }
        })
      );
    });
  });

  test('handles visual style changes', async () => {
    const user = userEvent.setup();
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignBrandingSettings {...customBrandingProps} />
      </TestWrapper>
    );

    // Navigate to Visual Style tab
    await user.click(screen.getByText('Visual Style'));

    const changeStyleButton = screen.getByText('Change Style');
    await user.click(changeStyleButton);

    await waitFor(() => {
      expect(mockProps.onBrandingChange).toHaveBeenCalledWith(
        expect.objectContaining({
          visualStyle: { photographyStyle: 'modern' }
        })
      );
    });
  });

  test('handles typography changes', async () => {
    const user = userEvent.setup();
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignBrandingSettings {...customBrandingProps} />
      </TestWrapper>
    );

    // Navigate to Typography tab
    await user.click(screen.getByText('Typography'));

    const changeTypographyButton = screen.getByText('Change Typography');
    await user.click(changeTypographyButton);

    await waitFor(() => {
      expect(mockProps.onBrandingChange).toHaveBeenCalledWith(
        expect.objectContaining({
          fonts: ['Arial'],
          style: 'modern'
        })
      );
    });
  });

  test('handles logo changes', async () => {
    const user = userEvent.setup();
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignBrandingSettings {...customBrandingProps} />
      </TestWrapper>
    );

    // Navigate to Logo & Assets tab
    await user.click(screen.getByText('Logo & Assets'));

    const changeLogoButton = screen.getByText('Change Logo');
    await user.click(changeLogoButton);

    await waitFor(() => {
      expect(mockProps.onBrandingChange).toHaveBeenCalledWith(
        expect.objectContaining({
          logo_url: 'new-logo.png'
        })
      );
    });
  });

  test('handles image composition changes', async () => {
    const user = userEvent.setup();
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignBrandingSettings {...customBrandingProps} />
      </TestWrapper>
    );

    // Navigate to Composition tab
    await user.click(screen.getByText('Composition'));

    const changeCompositionButton = screen.getByText('Change Composition');
    await user.click(changeCompositionButton);

    await waitFor(() => {
      expect(mockProps.onBrandingChange).toHaveBeenCalledWith(
        expect.objectContaining({
          imageComposition: { layout: 'centered' }
        })
      );
    });
  });

  test('handles pattern changes', async () => {
    const user = userEvent.setup();
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignBrandingSettings {...customBrandingProps} />
      </TestWrapper>
    );

    // Navigate to Patterns tab
    await user.click(screen.getByText('Patterns'));

    const changePatternsButton = screen.getByText('Change Patterns');
    await user.click(changePatternsButton);

    await waitFor(() => {
      expect(mockProps.onBrandingChange).toHaveBeenCalledWith(
        expect.objectContaining({
          visualElements: { patterns: ['new-pattern'] }
        })
      );
    });
  });

  test('initializes with campaign branding when custom branding is enabled', () => {
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignBrandingSettings {...customBrandingProps} />
      </TestWrapper>
    );

    // Component should initialize with campaign branding data
    expect(screen.getByTestId('color-system')).toBeInTheDocument();
  });

  test('initializes with global branding when custom branding is disabled', () => {
    render(
      <TestWrapper>
        <CampaignBrandingSettings {...mockProps} />
      </TestWrapper>
    );

    // Should show global branding alert
    expect(screen.getByText(/This campaign will use your global branding settings/)).toBeInTheDocument();
  });

  test('renders with default props when no campaign branding provided', () => {
    const propsWithoutCampaignBranding = {
      ...mockProps,
      campaignBranding: null
    };

    render(
      <TestWrapper>
        <CampaignBrandingSettings {...propsWithoutCampaignBranding} />
      </TestWrapper>
    );

    expect(screen.getByText('Campaign Branding Settings')).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignBrandingSettings {...customBrandingProps} />
      </TestWrapper>
    );

    // Check for proper form controls
    expect(screen.getByRole('checkbox')).toBeInTheDocument();
    expect(screen.getByRole('tablist')).toBeInTheDocument();
    expect(screen.getAllByRole('tab')).toHaveLength(6);
    expect(screen.getByRole('button', { name: /copy from global branding/i })).toBeInTheDocument();
  });

  test('handles switching from custom to global branding', async () => {
    const user = userEvent.setup();
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignBrandingSettings {...customBrandingProps} />
      </TestWrapper>
    );

    // Toggle off custom branding
    const toggle = screen.getByRole('checkbox');
    await user.click(toggle);

    expect(mockProps.onUseCustomBrandingChange).toHaveBeenCalledWith(false);
  });

  test('shows tabs with proper icons', () => {
    const customBrandingProps = {
      ...mockProps,
      useCustomBranding: true
    };

    render(
      <TestWrapper>
        <CampaignBrandingSettings {...customBrandingProps} />
      </TestWrapper>
    );

    // All tabs should be present with their labels
    expect(screen.getByText('Colors')).toBeInTheDocument();
    expect(screen.getByText('Visual Style')).toBeInTheDocument();
    expect(screen.getByText('Typography')).toBeInTheDocument();
    expect(screen.getByText('Logo & Assets')).toBeInTheDocument();
    expect(screen.getByText('Composition')).toBeInTheDocument();
    expect(screen.getByText('Patterns')).toBeInTheDocument();
  });
});
