"""
Enhanced Knowledge Base Schemas for ACE Social Platform

This module provides comprehensive request/response schemas for knowledge base
management API endpoints.

@since 2024-1-1 to 2025-25-7
"""
from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, ConfigDict, validator

from app.models.knowledge_base import (
    ArticleStatus, ArticleType, KnowledgeBaseCategory, 
    DifficultyLevel, ContentFormat, SEOMetadata, MediaAttachment
)
from app.models.base import PyObjectId


# Article Management Schemas

class ArticleCreateRequest(BaseModel):
    """Request schema for creating knowledge base articles."""
    title: str = Field(..., min_length=1, max_length=200)
    content: str = Field(..., min_length=1)
    summary: Optional[str] = Field(None, max_length=500)
    content_format: ContentFormat = Field(default=ContentFormat.RICH_TEXT)
    category: KnowledgeBaseCategory = Field(default=KnowledgeBaseCategory.GENERAL)
    article_type: ArticleType = Field(default=ArticleType.HELP_ARTICLE)
    tags: List[str] = Field(default_factory=list)
    difficulty_level: DifficultyLevel = Field(default=DifficultyLevel.BEGINNER)
    status: ArticleStatus = Field(default=ArticleStatus.DRAFT)
    is_featured: bool = Field(default=False)
    is_internal: bool = Field(default=False)
    scheduled_publish_at: Optional[datetime] = None
    template_id: Optional[str] = None
    seo: Optional[SEOMetadata] = None
    search_keywords: List[str] = Field(default_factory=list)
    related_articles: List[str] = Field(default_factory=list)
    featured_image: Optional[str] = None
    email_template_variables: Dict[str, str] = Field(default_factory=dict)
    can_embed_in_emails: bool = Field(default=True)
    email_snippet: Optional[str] = None


class ArticleUpdateRequest(BaseModel):
    """Request schema for updating knowledge base articles."""
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    content: Optional[str] = Field(None, min_length=1)
    summary: Optional[str] = Field(None, max_length=500)
    content_format: Optional[ContentFormat] = None
    category: Optional[KnowledgeBaseCategory] = None
    article_type: Optional[ArticleType] = None
    tags: Optional[List[str]] = None
    difficulty_level: Optional[DifficultyLevel] = None
    status: Optional[ArticleStatus] = None
    is_featured: Optional[bool] = None
    is_internal: Optional[bool] = None
    scheduled_publish_at: Optional[datetime] = None
    seo: Optional[SEOMetadata] = None
    search_keywords: Optional[List[str]] = None
    related_articles: Optional[List[str]] = None
    featured_image: Optional[str] = None
    email_template_variables: Optional[Dict[str, str]] = None
    can_embed_in_emails: Optional[bool] = None
    email_snippet: Optional[str] = None
    change_summary: Optional[str] = Field(None, min_length=1, max_length=200)


class BulkArticleUpdateRequest(BaseModel):
    """Request schema for bulk article updates."""
    status: Optional[ArticleStatus] = None
    category: Optional[KnowledgeBaseCategory] = None
    tags: Optional[List[str]] = None
    is_featured: Optional[bool] = None
    is_internal: Optional[bool] = None
    change_summary: str = Field(..., min_length=1, max_length=200)


class ArticleListRequest(BaseModel):
    """Request schema for listing articles with filters."""
    skip: int = Field(default=0, ge=0)
    limit: int = Field(default=20, ge=1, le=100)
    category: Optional[KnowledgeBaseCategory] = None
    article_type: Optional[ArticleType] = None
    status: Optional[ArticleStatus] = None
    difficulty_level: Optional[DifficultyLevel] = None
    is_featured: Optional[bool] = None
    is_internal: Optional[bool] = None
    author_id: Optional[str] = None
    tags: Optional[List[str]] = None
    search: Optional[str] = None
    sort_by: str = Field(default="updated_at")  # title, created_at, updated_at, view_count, rating
    sort_order: str = Field(default="desc")  # asc, desc
    include_drafts: bool = Field(default=True)
    include_archived: bool = Field(default=False)


class ArticleResponse(BaseModel):
    """Response schema for knowledge base articles."""
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "id": "64f8a1b2c3d4e5f6a7b8c9d0",
                "title": "How to Connect Your Instagram Account",
                "summary": "Step-by-step guide to connect Instagram account",
                "category": "tutorials",
                "article_type": "tutorial",
                "status": "published",
                "view_count": 1250,
                "helpful_votes": 45,
                "helpfulness_score": 0.94,
                "reading_time_minutes": 5,
                "created_at": "2024-01-01T00:00:00Z"
            }
        }
    )
    
    id: str
    title: str
    content: str
    summary: Optional[str] = None
    content_format: ContentFormat
    category: KnowledgeBaseCategory
    article_type: ArticleType
    tags: List[str]
    difficulty_level: DifficultyLevel
    status: ArticleStatus
    is_featured: bool
    is_internal: bool
    version: int
    seo: Optional[SEOMetadata] = None
    search_keywords: List[str]
    related_articles: List[str]
    featured_image: Optional[str] = None
    attachments: List[MediaAttachment]
    view_count: int
    unique_view_count: int
    helpful_votes: int
    not_helpful_votes: int
    average_rating: float
    comment_count: int
    helpfulness_score: float
    reading_time_minutes: int
    author_id: str
    author_name: str
    reviewer_id: Optional[str] = None
    reviewer_name: Optional[str] = None
    email_template_variables: Dict[str, str]
    can_embed_in_emails: bool
    email_snippet: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    published_at: Optional[datetime] = None
    last_reviewed_at: Optional[datetime] = None


class ArticleListResponse(BaseModel):
    """Response schema for article lists."""
    articles: List[ArticleResponse]
    total: int
    skip: int
    limit: int
    has_more: bool


class ArticleDuplicateRequest(BaseModel):
    """Request schema for duplicating articles."""
    new_title: str = Field(..., min_length=1, max_length=200)
    new_summary: Optional[str] = Field(None, max_length=500)
    copy_content: bool = Field(default=True)
    copy_seo: bool = Field(default=False)
    copy_tags: bool = Field(default=True)
    copy_attachments: bool = Field(default=False)


# Search and Analytics Schemas

class ArticleSearchRequest(BaseModel):
    """Request schema for searching articles."""
    query: str = Field(..., min_length=1, max_length=200)
    category: Optional[KnowledgeBaseCategory] = None
    article_type: Optional[ArticleType] = None
    difficulty_level: Optional[DifficultyLevel] = None
    tags: Optional[List[str]] = None
    include_internal: bool = Field(default=False)
    limit: int = Field(default=10, ge=1, le=50)
    include_content: bool = Field(default=False)


class ArticleSearchResult(BaseModel):
    """Individual search result."""
    id: str
    title: str
    summary: Optional[str] = None
    category: KnowledgeBaseCategory
    article_type: ArticleType
    tags: List[str]
    difficulty_level: DifficultyLevel
    view_count: int
    helpfulness_score: float
    reading_time_minutes: int
    relevance_score: float
    highlighted_content: Optional[str] = None
    created_at: datetime
    updated_at: datetime


class ArticleSearchResponse(BaseModel):
    """Response schema for article search."""
    results: List[ArticleSearchResult]
    total_results: int
    query: str
    search_time_ms: float
    suggestions: List[str] = Field(default_factory=list)


class ArticleAnalyticsRequest(BaseModel):
    """Request schema for article analytics."""
    article_ids: Optional[List[str]] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    group_by: str = Field(default="day")  # day, week, month
    metrics: List[str] = Field(default=["views", "helpful_votes", "time_on_page"])


class ArticleAnalyticsResponse(BaseModel):
    """Response schema for article analytics."""
    article_id: str
    title: str
    total_views: int
    unique_views: int
    total_helpful_votes: int
    total_not_helpful_votes: int
    average_time_on_page: float
    bounce_rate: float
    conversion_rate: float
    top_search_queries: List[Dict[str, Any]]
    referrer_sources: Dict[str, int]
    daily_metrics: List[Dict[str, Any]]


# Template Management Schemas

class TemplateCreateRequest(BaseModel):
    """Request schema for creating article templates."""
    name: str = Field(..., min_length=1, max_length=100)
    description: str = Field(..., min_length=1, max_length=500)
    article_type: ArticleType
    category: KnowledgeBaseCategory
    template_content: str = Field(..., min_length=1)
    variables: List[str] = Field(default_factory=list)


class TemplateUpdateRequest(BaseModel):
    """Request schema for updating article templates."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, min_length=1, max_length=500)
    article_type: Optional[ArticleType] = None
    category: Optional[KnowledgeBaseCategory] = None
    template_content: Optional[str] = Field(None, min_length=1)
    variables: Optional[List[str]] = None
    is_active: Optional[bool] = None


class TemplateResponse(BaseModel):
    """Response schema for article templates."""
    id: str
    name: str
    description: str
    article_type: ArticleType
    category: KnowledgeBaseCategory
    template_content: str
    variables: List[str]
    is_active: bool
    usage_count: int
    created_by: str
    created_at: datetime
    updated_at: datetime


class TemplateListResponse(BaseModel):
    """Response schema for template lists."""
    templates: List[TemplateResponse]
    total: int


# Dashboard and Statistics Schemas

class KnowledgeBaseDashboardResponse(BaseModel):
    """Response schema for knowledge base dashboard."""
    total_articles: int
    published_articles: int
    draft_articles: int
    archived_articles: int
    total_views: int
    total_helpful_votes: int
    average_helpfulness_score: float
    most_viewed_articles: List[ArticleResponse]
    most_helpful_articles: List[ArticleResponse]
    recent_articles: List[ArticleResponse]
    popular_categories: List[Dict[str, Any]]
    search_analytics: Dict[str, Any]
    content_performance: Dict[str, Any]


class BulkOperationRequest(BaseModel):
    """Request schema for bulk operations on articles."""
    article_ids: List[str] = Field(..., min_length=1)
    operation: str = Field(..., pattern="^(publish|archive|delete|update_category|update_tags)$")
    parameters: Dict[str, Any] = Field(default_factory=dict)
    confirm: bool = Field(default=False)


class BulkOperationResponse(BaseModel):
    """Response schema for bulk operations."""
    operation: str
    total_items: int
    successful_items: int
    failed_items: int
    errors: List[Dict[str, str]]
    processed_ids: List[str]


# Comment Management Schemas

class CommentCreateRequest(BaseModel):
    """Request schema for creating comments."""
    content: str = Field(..., min_length=1, max_length=1000)
    is_helpful: Optional[bool] = None
    parent_comment_id: Optional[str] = None


class CommentResponse(BaseModel):
    """Response schema for comments."""
    id: str
    article_id: str
    user_name: str
    content: str
    is_helpful: Optional[bool] = None
    is_approved: bool
    parent_comment_id: Optional[str] = None
    replies: List['CommentResponse'] = Field(default_factory=list)
    created_at: datetime
    updated_at: datetime


# Email Integration Schemas

class EmailIntegrationRequest(BaseModel):
    """Request schema for email integration."""
    article_ids: List[str]
    email_template_id: str
    variable_mappings: Dict[str, str] = Field(default_factory=dict)
    include_full_content: bool = Field(default=False)
    include_snippets: bool = Field(default=True)


class EmailIntegrationResponse(BaseModel):
    """Response schema for email integration."""
    template_variables: Dict[str, str]
    article_links: Dict[str, str]
    content_snippets: Dict[str, str]
    suggested_articles: List[ArticleResponse]


# Settings and Configuration Schemas

class KnowledgeBaseSettingsRequest(BaseModel):
    """Request schema for updating knowledge base settings."""
    site_title: Optional[str] = None
    site_description: Optional[str] = None
    contact_email: Optional[str] = None
    articles_per_page: Optional[int] = Field(None, ge=1, le=100)
    show_article_ratings: Optional[bool] = None
    show_view_counts: Optional[bool] = None
    enable_comments: Optional[bool] = None
    require_comment_approval: Optional[bool] = None
    enable_search_suggestions: Optional[bool] = None
    search_results_per_page: Optional[int] = Field(None, ge=1, le=50)
    include_kb_links_in_emails: Optional[bool] = None
    auto_suggest_articles: Optional[bool] = None
    track_user_behavior: Optional[bool] = None
    anonymize_analytics: Optional[bool] = None


class KnowledgeBaseSettingsResponse(BaseModel):
    """Response schema for knowledge base settings."""
    site_title: str
    site_description: str
    contact_email: str
    articles_per_page: int
    show_article_ratings: bool
    show_view_counts: bool
    enable_comments: bool
    require_comment_approval: bool
    enable_search_suggestions: bool
    search_results_per_page: int
    include_kb_links_in_emails: bool
    auto_suggest_articles: bool
    track_user_behavior: bool
    anonymize_analytics: bool
    updated_at: datetime
    updated_by: str
