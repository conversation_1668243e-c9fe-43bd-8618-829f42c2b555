/**
 * Comprehensive tests for BrandTypography component
 * Tests functionality, accessibility, error handling, and API integration
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi, afterEach } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import BrandTypography from '../BrandTypography';
import { NotificationContext } from '../../../contexts/NotificationContext';

// Mock the font upload API
vi.mock('../../../api/fontUpload', () => ({
  validateFontFile: vi.fn(),
  uploadFontFile: vi.fn(),
  loadFontInBrowser: vi.fn(),
  isFontLoaded: vi.fn(),
  getFontMetrics: vi.fn(),
}));

const theme = createTheme();

// Mock notification context
const mockNotificationContext = {
  showSuccessNotification: vi.fn(),
  showErrorNotification: vi.fn(),
  showInfoNotification: vi.fn(),
  showWarningNotification: vi.fn(),
};

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    <NotificationContext.Provider value={mockNotificationContext}>
      {children}
    </NotificationContext.Provider>
  </ThemeProvider>
);

describe('BrandTypography', () => {
  const mockTypography = {
    fonts: ['Inter', 'Roboto'],
    style: 'professional',
    primary_font: 'Inter',
    secondary_font: 'Roboto',
    custom_fonts: [
      {
        name: 'CustomFont',
        url: 'https://example.com/font.woff2',
        file_name: 'custom.woff2',
        size: 50000,
        uploaded_at: '2024-01-01T00:00:00Z'
      }
    ]
  };

  const mockProps = {
    typography: mockTypography,
    onChange: vi.fn(),
    onError: vi.fn(),
    onFontUpload: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders typography component correctly', () => {
    render(
      <TestWrapper>
        <BrandTypography {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Typography')).toBeInTheDocument();
    expect(screen.getByText(/Choose fonts and typography styles/)).toBeInTheDocument();
    expect(screen.getByText('Brand Fonts')).toBeInTheDocument();
    expect(screen.getByText('Typography Style')).toBeInTheDocument();
    expect(screen.getByText('Typography Preview')).toBeInTheDocument();
  });

  test('displays selected fonts correctly', () => {
    render(
      <TestWrapper>
        <BrandTypography {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Primary: Inter')).toBeInTheDocument();
    expect(screen.getByText('Secondary: Roboto')).toBeInTheDocument();
  });

  test('handles font style change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandTypography {...mockProps} />
      </TestWrapper>
    );

    const boldButton = screen.getByRole('button', { name: /Select Bold typography style/i });
    await user.click(boldButton);

    expect(mockProps.onChange).toHaveBeenCalledWith({
      ...mockTypography,
      style: 'bold'
    });
  });

  test('handles adding system font', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandTypography {...mockProps} />
      </TestWrapper>
    );

    // Select a font from dropdown
    const fontSelect = screen.getByLabelText('Add System Font');
    await user.click(fontSelect);
    
    const montserratOption = screen.getByText('Montserrat');
    await user.click(montserratOption);

    // Click add button
    const addButton = screen.getByRole('button', { name: /Add/i });
    await user.click(addButton);

    expect(mockProps.onChange).toHaveBeenCalledWith({
      ...mockTypography,
      fonts: ['Inter', 'Montserrat'] // Should replace second font due to maxFonts=2
    });
  });

  test('handles font removal', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandTypography {...mockProps} />
      </TestWrapper>
    );

    // Find and click delete button for Inter font
    const deleteButtons = screen.getAllByLabelText(/Remove .* from brand/);
    await user.click(deleteButtons[0]);

    expect(mockProps.onChange).toHaveBeenCalledWith({
      ...mockTypography,
      fonts: ['Roboto'],
      custom_fonts: mockTypography.custom_fonts
    });
  });

  test('displays validation errors', async () => {
    const invalidTypography = {
      fonts: [],
      style: '',
    };

    render(
      <TestWrapper>
        <BrandTypography {...mockProps} typography={invalidTypography} />
      </TestWrapper>
    );

    // Should show error for missing fonts and style
    await waitFor(() => {
      expect(screen.getByText(/At least one font must be selected/)).toBeInTheDocument();
      expect(screen.getByText(/Typography style is required/)).toBeInTheDocument();
    });
  });

  test('handles disabled state correctly', () => {
    render(
      <TestWrapper>
        <BrandTypography {...mockProps} disabled={true} />
      </TestWrapper>
    );

    // All interactive elements should be disabled
    const addButton = screen.getByRole('button', { name: /Add/i });
    const uploadButton = screen.getByLabelText(/Upload custom font file/i);
    const styleButtons = screen.getAllByRole('button');

    expect(addButton).toBeDisabled();
    expect(uploadButton).toHaveAttribute('aria-disabled', 'true');

    // Check style buttons are disabled
    const selectButtons = styleButtons.filter(button =>
      button.getAttribute('aria-label')?.includes('Select')
    );
    selectButtons.forEach(button => {
      expect(button).toBeDisabled();
    });
  });

  test('handles read-only state correctly', () => {
    render(
      <TestWrapper>
        <BrandTypography {...mockProps} readOnly={true} />
      </TestWrapper>
    );

    // Delete buttons should not be present in read-only mode
    const deleteButtons = screen.queryAllByLabelText(/Remove .* from brand/);
    expect(deleteButtons).toHaveLength(0);
  });

  test('displays custom fonts list', () => {
    render(
      <TestWrapper>
        <BrandTypography {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Uploaded Custom Fonts')).toBeInTheDocument();
    expect(screen.getByText('CustomFont')).toBeInTheDocument();
    expect(screen.getByText(/48.8 KB • custom.woff2/)).toBeInTheDocument();
  });

  test('handles font preview dialog', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <BrandTypography {...mockProps} />
      </TestWrapper>
    );

    // Click preview button for a font
    const previewButtons = screen.getAllByLabelText(/Preview .* font/);
    await user.click(previewButtons[0]);

    // Dialog should open
    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    expect(screen.getByText('Font Preview: Inter')).toBeInTheDocument();
    expect(screen.getByText('The Quick Brown Fox')).toBeInTheDocument();

    // Close dialog
    const closeButton = screen.getByRole('button', { name: /Close/i });
    await user.click(closeButton);

    // Wait for dialog to close
    await waitFor(() => {
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });
  });

  test('shows upload progress during font upload', async () => {
    const { uploadFontFile, validateFontFile } = await import('../../../api/fontUpload');

    // Mock validation
    validateFontFile.mockResolvedValue({
      isValid: true,
      errors: [],
      warnings: [],
      metadata: {}
    });

    // Mock upload with delayed progress to ensure we can catch the loading state
    uploadFontFile.mockImplementation((file, options) => {
      return new Promise((resolve) => {
        // Simulate progress updates with delays
        setTimeout(() => {
          if (options.onProgress) options.onProgress(25);
        }, 50);

        setTimeout(() => {
          if (options.onProgress) options.onProgress(50);
        }, 100);

        setTimeout(() => {
          if (options.onProgress) options.onProgress(75);
        }, 150);

        setTimeout(() => {
          if (options.onProgress) options.onProgress(100);
          resolve({
            success: true,
            data: {
              name: 'TestFont',
              url: 'https://example.com/test.woff2'
            }
          });
        }, 200);
      });
    });

    const user = userEvent.setup();

    render(
      <TestWrapper>
        <BrandTypography {...mockProps} />
      </TestWrapper>
    );

    // Create a mock file
    const file = new File(['font content'], 'test.woff2', { type: 'font/woff2' });

    // Find file input and upload file
    const fileInput = screen.getByLabelText(/Upload custom font file/i);

    // Start the upload
    await user.upload(fileInput, file);

    // Should show loading state immediately
    await waitFor(() => {
      expect(screen.getByText('Uploading...')).toBeInTheDocument();
    }, { timeout: 1000 });

    // Should eventually complete
    await waitFor(() => {
      expect(screen.queryByText('Uploading...')).not.toBeInTheDocument();
    }, { timeout: 2000 });
  });

  test('handles upload errors gracefully', async () => {
    const { uploadFontFile, validateFontFile } = await import('../../../api/fontUpload');

    // Mock validation to pass
    validateFontFile.mockResolvedValue({
      isValid: true,
      errors: [],
      warnings: [],
      metadata: {}
    });

    uploadFontFile.mockRejectedValue(new Error('Upload failed'));

    const user = userEvent.setup();

    render(
      <TestWrapper>
        <BrandTypography {...mockProps} />
      </TestWrapper>
    );

    const file = new File(['font content'], 'test.woff2', { type: 'font/woff2' });
    const fileInput = screen.getByLabelText(/Upload custom font file/i);

    await user.upload(fileInput, file);

    await waitFor(() => {
      expect(mockNotificationContext.showErrorNotification).toHaveBeenCalledWith(
        'Font upload failed: Upload failed',
        expect.any(Object)
      );
    });
  });

  test('respects maxFonts limit', async () => {
    const user = userEvent.setup();

    // Use a typography with only one font to test the limit
    const singleFontTypography = {
      ...mockTypography,
      fonts: ['Inter']
    };

    render(
      <TestWrapper>
        <BrandTypography {...mockProps} typography={singleFontTypography} maxFonts={1} />
      </TestWrapper>
    );

    // Try to add another font when already at limit
    const fontSelect = screen.getByLabelText('Add System Font');
    await user.click(fontSelect);

    const montserratOption = screen.getByText('Montserrat');
    await user.click(montserratOption);

    const addButton = screen.getByRole('button', { name: /Add/i });
    await user.click(addButton);

    // Should replace existing font, not add new one
    expect(mockProps.onChange).toHaveBeenCalledWith({
      ...singleFontTypography,
      fonts: ['Montserrat'] // Should replace, not add
    });
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <BrandTypography {...mockProps} />
      </TestWrapper>
    );

    // Check for proper ARIA labels and roles
    expect(screen.getByLabelText('Add System Font')).toBeInTheDocument();
    expect(screen.getByLabelText(/Upload custom font file/i)).toBeInTheDocument();
    
    const styleButtons = screen.getAllByRole('button', { pressed: false });
    styleButtons.forEach(button => {
      if (button.getAttribute('aria-label')?.includes('Select')) {
        expect(button).toHaveAttribute('aria-pressed');
      }
    });
  });
});
