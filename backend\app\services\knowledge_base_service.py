"""
Enhanced Knowledge Base Service for ACE Social Platform

This service provides comprehensive knowledge base management functionality
including CRUD operations, search, analytics, and email integration.

@since 2024-1-1 to 2025-25-7
"""
import re
import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import List, Optional, Dict, Any, <PERSON><PERSON>
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase
import logging

from app.db.mongodb import get_database
from app.models.knowledge_base import (
    KnowledgeBaseArticle, ArticleRevision, ArticleTemplate,
    ArticleAnalytics, KnowledgeBaseComment, KnowledgeBaseSearch,
    KnowledgeBaseSettings, ArticleStatus, ArticleType,
    KnowledgeBaseCategory, DifficultyLevel, ContentFormat
)
from app.schemas.knowledge_base import (
    ArticleCreateRequest, ArticleUpdateRequest, ArticleListRequest,
    ArticleSearchRequest, TemplateCreateRequest, TemplateUpdateRequest,
    BulkOperationRequest, CommentCreateRequest
)
from app.models.user import utc_now, PyObjectId

logger = logging.getLogger(__name__)


class KnowledgeBaseService:
    """Enhanced service for knowledge base management."""
    
    def __init__(self):
        self.db: AsyncIOMotorDatabase | None = None
        self.initialized = False
    
    async def initialize(self):
        """Initialize the service with database connection."""
        if not self.initialized:
            self.db = await get_database()
            await self._create_indexes()
            self.initialized = True

    def _ensure_db(self) -> AsyncIOMotorDatabase:
        """Ensure database is initialized and return it."""
        if self.db is None:
            raise RuntimeError("KnowledgeBaseService not initialized. Call initialize() first.")
        return self.db
    
    async def _create_indexes(self):
        """Create database indexes for optimal performance."""
        try:
            db = self._ensure_db()
            # Articles collection indexes
            await db.knowledge_base_articles.create_index([
                ("title", "text"),
                ("content", "text"),
                ("summary", "text"),
                ("search_keywords", "text")
            ], name="article_text_search")
            
            await db.knowledge_base_articles.create_index([
                ("status", 1),
                ("category", 1),
                ("article_type", 1)
            ], name="article_filters")

            await db.knowledge_base_articles.create_index([
                ("created_at", -1)
            ], name="article_created_desc")

            await db.knowledge_base_articles.create_index([
                ("view_count", -1)
            ], name="article_popularity")

            await db.knowledge_base_articles.create_index([
                ("helpful_votes", -1)
            ], name="article_helpfulness")

            # Search tracking indexes
            await db.knowledge_base_searches.create_index([
                ("query", 1),
                ("created_at", -1)
            ], name="search_analytics")

            # Comments indexes
            await db.knowledge_base_comments.create_index([
                ("article_id", 1),
                ("created_at", -1)
            ], name="article_comments")

            # Analytics indexes
            await db.knowledge_base_analytics.create_index([
                ("article_id", 1),
                ("date", -1)
            ], name="article_analytics")
            
            logger.info("Knowledge base indexes created successfully")
            
        except Exception as e:
            logger.error(f"Error creating knowledge base indexes: {str(e)}")
    
    # Article Management Methods
    
    async def create_article(
        self, 
        request: ArticleCreateRequest, 
        author_id: str, 
        author_name: str
    ) -> KnowledgeBaseArticle:
        """Create a new knowledge base article."""
        try:
            # Generate SEO-friendly slug
            slug = self._generate_slug(request.title)
            
            # Ensure slug is unique
            slug = await self._ensure_unique_slug(slug)
            
            # Create article data
            article_data: Dict[str, Any] = {
                "title": request.title,
                "content": request.content,
                "summary": request.summary,
                "content_format": request.content_format,
                "category": request.category,
                "article_type": request.article_type,
                "tags": request.tags,
                "difficulty_level": request.difficulty_level,
                "status": request.status,
                "is_featured": request.is_featured,
                "is_internal": request.is_internal,
                "scheduled_publish_at": request.scheduled_publish_at,
                "template_id": ObjectId(request.template_id) if request.template_id else None,
                "seo": request.seo.dict() if request.seo else {},
                "search_keywords": request.search_keywords,
                "related_articles": [ObjectId(id) for id in request.related_articles],
                "featured_image": request.featured_image,
                "email_template_variables": request.email_template_variables,
                "can_embed_in_emails": request.can_embed_in_emails,
                "email_snippet": request.email_snippet,
                "author_id": ObjectId(author_id),
                "author_name": author_name,
                "version": 1,
                "revisions": [],
                "view_count": 0,
                "unique_view_count": 0,
                "helpful_votes": 0,
                "not_helpful_votes": 0,
                "average_rating": 0.0,
                "comment_count": 0,
                "attachments": [],
                "created_at": utc_now(),
                "updated_at": utc_now()
            }
            
            # Set slug in SEO metadata
            if "seo" not in article_data:
                article_data["seo"] = {}
            article_data["seo"]["slug"] = slug
            
            # Auto-publish if status is published
            if request.status == ArticleStatus.PUBLISHED:
                article_data["published_at"] = utc_now()
            
            # Insert into database
            db = self._ensure_db()
            result = await db.knowledge_base_articles.insert_one(article_data)
            article_data["_id"] = result.inserted_id

            # Update template usage count if template was used
            if request.template_id:
                await db.knowledge_base_templates.update_one(
                    {"_id": ObjectId(request.template_id)},
                    {"$inc": {"usage_count": 1}}
                )
            
            logger.info(f"Created knowledge base article: {request.title}")
            return KnowledgeBaseArticle(**article_data)
            
        except Exception as e:
            logger.error(f"Error creating knowledge base article: {str(e)}")
            raise
    
    async def get_article(self, article_id: str) -> Optional[KnowledgeBaseArticle]:
        """Get knowledge base article by ID."""
        try:
            db = self._ensure_db()
            article_data = await db.knowledge_base_articles.find_one(
                {"_id": ObjectId(article_id)}
            )
            
            if not article_data:
                return None
            
            return KnowledgeBaseArticle(**article_data)
            
        except Exception as e:
            logger.error(f"Error getting knowledge base article {article_id}: {str(e)}")
            return None
    
    async def list_articles(
        self, 
        request: ArticleListRequest
    ) -> Tuple[List[KnowledgeBaseArticle], int]:
        """List knowledge base articles with filtering and pagination."""
        try:
            # Build query filter
            query_filter = {}
            
            if request.category:
                query_filter["category"] = request.category
            
            if request.article_type:
                query_filter["article_type"] = request.article_type
            
            if request.status:
                query_filter["status"] = request.status
            elif not request.include_drafts:
                query_filter["status"] = {"$ne": ArticleStatus.DRAFT}
            
            if not request.include_archived:
                query_filter["status"] = {"$ne": ArticleStatus.ARCHIVED}
            
            if request.difficulty_level:
                query_filter["difficulty_level"] = request.difficulty_level
            
            if request.is_featured is not None:
                query_filter["is_featured"] = request.is_featured
            
            if request.is_internal is not None:
                query_filter["is_internal"] = request.is_internal
            
            if request.author_id:
                query_filter["author_id"] = ObjectId(request.author_id)
            
            if request.tags:
                query_filter["tags"] = {"$in": request.tags}
            
            if request.search:
                query_filter["$text"] = {"$search": request.search}
            
            # Build sort criteria
            sort_criteria = []
            if request.search and "$text" in query_filter:
                sort_criteria.append(("score", {"$meta": "textScore"}))
            
            sort_direction = -1 if request.sort_order == "desc" else 1
            sort_criteria.append((request.sort_by, sort_direction))
            
            # Get total count
            db = self._ensure_db()
            total = await db.knowledge_base_articles.count_documents(query_filter)

            # Get articles with pagination
            cursor = db.knowledge_base_articles.find(query_filter)
            
            if sort_criteria:
                cursor = cursor.sort(sort_criteria)
            
            cursor = cursor.skip(request.skip).limit(request.limit)
            
            articles = []
            async for article_data in cursor:
                articles.append(KnowledgeBaseArticle(**article_data))
            
            return articles, total
            
        except Exception as e:
            logger.error(f"Error listing knowledge base articles: {str(e)}")
            return [], 0
    
    async def update_article(
        self, 
        article_id: str, 
        request: ArticleUpdateRequest, 
        editor_id: str, 
        editor_name: str
    ) -> Optional[KnowledgeBaseArticle]:
        """Update knowledge base article with version control."""
        try:
            # Get current article
            current_article = await self.get_article(article_id)
            if not current_article:
                return None
            
            # Create revision record
            revision = ArticleRevision(
                title=current_article.title,
                content=current_article.content,
                summary=current_article.summary,
                author_id=PyObjectId(editor_id),
                author_name=editor_name,
                change_summary=request.change_summary or "Article updated",
                version_number=current_article.version,
                created_at=utc_now()
            )
            
            # Build update data
            update_data: Dict[str, Any] = {
                "updated_at": utc_now(),
                "version": current_article.version + 1,
                "$push": {"revisions": revision.model_dump()}
            }
            
            # Add fields that are being updated
            if request.title is not None:
                update_data["title"] = request.title
                # Update slug if title changed
                if request.title != current_article.title:
                    new_slug = await self._ensure_unique_slug(
                        self._generate_slug(request.title), 
                        exclude_id=article_id
                    )
                    update_data["seo.slug"] = new_slug
            
            if request.content is not None:
                update_data["content"] = request.content
            
            if request.summary is not None:
                update_data["summary"] = request.summary
            
            if request.content_format is not None:
                update_data["content_format"] = request.content_format
            
            if request.category is not None:
                update_data["category"] = request.category
            
            if request.article_type is not None:
                update_data["article_type"] = request.article_type
            
            if request.tags is not None:
                update_data["tags"] = request.tags
            
            if request.difficulty_level is not None:
                update_data["difficulty_level"] = request.difficulty_level
            
            if request.status is not None:
                update_data["status"] = request.status
                # Set published_at if publishing for first time
                if (request.status == ArticleStatus.PUBLISHED and 
                    current_article.status != ArticleStatus.PUBLISHED):
                    update_data["published_at"] = utc_now()
            
            if request.is_featured is not None:
                update_data["is_featured"] = request.is_featured
            
            if request.is_internal is not None:
                update_data["is_internal"] = request.is_internal
            
            if request.scheduled_publish_at is not None:
                update_data["scheduled_publish_at"] = request.scheduled_publish_at
            
            if request.seo is not None:
                # Merge SEO data
                current_seo = current_article.seo.model_dump() if current_article.seo else {}
                current_seo.update(request.seo.model_dump())
                update_data["seo"] = current_seo
            
            if request.search_keywords is not None:
                update_data["search_keywords"] = request.search_keywords
            
            if request.related_articles is not None:
                update_data["related_articles"] = [ObjectId(id) for id in request.related_articles]
            
            if request.featured_image is not None:
                update_data["featured_image"] = request.featured_image
            
            if request.email_template_variables is not None:
                update_data["email_template_variables"] = request.email_template_variables
            
            if request.can_embed_in_emails is not None:
                update_data["can_embed_in_emails"] = request.can_embed_in_emails
            
            if request.email_snippet is not None:
                update_data["email_snippet"] = request.email_snippet
            
            # Update in database
            db = self._ensure_db()
            await db.knowledge_base_articles.update_one(
                {"_id": ObjectId(article_id)},
                {"$set": update_data, "$push": {"revisions": revision.model_dump()}}
            )
            
            # Return updated article
            return await self.get_article(article_id)
            
        except Exception as e:
            logger.error(f"Error updating knowledge base article {article_id}: {str(e)}")
            raise
    
    async def delete_article(self, article_id: str) -> bool:
        """Delete knowledge base article."""
        try:
            db = self._ensure_db()
            # Check for dependencies (comments, analytics, etc.)
            comment_count = await db.knowledge_base_comments.count_documents(
                {"article_id": ObjectId(article_id)}
            )

            if comment_count > 0:
                # Archive instead of delete if there are comments
                await db.knowledge_base_articles.update_one(
                    {"_id": ObjectId(article_id)},
                    {"$set": {"status": ArticleStatus.ARCHIVED, "updated_at": utc_now()}}
                )
                logger.info(f"Archived knowledge base article {article_id} (had comments)")
                return True

            # Delete the article
            result = await db.knowledge_base_articles.delete_one(
                {"_id": ObjectId(article_id)}
            )
            
            if result.deleted_count > 0:
                # Clean up related data
                await self._cleanup_article_data(article_id)
                logger.info(f"Deleted knowledge base article: {article_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error deleting knowledge base article {article_id}: {str(e)}")
            raise
    
    # Helper Methods
    
    def _generate_slug(self, title: str) -> str:
        """Generate URL-friendly slug from title."""
        slug = re.sub(r'[^\w\s-]', '', title.lower())
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug.strip('-')
    
    async def _ensure_unique_slug(self, slug: str, exclude_id: str | None = None) -> str:
        """Ensure slug is unique by appending number if needed."""
        original_slug = slug
        counter = 1
        
        while True:
            query: Dict[str, Any] = {"seo.slug": slug}
            if exclude_id:
                query["_id"] = {"$ne": ObjectId(exclude_id)}

            db = self._ensure_db()
            existing = await db.knowledge_base_articles.find_one(query)
            if not existing:
                return slug
            
            slug = f"{original_slug}-{counter}"
            counter += 1
    
    async def _cleanup_article_data(self, article_id: str):
        """Clean up related data when article is deleted."""
        try:
            db = self._ensure_db()
            # Remove comments
            await db.knowledge_base_comments.delete_many(
                {"article_id": ObjectId(article_id)}
            )

            # Remove analytics
            await db.knowledge_base_analytics.delete_many(
                {"article_id": ObjectId(article_id)}
            )

            # Remove from related articles lists
            await db.knowledge_base_articles.update_many(
                {"related_articles": ObjectId(article_id)},
                {"$pull": {"related_articles": ObjectId(article_id)}}
            )
            
        except Exception as e:
            logger.error(f"Error cleaning up article data for {article_id}: {str(e)}")


    # Search and Analytics Methods

    async def search_articles(
        self,
        request: ArticleSearchRequest,
        user_id: str | None = None
    ) -> Tuple[List[Dict[str, Any]], int, float]:
        """Search knowledge base articles with full-text search."""
        try:
            start_time = datetime.now()

            # Build search query
            query_filter = {
                "status": ArticleStatus.PUBLISHED,
                "$text": {"$search": request.query}
            }

            if not request.include_internal:
                query_filter["is_internal"] = False

            if request.category:
                query_filter["category"] = request.category

            if request.article_type:
                query_filter["article_type"] = request.article_type

            if request.difficulty_level:
                query_filter["difficulty_level"] = request.difficulty_level

            if request.tags:
                query_filter["tags"] = {"$in": request.tags}

            # Execute search with text score
            db = self._ensure_db()
            cursor = db.knowledge_base_articles.find(
                query_filter,
                {"score": {"$meta": "textScore"}}
            ).sort([("score", {"$meta": "textScore"}), ("view_count", -1)])

            cursor = cursor.limit(request.limit)

            results = []
            async for article_data in cursor:
                # Calculate relevance score
                relevance_score = article_data.get("score", 0.0)

                # Prepare search result
                result = {
                    "id": str(article_data["_id"]),
                    "title": article_data["title"],
                    "summary": article_data.get("summary"),
                    "category": article_data["category"],
                    "article_type": article_data["article_type"],
                    "tags": article_data.get("tags", []),
                    "difficulty_level": article_data["difficulty_level"],
                    "view_count": article_data.get("view_count", 0),
                    "helpfulness_score": self._calculate_helpfulness_score(article_data),
                    "reading_time_minutes": self._calculate_reading_time(article_data["content"]),
                    "relevance_score": relevance_score,
                    "created_at": article_data["created_at"],
                    "updated_at": article_data["updated_at"]
                }

                # Add highlighted content if requested
                if request.include_content:
                    result["highlighted_content"] = self._highlight_search_terms(
                        article_data["content"], request.query
                    )

                results.append(result)

            # Track search query
            if user_id:
                await self._track_search_query(request.query, user_id, len(results))

            # Calculate search time
            search_time = (datetime.now() - start_time).total_seconds() * 1000

            return results, len(results), search_time

        except Exception as e:
            logger.error(f"Error searching knowledge base articles: {str(e)}")
            return [], 0, 0.0

    async def get_article_analytics(
        self,
        article_id: str,
        start_date: datetime | None = None,
        end_date: datetime | None = None
    ) -> Dict[str, Any]:
        """Get analytics for a specific article."""
        try:
            # Default date range (last 30 days)
            if not end_date:
                end_date = utc_now()
            if not start_date:
                start_date = end_date - timedelta(days=30)

            # Get article
            article = await self.get_article(article_id)
            if not article:
                return {}

            # Aggregate analytics data
            pipeline = [
                {
                    "$match": {
                        "article_id": ObjectId(article_id),
                        "date": {"$gte": start_date, "$lte": end_date}
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "total_views": {"$sum": "$views"},
                        "unique_views": {"$sum": "$unique_views"},
                        "total_time_on_page": {"$sum": "$time_on_page"},
                        "view_count": {"$sum": 1}
                    }
                }
            ]

            db = self._ensure_db()
            analytics_result = await db.knowledge_base_analytics.aggregate(pipeline).to_list(1)
            analytics_data = analytics_result[0] if analytics_result else {}

            # Get search queries that led to this article
            search_queries = await db.knowledge_base_searches.find(
                {
                    "clicked_article_id": ObjectId(article_id),
                    "created_at": {"$gte": start_date, "$lte": end_date}
                }
            ).limit(10).to_list(10)

            return {
                "article_id": article_id,
                "title": article.title,
                "total_views": analytics_data.get("total_views", 0),
                "unique_views": analytics_data.get("unique_views", 0),
                "total_helpful_votes": article.helpful_votes,
                "total_not_helpful_votes": article.not_helpful_votes,
                "average_time_on_page": (
                    analytics_data.get("total_time_on_page", 0) /
                    max(analytics_data.get("view_count", 1), 1)
                ),
                "helpfulness_score": article.helpfulness_score,
                "top_search_queries": [sq["query"] for sq in search_queries],
                "date_range": {
                    "start_date": start_date,
                    "end_date": end_date
                }
            }

        except Exception as e:
            logger.error(f"Error getting article analytics for {article_id}: {str(e)}")
            return {}

    async def get_dashboard_data(self) -> Dict[str, Any]:
        """Get knowledge base dashboard overview data."""
        try:
            db = self._ensure_db()
            # Get article counts by status
            status_pipeline = [
                {"$group": {"_id": "$status", "count": {"$sum": 1}}}
            ]
            status_counts = await db.knowledge_base_articles.aggregate(status_pipeline).to_list(10)
            status_dict = {item["_id"]: item["count"] for item in status_counts}

            # Get most viewed articles
            most_viewed = await db.knowledge_base_articles.find(
                {"status": ArticleStatus.PUBLISHED}
            ).sort("view_count", -1).limit(5).to_list(5)

            # Get most helpful articles
            most_helpful = await db.knowledge_base_articles.find(
                {"status": ArticleStatus.PUBLISHED, "helpful_votes": {"$gt": 0}}
            ).sort("helpful_votes", -1).limit(5).to_list(5)

            # Get recent articles
            recent_articles = await db.knowledge_base_articles.find(
                {"status": ArticleStatus.PUBLISHED}
            ).sort("created_at", -1).limit(5).to_list(5)

            # Get category distribution
            category_pipeline = [
                {"$match": {"status": ArticleStatus.PUBLISHED}},
                {"$group": {"_id": "$category", "count": {"$sum": 1}}}
            ]
            category_counts = await db.knowledge_base_articles.aggregate(category_pipeline).to_list(20)

            # Calculate total metrics
            total_articles = sum(status_dict.values())
            published_articles = status_dict.get(ArticleStatus.PUBLISHED, 0)

            # Get total views and votes
            total_views = await db.knowledge_base_articles.aggregate([
                {"$group": {"_id": None, "total_views": {"$sum": "$view_count"}}}
            ]).to_list(1)

            total_helpful_votes = await db.knowledge_base_articles.aggregate([
                {"$group": {"_id": None, "total_votes": {"$sum": "$helpful_votes"}}}
            ]).to_list(1)

            return {
                "total_articles": total_articles,
                "published_articles": published_articles,
                "draft_articles": status_dict.get(ArticleStatus.DRAFT, 0),
                "archived_articles": status_dict.get(ArticleStatus.ARCHIVED, 0),
                "total_views": total_views[0]["total_views"] if total_views else 0,
                "total_helpful_votes": total_helpful_votes[0]["total_votes"] if total_helpful_votes else 0,
                "most_viewed_articles": [self._format_article_summary(a) for a in most_viewed],
                "most_helpful_articles": [self._format_article_summary(a) for a in most_helpful],
                "recent_articles": [self._format_article_summary(a) for a in recent_articles],
                "popular_categories": [
                    {"category": item["_id"], "count": item["count"]}
                    for item in category_counts
                ]
            }

        except Exception as e:
            logger.error(f"Error getting knowledge base dashboard data: {str(e)}")
            return {}

    # Template Management Methods

    async def create_template(
        self,
        request: TemplateCreateRequest,
        created_by: str
    ) -> ArticleTemplate:
        """Create a new article template."""
        try:
            template_data: Dict[str, Any] = {
                "name": request.name,
                "description": request.description,
                "article_type": request.article_type,
                "category": request.category,
                "template_content": request.template_content,
                "variables": request.variables,
                "is_active": True,
                "usage_count": 0,
                "created_by": ObjectId(created_by),
                "created_at": utc_now(),
                "updated_at": utc_now()
            }

            db = self._ensure_db()
            result = await db.knowledge_base_templates.insert_one(template_data)
            template_data["_id"] = result.inserted_id

            logger.info(f"Created knowledge base template: {request.name}")
            return ArticleTemplate(**template_data)

        except Exception as e:
            logger.error(f"Error creating knowledge base template: {str(e)}")
            raise

    async def get_template(self, template_id: str) -> Optional[ArticleTemplate]:
        """Get article template by ID."""
        try:
            db = self._ensure_db()
            template_data = await db.knowledge_base_templates.find_one(
                {"_id": ObjectId(template_id)}
            )

            if not template_data:
                return None

            return ArticleTemplate(**template_data)

        except Exception as e:
            logger.error(f"Error getting knowledge base template {template_id}: {str(e)}")
            return None

    async def list_templates(
        self,
        filters: Optional[Dict[str, Any]] = None
    ) -> Tuple[List[ArticleTemplate], int]:
        """List article templates with filtering."""
        try:
            if filters is None:
                filters = {}

            # Build query filter
            query_filter = {}

            if filters.get('article_type'):
                query_filter["article_type"] = filters['article_type']

            if filters.get('category'):
                query_filter["category"] = filters['category']

            if filters.get('is_active') is not None:
                query_filter["is_active"] = filters['is_active']

            # Get total count
            db = self._ensure_db()
            total = await db.knowledge_base_templates.count_documents(query_filter)

            # Get templates with pagination
            skip = filters.get('skip', 0)
            limit = filters.get('limit', 50)

            cursor = db.knowledge_base_templates.find(query_filter)
            cursor = cursor.sort("updated_at", -1).skip(skip).limit(limit)

            templates = []
            async for template_data in cursor:
                templates.append(ArticleTemplate(**template_data))

            return templates, total

        except Exception as e:
            logger.error(f"Error listing knowledge base templates: {str(e)}")
            return [], 0

    async def update_template(
        self,
        template_id: str,
        request: 'TemplateUpdateRequest'
    ) -> Optional[ArticleTemplate]:
        """Update article template."""
        try:
            # Build update data
            update_data: Dict[str, Any] = {
                "updated_at": utc_now()
            }

            if request.name is not None:
                update_data["name"] = request.name

            if request.description is not None:
                update_data["description"] = request.description

            if request.article_type is not None:
                update_data["article_type"] = request.article_type

            if request.category is not None:
                update_data["category"] = request.category

            if request.template_content is not None:
                update_data["template_content"] = request.template_content

            if request.variables is not None:
                update_data["variables"] = request.variables

            if request.is_active is not None:
                update_data["is_active"] = request.is_active

            # Update in database
            db = self._ensure_db()
            result = await db.knowledge_base_templates.update_one(
                {"_id": ObjectId(template_id)},
                {"$set": update_data}
            )

            if result.modified_count > 0:
                return await self.get_template(template_id)

            return None

        except Exception as e:
            logger.error(f"Error updating knowledge base template {template_id}: {str(e)}")
            raise

    async def delete_template(self, template_id: str) -> bool:
        """Delete article template."""
        try:
            db = self._ensure_db()
            # Check if template is being used
            usage_count = await db.knowledge_base_articles.count_documents(
                {"template_id": ObjectId(template_id)}
            )

            if usage_count > 0:
                # Deactivate instead of delete if in use
                await db.knowledge_base_templates.update_one(
                    {"_id": ObjectId(template_id)},
                    {"$set": {"is_active": False, "updated_at": utc_now()}}
                )
                logger.info(f"Deactivated knowledge base template {template_id} (in use)")
                return True

            # Delete the template
            result = await db.knowledge_base_templates.delete_one(
                {"_id": ObjectId(template_id)}
            )

            if result.deleted_count > 0:
                logger.info(f"Deleted knowledge base template: {template_id}")
                return True

            return False

        except Exception as e:
            logger.error(f"Error deleting knowledge base template {template_id}: {str(e)}")
            raise

    # Helper Methods

    def _calculate_helpfulness_score(self, article_data: Dict[str, Any]) -> float:
        """Calculate helpfulness score from votes."""
        helpful = article_data.get("helpful_votes", 0)
        not_helpful = article_data.get("not_helpful_votes", 0)
        total = helpful + not_helpful
        return helpful / total if total > 0 else 0.0

    def _calculate_reading_time(self, content: str) -> int:
        """Calculate estimated reading time in minutes."""
        word_count = len(content.split())
        return max(1, round(word_count / 200))  # 200 words per minute

    def _highlight_search_terms(self, content: str, query: str) -> str:
        """Highlight search terms in content."""
        # Simple highlighting - in production, use a proper search highlighting library
        words = query.split()
        highlighted = content
        for word in words:
            highlighted = re.sub(
                f'({re.escape(word)})',
                r'<mark>\1</mark>',
                highlighted,
                flags=re.IGNORECASE
            )
        return highlighted[:500] + "..." if len(highlighted) > 500 else highlighted

    async def _track_search_query(self, query: str, user_id: str, results_count: int):
        """Track search query for analytics."""
        try:
            search_data: Dict[str, Any] = {
                "query": query,
                "user_id": ObjectId(user_id) if user_id else None,
                "results_count": results_count,
                "created_at": utc_now()
            }

            db = self._ensure_db()
            await db.knowledge_base_searches.insert_one(search_data)

        except Exception as e:
            logger.error(f"Error tracking search query: {str(e)}")

    def _format_article_summary(self, article_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format article data for dashboard summaries."""
        return {
            "id": str(article_data["_id"]),
            "title": article_data["title"],
            "category": article_data["category"],
            "view_count": article_data.get("view_count", 0),
            "helpful_votes": article_data.get("helpful_votes", 0),
            "helpfulness_score": self._calculate_helpfulness_score(article_data),
            "created_at": article_data["created_at"],
            "updated_at": article_data["updated_at"]
        }


# Global service instance
knowledge_base_service = KnowledgeBaseService()
