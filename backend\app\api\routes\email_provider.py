"""
Email Provider Configuration API Routes

This module provides REST API endpoints for managing email provider configurations
in the ACE Social admin panel.

@since 2024-1-1 to 2025-25-7
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>ear<PERSON>

from app.middleware.auth import get_current_admin_user
from app.services.advanced_rate_limiter import rate_limit
from app.models.user import User
from app.models.email_provider import EmailProviderConfig, EmailProviderType, EmailType, ProviderStatus
from app.schemas.email_provider import (
    EmailProviderCreateRequest, EmailProviderUpdateRequest,
    EmailProviderTestRequest, EmailProviderListRequest,
    EmailProviderResponse, EmailProviderListResponse,
    EmailProviderTestResponse, EmailProviderDashboardResponse,
    EmailProviderHealthResponse, EmailProviderStatsResponse
)
from app.services.email_provider_service import email_provider_service
from app.utils.response import create_response, create_error_response
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/admin/email-providers", tags=["email-providers"])
security = HTTPBearer()


@router.post("/", response_model=EmailProviderResponse)
@rate_limit(calls=10, period=60)  # 10 creates per minute
async def create_email_provider(
    request: Request,
    provider_request: EmailProviderCreateRequest,
    current_admin: User = Depends(get_current_admin_user)
):
    """Create a new email provider configuration."""
    try:
        # Ensure email provider service is initialized
        await email_provider_service.initialize()
        
        # Create the provider
        provider = await email_provider_service.create_provider(
            provider_request, 
            str(current_admin.id)
        )
        
        # Convert to response format (without sensitive data)
        response_data = provider.dict()
        response_data["id"] = response_data.pop("_id", provider.id)
        response_data["settings_summary"] = email_provider_service._get_settings_summary(
            provider.provider_type,
            provider.settings.dict() if provider.settings else {}
        )
        response_data.pop("settings", None)  # Remove sensitive settings
        
        return create_response(
            data=EmailProviderResponse(**response_data),
            message="Email provider created successfully"
        )
        
    except Exception as e:
        logger.error(f"Error creating email provider: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create email provider: {str(e)}"
        )


@router.get("/", response_model=EmailProviderListResponse)
@rate_limit(calls=100, period=60)  # 100 requests per minute
async def list_email_providers(
    request: Request,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    provider_type: Optional[str] = Query(None),
    email_type: Optional[EmailType] = Query(None),
    provider_status: Optional[ProviderStatus] = Query(None),
    is_active: Optional[bool] = Query(None),
    search: Optional[str] = Query(None),
    current_admin: User = Depends(get_current_admin_user)
):
    """List email provider configurations with filtering."""
    try:
        # Ensure email provider service is initialized
        await email_provider_service.initialize()
        
        # Create list request
        list_request = EmailProviderListRequest(
            skip=skip,
            limit=limit,
            provider_type=EmailProviderType(provider_type) if provider_type else None,
            email_type=EmailType(email_type) if email_type else None,
            status=ProviderStatus(provider_status) if provider_status else None,
            is_active=is_active,
            search=search
        )
        
        # Get providers
        providers, total = await email_provider_service.list_providers(list_request)
        
        # Convert to response format
        provider_responses = []
        for provider in providers:
            response_data = provider.dict()
            response_data["id"] = response_data.pop("_id", provider.id)
            response_data["settings_summary"] = email_provider_service._get_settings_summary(
                provider.provider_type,
                provider.settings.dict() if provider.settings else {}
            )
            response_data.pop("settings", None)  # Remove sensitive settings
            provider_responses.append(EmailProviderResponse(**response_data))
        
        return create_response(
            data=EmailProviderListResponse(
                providers=provider_responses,
                total=total,
                skip=skip,
                limit=limit
            ),
            message=f"Retrieved {len(provider_responses)} email providers"
        )
        
    except Exception as e:
        logger.error(f"Error listing email providers: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list email providers: {str(e)}"
        )


@router.get("/{provider_id}", response_model=EmailProviderResponse)
@rate_limit(calls=100, period=60)
async def get_email_provider(
    request: Request,
    provider_id: str,
    current_admin: User = Depends(get_current_admin_user)
):
    """Get email provider configuration by ID."""
    try:
        # Ensure email provider service is initialized
        await email_provider_service.initialize()
        
        # Get provider
        provider = await email_provider_service.get_provider(provider_id)
        if not provider:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Email provider not found"
            )
        
        # Convert to response format (without sensitive data)
        response_data = provider.dict()
        response_data["id"] = response_data.pop("_id", provider.id)
        response_data["settings_summary"] = email_provider_service._get_settings_summary(
            provider.provider_type,
            provider.settings.dict() if provider.settings else {}
        )
        response_data.pop("settings", None)  # Remove sensitive settings
        
        return create_response(
            data=EmailProviderResponse(**response_data),
            message="Email provider retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting email provider {provider_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get email provider: {str(e)}"
        )


@router.put("/{provider_id}", response_model=EmailProviderResponse)
@rate_limit(calls=20, period=60)  # 20 updates per minute
async def update_email_provider(
    request: Request,
    provider_id: str,
    provider_request: EmailProviderUpdateRequest,
    current_admin: User = Depends(get_current_admin_user)
):
    """Update email provider configuration."""
    try:
        # Ensure email provider service is initialized
        await email_provider_service.initialize()
        
        # Update the provider
        provider = await email_provider_service.update_provider(
            provider_id,
            provider_request,
            str(current_admin.id)
        )
        
        if not provider:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Email provider not found"
            )
        
        # Convert to response format (without sensitive data)
        response_data = provider.dict()
        response_data["id"] = response_data.pop("_id", provider.id)
        response_data["settings_summary"] = email_provider_service._get_settings_summary(
            provider.provider_type,
            provider.settings.dict() if provider.settings else {}
        )
        response_data.pop("settings", None)  # Remove sensitive settings
        
        return create_response(
            data=EmailProviderResponse(**response_data),
            message="Email provider updated successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating email provider {provider_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update email provider: {str(e)}"
        )


@router.delete("/{provider_id}")
@rate_limit(calls=10, period=60)  # 10 deletes per minute
async def delete_email_provider(
    request: Request,
    provider_id: str,
    current_admin: User = Depends(get_current_admin_user)
):
    """Delete email provider configuration."""
    try:
        # Ensure email provider service is initialized
        await email_provider_service.initialize()
        
        # Delete the provider
        success = await email_provider_service.delete_provider(provider_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Email provider not found"
            )
        
        return create_response(
            data={"deleted": True},
            message="Email provider deleted successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting email provider {provider_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete email provider: {str(e)}"
        )


@router.post("/{provider_id}/test", response_model=EmailProviderTestResponse)
@rate_limit(calls=5, period=60)  # 5 tests per minute
async def test_email_provider(
    request: Request,
    provider_id: str,
    test_request: EmailProviderTestRequest,
    current_admin: User = Depends(get_current_admin_user)
):
    """Test email provider configuration."""
    try:
        # Ensure email provider service is initialized
        await email_provider_service.initialize()
        
        # Test the provider
        test_result = await email_provider_service.test_provider(provider_id, test_request)
        
        return create_response(
            data=EmailProviderTestResponse(**test_result),
            message="Email provider test completed"
        )
        
    except Exception as e:
        logger.error(f"Error testing email provider {provider_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to test email provider: {str(e)}"
        )


@router.get("/dashboard/overview", response_model=EmailProviderDashboardResponse)
@rate_limit(calls=60, period=60)  # 60 requests per minute
async def get_email_provider_dashboard(
    request: Request,
    current_admin: User = Depends(get_current_admin_user)
):
    """Get email provider dashboard overview."""
    try:
        # Ensure email provider service is initialized
        await email_provider_service.initialize()

        # Get all providers for dashboard
        list_request = EmailProviderListRequest(skip=0, limit=100, search=None)
        providers, total = await email_provider_service.list_providers(list_request)

        # Calculate dashboard metrics
        active_providers = sum(1 for p in providers if p.is_active and p.status == ProviderStatus.ACTIVE)
        failed_providers = sum(1 for p in providers if p.status == ProviderStatus.FAILED)

        # Calculate email metrics (placeholder - would be from delivery logs)
        total_emails_sent_today = 0
        total_emails_failed_today = 0
        overall_delivery_rate = 95.0  # Placeholder
        overall_bounce_rate = 2.5     # Placeholder

        # Create provider health responses
        provider_health = []
        for provider in providers:
            health_response = EmailProviderHealthResponse(
                provider_id=provider.id or "unknown",
                provider_name=provider.name,
                status=provider.status,
                is_healthy=provider.status == ProviderStatus.ACTIVE,
                last_check=provider.last_test_date or provider.updated_at,
                response_time=None,  # Would be from last test
                error_message=None,
                uptime_percentage=98.5  # Placeholder
            )
            provider_health.append(health_response)

        dashboard_data = EmailProviderDashboardResponse(
            total_providers=total,
            active_providers=active_providers,
            failed_providers=failed_providers,
            total_emails_sent_today=total_emails_sent_today,
            total_emails_failed_today=total_emails_failed_today,
            overall_delivery_rate=overall_delivery_rate,
            overall_bounce_rate=overall_bounce_rate,
            providers=provider_health
        )

        return create_response(
            data=dashboard_data,
            message="Email provider dashboard retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Error getting email provider dashboard: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get email provider dashboard: {str(e)}"
        )


@router.get("/{provider_id}/health", response_model=EmailProviderHealthResponse)
@rate_limit(calls=100, period=60)
async def get_email_provider_health(
    request: Request,
    provider_id: str,
    current_admin: User = Depends(get_current_admin_user)
):
    """Get email provider health status."""
    try:
        # Ensure email provider service is initialized
        await email_provider_service.initialize()

        # Get provider
        provider = await email_provider_service.get_provider(provider_id)
        if not provider:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Email provider not found"
            )

        # Create health response
        health_response = EmailProviderHealthResponse(
            provider_id=provider.id or "unknown",
            provider_name=provider.name,
            status=provider.status,
            is_healthy=provider.status == ProviderStatus.ACTIVE,
            last_check=provider.last_test_date or provider.updated_at,
            response_time=None,  # Would be from last test result
            error_message=None,  # Would be from last test result
            uptime_percentage=98.5  # Placeholder - would be calculated from logs
        )

        return create_response(
            data=health_response,
            message="Email provider health retrieved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting email provider health {provider_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get email provider health: {str(e)}"
        )
