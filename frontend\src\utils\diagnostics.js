/**
 * Diagnostic Utilities for 404 Error Detection
 * Helps identify which resources are failing to load
 @since 2024-1-1 to 2025-25-7
*/

/**
 * Test API endpoint availability
 * @param {string} endpoint - API endpoint to test
 * @returns {Promise<Object>} Test result
 */
export const testEndpoint = async (endpoint) => {
  const startTime = Date.now();
  
  try {
    const response = await fetch(endpoint, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    return {
      endpoint,
      status: response.status,
      statusText: response.statusText,
      available: response.ok,
      duration,
      error: null
    };
  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    return {
      endpoint,
      status: null,
      statusText: error.message,
      available: false,
      duration,
      error: error.message
    };
  }
};

/**
 * Test multiple endpoints
 * @param {string[]} endpoints - Array of endpoints to test
 * @returns {Promise<Object[]>} Array of test results
 */
export const testMultipleEndpoints = async (endpoints) => {
  const results = await Promise.all(
    endpoints.map(endpoint => testEndpoint(endpoint))
  );
  
  return results;
};

/**
 * Test all known API endpoints
 * @returns {Promise<Object>} Comprehensive test results
 */
export const runAPIHealthCheck = async () => {
  console.log('🔍 Running API Health Check...');
  
  const endpoints = [
    '/api/health',
    '/api/errors/report',
    '/api/errors/summary',
    '/api/metrics/social-media-messaging',
    '/api/metrics/token-manager',
    '/api/metrics/websocket',
    '/api/metrics/platform',
    '/api/platform-config/status',
    '/api/platform/status',
    '/api/platform/health'
  ];
  
  const results = await testMultipleEndpoints(endpoints);
  
  // Categorize results
  const available = results.filter(r => r.available);
  const unavailable = results.filter(r => !r.available);
  const errors404 = results.filter(r => r.status === 404);
  const errors500 = results.filter(r => r.status >= 500);
  const networkErrors = results.filter(r => r.error && !r.status);
  
  const summary = {
    total: results.length,
    available: available.length,
    unavailable: unavailable.length,
    errors404: errors404.length,
    errors500: errors500.length,
    networkErrors: networkErrors.length,
    results: results
  };
  
  // Log results
  console.log('📊 API Health Check Results:');
  console.table(results);
  
  console.log('📈 Summary:');
  console.log(`✅ Available: ${available.length}/${results.length}`);
  console.log(`❌ Unavailable: ${unavailable.length}/${results.length}`);
  console.log(`🔍 404 Errors: ${errors404.length}`);
  console.log(`💥 500 Errors: ${errors500.length}`);
  console.log(`🌐 Network Errors: ${networkErrors.length}`);
  
  if (errors404.length > 0) {
    console.log('🚨 404 Errors Found:');
    errors404.forEach(result => {
      console.log(`   - ${result.endpoint} (${result.statusText})`);
    });
  }
  
  if (networkErrors.length > 0) {
    console.log('🌐 Network Errors Found:');
    networkErrors.forEach(result => {
      console.log(`   - ${result.endpoint} (${result.error})`);
    });
  }
  
  return summary;
};

/**
 * Monitor network requests for 404 errors
 * @param {number} duration - Duration to monitor in milliseconds
 * @returns {Promise<Object[]>} Array of 404 errors detected
 */
export const monitor404Errors = (duration = 30000) => {
  return new Promise((resolve) => {
    const errors404 = [];
    const originalFetch = window.fetch;
    
    // Override fetch to monitor requests
    window.fetch = async (...args) => {
      try {
        const response = await originalFetch(...args);
        
        if (response.status === 404) {
          const url = typeof args[0] === 'string' ? args[0] : args[0].url;
          errors404.push({
            url,
            status: response.status,
            statusText: response.statusText,
            timestamp: new Date().toISOString()
          });
          
          console.warn(`🚨 404 Error Detected: ${url}`);
        }
        
        return response;
      } catch (error) {
        const url = typeof args[0] === 'string' ? args[0] : args[0].url;
        errors404.push({
          url,
          status: null,
          statusText: error.message,
          timestamp: new Date().toISOString(),
          error: true
        });
        
        console.error(`🌐 Network Error Detected: ${url} - ${error.message}`);
        throw error;
      }
    };
    
    console.log(`🔍 Monitoring 404 errors for ${duration / 1000} seconds...`);
    
    setTimeout(() => {
      // Restore original fetch
      window.fetch = originalFetch;
      
      console.log(`📊 404 Monitoring Complete. Found ${errors404.length} errors:`);
      if (errors404.length > 0) {
        console.table(errors404);
      } else {
        console.log('✅ No 404 errors detected during monitoring period');
      }
      
      resolve(errors404);
    }, duration);
  });
};

/**
 * Check if backend server is running
 * @returns {Promise<Object>} Server status
 */
export const checkBackendServer = async () => {
  console.log('🔍 Checking backend server status...');
  
  const baseURL = import.meta.env.VITE_API_URL || 'http://localhost:8001';
  
  try {
    const response = await fetch(`${baseURL}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Backend server is running:', data);
      return {
        running: true,
        status: response.status,
        data: data
      };
    } else {
      console.warn('⚠️ Backend server responded with error:', response.status);
      return {
        running: false,
        status: response.status,
        error: response.statusText
      };
    }
  } catch (error) {
    console.error('❌ Backend server is not accessible:', error.message);
    return {
      running: false,
      status: null,
      error: error.message
    };
  }
};

/**
 * Run comprehensive diagnostics
 * @returns {Promise<Object>} Complete diagnostic results
 */
export const runComprehensiveDiagnostics = async () => {
  console.log('🚀 Running Comprehensive Diagnostics...');
  
  const results = {
    timestamp: new Date().toISOString(),
    backendServer: await checkBackendServer(),
    apiHealthCheck: await runAPIHealthCheck(),
    environment: {
      hostname: window.location.hostname,
      protocol: window.location.protocol,
      port: window.location.port,
      apiBaseUrl: import.meta.env.VITE_API_URL || 'http://localhost:8001'
    }
  };
  
  console.log('📋 Comprehensive Diagnostics Complete:');
  console.log(results);
  
  // Generate recommendations
  const recommendations = [];
  
  if (!results.backendServer.running) {
    recommendations.push('Start the backend server (usually: npm run dev or python main.py)');
  }
  
  if (results.apiHealthCheck.errors404 > 0) {
    recommendations.push('Check if all API endpoints are properly configured in the backend');
  }
  
  if (results.apiHealthCheck.networkErrors > 0) {
    recommendations.push('Check network connectivity and CORS configuration');
  }
  
  if (recommendations.length > 0) {
    console.log('💡 Recommendations:');
    recommendations.forEach((rec, index) => {
      console.log(`   ${index + 1}. ${rec}`);
    });
  }
  
  return results;
};

// Auto-run diagnostics in development mode if requested
if (process.env.NODE_ENV === 'development' && window.location.search.includes('run-diagnostics')) {
  console.log('🔧 Auto-running diagnostics...');
  setTimeout(() => {
    runComprehensiveDiagnostics();
  }, 2000);
}
