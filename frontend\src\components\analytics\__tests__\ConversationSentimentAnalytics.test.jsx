/**
 * Tests for ConversationSentimentAnalytics component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import ConversationSentimentAnalytics from '../ConversationSentimentAnalytics';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the hooks and API
vi.mock('../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showErrorNotification: vi.fn(),
    showSuccessNotification: vi.fn()
  }))
}));

vi.mock('../../hooks/useSubscription', () => ({
  useSubscription: vi.fn(() => ({
    canAccessSentiment: true,
    canAccessAdvanced: true
  }))
}));

vi.mock('../../api', () => ({
  default: {
    get: vi.fn()
  }
}));

// Mock Recharts components
vi.mock('recharts', () => ({
  PieChart: ({ children }) => <div data-testid="pie-chart">{children}</div>,
  Pie: ({ data }) => <div data-testid="pie">{JSON.stringify(data)}</div>,
  Cell: () => <div data-testid="cell" />,
  BarChart: ({ children }) => <div data-testid="bar-chart">{children}</div>,
  Bar: ({ data }) => <div data-testid="bar">{JSON.stringify(data)}</div>,
  LineChart: ({ children }) => <div data-testid="line-chart">{children}</div>,
  Line: ({ data }) => <div data-testid="line">{JSON.stringify(data)}</div>,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  ResponsiveContainer: ({ children }) => <div data-testid="responsive-container">{children}</div>
}));

describe('ConversationSentimentAnalytics', () => {
  const mockApi = require('../../api').default;
  const mockNotification = {
    showErrorNotification: vi.fn(),
    showSuccessNotification: vi.fn()
  };

  const mockData = {
    summary: {
      total_conversations: 150,
      average_sentiment: 0.65,
      urgent_conversations: 12,
      churn_risk_conversations: 8,
      sentiment_distribution: {
        positive: 85,
        neutral: 45,
        negative: 20
      },
      intent_distribution: {
        purchase_intent: 45,
        information_seeking: 60,
        support_issue: 25,
        engagement: 15,
        churn_risk: 5
      }
    },
    trends: [
      { date: '2023-01-01', sentiment: 0.6, conversations: 25 },
      { date: '2023-01-02', sentiment: 0.7, conversations: 30 },
      { date: '2023-01-03', sentiment: 0.65, conversations: 28 }
    ]
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockApi.get.mockResolvedValue({ data: mockData });

    const { useNotification } = require('../../hooks/useNotification');
    useNotification.mockReturnValue(mockNotification);

    const { useSubscription } = require('../../hooks/useSubscription');
    useSubscription.mockReturnValue({
      canAccessSentiment: true,
      canAccessAdvanced: true
    });
  });

  test('renders conversation sentiment analytics dashboard', async () => {
    render(
      <TestWrapper>
        <ConversationSentimentAnalytics />
      </TestWrapper>
    );

    expect(screen.getByText('Conversation Sentiment Analytics')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('150')).toBeInTheDocument(); // Total conversations
      expect(screen.getByText('65.0%')).toBeInTheDocument(); // Average sentiment
      expect(screen.getByText('12')).toBeInTheDocument(); // Urgent conversations
      expect(screen.getByText('8')).toBeInTheDocument(); // Churn risk conversations
    });
  });

  test('displays sentiment metrics correctly', async () => {
    render(
      <TestWrapper>
        <ConversationSentimentAnalytics data={mockData} />
      </TestWrapper>
    );

    expect(screen.getByText('150')).toBeInTheDocument(); // Total conversations
    expect(screen.getByText('65.0%')).toBeInTheDocument(); // Average sentiment
    expect(screen.getByText('12')).toBeInTheDocument(); // Urgent conversations
    expect(screen.getByText('8')).toBeInTheDocument(); // Churn risk conversations
  });

  test('shows loading state when loading prop is true', () => {
    render(
      <TestWrapper>
        <ConversationSentimentAnalytics loading={true} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('shows subscription upgrade message when access is denied', () => {
    const { useSubscription } = require('../../hooks/useSubscription');
    useSubscription.mockReturnValue({
      canAccessSentiment: false,
      canAccessAdvanced: false
    });

    render(
      <TestWrapper>
        <ConversationSentimentAnalytics />
      </TestWrapper>
    );

    expect(screen.getByText(/Conversation sentiment analytics is available with Creator plan/)).toBeInTheDocument();
    expect(screen.getByText('Upgrade your subscription')).toBeInTheDocument();
  });

  test('fetches data automatically on mount', async () => {
    render(
      <TestWrapper>
        <ConversationSentimentAnalytics />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/inbox/sentiment/conversations/sentiment/summary');
    });
  });

  test('handles API error gracefully', async () => {
    mockApi.get.mockRejectedValue(new Error('API Error'));

    render(
      <TestWrapper>
        <ConversationSentimentAnalytics />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('API Error')).toBeInTheDocument();
    });
  });

  test('handles refresh functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ConversationSentimentAnalytics />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledTimes(1);
    });

    // Clear previous calls
    vi.clearAllMocks();

    // Click refresh button
    const refreshButton = screen.getByLabelText('Refresh sentiment analytics data');
    await user.click(refreshButton);

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/inbox/sentiment/conversations/sentiment/summary');
      expect(mockNotification.showSuccessNotification).toHaveBeenCalledWith('Sentiment analytics updated');
    });
  });

  test('handles export functionality', async () => {
    const user = userEvent.setup();
    
    // Mock document.createElement and related methods
    const mockLink = {
      setAttribute: vi.fn(),
      click: vi.fn(),
      style: {}
    };
    const mockCreateElement = vi.spyOn(document, 'createElement').mockReturnValue(mockLink);
    const mockAppendChild = vi.spyOn(document.body, 'appendChild').mockImplementation(() => {});
    const mockRemoveChild = vi.spyOn(document.body, 'removeChild').mockImplementation(() => {});
    
    render(
      <TestWrapper>
        <ConversationSentimentAnalytics data={mockData} />
      </TestWrapper>
    );

    // Click export button
    const exportButton = screen.getByLabelText('Export sentiment analytics data');
    await user.click(exportButton);

    expect(mockCreateElement).toHaveBeenCalledWith('a');
    expect(mockLink.click).toHaveBeenCalled();
    expect(mockNotification.showSuccessNotification).toHaveBeenCalledWith(
      'Generating sentiment analytics report...'
    );

    // Cleanup mocks
    mockCreateElement.mockRestore();
    mockAppendChild.mockRestore();
    mockRemoveChild.mockRestore();
  });

  test('displays sentiment distribution chart', () => {
    render(
      <TestWrapper>
        <ConversationSentimentAnalytics data={mockData} />
      </TestWrapper>
    );

    expect(screen.getByTestId('pie-chart')).toBeInTheDocument();
    expect(screen.getByText('Sentiment Distribution')).toBeInTheDocument();
  });

  test('displays intent distribution chart', () => {
    render(
      <TestWrapper>
        <ConversationSentimentAnalytics data={mockData} />
      </TestWrapper>
    );

    expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
    expect(screen.getByText('Intent Distribution')).toBeInTheDocument();
  });

  test('displays sentiment trend chart when advanced access is available', () => {
    render(
      <TestWrapper>
        <ConversationSentimentAnalytics data={mockData} />
      </TestWrapper>
    );

    expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    expect(screen.getByText('Sentiment Trends')).toBeInTheDocument();
  });

  test('hides advanced features when access is denied', () => {
    const { useSubscription } = require('../../hooks/useSubscription');
    useSubscription.mockReturnValue({
      canAccessSentiment: true,
      canAccessAdvanced: false
    });

    render(
      <TestWrapper>
        <ConversationSentimentAnalytics data={mockData} />
      </TestWrapper>
    );

    expect(screen.queryByTestId('line-chart')).not.toBeInTheDocument();
    expect(screen.queryByText('Sentiment Trends')).not.toBeInTheDocument();
  });

  test('works with external data prop', () => {
    render(
      <TestWrapper>
        <ConversationSentimentAnalytics data={mockData} />
      </TestWrapper>
    );

    expect(screen.getByText('150')).toBeInTheDocument();
    expect(mockApi.get).not.toHaveBeenCalled(); // Should not fetch when external data provided
  });

  test('works with external loading prop', () => {
    render(
      <TestWrapper>
        <ConversationSentimentAnalytics loading={true} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('calls onRefresh prop when provided', async () => {
    const mockOnRefresh = vi.fn();
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ConversationSentimentAnalytics data={mockData} onRefresh={mockOnRefresh} />
      </TestWrapper>
    );

    const refreshButton = screen.getByLabelText('Refresh sentiment analytics data');
    await user.click(refreshButton);

    expect(mockOnRefresh).toHaveBeenCalledWith(mockData);
  });

  test('disables export when no data available', () => {
    render(
      <TestWrapper>
        <ConversationSentimentAnalytics data={null} />
      </TestWrapper>
    );

    const exportButton = screen.getByLabelText('Export sentiment analytics data');
    expect(exportButton).toBeDisabled();
  });

  test('disables refresh during loading', () => {
    render(
      <TestWrapper>
        <ConversationSentimentAnalytics loading={true} />
      </TestWrapper>
    );

    const refreshButton = screen.getByLabelText('Refresh sentiment analytics data');
    expect(refreshButton).toBeDisabled();
  });

  test('handles missing sentiment distribution gracefully', () => {
    const incompleteData = {
      summary: {
        total_conversations: 150,
        average_sentiment: 0.65,
        urgent_conversations: 12,
        churn_risk_conversations: 8
      }
    };

    render(
      <TestWrapper>
        <ConversationSentimentAnalytics data={incompleteData} />
      </TestWrapper>
    );

    expect(screen.getByText('150')).toBeInTheDocument();
    expect(screen.getByText('65.0%')).toBeInTheDocument();
  });

  test('handles missing intent distribution gracefully', () => {
    const incompleteData = {
      summary: {
        total_conversations: 150,
        average_sentiment: 0.65,
        urgent_conversations: 12,
        churn_risk_conversations: 8,
        sentiment_distribution: {
          positive: 85,
          neutral: 45,
          negative: 20
        }
      }
    };

    render(
      <TestWrapper>
        <ConversationSentimentAnalytics data={incompleteData} />
      </TestWrapper>
    );

    expect(screen.getByText('Sentiment Distribution')).toBeInTheDocument();
    expect(screen.queryByText('Intent Distribution')).not.toBeInTheDocument();
  });

  test('updates data when external data prop changes', () => {
    const { rerender } = render(
      <TestWrapper>
        <ConversationSentimentAnalytics data={mockData} />
      </TestWrapper>
    );

    expect(screen.getByText('150')).toBeInTheDocument();

    const newData = {
      ...mockData,
      summary: {
        ...mockData.summary,
        total_conversations: 200
      }
    };

    rerender(
      <TestWrapper>
        <ConversationSentimentAnalytics data={newData} />
      </TestWrapper>
    );

    expect(screen.getByText('200')).toBeInTheDocument();
  });
});
