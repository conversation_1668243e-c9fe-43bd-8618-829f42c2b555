/**
 * Tests for URLAnalytics component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import URLAnalytics from '../URLAnalytics';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the API
vi.mock('../../../api/urlAnalytics', () => ({
  getURLAnalytics: vi.fn()
}));

// Mock Nivo charts
vi.mock('@nivo/bar', () => ({
  ResponsiveBar: ({ data }) => <div data-testid="responsive-bar">{JSON.stringify(data)}</div>
}));

vi.mock('@nivo/pie', () => ({
  ResponsivePie: ({ data }) => <div data-testid="responsive-pie">{JSON.stringify(data)}</div>
}));

vi.mock('@nivo/line', () => ({
  ResponsiveLine: ({ data }) => <div data-testid="responsive-line">{JSON.stringify(data)}</div>
}));

vi.mock('@nivo/geo', () => ({
  ResponsiveChoropleth: ({ data }) => <div data-testid="responsive-choropleth">{JSON.stringify(data)}</div>
}));

// Mock world countries data
vi.mock('../../../data/world-countries.json', () => ({
  default: {
    features: [
      {
        id: 'USA',
        properties: { name: 'United States' }
      },
      {
        id: 'GBR',
        properties: { name: 'United Kingdom' }
      }
    ]
  }
}));

describe('URLAnalytics', () => {
  const mockAnalytics = {
    total_clicks: 1250,
    urls: [
      {
        original_url: 'https://example.com/article1',
        short_url: 'https://short.ly/abc123',
        platform: 'linkedin',
        click_count: 750,
        created_at: '2023-01-01T00:00:00Z',
        clicks_by_date: {
          '2023-01-01': 100,
          '2023-01-02': 150,
          '2023-01-03': 200
        },
        clicks_by_device: {
          desktop: 400,
          mobile: 300,
          tablet: 50
        },
        clicks_by_browser: {
          chrome: 500,
          firefox: 150,
          safari: 100
        },
        clicks_by_country: {
          'United States': 400,
          'United Kingdom': 200,
          'Canada': 150
        },
        clicks_by_city: {
          'New York': 200,
          'London': 150,
          'Toronto': 100
        },
        clicks_by_referrer: {
          'google.com': 300,
          'twitter.com': 200,
          'direct': 250
        }
      },
      {
        original_url: 'https://example.com/article2',
        short_url: 'https://short.ly/def456',
        platform: 'twitter',
        click_count: 500,
        created_at: '2023-01-02T00:00:00Z',
        clicks_by_date: {
          '2023-01-02': 80,
          '2023-01-03': 120,
          '2023-01-04': 150
        },
        clicks_by_device: {
          desktop: 200,
          mobile: 250,
          tablet: 50
        },
        clicks_by_browser: {
          chrome: 300,
          firefox: 100,
          safari: 100
        },
        clicks_by_country: {
          'United States': 250,
          'Germany': 150,
          'France': 100
        },
        clicks_by_city: {
          'Los Angeles': 150,
          'Berlin': 100,
          'Paris': 80
        },
        clicks_by_referrer: {
          'google.com': 200,
          'facebook.com': 150,
          'direct': 150
        }
      }
    ],
    aggregated: {
      clicks_by_device: {
        desktop: 600,
        mobile: 550,
        tablet: 100
      },
      clicks_by_country: {
        'United States': 650,
        'United Kingdom': 200,
        'Canada': 150,
        'Germany': 150,
        'France': 100
      },
      clicks_by_city: {
        'New York': 200,
        'Los Angeles': 150,
        'London': 150,
        'Toronto': 100,
        'Berlin': 100,
        'Paris': 80
      }
    }
  };

  const mockProps = {
    contentId: 'content123',
    onRefresh: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock document.createElement for export functionality
    global.document.createElement = vi.fn(() => ({
      setAttribute: vi.fn(),
      click: vi.fn(),
      style: {}
    }));

    global.document.body.appendChild = vi.fn();
    global.document.body.removeChild = vi.fn();

    // Mock clipboard API
    global.navigator.clipboard = {
      writeText: vi.fn().mockResolvedValue()
    };
  });

  test('renders URL analytics component', async () => {
    const { getURLAnalytics } = await import('../../../api/urlAnalytics');
    getURLAnalytics.mockResolvedValue(mockAnalytics);
    
    render(
      <TestWrapper>
        <URLAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('URL Analytics')).toBeInTheDocument();
    });

    expect(screen.getByText('1250')).toBeInTheDocument(); // Total clicks
    expect(screen.getByText('Tracked URLs')).toBeInTheDocument();
  });

  test('shows loading state correctly', () => {
    const { getURLAnalytics } = require('../../../api/urlAnalytics');
    getURLAnalytics.mockImplementation(() => new Promise(() => {})); // Never resolves
    
    render(
      <TestWrapper>
        <URLAnalytics {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('shows error state when API fails', async () => {
    const { getURLAnalytics } = await import('../../../api/urlAnalytics');
    getURLAnalytics.mockRejectedValue(new Error('API Error'));
    
    render(
      <TestWrapper>
        <URLAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Failed to load URL analytics. Please try again.')).toBeInTheDocument();
    });
  });

  test('shows no data state when no URLs available', async () => {
    const { getURLAnalytics } = await import('../../../api/urlAnalytics');
    getURLAnalytics.mockResolvedValue({ urls: [], total_clicks: 0 });
    
    render(
      <TestWrapper>
        <URLAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('No URL analytics available for this content.')).toBeInTheDocument();
    });
  });

  test('displays URL table with correct data', async () => {
    const { getURLAnalytics } = await import('../../../api/urlAnalytics');
    getURLAnalytics.mockResolvedValue(mockAnalytics);
    
    render(
      <TestWrapper>
        <URLAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('https://example.com/article1')).toBeInTheDocument();
      expect(screen.getByText('https://short.ly/abc123')).toBeInTheDocument();
      expect(screen.getByText('750')).toBeInTheDocument(); // Click count
    });
  });

  test('handles URL copying functionality', async () => {
    const user = userEvent.setup();
    const { getURLAnalytics } = await import('../../../api/urlAnalytics');
    getURLAnalytics.mockResolvedValue(mockAnalytics);
    
    render(
      <TestWrapper>
        <URLAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('https://short.ly/abc123')).toBeInTheDocument();
    });

    const copyButtons = screen.getAllByLabelText(/copy url/i);
    await user.click(copyButtons[0]);

    expect(global.navigator.clipboard.writeText).toHaveBeenCalledWith('https://short.ly/abc123');
  });

  test('renders charts when data is available', async () => {
    const { getURLAnalytics } = await import('../../../api/urlAnalytics');
    getURLAnalytics.mockResolvedValue(mockAnalytics);
    
    render(
      <TestWrapper>
        <URLAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByTestId('responsive-line')).toBeInTheDocument(); // Clicks over time
      expect(screen.getByTestId('responsive-pie')).toBeInTheDocument(); // Device distribution
      expect(screen.getAllByTestId('responsive-bar')).toHaveLength(2); // Browser and referrer charts
    });
  });

  test('handles geographic tab navigation', async () => {
    const user = userEvent.setup();
    const { getURLAnalytics } = await import('../../../api/urlAnalytics');
    getURLAnalytics.mockResolvedValue(mockAnalytics);
    
    render(
      <TestWrapper>
        <URLAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Countries')).toBeInTheDocument();
    });

    // Click on Cities tab
    const citiesTab = screen.getByText('Cities');
    await user.click(citiesTab);

    expect(screen.getByText('New York')).toBeInTheDocument();
    expect(screen.getByText('200')).toBeInTheDocument();

    // Click on Referrers tab
    const referrersTab = screen.getByText('Referrers');
    await user.click(referrersTab);

    expect(screen.getByTestId('responsive-bar')).toBeInTheDocument();
  });

  test('handles export functionality', async () => {
    const user = userEvent.setup();
    const { getURLAnalytics } = await import('../../../api/urlAnalytics');
    getURLAnalytics.mockResolvedValue(mockAnalytics);
    
    render(
      <TestWrapper>
        <URLAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Export URL analytics')).toBeInTheDocument();
    });

    const exportButton = screen.getByLabelText('Export URL analytics');
    await user.click(exportButton);

    // Should create download link
    expect(global.document.createElement).toHaveBeenCalledWith('a');
  });

  test('disables export when no data available', async () => {
    const { getURLAnalytics } = await import('../../../api/urlAnalytics');
    getURLAnalytics.mockResolvedValue({ urls: [], total_clicks: 0 });
    
    render(
      <TestWrapper>
        <URLAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      const exportButton = screen.getByLabelText('Export URL analytics');
      expect(exportButton).toBeDisabled();
    });
  });

  test('hides export button when showExport is false', async () => {
    const { getURLAnalytics } = await import('../../../api/urlAnalytics');
    getURLAnalytics.mockResolvedValue(mockAnalytics);
    
    render(
      <TestWrapper>
        <URLAnalytics {...mockProps} showExport={false} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByLabelText('Export URL analytics')).not.toBeInTheDocument();
    });
  });

  test('handles refresh functionality', async () => {
    const user = userEvent.setup();
    const { getURLAnalytics } = await import('../../../api/urlAnalytics');
    getURLAnalytics.mockResolvedValue(mockAnalytics);
    
    render(
      <TestWrapper>
        <URLAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Refresh URL analytics')).toBeInTheDocument();
    });

    const refreshButton = screen.getByLabelText('Refresh URL analytics');
    await user.click(refreshButton);

    expect(mockProps.onRefresh).toHaveBeenCalled();
  });

  test('displays platform chips with correct colors', async () => {
    const { getURLAnalytics } = await import('../../../api/urlAnalytics');
    getURLAnalytics.mockResolvedValue(mockAnalytics);
    
    render(
      <TestWrapper>
        <URLAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('linkedin')).toBeInTheDocument();
      expect(screen.getByText('twitter')).toBeInTheDocument();
    });
  });

  test('handles country code mapping correctly', async () => {
    const { getURLAnalytics } = await import('../../../api/urlAnalytics');
    getURLAnalytics.mockResolvedValue(mockAnalytics);
    
    render(
      <TestWrapper>
        <URLAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByTestId('responsive-choropleth')).toBeInTheDocument();
    });
  });

  test('applies correct accessibility attributes', async () => {
    const { getURLAnalytics } = await import('../../../api/urlAnalytics');
    getURLAnalytics.mockResolvedValue(mockAnalytics);
    
    render(
      <TestWrapper>
        <URLAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      // Check ARIA labels
      expect(screen.getByLabelText('Export URL analytics')).toBeInTheDocument();
      expect(screen.getByLabelText('Refresh URL analytics')).toBeInTheDocument();

      // Check tooltips
      expect(screen.getByTitle('Export URL Analytics')).toBeInTheDocument();
      expect(screen.getByTitle('Refresh Data')).toBeInTheDocument();
    });
  });

  test('handles missing contentId gracefully', () => {
    render(
      <TestWrapper>
        <URLAnalytics contentId={null} />
      </TestWrapper>
    );

    // Should not crash and should show loading state
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });
});
