// @since 2024-1-1 to 2025-25-7
import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  LinearProgress,
  Chip,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  CircularProgress,
  Avatar,
  Divider
} from '@mui/material';
import {
  Email as EmailIcon,
  Campaign as CampaignIcon,
  Trigger as TriggerIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Send as SendIcon,
  Refresh as RefreshIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Analytics as AnalyticsIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';

const EmailManagementDashboard = ({ 
  dashboard, 
  loading = false, 
  onRefresh 
}) => {
  const formatNumber = (value) => {
    return value?.toLocaleString() || '0';
  };

  const formatPercentage = (value) => {
    return `${(value || 0).toFixed(1)}%`;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
      case 'running':
        return 'success';
      case 'draft':
        return 'default';
      case 'completed':
        return 'primary';
      case 'paused':
        return 'warning';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  if (loading && !dashboard) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!dashboard) {
    return (
      <Alert severity="info">
        No dashboard data available. Please check your email management configuration.
      </Alert>
    );
  }

  return (
    <Box>
      {/* Overview Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <EmailIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" component="div">
                    Templates
                  </Typography>
                  <Typography variant="h4" component="div" color="primary">
                    {dashboard.total_templates || 0}
                  </Typography>
                </Box>
              </Box>
              <Typography variant="body2" color="text.secondary">
                {dashboard.active_templates || 0} active, {dashboard.draft_templates || 0} draft
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                  <CampaignIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" component="div">
                    Campaigns
                  </Typography>
                  <Typography variant="h4" component="div" color="secondary.main">
                    {dashboard.total_campaigns || 0}
                  </Typography>
                </Box>
              </Box>
              <Typography variant="body2" color="text.secondary">
                {dashboard.active_campaigns || 0} running, {dashboard.completed_campaigns || 0} completed
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                  <TriggerIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" component="div">
                    Triggers
                  </Typography>
                  <Typography variant="h4" component="div" color="info.main">
                    {dashboard.total_triggers || 0}
                  </Typography>
                </Box>
              </Box>
              <Typography variant="body2" color="text.secondary">
                {dashboard.active_triggers || 0} active triggers
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                  <SendIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" component="div">
                    Emails Sent
                  </Typography>
                  <Typography variant="h4" component="div" color="success.main">
                    {formatNumber(dashboard.emails_sent_today || 0)}
                  </Typography>
                </Box>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Today
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Performance Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <SuccessIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Delivery Rate
                </Typography>
              </Box>
              <Typography variant="h3" component="div" color="success.main">
                {formatPercentage(dashboard.overall_delivery_rate || 0)}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={dashboard.overall_delivery_rate || 0}
                color="success"
                sx={{ mt: 1, height: 8, borderRadius: 4 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TrendingUpIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Open Rate
                </Typography>
              </Box>
              <Typography variant="h3" component="div" color="primary.main">
                {formatPercentage(dashboard.overall_open_rate || 0)}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={dashboard.overall_open_rate || 0}
                color="primary"
                sx={{ mt: 1, height: 8, borderRadius: 4 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <AnalyticsIcon color="secondary" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Click Rate
                </Typography>
              </Box>
              <Typography variant="h3" component="div" color="secondary.main">
                {formatPercentage(dashboard.overall_click_rate || 0)}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={dashboard.overall_click_rate || 0}
                color="secondary"
                sx={{ mt: 1, height: 8, borderRadius: 4 }}
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Email Volume Trends */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6" component="div">
                  Email Volume
                </Typography>
                <Tooltip title="Refresh Data">
                  <IconButton onClick={onRefresh} size="small">
                    <RefreshIcon />
                  </IconButton>
                </Tooltip>
              </Box>
              
              <Grid container spacing={2}>
                <Grid item xs={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="primary">
                      {formatNumber(dashboard.emails_sent_today || 0)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Today
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="secondary">
                      {formatNumber(dashboard.emails_sent_this_week || 0)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      This Week
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="info">
                      {formatNumber(dashboard.emails_sent_this_month || 0)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      This Month
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" component="div" gutterBottom>
                Quick Actions
              </Typography>
              
              <Alert severity="info" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  Monitor your email performance and optimize your campaigns for better engagement.
                </Typography>
              </Alert>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Typography variant="body2">
                  • Review template performance regularly
                </Typography>
                <Typography variant="body2">
                  • Test different subject lines and content
                </Typography>
                <Typography variant="body2">
                  • Monitor delivery rates and bounce rates
                </Typography>
                <Typography variant="body2">
                  • Set up automated triggers for user events
                </Typography>
                <Typography variant="body2">
                  • Segment your audience for better targeting
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Top Performing Templates */}
      {dashboard.top_performing_templates && dashboard.top_performing_templates.length > 0 && (
        <Card>
          <CardContent>
            <Typography variant="h6" component="div" gutterBottom>
              Top Performing Templates
            </Typography>
            
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Template</TableCell>
                    <TableCell>Sends</TableCell>
                    <TableCell>Open Rate</TableCell>
                    <TableCell>Click Rate</TableCell>
                    <TableCell>Performance</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {dashboard.top_performing_templates.slice(0, 5).map((template) => (
                    <TableRow key={template.template_id} hover>
                      <TableCell>
                        <Typography variant="subtitle2" fontWeight="medium">
                          {template.template_name}
                        </Typography>
                      </TableCell>
                      
                      <TableCell>
                        <Typography variant="body2">
                          {formatNumber(template.total_sends)}
                        </Typography>
                      </TableCell>
                      
                      <TableCell>
                        <Typography variant="body2">
                          {formatPercentage(template.open_rate)}
                        </Typography>
                      </TableCell>
                      
                      <TableCell>
                        <Typography variant="body2">
                          {formatPercentage(template.click_rate)}
                        </Typography>
                      </TableCell>
                      
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <LinearProgress
                            variant="determinate"
                            value={template.open_rate || 0}
                            sx={{ width: 60, height: 4 }}
                          />
                          <Chip
                            label={template.open_rate > 25 ? 'Good' : 'Needs Improvement'}
                            color={template.open_rate > 25 ? 'success' : 'warning'}
                            size="small"
                          />
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default EmailManagementDashboard;
