// @since 2024-1-1 to 2025-25-7
import { useState, useCallback } from 'react';
import { knowledgeBaseService } from '../services/knowledgeBaseService';

/**
 * Custom hook for managing knowledge base data and operations
 * Provides state management and API integration for comprehensive knowledge base management
 */
export const useKnowledgeBaseData = () => {
  const [articles, setArticles] = useState([]);
  const [templates, setTemplates] = useState([]);
  const [dashboard, setDashboard] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Network status monitoring
  const handleOnline = useCallback(() => setIsOnline(true), []);
  const handleOffline = useCallback(() => setIsOnline(false), []);

  // Add event listeners for network status
  if (typeof window !== 'undefined') {
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
  }

  /**
   * Fetch all knowledge base articles with optional filtering
   */
  const fetchArticles = useCallback(async (filters = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await knowledgeBaseService.getArticles(filters);
      setArticles(response.data.articles || []);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to fetch knowledge base articles';
      setError(errorMessage);
      console.error('Error fetching knowledge base articles:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Fetch all article templates
   */
  const fetchTemplates = useCallback(async (filters = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await knowledgeBaseService.getTemplates(filters);
      setTemplates(response.data.templates || []);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to fetch article templates';
      setError(errorMessage);
      console.error('Error fetching article templates:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Fetch dashboard overview data
   */
  const fetchDashboard = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await knowledgeBaseService.getDashboard();
      setDashboard(response.data);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to fetch dashboard data';
      setError(errorMessage);
      console.error('Error fetching dashboard data:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Get a specific knowledge base article by ID
   */
  const getArticle = useCallback(async (articleId) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await knowledgeBaseService.getArticle(articleId);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to fetch knowledge base article';
      setError(errorMessage);
      console.error('Error fetching knowledge base article:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Create a new knowledge base article
   */
  const createArticle = useCallback(async (articleData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await knowledgeBaseService.createArticle(articleData);
      
      // Update local state
      setArticles(prev => [response.data, ...prev]);
      
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to create knowledge base article';
      setError(errorMessage);
      console.error('Error creating knowledge base article:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Update an existing knowledge base article
   */
  const updateArticle = useCallback(async (articleId, articleData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await knowledgeBaseService.updateArticle(articleId, articleData);
      
      // Update local state
      setArticles(prev => 
        prev.map(article => 
          article.id === articleId ? response.data : article
        )
      );
      
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to update knowledge base article';
      setError(errorMessage);
      console.error('Error updating knowledge base article:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Delete a knowledge base article
   */
  const deleteArticle = useCallback(async (articleId) => {
    setLoading(true);
    setError(null);
    
    try {
      await knowledgeBaseService.deleteArticle(articleId);
      
      // Update local state
      setArticles(prev => prev.filter(article => article.id !== articleId));
      
      return true;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to delete knowledge base article';
      setError(errorMessage);
      console.error('Error deleting knowledge base article:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Duplicate a knowledge base article
   */
  const duplicateArticle = useCallback(async (articleId, duplicateData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await knowledgeBaseService.duplicateArticle(articleId, duplicateData);
      
      // Update local state
      setArticles(prev => [response.data, ...prev]);
      
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to duplicate knowledge base article';
      setError(errorMessage);
      console.error('Error duplicating knowledge base article:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Search knowledge base articles
   */
  const searchArticles = useCallback(async (searchQuery, filters = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await knowledgeBaseService.searchArticles(searchQuery, filters);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to search knowledge base articles';
      setError(errorMessage);
      console.error('Error searching knowledge base articles:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Get article analytics
   */
  const getArticleAnalytics = useCallback(async (articleId, dateRange = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await knowledgeBaseService.getArticleAnalytics(articleId, dateRange);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to fetch article analytics';
      setError(errorMessage);
      console.error('Error fetching article analytics:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Create a new article template
   */
  const createTemplate = useCallback(async (templateData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await knowledgeBaseService.createTemplate(templateData);
      
      // Update local state
      setTemplates(prev => [response.data, ...prev]);
      
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to create article template';
      setError(errorMessage);
      console.error('Error creating article template:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Update an existing article template
   */
  const updateTemplate = useCallback(async (templateId, templateData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await knowledgeBaseService.updateTemplate(templateId, templateData);
      
      // Update local state
      setTemplates(prev => 
        prev.map(template => 
          template.id === templateId ? response.data : template
        )
      );
      
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to update article template';
      setError(errorMessage);
      console.error('Error updating article template:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Delete an article template
   */
  const deleteTemplate = useCallback(async (templateId) => {
    setLoading(true);
    setError(null);
    
    try {
      await knowledgeBaseService.deleteTemplate(templateId);
      
      // Update local state
      setTemplates(prev => prev.filter(template => template.id !== templateId));
      
      return true;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to delete article template';
      setError(errorMessage);
      console.error('Error deleting article template:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Perform bulk operations on articles
   */
  const bulkOperation = useCallback(async (operation, itemIds, parameters = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await knowledgeBaseService.bulkOperation(operation, itemIds, parameters);
      
      // Refresh articles after bulk operation
      await fetchArticles();
      
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to perform bulk operation';
      setError(errorMessage);
      console.error('Error performing bulk operation:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [fetchArticles]);

  /**
   * Refresh all data
   */
  const refreshData = useCallback(async () => {
    try {
      await Promise.all([
        fetchArticles(),
        fetchTemplates(),
        fetchDashboard()
      ]);
    } catch (err) {
      console.error('Error refreshing data:', err);
      throw err;
    }
  }, [fetchArticles, fetchTemplates, fetchDashboard]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Reset all state
   */
  const reset = useCallback(() => {
    setArticles([]);
    setTemplates([]);
    setDashboard(null);
    setLoading(false);
    setError(null);
  }, []);

  return {
    // State
    articles,
    templates,
    dashboard,
    loading,
    error,
    isOnline,
    
    // Article Actions
    fetchArticles,
    getArticle,
    createArticle,
    updateArticle,
    deleteArticle,
    duplicateArticle,
    searchArticles,
    getArticleAnalytics,
    
    // Template Actions
    fetchTemplates,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    
    // Dashboard Actions
    fetchDashboard,
    
    // Bulk Operations
    bulkOperation,
    
    // Utility Actions
    refreshData,
    clearError,
    reset,
    
    // Computed values
    totalArticles: articles.length,
    publishedArticles: articles.filter(a => a.status === 'published').length,
    draftArticles: articles.filter(a => a.status === 'draft').length,
    archivedArticles: articles.filter(a => a.status === 'archived').length,
    totalTemplates: templates.length,
    activeTemplates: templates.filter(t => t.is_active).length,
    hasArticles: articles.length > 0,
    hasTemplates: templates.length > 0,
    hasError: !!error
  };
};
