"""
Enhanced Email Template Schemas

This module defines comprehensive Pydantic schemas for email template management API.

@since 2024-1-1 to 2025-25-7
"""
from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field, validator
from app.models.email_template import (
    TemplateCategory, TemplateStatus, CampaignStatus, TriggerType,
    TemplateVariable, CampaignAudience, CampaignSchedule, ABTestConfig
)


# Template Request Schemas

class TemplateCreateRequest(BaseModel):
    """Schema for creating a new email template."""
    name: str = Field(..., min_length=1, max_length=200)
    description: str = Field(default="", max_length=1000)
    category: TemplateCategory
    subject: str = Field(..., min_length=1, max_length=200)
    html_content: str = Field(..., min_length=1)
    text_content: Optional[str] = None
    variables: List[TemplateVariable] = Field(default_factory=list)
    tags: List[str] = Field(default_factory=list)
    language: str = Field(default="en")
    allow_unsubscribe: bool = Field(default=True)
    track_opens: bool = Field(default=True)
    track_clicks: bool = Field(default=True)


class TemplateUpdateRequest(BaseModel):
    """Schema for updating an email template."""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    category: Optional[TemplateCategory] = None
    subject: Optional[str] = Field(None, min_length=1, max_length=200)
    html_content: Optional[str] = Field(None, min_length=1)
    text_content: Optional[str] = None
    variables: Optional[List[TemplateVariable]] = None
    tags: Optional[List[str]] = None
    language: Optional[str] = None
    status: Optional[TemplateStatus] = None
    allow_unsubscribe: Optional[bool] = None
    track_opens: Optional[bool] = None
    track_clicks: Optional[bool] = None


class TemplateListRequest(BaseModel):
    """Schema for listing templates with filters."""
    skip: int = Field(default=0, ge=0)
    limit: int = Field(default=20, ge=1, le=100)
    category: Optional[TemplateCategory] = None
    status: Optional[TemplateStatus] = None
    search: Optional[str] = Field(None, max_length=255)
    tags: Optional[List[str]] = None
    language: Optional[str] = None
    created_by: Optional[str] = None


class TemplatePreviewRequest(BaseModel):
    """Schema for template preview with sample data."""
    template_data: Dict[str, Any] = Field(default_factory=dict)
    recipient_email: Optional[str] = None


class TemplateDuplicateRequest(BaseModel):
    """Schema for duplicating a template."""
    new_name: str = Field(..., min_length=1, max_length=200)
    new_description: Optional[str] = Field(None, max_length=1000)
    copy_variables: bool = Field(default=True)
    copy_tags: bool = Field(default=True)


# Template Response Schemas

class TemplateResponse(BaseModel):
    """Schema for template response."""
    id: str
    name: str
    description: str
    category: TemplateCategory
    status: TemplateStatus
    subject: str
    html_content: str
    text_content: Optional[str]
    variables: List[TemplateVariable]
    tags: List[str]
    language: str
    version: int
    usage_count: int
    last_used: Optional[datetime]
    is_system_template: bool
    allow_unsubscribe: bool
    track_opens: bool
    track_clicks: bool
    created_at: datetime
    updated_at: datetime
    created_by: str
    updated_by: Optional[str]


class TemplateListResponse(BaseModel):
    """Schema for template list response."""
    templates: List[TemplateResponse]
    total: int
    skip: int
    limit: int


class TemplatePreviewResponse(BaseModel):
    """Schema for template preview response."""
    rendered_html: str
    rendered_text: Optional[str]
    rendered_subject: str
    variables_used: List[str]
    missing_variables: List[str]
    preview_url: Optional[str]


class TemplateVersionResponse(BaseModel):
    """Schema for template version response."""
    version: int
    html_content: str
    text_content: Optional[str]
    subject: str
    variables: List[TemplateVariable]
    created_at: datetime
    created_by: str
    change_notes: str
    is_current: bool


# Campaign Request Schemas

class CampaignCreateRequest(BaseModel):
    """Schema for creating a new email campaign."""
    name: str = Field(..., min_length=1, max_length=200)
    description: str = Field(default="", max_length=1000)
    template_id: str
    audience: CampaignAudience = Field(default_factory=CampaignAudience)
    schedule: CampaignSchedule = Field(default_factory=CampaignSchedule)
    ab_test: ABTestConfig = Field(default_factory=ABTestConfig)


class CampaignUpdateRequest(BaseModel):
    """Schema for updating an email campaign."""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    template_id: Optional[str] = None
    status: Optional[CampaignStatus] = None
    audience: Optional[CampaignAudience] = None
    schedule: Optional[CampaignSchedule] = None
    ab_test: Optional[ABTestConfig] = None


class CampaignListRequest(BaseModel):
    """Schema for listing campaigns with filters."""
    skip: int = Field(default=0, ge=0)
    limit: int = Field(default=20, ge=1, le=100)
    status: Optional[CampaignStatus] = None
    template_id: Optional[str] = None
    search: Optional[str] = Field(None, max_length=255)
    created_by: Optional[str] = None


class CampaignTestRequest(BaseModel):
    """Schema for testing a campaign."""
    test_emails: List[str] = Field(..., min_length=1, max_length=10)
    use_sample_data: bool = Field(default=True)
    sample_data: Dict[str, Any] = Field(default_factory=dict)


# Campaign Response Schemas

class CampaignResponse(BaseModel):
    """Schema for campaign response."""
    id: str
    name: str
    description: str
    template_id: str
    template_name: str
    status: CampaignStatus
    audience: CampaignAudience
    schedule: CampaignSchedule
    ab_test: ABTestConfig
    total_recipients: int
    emails_sent: int
    emails_delivered: int
    emails_opened: int
    emails_clicked: int
    emails_bounced: int
    unsubscribes: int
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    last_sent_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    created_by: str
    updated_by: Optional[str]


class CampaignListResponse(BaseModel):
    """Schema for campaign list response."""
    campaigns: List[CampaignResponse]
    total: int
    skip: int
    limit: int


class CampaignStatsResponse(BaseModel):
    """Schema for campaign statistics response."""
    campaign_id: str
    campaign_name: str
    total_recipients: int
    emails_sent: int
    emails_delivered: int
    emails_opened: int
    emails_clicked: int
    emails_bounced: int
    unsubscribes: int
    delivery_rate: float
    open_rate: float
    click_rate: float
    bounce_rate: float
    unsubscribe_rate: float
    revenue_generated: Optional[float]
    cost_per_email: Optional[float]
    roi: Optional[float]


# Trigger Request Schemas

class TriggerCreateRequest(BaseModel):
    """Schema for creating an email trigger."""
    name: str = Field(..., min_length=1, max_length=200)
    description: str = Field(default="", max_length=1000)
    trigger_type: TriggerType
    event_name: str = Field(..., min_length=1, max_length=100)
    template_id: str
    conditions: Dict[str, Any] = Field(default_factory=dict)
    delay_minutes: int = Field(default=0, ge=0)
    max_sends_per_user: Optional[int] = Field(default=None, ge=1)
    cooldown_hours: int = Field(default=0, ge=0)


class TriggerUpdateRequest(BaseModel):
    """Schema for updating an email trigger."""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    template_id: Optional[str] = None
    conditions: Optional[Dict[str, Any]] = None
    delay_minutes: Optional[int] = Field(None, ge=0)
    is_active: Optional[bool] = None
    max_sends_per_user: Optional[int] = Field(None, ge=1)
    cooldown_hours: Optional[int] = Field(None, ge=0)


class TriggerListRequest(BaseModel):
    """Schema for listing triggers with filters."""
    skip: int = Field(default=0, ge=0)
    limit: int = Field(default=20, ge=1, le=100)
    trigger_type: Optional[TriggerType] = None
    event_name: Optional[str] = None
    template_id: Optional[str] = None
    is_active: Optional[bool] = None
    search: Optional[str] = Field(None, max_length=255)


# Trigger Response Schemas

class TriggerResponse(BaseModel):
    """Schema for trigger response."""
    id: str
    name: str
    description: str
    trigger_type: TriggerType
    event_name: str
    template_id: str
    template_name: str
    conditions: Dict[str, Any]
    delay_minutes: int
    is_active: bool
    max_sends_per_user: Optional[int]
    cooldown_hours: int
    trigger_count: int
    last_triggered: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    created_by: str
    updated_by: Optional[str]


class TriggerListResponse(BaseModel):
    """Schema for trigger list response."""
    triggers: List[TriggerResponse]
    total: int
    skip: int
    limit: int


# Analytics Schemas

class TemplateAnalyticsResponse(BaseModel):
    """Schema for template analytics response."""
    template_id: str
    template_name: str
    total_sends: int
    total_deliveries: int
    total_opens: int
    total_clicks: int
    total_bounces: int
    total_unsubscribes: int
    delivery_rate: float
    open_rate: float
    click_rate: float
    bounce_rate: float
    unsubscribe_rate: float
    last_30_days_sends: int
    last_7_days_sends: int
    avg_open_time_hours: Optional[float]
    last_updated: datetime


class EmailDashboardResponse(BaseModel):
    """Schema for email management dashboard response."""
    total_templates: int
    active_templates: int
    draft_templates: int
    total_campaigns: int
    active_campaigns: int
    completed_campaigns: int
    total_triggers: int
    active_triggers: int
    emails_sent_today: int
    emails_sent_this_week: int
    emails_sent_this_month: int
    overall_delivery_rate: float
    overall_open_rate: float
    overall_click_rate: float
    top_performing_templates: List[TemplateAnalyticsResponse]
    recent_campaigns: List[CampaignResponse]


# Utility Schemas

class TemplateValidationResponse(BaseModel):
    """Schema for template validation response."""
    is_valid: bool
    validation_errors: List[str]
    warnings: List[str]
    suggestions: List[str]
    html_issues: List[str]
    accessibility_issues: List[str]


class BulkOperationRequest(BaseModel):
    """Schema for bulk operations on templates/campaigns."""
    item_ids: List[str] = Field(..., min_length=1, max_length=100)
    operation: str = Field(..., pattern="^(delete|archive|activate|deactivate)$")
    confirm: bool = Field(default=False)


class BulkOperationResponse(BaseModel):
    """Schema for bulk operation response."""
    operation: str
    total_items: int
    successful_items: int
    failed_items: int
    errors: List[Dict[str, str]]
    processed_ids: List[str]
