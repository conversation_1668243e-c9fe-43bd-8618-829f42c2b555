// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import AvatarUploader from '../AvatarUploader';

// Mock the notification hook
const mockNotification = {
  showErrorNotification: vi.fn(),
  showSuccessNotification: vi.fn()
};

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => mockNotification,
}));

// Mock react-image-crop
vi.mock('react-image-crop', () => ({
  default: ({ children, ...props }) => (
    <div data-testid="react-crop" {...props}>
      {children}
    </div>
  ),
}));

// Mock FileReader
global.FileReader = class {
  constructor() {
    this.result = null;
    this.onload = null;
    this.onerror = null;
  }
  
  readAsDataURL(file) {
    this.result = `data:image/jpeg;base64,${file.name}`;
    if (this.onload) {
      this.onload();
    }
  }
};

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn(() => 'mock-url');
global.URL.revokeObjectURL = vi.fn();

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      error: {
        main: '#F44336',
      },
      divider: '#E0E0E0',
    },
    spacing: (factor) => `${8 * factor}px`,
    transitions: {
      create: () => 'all 0.3s ease',
      duration: {
        short: 250,
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('AvatarUploader', () => {
  const mockProps = {
    onUpload: vi.fn(),
    onRemove: vi.fn(),
    currentAvatar: null,
    loading: false,
    size: 100,
    name: 'John Doe'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders avatar uploader correctly', () => {
    render(
      <TestWrapper>
        <AvatarUploader {...mockProps} />
      </TestWrapper>
    );

    // Should render avatar with initials
    expect(screen.getByText('JD')).toBeInTheDocument();
    
    // Should render upload button
    expect(screen.getByText('Upload Avatar')).toBeInTheDocument();
  });

  test('displays current avatar when provided', () => {
    render(
      <TestWrapper>
        <AvatarUploader {...mockProps} currentAvatar="https://example.com/avatar.jpg" />
      </TestWrapper>
    );

    const avatar = screen.getByRole('img');
    expect(avatar).toHaveAttribute('src', 'https://example.com/avatar.jpg');
  });

  test('shows loading state correctly', () => {
    render(
      <TestWrapper>
        <AvatarUploader {...mockProps} loading={true} />
      </TestWrapper>
    );

    // Should show loading indicator
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('handles file selection', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AvatarUploader {...mockProps} />
      </TestWrapper>
    );

    // Create a mock file
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    
    // Get file input
    const fileInput = screen.getByLabelText(/upload avatar/i);
    
    // Upload file
    await user.upload(fileInput, file);

    // Should open crop dialog
    await waitFor(() => {
      expect(screen.getByText('Crop Avatar')).toBeInTheDocument();
    });
  });

  test('validates file type', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AvatarUploader {...mockProps} />
      </TestWrapper>
    );

    // Create invalid file type
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });
    
    const fileInput = screen.getByLabelText(/upload avatar/i);
    await user.upload(fileInput, file);

    // Should show error notification
    expect(mockNotification.showErrorNotification).toHaveBeenCalledWith(
      expect.stringContaining('Invalid file type')
    );
  });

  test('validates file size', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AvatarUploader {...mockProps} maxFileSize={1024} />
      </TestWrapper>
    );

    // Create large file
    const largeContent = 'x'.repeat(2048);
    const file = new File([largeContent], 'large.jpg', { type: 'image/jpeg' });
    
    const fileInput = screen.getByLabelText(/upload avatar/i);
    await user.upload(fileInput, file);

    // Should show error notification
    expect(mockNotification.showErrorNotification).toHaveBeenCalledWith(
      expect.stringContaining('File too large')
    );
  });

  test('handles crop dialog interactions', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AvatarUploader {...mockProps} />
      </TestWrapper>
    );

    // Upload valid file
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const fileInput = screen.getByLabelText(/upload avatar/i);
    await user.upload(fileInput, file);

    // Wait for dialog to open
    await waitFor(() => {
      expect(screen.getByText('Crop Avatar')).toBeInTheDocument();
    });

    // Should show crop controls
    expect(screen.getByRole('slider')).toBeInTheDocument();
    expect(screen.getByText('Reset')).toBeInTheDocument();
    expect(screen.getByText('Save')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  test('handles zoom slider', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AvatarUploader {...mockProps} />
      </TestWrapper>
    );

    // Upload file and open dialog
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const fileInput = screen.getByLabelText(/upload avatar/i);
    await user.upload(fileInput, file);

    await waitFor(() => {
      expect(screen.getByText('Crop Avatar')).toBeInTheDocument();
    });

    // Adjust zoom slider
    const slider = screen.getByRole('slider');
    fireEvent.change(slider, { target: { value: 150 } });

    expect(slider).toHaveValue('150');
  });

  test('handles crop reset', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AvatarUploader {...mockProps} />
      </TestWrapper>
    );

    // Upload file and open dialog
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const fileInput = screen.getByLabelText(/upload avatar/i);
    await user.upload(fileInput, file);

    await waitFor(() => {
      expect(screen.getByText('Crop Avatar')).toBeInTheDocument();
    });

    // Click reset button
    const resetButton = screen.getByText('Reset');
    await user.click(resetButton);

    // Zoom should reset to 100
    const slider = screen.getByRole('slider');
    expect(slider).toHaveValue('100');
  });

  test('handles crop save', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AvatarUploader {...mockProps} />
      </TestWrapper>
    );

    // Upload file and open dialog
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const fileInput = screen.getByLabelText(/upload avatar/i);
    await user.upload(fileInput, file);

    await waitFor(() => {
      expect(screen.getByText('Crop Avatar')).toBeInTheDocument();
    });

    // Click save button
    const saveButton = screen.getByText('Save');
    await user.click(saveButton);

    // Should call onUpload
    expect(mockProps.onUpload).toHaveBeenCalled();
  });

  test('handles crop cancel', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AvatarUploader {...mockProps} />
      </TestWrapper>
    );

    // Upload file and open dialog
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const fileInput = screen.getByLabelText(/upload avatar/i);
    await user.upload(fileInput, file);

    await waitFor(() => {
      expect(screen.getByText('Crop Avatar')).toBeInTheDocument();
    });

    // Click cancel button
    const cancelButton = screen.getByText('Cancel');
    await user.click(cancelButton);

    // Dialog should close
    await waitFor(() => {
      expect(screen.queryByText('Crop Avatar')).not.toBeInTheDocument();
    });
  });

  test('handles avatar removal', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AvatarUploader {...mockProps} currentAvatar="https://example.com/avatar.jpg" />
      </TestWrapper>
    );

    // Click remove button
    const removeButton = screen.getByLabelText(/remove avatar/i);
    await user.click(removeButton);

    // Should call onRemove
    expect(mockProps.onRemove).toHaveBeenCalled();
  });

  test('shows hover effect on avatar', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AvatarUploader {...mockProps} currentAvatar="https://example.com/avatar.jpg" />
      </TestWrapper>
    );

    const avatarContainer = screen.getByRole('img').parentElement;
    
    // Hover over avatar
    await user.hover(avatarContainer);

    // Should show camera icon
    expect(screen.getByTestId('PhotoCameraIcon')).toBeInTheDocument();
  });

  test('handles direct upload when cropping disabled', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AvatarUploader {...mockProps} enableCropping={false} />
      </TestWrapper>
    );

    // Upload file
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const fileInput = screen.getByLabelText(/upload avatar/i);
    await user.upload(fileInput, file);

    // Should directly call onUpload without opening dialog
    await waitFor(() => {
      expect(mockProps.onUpload).toHaveBeenCalledWith(file);
    });
  });

  test('tracks analytics when enabled', async () => {
    const user = userEvent.setup();
    const onAnalytics = vi.fn();
    
    render(
      <TestWrapper>
        <AvatarUploader {...mockProps} enableAnalytics={true} onAnalytics={onAnalytics} />
      </TestWrapper>
    );

    // Upload file
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const fileInput = screen.getByLabelText(/upload avatar/i);
    await user.upload(fileInput, file);

    // Should track file selection
    await waitFor(() => {
      expect(onAnalytics).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'file_selected',
          component: 'AvatarUploader'
        })
      );
    });
  });

  test('handles custom validation', async () => {
    const user = userEvent.setup();
    const customValidation = vi.fn().mockReturnValue(['Custom error']);
    
    render(
      <TestWrapper>
        <AvatarUploader {...mockProps} customValidation={customValidation} />
      </TestWrapper>
    );

    // Upload file
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const fileInput = screen.getByLabelText(/upload avatar/i);
    await user.upload(fileInput, file);

    // Should show custom validation error
    await waitFor(() => {
      expect(mockNotification.showErrorNotification).toHaveBeenCalledWith('Custom error');
    });
  });

  test('handles different avatar sizes', () => {
    const { rerender } = render(
      <TestWrapper>
        <AvatarUploader {...mockProps} size={50} />
      </TestWrapper>
    );

    let avatar = screen.getByText('JD');
    expect(avatar).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <AvatarUploader {...mockProps} size={150} />
      </TestWrapper>
    );

    avatar = screen.getByText('JD');
    expect(avatar).toBeInTheDocument();
  });

  test('generates correct initials from name', () => {
    const { rerender } = render(
      <TestWrapper>
        <AvatarUploader {...mockProps} name="John Doe" />
      </TestWrapper>
    );

    expect(screen.getByText('JD')).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <AvatarUploader {...mockProps} name="Alice" />
      </TestWrapper>
    );

    expect(screen.getByText('A')).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <AvatarUploader {...mockProps} name="" />
      </TestWrapper>
    );

    expect(screen.getByText('?')).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <AvatarUploader {...mockProps} />
      </TestWrapper>
    );

    // File input should have proper label
    const fileInput = screen.getByLabelText(/upload avatar/i);
    expect(fileInput).toBeInTheDocument();
    expect(fileInput).toHaveAttribute('accept', 'image/jpeg,image/png,image/gif,image/webp');

    // Avatar should have proper alt text
    expect(screen.getByText('JD')).toBeInTheDocument();
  });

  test('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AvatarUploader {...mockProps} />
      </TestWrapper>
    );

    // Should be able to focus on upload button
    const uploadButton = screen.getByText('Upload Avatar');
    await user.tab();
    expect(uploadButton).toHaveFocus();
  });
});
