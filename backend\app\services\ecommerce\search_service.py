"""
Advanced Product Search and Filtering Service.
Provides intelligent search with fuzzy matching, autocomplete, and advanced filtering.
@since 2024-1-1 to 2025-25-7
"""

import logging
import asyncio
import json
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from bson import ObjectId
import pymongo
# from fuzzywuzzy import fuzz, process  # Optional dependency

from app.models.ecommerce import SyncedProduct
from app.models.user import User
from app.db.mongodb import get_database
from app.core.redis import get_redis_client
from app.core.monitoring import monitor_performance, log_audit_event, OperationType

logger = logging.getLogger(__name__)

# Collection names
PRODUCTS_COLLECTION = "synced_products"
SEARCH_ANALYTICS_COLLECTION = "search_analytics"
SEARCH_SUGGESTIONS_COLLECTION = "search_suggestions"

# Redis keys
SEARCH_CACHE_KEY = "search:{query_hash}"
AUTOCOMPLETE_CACHE_KEY = "autocomplete:{prefix}"
POPULAR_SEARCHES_KEY = "popular_searches:{user_id}"
SEARCH_FILTERS_CACHE_KEY = "search_filters:{store_id}"

# Search configuration
FUZZY_THRESHOLD = 70
MAX_SUGGESTIONS = 10
CACHE_TTL = 1800  # 30 minutes


class AdvancedSearchService:
    """
    Comprehensive search service with intelligent features.
    """
    
    def __init__(self):
        self.redis_client = None
        self.db = None
        
    async def _get_redis_client(self):
        """Get Redis client for caching."""
        if not self.redis_client:
            self.redis_client = await get_redis_client()
        return self.redis_client
    
    async def _get_database(self):
        """Get MongoDB database."""
        if not self.db:
            self.db = await get_database()
        return self.db
    
    @monitor_performance("intelligent_search")
    async def intelligent_search(
        self,
        user_id: str,
        query: str,
        store_ids: Optional[List[str]] = None,
        filters: Optional[Dict[str, Any]] = None,
        sort_by: str = "relevance",
        limit: int = 20,
        offset: int = 0,
        enable_fuzzy: bool = True,
        enable_autocorrect: bool = True
    ) -> Dict[str, Any]:
        """
        Perform intelligent product search with fuzzy matching and autocorrect.
        
        Args:
            user_id: User ID
            query: Search query
            store_ids: Optional store IDs to search
            filters: Optional additional filters
            sort_by: Sort criteria (relevance, price, name, date)
            limit: Number of results to return
            offset: Number of results to skip
            enable_fuzzy: Enable fuzzy matching
            enable_autocorrect: Enable query autocorrection
            
        Returns:
            Search results with metadata
        """
        try:
            start_time = datetime.now(timezone.utc)
            
            # Normalize and validate query
            normalized_query = self._normalize_query(query)
            if not normalized_query:
                return {
                    "success": False,
                    "error": "Empty search query"
                }
            
            # Check cache first
            query_hash = self._generate_query_hash(
                normalized_query, store_ids, filters, sort_by, limit, offset
            )
            
            redis = await self._get_redis_client()
            if redis:
                cached_result = await redis.get(SEARCH_CACHE_KEY.format(query_hash=query_hash))
                if cached_result:
                    import json
                    result = json.loads(cached_result)
                    result["from_cache"] = True
                    return result
            
            # Build search pipeline
            db = await self._get_database()
            pipeline = await self._build_search_pipeline(
                user_id, normalized_query, store_ids, filters, sort_by, 
                limit, offset, enable_fuzzy, enable_autocorrect
            )
            
            # Execute search
            search_results = await db[PRODUCTS_COLLECTION].aggregate(pipeline).to_list(None)
            
            # Process results
            products = []
            total_count = 0
            
            if search_results:
                # Extract total count from first result
                if search_results[0].get("total_count"):
                    total_count = search_results[0]["total_count"][0]["count"] if search_results[0]["total_count"] else 0
                
                # Extract products
                products = search_results[0].get("products", [])
            
            # Enhance results with relevance scoring
            enhanced_products = await self._enhance_search_results(
                products, normalized_query, enable_fuzzy
            )
            
            # Calculate search time
            search_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            
            # Prepare result
            result = {
                "success": True,
                "query": query,
                "normalized_query": normalized_query,
                "results": enhanced_products,
                "total_count": total_count,
                "limit": limit,
                "offset": offset,
                "has_more": offset + limit < total_count,
                "search_time_ms": int(search_time),
                "sort_by": sort_by,
                "filters_applied": filters or {},
                "suggestions": await self._get_search_suggestions(normalized_query, user_id)
            }
            
            # Cache result
            if redis:
                import json
                await redis.setex(
                    SEARCH_CACHE_KEY.format(query_hash=query_hash),
                    CACHE_TTL,
                    json.dumps(result, default=str)
                )
            
            # Log search analytics
            await self._log_search_analytics(
                user_id, query, normalized_query, total_count, search_time
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error in intelligent search: {str(e)}")
            return {
                "success": False,
                "error": f"Search failed: {str(e)}",
                "query": query
            }
    
    def _normalize_query(self, query: str) -> str:
        """Normalize search query."""
        if not query:
            return ""
        
        # Remove extra whitespace and convert to lowercase
        normalized = re.sub(r'\s+', ' ', query.strip().lower())
        
        # Remove special characters but keep alphanumeric and spaces
        normalized = re.sub(r'[^\w\s-]', '', normalized)
        
        return normalized
    
    def _generate_query_hash(
        self,
        query: str,
        store_ids: Optional[List[str]],
        filters: Optional[Dict[str, Any]],
        sort_by: str,
        limit: int,
        offset: int
    ) -> str:
        """Generate hash for query caching."""
        import hashlib
        
        cache_key_parts = [
            query,
            str(sorted(store_ids) if store_ids else []),
            str(sorted(filters.items()) if filters else {}),
            sort_by,
            str(limit),
            str(offset)
        ]
        
        cache_string = "|".join(cache_key_parts)
        return hashlib.md5(cache_string.encode()).hexdigest()
    
    async def _build_search_pipeline(
        self,
        user_id: str,
        query: str,
        store_ids: Optional[List[str]],
        filters: Optional[Dict[str, Any]],
        sort_by: str,
        limit: int,
        offset: int,
        enable_fuzzy: bool,
        enable_autocorrect: bool
    ) -> List[Dict[str, Any]]:
        """Build MongoDB aggregation pipeline for search."""
        
        pipeline = []
        
        # Base match stage
        match_stage: Dict[str, Any] = {
            "user_id": ObjectId(user_id)
        }

        # Add store filter
        if store_ids:
            match_stage["store_id"] = {"$in": [ObjectId(sid) for sid in store_ids]}
        
        # Add text search
        if query:
            # Create text search conditions
            text_conditions = []
            
            # Exact phrase match (highest priority)
            text_conditions.append({
                "$or": [
                    {"title": {"$regex": re.escape(query), "$options": "i"}},
                    {"description": {"$regex": re.escape(query), "$options": "i"}},
                    {"sku": {"$regex": re.escape(query), "$options": "i"}},
                    {"tags": {"$regex": re.escape(query), "$options": "i"}}
                ]
            })
            
            # Word-based search
            words = query.split()
            if len(words) > 1:
                word_conditions = []
                for word in words:
                    if len(word) > 2:  # Skip very short words
                        word_conditions.append({
                            "$or": [
                                {"title": {"$regex": re.escape(word), "$options": "i"}},
                                {"description": {"$regex": re.escape(word), "$options": "i"}},
                                {"tags": {"$regex": re.escape(word), "$options": "i"}}
                            ]
                        })
                
                if word_conditions:
                    text_conditions.append({"$and": word_conditions})
            
            if text_conditions:
                match_stage["$or"] = text_conditions
        
        # Add additional filters
        if filters:
            if filters.get("categories"):
                match_stage["category"] = {"$in": filters["categories"]}
            
            if filters.get("price_range"):
                price_filter = {}
                if filters["price_range"].get("min") is not None:
                    price_filter["$gte"] = float(filters["price_range"]["min"])
                if filters["price_range"].get("max") is not None:
                    price_filter["$lte"] = float(filters["price_range"]["max"])
                if price_filter:
                    match_stage["price"] = price_filter
            
            if filters.get("in_stock_only"):
                match_stage["inventory_quantity"] = {"$gt": 0}
            
            if filters.get("tags"):
                match_stage["tags"] = {"$in": filters["tags"]}
            
            if filters.get("vendor"):
                match_stage["vendor"] = {"$regex": re.escape(filters["vendor"]), "$options": "i"}
        
        pipeline.append({"$match": match_stage})
        
        # Add relevance scoring
        if query:
            pipeline.append({
                "$addFields": {
                    "relevance_score": {
                        "$add": [
                            # Title exact match (highest score)
                            {"$cond": [
                                {"$regexMatch": {"input": "$title", "regex": re.escape(query), "options": "i"}},
                                100, 0
                            ]},
                            # SKU exact match
                            {"$cond": [
                                {"$regexMatch": {"input": "$sku", "regex": re.escape(query), "options": "i"}},
                                80, 0
                            ]},
                            # Title word match
                            {"$cond": [
                                {"$regexMatch": {"input": "$title", "regex": f"\\b{re.escape(query)}", "options": "i"}},
                                60, 0
                            ]},
                            # Description match
                            {"$cond": [
                                {"$regexMatch": {"input": "$description", "regex": re.escape(query), "options": "i"}},
                                40, 0
                            ]},
                            # Tags match
                            {"$cond": [
                                {"$regexMatch": {"input": "$tags", "regex": re.escape(query), "options": "i"}},
                                30, 0
                            ]},
                            # Boost for in-stock items
                            {"$cond": [{"$gt": ["$inventory_quantity", 0]}, 10, 0]},
                            # Boost for items with images
                            {"$cond": [{"$ne": ["$featured_image", None]}, 5, 0]}
                        ]
                    }
                }
            })
        
        # Add facet stage for count and results
        facet_stage = {
            "$facet": {
                "total_count": [{"$count": "count"}],
                "products": []
            }
        }
        
        # Add sorting
        sort_stage = {}
        if sort_by == "relevance" and query:
            sort_stage = {"relevance_score": -1, "created_at": -1}
        elif sort_by == "price_low":
            sort_stage = {"price": 1}
        elif sort_by == "price_high":
            sort_stage = {"price": -1}
        elif sort_by == "name":
            sort_stage = {"title": 1}
        elif sort_by == "date_new":
            sort_stage = {"created_at": -1}
        elif sort_by == "date_old":
            sort_stage = {"created_at": 1}
        else:
            sort_stage = {"created_at": -1}
        
        facet_stage["$facet"]["products"].extend([
            {"$sort": sort_stage},
            {"$skip": offset},
            {"$limit": limit}
        ])
        
        pipeline.append(facet_stage)
        
        return pipeline
    
    async def _enhance_search_results(
        self,
        products: List[Dict[str, Any]],
        query: str,
        enable_fuzzy: bool
    ) -> List[Dict[str, Any]]:
        """Enhance search results with additional metadata."""
        
        enhanced_products = []
        
        for product in products:
            enhanced_product = {
                **product,
                "id": str(product.get("_id", "")),
                "store_id": str(product.get("store_id", "")),
                "user_id": str(product.get("user_id", "")),
                "match_highlights": self._generate_match_highlights(product, query),
                "relevance_score": product.get("relevance_score", 0)
            }
            
            # Add fuzzy match score if enabled
            if enable_fuzzy and query:
                fuzzy_score = self._calculate_fuzzy_score(product, query)
                enhanced_product["fuzzy_score"] = fuzzy_score
            
            # Add stock status
            inventory = product.get("inventory_quantity", 0)
            enhanced_product["stock_status"] = (
                "in_stock" if inventory > 0 else "out_of_stock"
            )
            
            # Add price tier
            price = product.get("price", 0)
            enhanced_product["price_tier"] = (
                "low" if price < 50 else "medium" if price < 200 else "high"
            )
            
            enhanced_products.append(enhanced_product)
        
        return enhanced_products
    
    def _generate_match_highlights(self, product: Dict[str, Any], query: str) -> Dict[str, List[str]]:
        """Generate highlighted text snippets for matches."""
        highlights = {}
        
        if not query:
            return highlights
        
        # Highlight matches in title
        title = product.get("title", "")
        if title and query.lower() in title.lower():
            highlights["title"] = [self._highlight_text(title, query)]
        
        # Highlight matches in description
        description = product.get("description", "")
        if description and query.lower() in description.lower():
            highlights["description"] = [self._highlight_text(description, query, max_length=200)]
        
        return highlights
    
    def _highlight_text(self, text: str, query: str, max_length: int = 100) -> str:
        """Highlight query terms in text."""
        if not text or not query:
            return text
        
        # Find the position of the query in the text
        lower_text = text.lower()
        lower_query = query.lower()
        
        start_pos = lower_text.find(lower_query)
        if start_pos == -1:
            return text[:max_length] + ("..." if len(text) > max_length else "")
        
        # Calculate snippet boundaries
        snippet_start = max(0, start_pos - 50)
        snippet_end = min(len(text), start_pos + len(query) + 50)
        
        snippet = text[snippet_start:snippet_end]
        
        # Add ellipsis if needed
        if snippet_start > 0:
            snippet = "..." + snippet
        if snippet_end < len(text):
            snippet = snippet + "..."
        
        # Highlight the query term
        highlighted = re.sub(
            f"({re.escape(query)})",
            r"<mark>\1</mark>",
            snippet,
            flags=re.IGNORECASE
        )
        
        return highlighted
    
    def _calculate_fuzzy_score(self, product: Dict[str, Any], query: str) -> int:
        """Calculate fuzzy matching score using simple string similarity."""
        if not query:
            return 0

        title = product.get("title", "")
        description = product.get("description", "")
        sku = product.get("sku", "")

        scores = []

        # Simple similarity calculation without external dependencies
        if title:
            scores.append(self._simple_similarity(query.lower(), title.lower()))
        if description:
            scores.append(self._simple_similarity(query.lower(), description.lower()))
        if sku:
            scores.append(self._simple_similarity(query.lower(), sku.lower()))

        return max(scores) if scores else 0

    def _simple_similarity(self, s1: str, s2: str) -> int:
        """Calculate simple string similarity score (0-100)."""
        if not s1 or not s2:
            return 0

        # Exact match
        if s1 == s2:
            return 100

        # Substring match
        if s1 in s2 or s2 in s1:
            return 80

        # Word overlap
        words1 = set(s1.split())
        words2 = set(s2.split())

        if words1 and words2:
            overlap = len(words1.intersection(words2))
            total = len(words1.union(words2))
            return int((overlap / total) * 60) if total > 0 else 0

        return 0


    @monitor_performance("get_autocomplete_suggestions")
    async def get_autocomplete_suggestions(
        self,
        user_id: str,
        prefix: str,
        store_ids: Optional[List[str]] = None,
        limit: int = 10
    ) -> Dict[str, Any]:
        """
        Get autocomplete suggestions for search queries.

        Args:
            user_id: User ID
            prefix: Search prefix
            store_ids: Optional store IDs
            limit: Number of suggestions to return

        Returns:
            Autocomplete suggestions
        """
        try:
            if len(prefix) < 2:
                return {
                    "success": True,
                    "suggestions": [],
                    "prefix": prefix
                }

            # Check cache first
            redis = await self._get_redis_client()
            cache_key = AUTOCOMPLETE_CACHE_KEY.format(prefix=prefix.lower())

            if redis:
                cached_suggestions = await redis.get(cache_key)
                if cached_suggestions:
                    import json
                    return json.loads(cached_suggestions)

            db = await self._get_database()

            # Build aggregation pipeline for suggestions
            pipeline = [
                {
                    "$match": {
                        "user_id": ObjectId(user_id),
                        "$or": [
                            {"title": {"$regex": f"^{re.escape(prefix)}", "$options": "i"}},
                            {"title": {"$regex": f"\\b{re.escape(prefix)}", "$options": "i"}},
                            {"tags": {"$regex": f"\\b{re.escape(prefix)}", "$options": "i"}},
                            {"category": {"$regex": f"^{re.escape(prefix)}", "$options": "i"}}
                        ]
                    }
                }
            ]

            # Add store filter if provided
            if store_ids:
                pipeline[0]["$match"]["store_id"] = {"$in": [ObjectId(sid) for sid in store_ids]}

            # Group and collect suggestions
            pipeline.extend([
                {
                    "$group": {
                        "_id": None,
                        "title_suggestions": {"$addToSet": "$title"},
                        "category_suggestions": {"$addToSet": "$category"},
                        "tag_suggestions": {"$addToSet": "$tags"}
                    }
                },
                {
                    "$project": {
                        "suggestions": {
                            "$concatArrays": [
                                "$title_suggestions",
                                "$category_suggestions",
                                {"$reduce": {
                                    "input": "$tag_suggestions",
                                    "initialValue": [],
                                    "in": {"$concatArrays": ["$$value", {"$split": ["$$this", ","]}]}
                                }}
                            ]
                        }
                    }
                }
            ])

            results = await db[PRODUCTS_COLLECTION].aggregate(pipeline).to_list(None)

            suggestions = []
            if results and results[0].get("suggestions"):
                # Filter and rank suggestions
                all_suggestions = results[0]["suggestions"]

                for suggestion in all_suggestions:
                    if suggestion and isinstance(suggestion, str):
                        suggestion = suggestion.strip()
                        if (len(suggestion) >= len(prefix) and
                            prefix.lower() in suggestion.lower()):
                            suggestions.append({
                                "text": suggestion,
                                "type": "product",
                                "score": self._calculate_suggestion_score(suggestion, prefix)
                            })

                # Sort by score and limit
                suggestions.sort(key=lambda x: x["score"], reverse=True)
                suggestions = suggestions[:limit]

            # Add popular searches
            popular_searches = await self._get_popular_searches(user_id, prefix)
            for search in popular_searches[:3]:  # Add top 3 popular searches
                suggestions.insert(0, {
                    "text": search["query"],
                    "type": "popular",
                    "score": search["count"]
                })

            result = {
                "success": True,
                "suggestions": suggestions[:limit],
                "prefix": prefix,
                "total_suggestions": len(suggestions)
            }

            # Cache result
            if redis:
                import json
                await redis.setex(cache_key, 300, json.dumps(result))  # 5 minutes cache

            return result

        except Exception as e:
            logger.error(f"Error getting autocomplete suggestions: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "suggestions": [],
                "prefix": prefix
            }

    def _calculate_suggestion_score(self, suggestion: str, prefix: str) -> int:
        """Calculate relevance score for autocomplete suggestions."""
        suggestion_lower = suggestion.lower()
        prefix_lower = prefix.lower()

        score = 0

        # Exact prefix match gets highest score
        if suggestion_lower.startswith(prefix_lower):
            score += 100

        # Word boundary match
        if f" {prefix_lower}" in f" {suggestion_lower}":
            score += 80

        # Contains match
        if prefix_lower in suggestion_lower:
            score += 60

        # Shorter suggestions get higher scores (more specific)
        score += max(0, 50 - len(suggestion))

        return score

    async def _get_popular_searches(self, user_id: str, prefix: str) -> List[Dict[str, Any]]:
        """Get popular searches for the user."""
        try:
            db = await self._get_database()

            pipeline = [
                {
                    "$match": {
                        "user_id": ObjectId(user_id),
                        "query": {"$regex": f"^{re.escape(prefix)}", "$options": "i"}
                    }
                },
                {
                    "$group": {
                        "_id": "$normalized_query",
                        "count": {"$sum": "$search_count"},
                        "last_searched": {"$max": "$last_searched"}
                    }
                },
                {
                    "$sort": {"count": -1, "last_searched": -1}
                },
                {
                    "$limit": 5
                },
                {
                    "$project": {
                        "query": "$_id",
                        "count": 1,
                        "last_searched": 1
                    }
                }
            ]

            results = await db[SEARCH_ANALYTICS_COLLECTION].aggregate(pipeline).to_list(None)
            return results

        except Exception as e:
            logger.error(f"Error getting popular searches: {str(e)}")
            return []

    async def _get_search_suggestions(self, query: str, user_id: str) -> List[str]:
        """Get search suggestions for query refinement."""
        try:
            # This could be enhanced with ML-based suggestions
            # For now, return simple suggestions based on common patterns

            suggestions = []

            # Add category-based suggestions
            if len(query) > 3:
                db = await self._get_database()

                # Find related categories
                category_pipeline = [
                    {
                        "$match": {
                            "user_id": ObjectId(user_id),
                            "$or": [
                                {"title": {"$regex": re.escape(query), "$options": "i"}},
                                {"description": {"$regex": re.escape(query), "$options": "i"}}
                            ]
                        }
                    },
                    {
                        "$group": {
                            "_id": "$category",
                            "count": {"$sum": 1}
                        }
                    },
                    {
                        "$sort": {"count": -1}
                    },
                    {
                        "$limit": 3
                    }
                ]

                categories = await db[PRODUCTS_COLLECTION].aggregate(category_pipeline).to_list(None)

                for cat in categories:
                    if cat["_id"] and cat["_id"].lower() != query.lower():
                        suggestions.append(f"{query} in {cat['_id']}")

            return suggestions[:5]

        except Exception as e:
            logger.error(f"Error getting search suggestions: {str(e)}")
            return []

    async def _log_search_analytics(
        self,
        user_id: str,
        original_query: str,
        normalized_query: str,
        result_count: int,
        search_time: float
    ):
        """Log search analytics for insights."""
        try:
            db = await self._get_database()

            # Update or insert search analytics
            await db[SEARCH_ANALYTICS_COLLECTION].update_one(
                {
                    "user_id": ObjectId(user_id),
                    "normalized_query": normalized_query
                },
                {
                    "$set": {
                        "original_query": original_query,
                        "last_searched": datetime.now(timezone.utc),
                        "last_result_count": result_count,
                        "avg_search_time": search_time
                    },
                    "$inc": {"search_count": 1}
                },
                upsert=True
            )

        except Exception as e:
            logger.error(f"Error logging search analytics: {str(e)}")


# Create singleton instance
search_service = AdvancedSearchService()
