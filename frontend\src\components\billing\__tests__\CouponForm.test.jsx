/**
 * Tests for CouponForm component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CouponForm from '../CouponForm';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the API
vi.mock('../../../api', () => ({
  default: {
    post: vi.fn()
  }
}));

// Mock useNotification hook
vi.mock('../../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showErrorNotification: vi.fn()
  }))
}));

describe('CouponForm', () => {
  const mockProps = {
    planId: 'creator',
    isYearly: false,
    onApplyCoupon: vi.fn(),
    onRemoveCoupon: vi.fn(),
    onError: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders coupon form correctly', () => {
    render(
      <TestWrapper>
        <CouponForm {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Have a coupon code?')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter coupon code')).toBeInTheDocument();
    expect(screen.getByText('Apply')).toBeInTheDocument();
  });

  test('uses initial code when provided', () => {
    render(
      <TestWrapper>
        <CouponForm {...mockProps} initialCode="WELCOME25" />
      </TestWrapper>
    );

    const input = screen.getByPlaceholderText('Enter coupon code');
    expect(input).toHaveValue('WELCOME25');
  });

  test('converts input to uppercase', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CouponForm {...mockProps} />
      </TestWrapper>
    );

    const input = screen.getByPlaceholderText('Enter coupon code');
    await user.type(input, 'welcome25');

    expect(input).toHaveValue('WELCOME25');
  });

  test('validates coupon code successfully', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.post.mockResolvedValue({
      data: {
        is_valid: true,
        discount_amount: 10.00,
        final_price: 40.00,
        message: 'Coupon is valid! Save $10.00'
      }
    });
    
    render(
      <TestWrapper>
        <CouponForm {...mockProps} />
      </TestWrapper>
    );

    const input = screen.getByPlaceholderText('Enter coupon code');
    const applyButton = screen.getByText('Apply');

    await user.type(input, 'WELCOME25');
    await user.click(applyButton);

    await waitFor(() => {
      expect(api.default.post).toHaveBeenCalledWith('/api/coupons/validate', {
        code: 'WELCOME25',
        plan_id: 'creator',
        addon_id: undefined,
        is_yearly: false
      });
    });

    expect(screen.getByText('Coupon is valid! Save $10.00')).toBeInTheDocument();
    expect(screen.getByText('Confirm')).toBeInTheDocument();
  });

  test('handles invalid coupon code', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.post.mockResolvedValue({
      data: {
        is_valid: false,
        message: 'Coupon code has expired'
      }
    });
    
    render(
      <TestWrapper>
        <CouponForm {...mockProps} />
      </TestWrapper>
    );

    const input = screen.getByPlaceholderText('Enter coupon code');
    const applyButton = screen.getByText('Apply');

    await user.type(input, 'EXPIRED');
    await user.click(applyButton);

    await waitFor(() => {
      expect(screen.getByText('Coupon code has expired')).toBeInTheDocument();
    });

    expect(mockProps.onError).toHaveBeenCalledWith('Coupon code has expired');
  });

  test('validates empty coupon code', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CouponForm {...mockProps} />
      </TestWrapper>
    );

    const applyButton = screen.getByText('Apply');
    await user.click(applyButton);

    await waitFor(() => {
      expect(screen.getByText('Please enter a coupon code')).toBeInTheDocument();
    });

    expect(mockProps.onError).toHaveBeenCalledWith('Please enter a coupon code');
  });

  test('applies coupon successfully', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    // Mock validation response
    api.default.post.mockImplementation((url) => {
      if (url.includes('validate')) {
        return Promise.resolve({
          data: {
            is_valid: true,
            discount_amount: 10.00,
            final_price: 40.00,
            message: 'Coupon is valid!'
          }
        });
      }
      if (url.includes('apply')) {
        return Promise.resolve({
          data: {
            id: 'redemption-123',
            success: true
          }
        });
      }
    });
    
    render(
      <TestWrapper>
        <CouponForm {...mockProps} />
      </TestWrapper>
    );

    const input = screen.getByPlaceholderText('Enter coupon code');
    const applyButton = screen.getByText('Apply');

    // First validate the coupon
    await user.type(input, 'WELCOME25');
    await user.click(applyButton);

    await waitFor(() => {
      expect(screen.getByText('Confirm')).toBeInTheDocument();
    });

    // Then confirm/apply the coupon
    const confirmButton = screen.getByText('Confirm');
    await user.click(confirmButton);

    await waitFor(() => {
      expect(api.default.post).toHaveBeenCalledWith('/api/coupons/apply', {
        code: 'WELCOME25',
        plan_id: 'creator',
        addon_id: undefined,
        is_yearly: false
      });
    });

    expect(mockProps.onApplyCoupon).toHaveBeenCalledWith({
      code: 'WELCOME25',
      discount_amount: 10.00,
      final_price: 40.00,
      redemption_id: 'redemption-123'
    });

    // Should show applied coupon state
    expect(screen.getByText('Coupon Applied: WELCOME25')).toBeInTheDocument();
    expect(screen.getByText('You saved $10.00')).toBeInTheDocument();
  });

  test('handles API errors gracefully', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    const { useNotification } = await import('../../../hooks/useNotification');
    
    const mockShowError = vi.fn();
    useNotification.mockReturnValue({
      showErrorNotification: mockShowError
    });

    api.default.post.mockRejectedValue({
      response: {
        data: {
          detail: 'Network error'
        }
      }
    });
    
    render(
      <TestWrapper>
        <CouponForm {...mockProps} />
      </TestWrapper>
    );

    const input = screen.getByPlaceholderText('Enter coupon code');
    const applyButton = screen.getByText('Apply');

    await user.type(input, 'WELCOME25');
    await user.click(applyButton);

    await waitFor(() => {
      expect(screen.getByText('Network error')).toBeInTheDocument();
    });

    expect(mockShowError).toHaveBeenCalledWith('Failed to validate coupon');
    expect(mockProps.onError).toHaveBeenCalledWith('Network error');
  });

  test('removes applied coupon', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    // Mock successful validation and application
    api.default.post.mockImplementation((url) => {
      if (url.includes('validate')) {
        return Promise.resolve({
          data: {
            is_valid: true,
            discount_amount: 10.00,
            final_price: 40.00,
            message: 'Coupon is valid!'
          }
        });
      }
      if (url.includes('apply')) {
        return Promise.resolve({
          data: { id: 'redemption-123' }
        });
      }
    });
    
    render(
      <TestWrapper>
        <CouponForm {...mockProps} />
      </TestWrapper>
    );

    // Apply a coupon first
    const input = screen.getByPlaceholderText('Enter coupon code');
    await user.type(input, 'WELCOME25');
    await user.click(screen.getByText('Apply'));

    await waitFor(() => {
      expect(screen.getByText('Confirm')).toBeInTheDocument();
    });

    await user.click(screen.getByText('Confirm'));

    await waitFor(() => {
      expect(screen.getByText('Coupon Applied: WELCOME25')).toBeInTheDocument();
    });

    // Remove the coupon
    const removeButton = screen.getByLabelText('Remove coupon');
    await user.click(removeButton);

    expect(mockProps.onRemoveCoupon).toHaveBeenCalled();
    expect(screen.getByText('Have a coupon code?')).toBeInTheDocument();
  });

  test('shows loading state during validation', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.post.mockImplementation(() => new Promise(() => {})); // Never resolves
    
    render(
      <TestWrapper>
        <CouponForm {...mockProps} />
      </TestWrapper>
    );

    const input = screen.getByPlaceholderText('Enter coupon code');
    const applyButton = screen.getByText('Apply');

    await user.type(input, 'WELCOME25');
    await user.click(applyButton);

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(applyButton).toBeDisabled();
    expect(input).toBeDisabled();
  });

  test('disables form when disabled prop is true', () => {
    render(
      <TestWrapper>
        <CouponForm {...mockProps} disabled={true} />
      </TestWrapper>
    );

    const input = screen.getByPlaceholderText('Enter coupon code');
    const applyButton = screen.getByText('Apply');

    expect(input).toBeDisabled();
    expect(applyButton).toBeDisabled();
  });

  test('handles addon purchases', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.post.mockResolvedValue({
      data: {
        is_valid: true,
        discount_amount: 5.00,
        final_price: 15.00,
        message: 'Coupon is valid!'
      }
    });
    
    render(
      <TestWrapper>
        <CouponForm {...mockProps} addonId="extra-storage" />
      </TestWrapper>
    );

    const input = screen.getByPlaceholderText('Enter coupon code');
    const applyButton = screen.getByText('Apply');

    await user.type(input, 'ADDON10');
    await user.click(applyButton);

    await waitFor(() => {
      expect(api.default.post).toHaveBeenCalledWith('/api/coupons/validate', {
        code: 'ADDON10',
        plan_id: 'creator',
        addon_id: 'extra-storage',
        is_yearly: false
      });
    });
  });

  test('handles yearly subscriptions', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.post.mockResolvedValue({
      data: {
        is_valid: true,
        discount_amount: 50.00,
        final_price: 450.00,
        message: 'Yearly discount applied!'
      }
    });
    
    render(
      <TestWrapper>
        <CouponForm {...mockProps} isYearly={true} />
      </TestWrapper>
    );

    const input = screen.getByPlaceholderText('Enter coupon code');
    const applyButton = screen.getByText('Apply');

    await user.type(input, 'YEARLY50');
    await user.click(applyButton);

    await waitFor(() => {
      expect(api.default.post).toHaveBeenCalledWith('/api/coupons/validate', {
        code: 'YEARLY50',
        plan_id: 'creator',
        addon_id: undefined,
        is_yearly: true
      });
    });
  });

  test('clears applied coupon when typing new code', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    // Mock successful application
    api.default.post.mockImplementation((url) => {
      if (url.includes('validate')) {
        return Promise.resolve({
          data: { is_valid: true, discount_amount: 10.00, final_price: 40.00 }
        });
      }
      if (url.includes('apply')) {
        return Promise.resolve({ data: { id: 'redemption-123' } });
      }
    });
    
    render(
      <TestWrapper>
        <CouponForm {...mockProps} />
      </TestWrapper>
    );

    // Apply a coupon first
    const input = screen.getByPlaceholderText('Enter coupon code');
    await user.type(input, 'WELCOME25');
    await user.click(screen.getByText('Apply'));
    await waitFor(() => screen.getByText('Confirm'));
    await user.click(screen.getByText('Confirm'));

    await waitFor(() => {
      expect(screen.getByText('Coupon Applied: WELCOME25')).toBeInTheDocument();
    });

    // Start typing a new code - should remove applied coupon
    await user.clear(input);
    await user.type(input, 'NEW');

    expect(mockProps.onRemoveCoupon).toHaveBeenCalled();
  });

  test('applies correct accessibility attributes', () => {
    render(
      <TestWrapper>
        <CouponForm {...mockProps} />
      </TestWrapper>
    );

    const input = screen.getByPlaceholderText('Enter coupon code');
    expect(input).toHaveAttribute('aria-label', 'Coupon code');

    const applyButton = screen.getByText('Apply');
    expect(applyButton).toHaveAttribute('aria-label', 'Apply coupon code');
  });

  test('passes through additional props', () => {
    render(
      <TestWrapper>
        <CouponForm 
          {...mockProps} 
          data-testid="test-coupon-form"
          className="custom-class"
        />
      </TestWrapper>
    );

    const form = screen.getByTestId('test-coupon-form');
    expect(form).toHaveClass('custom-class');
  });
});
