// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { <PERSON>rowserRouter } from 'react-router-dom';
import '@testing-library/jest-dom';
import CampaignGracefulDegradation from '../CampaignGracefulDegradation';

// Mock the notification hook
const mockShowSuccessNotification = vi.fn();
const mockShowErrorNotification = vi.fn();

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showSuccessNotification: mockShowSuccessNotification,
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Mock react-router-dom hooks
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Test wrapper with theme and router
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      success: {
        main: '#4caf50',
      },
      warning: {
        main: '#ff9800',
      },
      error: {
        main: '#f44336',
      },
    },
    spacing: (factor) => `${8 * factor}px`,
  });
  
  return (
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        {children}
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('CampaignGracefulDegradation', () => {
  const mockOnRetry = vi.fn();
  const mockOnServiceCheck = vi.fn();

  const defaultProps = {
    onRetry: mockOnRetry,
    isRetrying: false,
    lastAttempt: null,
    circuitBreakerState: 'OPEN',
    serviceHealth: {},
    showAdvancedMetrics: false,
    onServiceCheck: mockOnServiceCheck
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.restoreAllMocks();
    vi.useRealTimers();
  });

  test('renders graceful degradation correctly', () => {
    render(
      <TestWrapper>
        <CampaignGracefulDegradation {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Campaigns')).toBeInTheDocument();
    expect(screen.getByText('Ready to Create Your First Campaign?')).toBeInTheDocument();
    expect(screen.getByText('Service Temporarily Unavailable')).toBeInTheDocument();
  });

  test('shows correct service status for different states', () => {
    const { rerender } = render(
      <TestWrapper>
        <CampaignGracefulDegradation {...defaultProps} circuitBreakerState="CLOSED" />
      </TestWrapper>
    );

    expect(screen.getByText('Service Available')).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <CampaignGracefulDegradation {...defaultProps} circuitBreakerState="HALF_OPEN" />
      </TestWrapper>
    );

    expect(screen.getByText('Service Degraded')).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <CampaignGracefulDegradation {...defaultProps} circuitBreakerState="OPEN" />
      </TestWrapper>
    );

    expect(screen.getByText('Service Temporarily Unavailable')).toBeInTheDocument();
  });

  test('handles retry with exponential backoff', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignGracefulDegradation {...defaultProps} />
      </TestWrapper>
    );

    const retryButton = screen.getByText('Retry Connection');
    await user.click(retryButton);

    expect(mockOnRetry).toHaveBeenCalledTimes(1);
    expect(screen.getByText('Retry (1)')).toBeInTheDocument();
  });

  test('shows retry countdown when next retry is scheduled', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignGracefulDegradation {...defaultProps} />
      </TestWrapper>
    );

    const retryButton = screen.getByText('Retry Connection');
    await user.click(retryButton);

    // Fast-forward time to show countdown
    act(() => {
      vi.advanceTimersByTime(500);
    });

    expect(screen.getByText(/Retry in \d+s/)).toBeInTheDocument();
  });

  test('handles service health check', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignGracefulDegradation {...defaultProps} />
      </TestWrapper>
    );

    const healthCheckButton = screen.getByLabelText('Check service health');
    await user.click(healthCheckButton);

    expect(mockOnServiceCheck).toHaveBeenCalledTimes(1);
  });

  test('shows advanced metrics when enabled', () => {
    const serviceHealth = {
      responseTime: 150,
      uptime: 99.5,
      errorRate: 0.1
    };

    render(
      <TestWrapper>
        <CampaignGracefulDegradation 
          {...defaultProps} 
          showAdvancedMetrics={true}
          serviceHealth={serviceHealth}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Response Time')).toBeInTheDocument();
    expect(screen.getByText('150ms')).toBeInTheDocument();
    expect(screen.getByText('Uptime')).toBeInTheDocument();
    expect(screen.getByText('99.5%')).toBeInTheDocument();
    expect(screen.getByText('Error Rate')).toBeInTheDocument();
    expect(screen.getByText('0.1%')).toBeInTheDocument();
  });

  test('shows advanced details accordion when metrics enabled', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignGracefulDegradation 
          {...defaultProps} 
          showAdvancedMetrics={true}
        />
      </TestWrapper>
    );

    const advancedDetailsButton = screen.getByText('Advanced Service Details');
    await user.click(advancedDetailsButton);

    expect(screen.getByText('Service Health Timeline')).toBeInTheDocument();
    expect(screen.getByText('Retry Strategy')).toBeInTheDocument();
  });

  test('handles navigation to services page', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignGracefulDegradation {...defaultProps} />
      </TestWrapper>
    );

    const getStartedButton = screen.getByText('Get Started');
    await user.click(getStartedButton);

    expect(mockNavigate).toHaveBeenCalledWith('/services');
  });

  test('shows last attempt timestamp when provided', () => {
    const lastAttempt = new Date(Date.now() - 120000).toISOString(); // 2 minutes ago
    
    render(
      <TestWrapper>
        <CampaignGracefulDegradation 
          {...defaultProps} 
          lastAttempt={lastAttempt}
        />
      </TestWrapper>
    );

    expect(screen.getByText(/Last attempt: 2 minutes ago/)).toBeInTheDocument();
  });

  test('disables retry button when retrying', () => {
    render(
      <TestWrapper>
        <CampaignGracefulDegradation 
          {...defaultProps} 
          isRetrying={true}
        />
      </TestWrapper>
    );

    const retryButton = screen.getByText('Retrying...');
    expect(retryButton).toBeDisabled();
  });

  test('shows circuit breaker state chip', () => {
    render(
      <TestWrapper>
        <CampaignGracefulDegradation 
          {...defaultProps} 
          circuitBreakerState="OPEN"
          showAdvancedMetrics={true}
        />
      </TestWrapper>
    );

    expect(screen.getByText('OPEN')).toBeInTheDocument();
  });

  test('handles retry success notification', async () => {
    const user = userEvent.setup();
    mockOnRetry.mockResolvedValueOnce();
    
    render(
      <TestWrapper>
        <CampaignGracefulDegradation {...defaultProps} />
      </TestWrapper>
    );

    const retryButton = screen.getByText('Retry Connection');
    await user.click(retryButton);

    await waitFor(() => {
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Connection restored successfully!');
    });
  });

  test('handles retry failure notification', async () => {
    const user = userEvent.setup();
    mockOnRetry.mockRejectedValueOnce(new Error('Retry failed'));
    
    render(
      <TestWrapper>
        <CampaignGracefulDegradation {...defaultProps} />
      </TestWrapper>
    );

    const retryButton = screen.getByText('Retry Connection');
    await user.click(retryButton);

    await waitFor(() => {
      expect(mockShowErrorNotification).toHaveBeenCalledWith(expect.stringContaining('Retry failed'));
    });
  });

  test('handles service health check success', async () => {
    const user = userEvent.setup();
    mockOnServiceCheck.mockResolvedValueOnce();
    
    render(
      <TestWrapper>
        <CampaignGracefulDegradation {...defaultProps} />
      </TestWrapper>
    );

    const healthCheckButton = screen.getByLabelText('Check service health');
    await user.click(healthCheckButton);

    await waitFor(() => {
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Service health check completed');
    });
  });

  test('handles service health check failure', async () => {
    const user = userEvent.setup();
    mockOnServiceCheck.mockRejectedValueOnce(new Error('Health check failed'));
    
    render(
      <TestWrapper>
        <CampaignGracefulDegradation {...defaultProps} />
      </TestWrapper>
    );

    const healthCheckButton = screen.getByLabelText('Check service health');
    await user.click(healthCheckButton);

    await waitFor(() => {
      expect(mockShowErrorNotification).toHaveBeenCalledWith('Service health check failed');
    });
  });

  test('shows health check button when onServiceCheck is provided', () => {
    render(
      <TestWrapper>
        <CampaignGracefulDegradation {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Check service health')).toBeInTheDocument();
  });

  test('hides health check button when onServiceCheck is not provided', () => {
    render(
      <TestWrapper>
        <CampaignGracefulDegradation 
          {...defaultProps} 
          onServiceCheck={null}
        />
      </TestWrapper>
    );

    expect(screen.queryByLabelText('Check service health')).not.toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <CampaignGracefulDegradation {...defaultProps} />
      </TestWrapper>
    );

    // Check for proper ARIA labels
    expect(screen.getByLabelText('Create your first campaign by selecting a service')).toBeInTheDocument();
    expect(screen.getByLabelText('Retry connecting to backend services with intelligent backoff')).toBeInTheDocument();
    expect(screen.getByLabelText('Check service health')).toBeInTheDocument();
    expect(screen.getByRole('region', { name: 'Campaign creation guidance' })).toBeInTheDocument();
  });

  test('formats last attempt time correctly', () => {
    const justNow = new Date().toISOString();
    const oneMinuteAgo = new Date(Date.now() - 60000).toISOString();
    const fiveMinutesAgo = new Date(Date.now() - 300000).toISOString();

    const { rerender } = render(
      <TestWrapper>
        <CampaignGracefulDegradation 
          {...defaultProps} 
          lastAttempt={justNow}
        />
      </TestWrapper>
    );

    expect(screen.getByText(/Last attempt: Just now/)).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <CampaignGracefulDegradation 
          {...defaultProps} 
          lastAttempt={oneMinuteAgo}
        />
      </TestWrapper>
    );

    expect(screen.getByText(/Last attempt: 1 minute ago/)).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <CampaignGracefulDegradation 
          {...defaultProps} 
          lastAttempt={fiveMinutesAgo}
        />
      </TestWrapper>
    );

    expect(screen.getByText(/Last attempt: 5 minutes ago/)).toBeInTheDocument();
  });

  test('shows service status badge in header', () => {
    render(
      <TestWrapper>
        <CampaignGracefulDegradation {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Service status: unhealthy')).toBeInTheDocument();
  });

  test('shows getting started steps', () => {
    render(
      <TestWrapper>
        <CampaignGracefulDegradation {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Getting Started:')).toBeInTheDocument();
    expect(screen.getByText('Define your service and target market')).toBeInTheDocument();
    expect(screen.getByText('Generate AI-powered Ideal Customer Profiles (ICPs)')).toBeInTheDocument();
    expect(screen.getByText('Create campaigns based on your ICPs')).toBeInTheDocument();
    expect(screen.getByText('Generate and schedule content for your campaigns')).toBeInTheDocument();
  });
});
