// @since 2024-1-1 to 2025-25-7
import { useState, useCallback } from 'react';
import { emailTemplateEnhancedService } from '../services/emailTemplateEnhancedService';

/**
 * Custom hook for managing enhanced email template data and operations
 * Provides state management and API integration for comprehensive email management
 */
export const useEmailTemplateEnhancedData = () => {
  const [templates, setTemplates] = useState([]);
  const [campaigns, setCampaigns] = useState([]);
  const [triggers, setTriggers] = useState([]);
  const [dashboard, setDashboard] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Network status monitoring
  const handleOnline = useCallback(() => setIsOnline(true), []);
  const handleOffline = useCallback(() => setIsOnline(false), []);

  // Add event listeners for network status
  if (typeof window !== 'undefined') {
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
  }

  /**
   * Fetch all email templates with optional filtering
   */
  const fetchTemplates = useCallback(async (filters = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailTemplateEnhancedService.getTemplates(filters);
      setTemplates(response.data.templates || []);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to fetch email templates';
      setError(errorMessage);
      console.error('Error fetching email templates:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Fetch all email campaigns with optional filtering
   */
  const fetchCampaigns = useCallback(async (filters = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailTemplateEnhancedService.getCampaigns(filters);
      setCampaigns(response.data.campaigns || []);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to fetch email campaigns';
      setError(errorMessage);
      console.error('Error fetching email campaigns:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Fetch all email triggers with optional filtering
   */
  const fetchTriggers = useCallback(async (filters = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailTemplateEnhancedService.getTriggers(filters);
      setTriggers(response.data.triggers || []);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to fetch email triggers';
      setError(errorMessage);
      console.error('Error fetching email triggers:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Fetch dashboard overview data
   */
  const fetchDashboard = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailTemplateEnhancedService.getDashboard();
      setDashboard(response.data);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to fetch dashboard data';
      setError(errorMessage);
      console.error('Error fetching dashboard data:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Get a specific email template by ID
   */
  const getTemplate = useCallback(async (templateId) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailTemplateEnhancedService.getTemplate(templateId);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to fetch email template';
      setError(errorMessage);
      console.error('Error fetching email template:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Create a new email template
   */
  const createTemplate = useCallback(async (templateData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailTemplateEnhancedService.createTemplate(templateData);
      
      // Update local state
      setTemplates(prev => [response.data, ...prev]);
      
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to create email template';
      setError(errorMessage);
      console.error('Error creating email template:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Update an existing email template
   */
  const updateTemplate = useCallback(async (templateId, templateData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailTemplateEnhancedService.updateTemplate(templateId, templateData);
      
      // Update local state
      setTemplates(prev => 
        prev.map(template => 
          template.id === templateId ? response.data : template
        )
      );
      
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to update email template';
      setError(errorMessage);
      console.error('Error updating email template:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Delete an email template
   */
  const deleteTemplate = useCallback(async (templateId) => {
    setLoading(true);
    setError(null);
    
    try {
      await emailTemplateEnhancedService.deleteTemplate(templateId);
      
      // Update local state
      setTemplates(prev => prev.filter(template => template.id !== templateId));
      
      return true;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to delete email template';
      setError(errorMessage);
      console.error('Error deleting email template:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Duplicate an email template
   */
  const duplicateTemplate = useCallback(async (templateId, duplicateData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailTemplateEnhancedService.duplicateTemplate(templateId, duplicateData);
      
      // Update local state
      setTemplates(prev => [response.data, ...prev]);
      
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to duplicate email template';
      setError(errorMessage);
      console.error('Error duplicating email template:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Preview an email template
   */
  const previewTemplate = useCallback(async (templateId, templateData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailTemplateEnhancedService.previewTemplate(templateId, templateData);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to preview email template';
      setError(errorMessage);
      console.error('Error previewing email template:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Create a new email campaign
   */
  const createCampaign = useCallback(async (campaignData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailTemplateEnhancedService.createCampaign(campaignData);
      
      // Update local state
      setCampaigns(prev => [response.data, ...prev]);
      
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to create email campaign';
      setError(errorMessage);
      console.error('Error creating email campaign:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Update an existing email campaign
   */
  const updateCampaign = useCallback(async (campaignId, campaignData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailTemplateEnhancedService.updateCampaign(campaignId, campaignData);
      
      // Update local state
      setCampaigns(prev => 
        prev.map(campaign => 
          campaign.id === campaignId ? response.data : campaign
        )
      );
      
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to update email campaign';
      setError(errorMessage);
      console.error('Error updating email campaign:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Delete an email campaign
   */
  const deleteCampaign = useCallback(async (campaignId) => {
    setLoading(true);
    setError(null);
    
    try {
      await emailTemplateEnhancedService.deleteCampaign(campaignId);
      
      // Update local state
      setCampaigns(prev => prev.filter(campaign => campaign.id !== campaignId));
      
      return true;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to delete email campaign';
      setError(errorMessage);
      console.error('Error deleting email campaign:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Create a new email trigger
   */
  const createTrigger = useCallback(async (triggerData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailTemplateEnhancedService.createTrigger(triggerData);
      
      // Update local state
      setTriggers(prev => [response.data, ...prev]);
      
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to create email trigger';
      setError(errorMessage);
      console.error('Error creating email trigger:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Update an existing email trigger
   */
  const updateTrigger = useCallback(async (triggerId, triggerData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailTemplateEnhancedService.updateTrigger(triggerId, triggerData);
      
      // Update local state
      setTriggers(prev => 
        prev.map(trigger => 
          trigger.id === triggerId ? response.data : trigger
        )
      );
      
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to update email trigger';
      setError(errorMessage);
      console.error('Error updating email trigger:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Delete an email trigger
   */
  const deleteTrigger = useCallback(async (triggerId) => {
    setLoading(true);
    setError(null);
    
    try {
      await emailTemplateEnhancedService.deleteTrigger(triggerId);
      
      // Update local state
      setTriggers(prev => prev.filter(trigger => trigger.id !== triggerId));
      
      return true;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to delete email trigger';
      setError(errorMessage);
      console.error('Error deleting email trigger:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Refresh all data
   */
  const refreshData = useCallback(async () => {
    try {
      await Promise.all([
        fetchTemplates(),
        fetchCampaigns(),
        fetchTriggers(),
        fetchDashboard()
      ]);
    } catch (err) {
      console.error('Error refreshing data:', err);
      throw err;
    }
  }, [fetchTemplates, fetchCampaigns, fetchTriggers, fetchDashboard]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Reset all state
   */
  const reset = useCallback(() => {
    setTemplates([]);
    setCampaigns([]);
    setTriggers([]);
    setDashboard(null);
    setLoading(false);
    setError(null);
  }, []);

  return {
    // State
    templates,
    campaigns,
    triggers,
    dashboard,
    loading,
    error,
    isOnline,
    
    // Template Actions
    fetchTemplates,
    getTemplate,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    duplicateTemplate,
    previewTemplate,
    
    // Campaign Actions
    fetchCampaigns,
    createCampaign,
    updateCampaign,
    deleteCampaign,
    
    // Trigger Actions
    fetchTriggers,
    createTrigger,
    updateTrigger,
    deleteTrigger,
    
    // Dashboard Actions
    fetchDashboard,
    
    // Utility Actions
    refreshData,
    clearError,
    reset,
    
    // Computed values
    totalTemplates: templates.length,
    activeTemplates: templates.filter(t => t.status === 'active').length,
    draftTemplates: templates.filter(t => t.status === 'draft').length,
    totalCampaigns: campaigns.length,
    activeCampaigns: campaigns.filter(c => c.status === 'running').length,
    totalTriggers: triggers.length,
    activeTriggers: triggers.filter(t => t.is_active).length,
    hasTemplates: templates.length > 0,
    hasCampaigns: campaigns.length > 0,
    hasTriggers: triggers.length > 0,
    hasError: !!error
  };
};
