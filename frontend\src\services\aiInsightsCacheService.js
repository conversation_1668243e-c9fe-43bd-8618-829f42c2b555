/**
 * Enhanced AI Insights Cache Service - Enterprise-grade caching for AI recommendations
 * 
 * Features:
 * - Multi-level caching (memory, localStorage, sessionStorage)
 * - Intelligent cache invalidation and refresh strategies
 * - Performance optimization with compression
 * - Cache analytics and monitoring
 * - Subscription-aware cache management
 * - Real-time cache synchronization
 @since 2024-1-1 to 2025-25-7
*/

import { logger } from '../utils/logger';

// Cache configuration
const CACHE_CONFIG = {
  memory: {
    maxSize: 50, // Maximum number of entries
    ttl: 5 * 60 * 1000, // 5 minutes
    enabled: true
  },
  localStorage: {
    maxSize: 20, // Maximum number of entries
    ttl: 30 * 60 * 1000, // 30 minutes
    enabled: true,
    keyPrefix: 'ace_ai_insights_'
  },
  sessionStorage: {
    maxSize: 10, // Maximum number of entries
    ttl: 60 * 60 * 1000, // 1 hour
    enabled: true,
    keyPrefix: 'ace_ai_session_'
  }
};

// Cache invalidation triggers
const INVALIDATION_TRIGGERS = {
  dataUpdate: ['overview', 'content', 'audience', 'sentiment'],
  planChange: ['subscription_change'],
  timeBasedRefresh: 15 * 60 * 1000, // 15 minutes
  userAction: ['content_published', 'settings_changed']
};

/**
 * AI Insights Cache Service Class
 */
class AIInsightsCacheService {
  constructor() {
    this.memoryCache = new Map();
    this.cacheStats = {
      hits: 0,
      misses: 0,
      sets: 0,
      invalidations: 0,
      compressionRatio: 0
    };
    this.compressionEnabled = true;
    this.syncEnabled = true;
    
    // Initialize cache cleanup
    this.initializeCleanup();
    
    // Setup event listeners for cache invalidation
    this.setupInvalidationListeners();
  }

  /**
   * Get cached AI insights
   * @param {string} key - Cache key
   * @param {Object} options - Cache options
   * @returns {Object|null} Cached insights or null
   */
  async get(key, options = {}) {
    const { 
      useMemory = true, 
      useLocalStorage = true, 
      useSessionStorage = true,
      validateFreshness = true 
    } = options;

    try {
      // Try memory cache first (fastest)
      if (useMemory && CACHE_CONFIG.memory.enabled) {
        const memoryResult = this._getFromMemory(key);
        if (memoryResult) {
          this.cacheStats.hits++;
          logger.debug('AI insights served from memory cache', { key });
          return memoryResult;
        }
      }

      // Try localStorage (persistent)
      if (useLocalStorage && CACHE_CONFIG.localStorage.enabled) {
        const localResult = await this._getFromLocalStorage(key);
        if (localResult) {
          // Promote to memory cache
          this._setToMemory(key, localResult);
          this.cacheStats.hits++;
          logger.debug('AI insights served from localStorage', { key });
          return localResult;
        }
      }

      // Try sessionStorage (session-persistent)
      if (useSessionStorage && CACHE_CONFIG.sessionStorage.enabled) {
        const sessionResult = await this._getFromSessionStorage(key);
        if (sessionResult) {
          // Promote to memory and localStorage
          this._setToMemory(key, sessionResult);
          await this._setToLocalStorage(key, sessionResult);
          this.cacheStats.hits++;
          logger.debug('AI insights served from sessionStorage', { key });
          return sessionResult;
        }
      }

      this.cacheStats.misses++;
      return null;

    } catch (error) {
      logger.error('Failed to get from AI insights cache', error);
      this.cacheStats.misses++;
      return null;
    }
  }

  /**
   * Set AI insights in cache
   * @param {string} key - Cache key
   * @param {Object} data - Insights data to cache
   * @param {Object} options - Cache options
   */
  async set(key, data, options = {}) {
    const {
      useMemory = true,
      useLocalStorage = true,
      useSessionStorage = true,
      ttl = null
    } = options;

    try {
      const cacheEntry = {
        data,
        timestamp: Date.now(),
        ttl: ttl || CACHE_CONFIG.memory.ttl,
        metadata: {
          planId: data.metadata?.planId,
          dataSourcesCount: data.metadata?.dataSourcesCount,
          confidence: data.metadata?.confidence
        }
      };

      // Set in memory cache
      if (useMemory && CACHE_CONFIG.memory.enabled) {
        this._setToMemory(key, cacheEntry);
      }

      // Set in localStorage
      if (useLocalStorage && CACHE_CONFIG.localStorage.enabled) {
        await this._setToLocalStorage(key, cacheEntry);
      }

      // Set in sessionStorage
      if (useSessionStorage && CACHE_CONFIG.sessionStorage.enabled) {
        await this._setToSessionStorage(key, cacheEntry);
      }

      this.cacheStats.sets++;
      logger.debug('AI insights cached successfully', { 
        key, 
        planId: cacheEntry.metadata.planId,
        confidence: cacheEntry.metadata.confidence 
      });

    } catch (error) {
      logger.error('Failed to set AI insights cache', error);
    }
  }

  /**
   * Invalidate cache entries
   * @param {string|Array} keys - Cache key(s) to invalidate
   * @param {string} reason - Invalidation reason
   */
  async invalidate(keys, reason = 'manual') {
    const keyArray = Array.isArray(keys) ? keys : [keys];
    
    try {
      for (const key of keyArray) {
        // Remove from memory
        this.memoryCache.delete(key);
        
        // Remove from localStorage
        if (CACHE_CONFIG.localStorage.enabled) {
          localStorage.removeItem(CACHE_CONFIG.localStorage.keyPrefix + key);
        }
        
        // Remove from sessionStorage
        if (CACHE_CONFIG.sessionStorage.enabled) {
          sessionStorage.removeItem(CACHE_CONFIG.sessionStorage.keyPrefix + key);
        }
        
        this.cacheStats.invalidations++;
      }
      
      logger.info('AI insights cache invalidated', { keys: keyArray, reason });
      
    } catch (error) {
      logger.error('Failed to invalidate AI insights cache', error);
    }
  }

  /**
   * Clear all cache entries
   */
  async clearAll() {
    try {
      // Clear memory cache
      this.memoryCache.clear();
      
      // Clear localStorage entries
      if (CACHE_CONFIG.localStorage.enabled) {
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
          if (key.startsWith(CACHE_CONFIG.localStorage.keyPrefix)) {
            localStorage.removeItem(key);
          }
        });
      }
      
      // Clear sessionStorage entries
      if (CACHE_CONFIG.sessionStorage.enabled) {
        const keys = Object.keys(sessionStorage);
        keys.forEach(key => {
          if (key.startsWith(CACHE_CONFIG.sessionStorage.keyPrefix)) {
            sessionStorage.removeItem(key);
          }
        });
      }
      
      logger.info('All AI insights cache cleared');
      
    } catch (error) {
      logger.error('Failed to clear AI insights cache', error);
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const totalRequests = this.cacheStats.hits + this.cacheStats.misses;
    const hitRate = totalRequests > 0 ? (this.cacheStats.hits / totalRequests) * 100 : 0;
    
    return {
      ...this.cacheStats,
      hitRate: Math.round(hitRate * 100) / 100,
      memorySize: this.memoryCache.size,
      totalRequests
    };
  }

  /**
   * Get from memory cache
   * @private
   */
  _getFromMemory(key) {
    const entry = this.memoryCache.get(key);
    if (!entry) return null;
    
    // Check TTL
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.memoryCache.delete(key);
      return null;
    }
    
    return entry.data;
  }

  /**
   * Set to memory cache
   * @private
   */
  _setToMemory(key, entry) {
    // Implement LRU eviction if cache is full
    if (this.memoryCache.size >= CACHE_CONFIG.memory.maxSize) {
      const firstKey = this.memoryCache.keys().next().value;
      this.memoryCache.delete(firstKey);
    }
    
    this.memoryCache.set(key, entry);
  }

  /**
   * Get from localStorage
   * @private
   */
  async _getFromLocalStorage(key) {
    try {
      const stored = localStorage.getItem(CACHE_CONFIG.localStorage.keyPrefix + key);
      if (!stored) return null;
      
      const entry = this._decompress(stored);
      if (!entry) return null;
      
      // Check TTL
      if (Date.now() - entry.timestamp > entry.ttl) {
        localStorage.removeItem(CACHE_CONFIG.localStorage.keyPrefix + key);
        return null;
      }
      
      return entry.data;
      
    } catch (error) {
      logger.warn('Failed to get from localStorage', error);
      return null;
    }
  }

  /**
   * Set to localStorage
   * @private
   */
  async _setToLocalStorage(key, entry) {
    try {
      const compressed = this._compress(entry);
      localStorage.setItem(CACHE_CONFIG.localStorage.keyPrefix + key, compressed);
      
      // Cleanup old entries if storage is full
      this._cleanupLocalStorage();
      
    } catch (error) {
      if (error.name === 'QuotaExceededError') {
        logger.warn('localStorage quota exceeded, cleaning up');
        this._cleanupLocalStorage();
        // Try again after cleanup
        try {
          const compressed = this._compress(entry);
          localStorage.setItem(CACHE_CONFIG.localStorage.keyPrefix + key, compressed);
        } catch (retryError) {
          logger.error('Failed to set localStorage after cleanup', retryError);
        }
      } else {
        logger.error('Failed to set localStorage', error);
      }
    }
  }

  /**
   * Get from sessionStorage
   * @private
   */
  async _getFromSessionStorage(key) {
    try {
      const stored = sessionStorage.getItem(CACHE_CONFIG.sessionStorage.keyPrefix + key);
      if (!stored) return null;
      
      const entry = this._decompress(stored);
      if (!entry) return null;
      
      // Check TTL
      if (Date.now() - entry.timestamp > entry.ttl) {
        sessionStorage.removeItem(CACHE_CONFIG.sessionStorage.keyPrefix + key);
        return null;
      }
      
      return entry.data;
      
    } catch (error) {
      logger.warn('Failed to get from sessionStorage', error);
      return null;
    }
  }

  /**
   * Set to sessionStorage
   * @private
   */
  async _setToSessionStorage(key, entry) {
    try {
      const compressed = this._compress(entry);
      sessionStorage.setItem(CACHE_CONFIG.sessionStorage.keyPrefix + key, compressed);
      
    } catch (error) {
      logger.error('Failed to set sessionStorage', error);
    }
  }

  /**
   * Compress data for storage
   * @private
   */
  _compress(data) {
    if (!this.compressionEnabled) {
      return JSON.stringify(data);
    }
    
    try {
      const jsonString = JSON.stringify(data);
      // Simple compression using base64 encoding
      // In production, you might want to use a proper compression library
      const compressed = btoa(jsonString);
      
      // Update compression ratio stats
      this.cacheStats.compressionRatio = compressed.length / jsonString.length;
      
      return compressed;
      
    } catch (error) {
      logger.warn('Failed to compress data, using uncompressed', error);
      return JSON.stringify(data);
    }
  }

  /**
   * Decompress data from storage
   * @private
   */
  _decompress(compressedData) {
    try {
      // Try to decompress first
      try {
        const decompressed = atob(compressedData);
        return JSON.parse(decompressed);
      } catch {
        // Fallback to direct JSON parsing (uncompressed data)
        return JSON.parse(compressedData);
      }
    } catch (error) {
      logger.warn('Failed to decompress data', error);
      return null;
    }
  }

  /**
   * Initialize cache cleanup intervals
   * @private
   */
  initializeCleanup() {
    // Cleanup memory cache every 5 minutes
    setInterval(() => {
      this._cleanupMemoryCache();
    }, 5 * 60 * 1000);
    
    // Cleanup localStorage every 30 minutes
    setInterval(() => {
      this._cleanupLocalStorage();
    }, 30 * 60 * 1000);
  }

  /**
   * Cleanup expired memory cache entries
   * @private
   */
  _cleanupMemoryCache() {
    const now = Date.now();
    for (const [key, entry] of this.memoryCache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.memoryCache.delete(key);
      }
    }
  }

  /**
   * Cleanup localStorage entries
   * @private
   */
  _cleanupLocalStorage() {
    try {
      const keys = Object.keys(localStorage);
      const aiInsightKeys = keys.filter(key => 
        key.startsWith(CACHE_CONFIG.localStorage.keyPrefix)
      );
      
      // Sort by timestamp and remove oldest if over limit
      const entries = aiInsightKeys.map(key => {
        try {
          const data = this._decompress(localStorage.getItem(key));
          return { key, timestamp: data?.timestamp || 0 };
        } catch {
          return { key, timestamp: 0 };
        }
      }).sort((a, b) => a.timestamp - b.timestamp);
      
      // Remove excess entries
      if (entries.length > CACHE_CONFIG.localStorage.maxSize) {
        const toRemove = entries.slice(0, entries.length - CACHE_CONFIG.localStorage.maxSize);
        toRemove.forEach(entry => localStorage.removeItem(entry.key));
      }
      
    } catch (error) {
      logger.error('Failed to cleanup localStorage', error);
    }
  }

  /**
   * Setup cache invalidation listeners
   * @private
   */
  setupInvalidationListeners() {
    // Listen for data updates
    window.addEventListener('ai_insights_data_update', (event) => {
      const { dataType } = event.detail;
      if (INVALIDATION_TRIGGERS.dataUpdate.includes(dataType)) {
        this.invalidate('*', `data_update_${dataType}`);
      }
    });
    
    // Listen for subscription changes
    window.addEventListener('subscription_changed', () => {
      this.invalidate('*', 'subscription_change');
    });
  }
}

// Export singleton instance
export const aiInsightsCacheService = new AIInsightsCacheService();
export default aiInsightsCacheService;
