#!/usr/bin/env python3
"""
Email Service Comprehensive Verification Script

This script performs comprehensive testing of the email service functionality,
including configuration, template integration, trigger verification, and
production readiness assessment.

Usage:
    python scripts/verify_email_service.py

@since 2024-1-1 to 2025-25-7
"""
import asyncio
import sys
import os
import inspect
from typing import List, Dict, Any
from datetime import datetime
import traceback

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.core.config import settings
from app.services.email_service import EmailService, email_service
from app.services.email_service import (
    send_welcome_email,
    send_password_reset_email,
    send_magic_link_email,
    send_email_verification,
    send_account_lockout_email,
    send_calendar_invitation_email,
    send_trial_started_email,
    send_trial_ending_soon_email,
    send_trial_ended_email,
    send_team_invitation_email,
    send_subscription_updated_email,
    send_admin_notification_email
)


class EmailServiceVerifier:
    """Comprehensive verification of email service implementation."""
    
    def __init__(self):
        self.results: List[Dict[str, Any]] = []
        self.email_service = EmailService()
        self.template_path = os.path.join(os.path.dirname(__file__), '..', 'app', 'templates', 'emails')
    
    def log_result(self, check_name: str, status: str, message: str, details: Dict | None = None):
        """Log verification result."""
        result = {
            "check": check_name,
            "status": status,  # "PASS", "FAIL", "WARNING"
            "message": message,
            "details": details or {},
            "timestamp": datetime.now().isoformat()
        }
        self.results.append(result)
        
        # Color coding for console output
        color = {
            "PASS": "\033[92m",    # Green
            "FAIL": "\033[91m",    # Red
            "WARNING": "\033[93m", # Yellow
        }.get(status, "\033[0m")
        
        reset_color = "\033[0m"
        print(f"[{color}{status}{reset_color}] {check_name}: {message}")
        
        if details:
            for key, value in details.items():
                print(f"  {key}: {value}")
    
    def check_email_service_configuration(self):
        """Check EmailService class configuration."""
        try:
            # Test EmailService initialization
            service = EmailService()
            
            # Check required configuration attributes
            required_attrs = ['smtp_host', 'smtp_port', 'smtp_user', 'smtp_password', 'from_email', 'from_name', 'use_tls']
            missing_attrs = []
            
            for attr in required_attrs:
                if not hasattr(service, attr):
                    missing_attrs.append(attr)
            
            if missing_attrs:
                self.log_result(
                    "EmailService Configuration",
                    "FAIL",
                    f"Missing required attributes: {', '.join(missing_attrs)}"
                )
            else:
                self.log_result(
                    "EmailService Configuration",
                    "PASS",
                    "EmailService properly configured with all required attributes",
                    {
                        "smtp_host": service.smtp_host or "Not configured",
                        "smtp_port": service.smtp_port or "Not configured",
                        "from_email": service.from_email or "Not configured",
                        "use_tls": service.use_tls
                    }
                )
                
        except Exception as e:
            self.log_result(
                "EmailService Configuration",
                "FAIL",
                f"Failed to initialize EmailService: {str(e)}"
            )
    
    def check_email_functions_implementation(self):
        """Check all email functions are properly implemented."""
        email_functions = [
            send_welcome_email,
            send_password_reset_email,
            send_magic_link_email,
            send_email_verification,
            send_account_lockout_email,
            send_calendar_invitation_email,
            send_trial_started_email,
            send_trial_ending_soon_email,
            send_trial_ended_email,
            send_team_invitation_email,
            send_subscription_updated_email,
            send_admin_notification_email
        ]
        
        implemented_functions = []
        failed_functions = []
        
        for func in email_functions:
            try:
                # Check if function is callable and has proper signature
                if callable(func):
                    sig = inspect.signature(func)
                    implemented_functions.append(f"{func.__name__}({', '.join(sig.parameters.keys())})")
                else:
                    failed_functions.append(func.__name__)
            except Exception as e:
                failed_functions.append(f"{func.__name__}: {str(e)}")
        
        if failed_functions:
            self.log_result(
                "Email Functions Implementation",
                "FAIL",
                f"Failed functions: {', '.join(failed_functions)}"
            )
        else:
            self.log_result(
                "Email Functions Implementation",
                "PASS",
                f"All {len(implemented_functions)} email functions properly implemented",
                {"functions": implemented_functions}
            )
    
    def check_email_templates_exist(self):
        """Check that all referenced email templates exist."""
        expected_templates = [
            'welcome.html',
            'password_reset.html',
            'magic_link.html',
            'email_verification.html',
            'account_lockout.html',
            'calendar_invitation.html',
            'trial_started.html',
            'trial_ending_soon.html',
            'trial_ended.html',
            'team_invitation.html',
            'subscription_updated.html',
            'admin_notification.html',
            'payment_failed.html',
            'payment_succeeded.html',
            'subscription_created.html',
            'subscription_canceled.html'
        ]
        
        existing_templates = []
        missing_templates = []
        
        for template in expected_templates:
            template_path = os.path.join(self.template_path, template)
            if os.path.exists(template_path):
                existing_templates.append(template)
            else:
                missing_templates.append(template)
        
        if missing_templates:
            self.log_result(
                "Email Templates Existence",
                "WARNING",
                f"Missing templates: {', '.join(missing_templates)}",
                {"existing": len(existing_templates), "missing": len(missing_templates)}
            )
        else:
            self.log_result(
                "Email Templates Existence",
                "PASS",
                f"All {len(existing_templates)} email templates found",
                {"templates_path": self.template_path}
            )
    
    async def test_email_function_calls(self):
        """Test email function calls with sample data."""
        test_cases = [
            {
                "function": send_welcome_email,
                "args": ["<EMAIL>", "Test User"],
                "name": "Welcome Email"
            },
            {
                "function": send_password_reset_email,
                "args": ["<EMAIL>", "Test User", "test_token_123"],
                "name": "Password Reset Email"
            },
            {
                "function": send_trial_started_email,
                "args": ["<EMAIL>", "Test User", "2025-08-01"],
                "name": "Trial Started Email"
            },
            {
                "function": send_team_invitation_email,
                "args": ["<EMAIL>", "Inviter Name", "Test Team", "invitation_token_123"],
                "name": "Team Invitation Email"
            },
            {
                "function": send_admin_notification_email,
                "args": ["Test Subject", "Test message content"],
                "name": "Admin Notification Email"
            }
        ]
        
        successful_calls = []
        failed_calls = []
        
        for test_case in test_cases:
            try:
                result = await test_case["function"](*test_case["args"])
                if result:
                    successful_calls.append(test_case["name"])
                else:
                    failed_calls.append(f"{test_case['name']}: Returned False")
            except Exception as e:
                failed_calls.append(f"{test_case['name']}: {str(e)}")
        
        if failed_calls:
            self.log_result(
                "Email Function Calls",
                "FAIL",
                f"Failed calls: {', '.join(failed_calls)}",
                {"successful": len(successful_calls), "failed": len(failed_calls)}
            )
        else:
            self.log_result(
                "Email Function Calls",
                "PASS",
                f"All {len(successful_calls)} email function calls successful",
                {"tested_functions": successful_calls}
            )
    
    def check_google_oauth_email_integration(self):
        """Check Google OAuth email integration."""
        try:
            # Check if Google OAuth routes import email functions correctly
            from app.api.routes.auth import send_welcome_email as oauth_welcome_email
            
            self.log_result(
                "Google OAuth Email Integration",
                "PASS",
                "Google OAuth routes successfully import email functions"
            )
        except ImportError as e:
            self.log_result(
                "Google OAuth Email Integration",
                "FAIL",
                f"Failed to import email functions in OAuth routes: {str(e)}"
            )
        except Exception as e:
            self.log_result(
                "Google OAuth Email Integration",
                "WARNING",
                f"Unexpected error checking OAuth integration: {str(e)}"
            )
    
    def generate_report(self):
        """Generate comprehensive verification report."""
        total_checks = len(self.results)
        passed_checks = len([r for r in self.results if r["status"] == "PASS"])
        failed_checks = len([r for r in self.results if r["status"] == "FAIL"])
        warning_checks = len([r for r in self.results if r["status"] == "WARNING"])
        
        print("\n" + "="*60)
        print("EMAIL SERVICE VERIFICATION REPORT")
        print("="*60)
        print(f"Total Checks: {total_checks}")
        print(f"Passed: {passed_checks}")
        print(f"Failed: {failed_checks}")
        print(f"Warnings: {warning_checks}")
        print()
        
        if failed_checks == 0:
            print("✅ EMAIL SERVICE: PRODUCTION READY")
        else:
            print("❌ EMAIL SERVICE: ISSUES FOUND")
            print("Please fix the failed checks before deploying to production.")
        
        return failed_checks == 0


async def main():
    """Main verification function."""
    print("Starting Email Service Comprehensive Verification...")
    print(f"Timestamp: {datetime.now().isoformat()}")
    print()
    
    verifier = EmailServiceVerifier()
    
    # Run all verification checks
    print("=== Email Service Configuration ===")
    verifier.check_email_service_configuration()
    
    print("\n=== Email Functions Implementation ===")
    verifier.check_email_functions_implementation()
    
    print("\n=== Email Templates ===")
    verifier.check_email_templates_exist()
    
    print("\n=== Email Function Calls ===")
    await verifier.test_email_function_calls()
    
    print("\n=== Google OAuth Integration ===")
    verifier.check_google_oauth_email_integration()
    
    # Generate final report
    success = verifier.generate_report()
    
    return 0 if success else 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nVerification interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error during verification: {str(e)}")
        traceback.print_exc()
        sys.exit(1)
