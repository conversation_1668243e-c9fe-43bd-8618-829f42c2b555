// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import BrandTypographyView from '../BrandTypographyView';

// Mock the notification hook
const mockShowSuccessNotification = vi.fn();
const mockShowErrorNotification = vi.fn();

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showSuccessNotification: mockShowSuccessNotification,
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Mock the font upload API
vi.mock('../../../api/fontUpload', () => ({
  loadFontInBrowser: vi.fn(),
}));

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme();
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('BrandTypographyView', () => {
  const mockTypography = {
    primary_font: 'Inter',
    secondary_font: 'Roboto',
    heading_font: 'Inter',
    body_font: 'Roboto',
    style: 'professional',
    custom_fonts: [
      {
        name: 'CustomFont',
        url: 'https://example.com/font.woff2',
        file_name: 'custom.woff2',
        size: 50000,
        uploaded_at: '2024-01-01T00:00:00Z'
      }
    ]
  };

  const mockProps = {
    typography: mockTypography,
    onError: vi.fn(),
    onFontLoad: vi.fn(),
    onExport: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Setup DOM for tests
    document.body.innerHTML = '';
    const div = document.createElement('div');
    div.setAttribute('id', 'root');
    document.body.appendChild(div);
  });

  afterEach(() => {
    vi.restoreAllMocks();
    document.body.innerHTML = '';
  });

  test('renders typography view correctly', () => {
    render(
      <TestWrapper>
        <BrandTypographyView {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Typography Settings')).toBeInTheDocument();
    expect(screen.getByText('Main Fonts')).toBeInTheDocument();
    expect(screen.getByText('Specific Usage')).toBeInTheDocument();
    expect(screen.getByText('Typography Preview')).toBeInTheDocument();
  });

  test('displays primary and secondary fonts correctly', () => {
    render(
      <TestWrapper>
        <BrandTypographyView {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getAllByText('Inter')).toHaveLength(2); // Primary and heading fonts
    expect(screen.getAllByText('Roboto')).toHaveLength(2); // Secondary and body fonts
    expect(screen.getByText('Professional')).toBeInTheDocument();
  });

  test('shows placeholder when no typography provided', () => {
    render(
      <TestWrapper>
        <BrandTypographyView typography={null} />
      </TestWrapper>
    );

    expect(screen.getByText('No typography settings defined')).toBeInTheDocument();
    expect(screen.getByText('Configure typography settings to see the preview')).toBeInTheDocument();
  });

  test('displays custom fonts with loading status', () => {
    render(
      <TestWrapper>
        <BrandTypographyView {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Custom Fonts (1)')).toBeInTheDocument();
    expect(screen.getByText('CustomFont')).toBeInTheDocument();
    expect(screen.getByText('https://example.com/font.woff2')).toBeInTheDocument();
  });

  test('handles font refresh functionality', async () => {
    const { loadFontInBrowser } = await import('../../../api/fontUpload');
    loadFontInBrowser.mockResolvedValue();

    const user = userEvent.setup();

    const { container } = render(
      <TestWrapper>
        <BrandTypographyView {...mockProps} />
      </TestWrapper>,
      { container: document.getElementById('root') }
    );

    const refreshButton = screen.getByLabelText('Refresh fonts');
    await user.click(refreshButton);

    await waitFor(() => {
      expect(loadFontInBrowser).toHaveBeenCalledWith('CustomFont', 'https://example.com/font.woff2');
    });
  });

  test('handles export functionality', async () => {
    // Mock URL.createObjectURL and related APIs
    global.URL.createObjectURL = vi.fn(() => 'mock-url');
    global.URL.revokeObjectURL = vi.fn();

    const mockLink = {
      href: '',
      download: '',
      click: vi.fn(),
    };

    vi.spyOn(document, 'createElement').mockReturnValue(mockLink);
    vi.spyOn(document.body, 'appendChild').mockImplementation(() => {});
    vi.spyOn(document.body, 'removeChild').mockImplementation(() => {});

    const user = userEvent.setup();

    const { container } = render(
      <TestWrapper>
        <BrandTypographyView {...mockProps} />
      </TestWrapper>,
      { container: document.getElementById('root') }
    );

    const exportButton = screen.getByLabelText('Export typography settings');
    await user.click(exportButton);

    await waitFor(() => {
      expect(mockProps.onExport).toHaveBeenCalled();
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Typography settings exported successfully');
    });
  });

  test('opens fullscreen preview dialog', async () => {
    const user = userEvent.setup();

    const { container } = render(
      <TestWrapper>
        <BrandTypographyView {...mockProps} />
      </TestWrapper>,
      { container: document.getElementById('root') }
    );

    const fullscreenButton = screen.getByLabelText('Open fullscreen preview');
    await user.click(fullscreenButton);

    await waitFor(() => {
      expect(screen.getByText('Typography Showcase')).toBeInTheDocument();
    });
  });

  test('handles preview settings changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandTypographyView {...mockProps} />
      </TestWrapper>
    );

    // Test grid toggle
    const gridSwitch = screen.getByRole('checkbox', { name: /grid/i });
    await user.click(gridSwitch);

    // Test dark mode toggle
    const darkModeSwitch = screen.getByRole('checkbox', { name: /dark/i });
    await user.click(darkModeSwitch);

    // Verify switches are checked
    expect(gridSwitch).toBeChecked();
    expect(darkModeSwitch).toBeChecked();
  });

  test('handles font loading errors gracefully', async () => {
    const { loadFontInBrowser } = await import('../../../api/fontUpload');
    loadFontInBrowser.mockRejectedValue(new Error('Font load failed'));

    render(
      <TestWrapper>
        <BrandTypographyView {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockShowErrorNotification).toHaveBeenCalledWith('Failed to load 1 custom font(s)');
    });
  });

  test('displays different typography styles correctly', () => {
    const elegantTypography = {
      ...mockTypography,
      style: 'elegant'
    };

    render(
      <TestWrapper>
        <BrandTypographyView typography={elegantTypography} />
      </TestWrapper>
    );

    expect(screen.getByText('Elegant')).toBeInTheDocument();
  });

  test('handles disabled state correctly', () => {
    render(
      <TestWrapper>
        <BrandTypographyView {...mockProps} disabled />
      </TestWrapper>
    );

    const refreshButton = screen.getByLabelText('Refresh fonts');
    const exportButton = screen.getByLabelText('Export typography settings');
    const fullscreenButton = screen.getByLabelText('Open fullscreen preview');

    expect(refreshButton).toBeDisabled();
    expect(exportButton).toBeDisabled();
    expect(fullscreenButton).toBeDisabled();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <BrandTypographyView {...mockProps} />
      </TestWrapper>
    );

    // Check for proper ARIA labels
    expect(screen.getByLabelText('Refresh fonts')).toBeInTheDocument();
    expect(screen.getByLabelText('Export typography settings')).toBeInTheDocument();
    expect(screen.getByLabelText('Open fullscreen preview')).toBeInTheDocument();

    // Check for proper headings
    const headings = screen.getAllByRole('heading');
    expect(headings.length).toBeGreaterThan(0);
  });

  test('handles preview without showPreview prop', () => {
    render(
      <TestWrapper>
        <BrandTypographyView {...mockProps} showPreview={false} />
      </TestWrapper>
    );

    expect(screen.queryByText('Typography Preview')).not.toBeInTheDocument();
  });

  test('handles typography without custom fonts', () => {
    const typographyWithoutCustomFonts = {
      ...mockTypography,
      custom_fonts: []
    };

    render(
      <TestWrapper>
        <BrandTypographyView typography={typographyWithoutCustomFonts} />
      </TestWrapper>
    );

    expect(screen.queryByText('Custom Fonts')).not.toBeInTheDocument();
  });
});
