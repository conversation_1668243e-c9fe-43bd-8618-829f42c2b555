// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import BrandVoiceEditor from '../BrandVoiceEditor';

// Mock the notification hook
const mockShowSuccessNotification = vi.fn();
const mockShowErrorNotification = vi.fn();

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showSuccessNotification: mockShowSuccessNotification,
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#1976d2',
      },
      secondary: {
        main: '#dc004e',
      },
      error: {
        main: '#d32f2f',
      },
      warning: {
        main: '#ed6c02',
      },
      success: {
        main: '#2e7d32',
      },
      grey: {
        300: '#e0e0e0',
        400: '#bdbdbd',
        500: '#9e9e9e',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('BrandVoiceEditor', () => {
  const mockBrandVoice = {
    tone: 'professional',
    personality_traits: ['confident', 'helpful', 'innovative'],
    writing_style: 'clear and concise',
    vocabulary: ['solution', 'partnership', 'excellence'],
    phrases_to_use: ['We\'re here to help', 'Let\'s solve this together'],
    phrases_to_avoid: ['We can\'t', 'That\'s not possible']
  };

  const mockProps = {
    brandVoice: mockBrandVoice,
    onChange: vi.fn(),
    onError: vi.fn(),
    onExport: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders brand voice editor correctly', () => {
    render(
      <TestWrapper>
        <BrandVoiceEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Brand Voice Editor')).toBeInTheDocument();
    expect(screen.getByText('Tone and Style')).toBeInTheDocument();
    expect(screen.getByText('Personality Traits')).toBeInTheDocument();
    expect(screen.getByText('Brand Vocabulary & Phrases')).toBeInTheDocument();
  });

  test('displays voice completeness indicator', () => {
    render(
      <TestWrapper>
        <BrandVoiceEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Voice Completeness')).toBeInTheDocument();
    expect(screen.getByText('100%')).toBeInTheDocument(); // All fields are filled
  });

  test('shows placeholder when no brand voice provided', () => {
    render(
      <TestWrapper>
        <BrandVoiceEditor brandVoice={null} onChange={vi.fn()} />
      </TestWrapper>
    );

    expect(screen.getByText('No brand voice defined')).toBeInTheDocument();
    expect(screen.getByText('Configure brand voice settings to establish your communication style')).toBeInTheDocument();
  });

  test('handles tone change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVoiceEditor {...mockProps} />
      </TestWrapper>
    );

    // Expand tone section
    const toneAccordion = screen.getByText('Tone Configuration');
    await user.click(toneAccordion);

    // Change tone
    const toneSelect = screen.getByRole('combobox', { name: /brand tone/i });
    await user.click(toneSelect);
    
    const friendlyOption = screen.getByText('Friendly');
    await user.click(friendlyOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockBrandVoice,
        tone: 'friendly'
      });
    });
  });

  test('handles writing style change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVoiceEditor {...mockProps} />
      </TestWrapper>
    );

    // Expand tone section
    const toneAccordion = screen.getByText('Tone Configuration');
    await user.click(toneAccordion);

    // Change writing style
    const styleSelect = screen.getByRole('combobox', { name: /writing style/i });
    await user.click(styleSelect);
    
    const storytellingOption = screen.getByText('Storytelling');
    await user.click(storytellingOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockBrandVoice,
        writing_style: 'storytelling'
      });
    });
  });

  test('adds and removes personality traits', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVoiceEditor {...mockProps} />
      </TestWrapper>
    );

    // Expand traits section
    const traitsAccordion = screen.getByText('Trait Management');
    await user.click(traitsAccordion);

    // Add custom trait
    const traitInput = screen.getByPlaceholderText('Add custom trait');
    await user.type(traitInput, 'creative');
    
    const addButton = screen.getByRole('button', { name: /add/i });
    await user.click(addButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockBrandVoice,
        personality_traits: [...mockBrandVoice.personality_traits, 'creative']
      });
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Added trait: creative');
    });
  });

  test('handles vocabulary management', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <BrandVoiceEditor {...mockProps} />
      </TestWrapper>
    );

    // Switch to vocabulary tab
    const vocabularyTab = screen.getByText('Vocabulary');
    await user.click(vocabularyTab);

    // Add vocabulary word
    const wordInput = screen.getByPlaceholderText('Add word or term');
    await user.type(wordInput, 'innovation');

    // Find the specific add button for vocabulary (not disabled)
    const addButtons = screen.getAllByRole('button', { name: /add/i });
    const enabledAddButton = addButtons.find(button => !button.disabled);
    await user.click(enabledAddButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockBrandVoice,
        vocabulary: [...mockBrandVoice.vocabulary, 'innovation']
      });
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Added vocabulary: innovation');
    });
  });

  test('handles phrases to use management', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <BrandVoiceEditor {...mockProps} />
      </TestWrapper>
    );

    // Switch to phrases to use tab
    const phrasesToUseTab = screen.getByText('Phrases to Use');
    await user.click(phrasesToUseTab);

    // Add phrase to use
    const phraseInput = screen.getByPlaceholderText('Add phrase to use');
    await user.type(phraseInput, 'We\'re excited to help');

    // Find the specific add button for phrases to use (success color)
    const addButtons = screen.getAllByRole('button', { name: /add/i });
    const successAddButton = addButtons.find(button =>
      button.className.includes('colorSuccess') && !button.disabled
    );
    await user.click(successAddButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockBrandVoice,
        phrases_to_use: [...mockBrandVoice.phrases_to_use, 'We\'re excited to help']
      });
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Added phrase to use: We\'re excited to help');
    });
  });

  test('handles phrases to avoid management', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <BrandVoiceEditor {...mockProps} />
      </TestWrapper>
    );

    // Switch to phrases to avoid tab
    const phrasesToAvoidTab = screen.getByText('Phrases to Avoid');
    await user.click(phrasesToAvoidTab);

    // Add phrase to avoid
    const phraseInput = screen.getByPlaceholderText('Add phrase to avoid');
    await user.type(phraseInput, 'It\'s impossible');

    // Find the specific add button for phrases to avoid (error color)
    const addButtons = screen.getAllByRole('button', { name: /add/i });
    const errorAddButton = addButtons.find(button =>
      button.className.includes('colorError') && !button.disabled
    );
    await user.click(errorAddButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockBrandVoice,
        phrases_to_avoid: [...mockBrandVoice.phrases_to_avoid, 'It\'s impossible']
      });
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Added phrase to avoid: It\'s impossible');
    });
  });

  test('handles export functionality', async () => {
    // Mock URL.createObjectURL and related APIs
    global.URL.createObjectURL = vi.fn(() => 'mock-url');
    global.URL.revokeObjectURL = vi.fn();
    
    const mockLink = {
      href: '',
      download: '',
      click: vi.fn(),
    };
    
    vi.spyOn(document, 'createElement').mockReturnValue(mockLink);
    vi.spyOn(document.body, 'appendChild').mockImplementation(() => {});
    vi.spyOn(document.body, 'removeChild').mockImplementation(() => {});

    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVoiceEditor {...mockProps} />
      </TestWrapper>
    );

    const exportButton = screen.getByLabelText('Export brand voice settings');
    await user.click(exportButton);

    await waitFor(() => {
      expect(mockProps.onExport).toHaveBeenCalled();
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Brand voice exported successfully');
    });
  });

  test('opens fullscreen preview dialog', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <BrandVoiceEditor {...mockProps} />
      </TestWrapper>
    );

    const fullscreenButton = screen.getByLabelText('Open fullscreen preview');
    await user.click(fullscreenButton);

    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });
  });

  test('opens voice guidelines dialog', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVoiceEditor {...mockProps} />
      </TestWrapper>
    );

    const guidelinesButton = screen.getByLabelText('Open voice guidelines');
    await user.click(guidelinesButton);

    await waitFor(() => {
      expect(screen.getByText('Brand Voice Guidelines')).toBeInTheDocument();
    });
  });

  test('handles disabled state correctly', () => {
    render(
      <TestWrapper>
        <BrandVoiceEditor {...mockProps} disabled />
      </TestWrapper>
    );

    const exportButton = screen.getByLabelText('Export brand voice settings');
    const fullscreenButton = screen.getByLabelText('Open fullscreen preview');

    expect(exportButton).toBeDisabled();
    expect(fullscreenButton).toBeDisabled();
  });

  test('handles read-only state correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVoiceEditor {...mockProps} readOnly />
      </TestWrapper>
    );

    // Expand traits section
    const traitsAccordion = screen.getByText('Trait Management');
    await user.click(traitsAccordion);

    // Should not show add controls in read-only mode
    expect(screen.queryByPlaceholderText('Add custom trait')).not.toBeInTheDocument();
  });

  test('displays preview content correctly', () => {
    render(
      <TestWrapper>
        <BrandVoiceEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Brand Voice Preview')).toBeInTheDocument();
    expect(screen.getByText('Tone: Professional • Style: Clear and Concise')).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <BrandVoiceEditor {...mockProps} />
      </TestWrapper>
    );

    // Check for proper ARIA labels
    expect(screen.getByLabelText('Export brand voice settings')).toBeInTheDocument();
    expect(screen.getByLabelText('Open fullscreen preview')).toBeInTheDocument();
    expect(screen.getByLabelText('Open voice guidelines')).toBeInTheDocument();

    // Check for proper headings
    const headings = screen.getAllByRole('heading');
    expect(headings.length).toBeGreaterThan(0);
  });

  test('handles preview without showPreview prop', () => {
    render(
      <TestWrapper>
        <BrandVoiceEditor {...mockProps} showPreview={false} />
      </TestWrapper>
    );

    expect(screen.queryByText('Brand Voice Preview')).not.toBeInTheDocument();
  });

  test('handles consistency score calculation', () => {
    const incompleteVoice = {
      tone: 'professional',
      personality_traits: [],
      writing_style: '',
      vocabulary: [],
      phrases_to_use: [],
      phrases_to_avoid: []
    };

    render(
      <TestWrapper>
        <BrandVoiceEditor brandVoice={incompleteVoice} onChange={vi.fn()} />
      </TestWrapper>
    );

    // Should show lower completeness score
    expect(screen.getByText('Voice Completeness')).toBeInTheDocument();
    // Score should be less than 100% since many fields are empty
  });
});
