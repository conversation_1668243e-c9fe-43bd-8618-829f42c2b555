// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Divider,
  Chip,
  CircularProgress,
  Alert,
  Tooltip,
  <PERSON>ton,
  Card,
  CardContent,

  Tab,
  Tabs
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Timeline as TimelineIcon,
  <PERSON><PERSON>hart as BarChartIcon,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Download as DownloadIcon
} from '@mui/icons-material';
import * as d3 from 'd3';
import { useNotification } from '../../hooks/useNotification';
import { analyzeICPPerformance } from '../../api/icp-performance';

// Statistical significance indicator component
const SignificanceIndicator = ({ isSignificant, isBetter, value }) => {
  if (!isSignificant) {
    return (
      <Tooltip title="Not statistically significant">
        <Chip 
          size="small" 
          icon={<InfoIcon />} 
          label="Not significant" 
          color="default" 
          variant="outlined"
        />
      </Tooltip>
    );
  }
  
  return isBetter ? (
    <Tooltip title="Statistically significant improvement">
      <Chip 
        size="small" 
        icon={<CheckCircleIcon />} 
        label={`Significant (+${value.toFixed(1)}%)`} 
        color="success" 
      />
    </Tooltip>
  ) : (
    <Tooltip title="Statistically significant decline">
      <Chip 
        size="small" 
        icon={<CancelIcon />} 
        label={`Significant (${value.toFixed(1)}%)`} 
        color="error" 
      />
    </Tooltip>
  );
};



const ICPPerformanceMetrics = ({ icpId, onDataLoaded }) => {
  // State
  const [performanceData, setPerformanceData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState(0);

  
  // Refs for chart containers
  const engagementChartRef = useRef(null);
  const platformChartRef = useRef(null);
  const contentTypeChartRef = useRef(null);
  
  // Hooks
  const { showSuccessNotification, showErrorNotification } = useNotification();
  
  // Load performance data
  useEffect(() => {
    const loadPerformanceData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const data = await analyzeICPPerformance(icpId);
        setPerformanceData(data);
        
        if (onDataLoaded) {
          onDataLoaded(data);
        }
      } catch (err) {
        console.error('Failed to load ICP performance data:', err);
        setError('Failed to load performance data. Please try again.');
        showErrorNotification('Failed to load ICP performance data');
      } finally {
        setLoading(false);
      }
    };
    
    loadPerformanceData();
  }, [icpId, showErrorNotification, onDataLoaded]);
  
  // Create engagement rate trend chart
  useEffect(() => {
    if (!performanceData || !engagementChartRef.current) return;
    
    const timeSeries = performanceData.time_series_data;
    if (!timeSeries || timeSeries.length === 0) return;
    
    // Clear previous chart
    d3.select(engagementChartRef.current).selectAll('*').remove();
    
    // Set up dimensions
    const margin = { top: 20, right: 30, bottom: 30, left: 40 };
    const width = engagementChartRef.current.clientWidth - margin.left - margin.right;
    const height = 300 - margin.top - margin.bottom;
    
    // Create SVG
    const svg = d3.select(engagementChartRef.current)
      .append('svg')
      .attr('width', width + margin.left + margin.right)
      .attr('height', height + margin.top + margin.bottom)
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);
    
    // Parse dates and prepare data
    const data = timeSeries.map(d => ({
      date: new Date(d.date),
      value: d.engagement_rate,
      movingAvg: d.moving_avg_engagement || d.engagement_rate
    }));
    
    // Set up scales
    const x = d3.scaleTime()
      .domain(d3.extent(data, d => d.date))
      .range([0, width]);
    
    const y = d3.scaleLinear()
      .domain([0, d3.max(data, d => Math.max(d.value, d.movingAvg)) * 1.1])
      .range([height, 0]);
    
    // Add X axis
    svg.append('g')
      .attr('transform', `translate(0,${height})`)
      .call(d3.axisBottom(x).ticks(5));
    
    // Add Y axis
    svg.append('g')
      .call(d3.axisLeft(y).ticks(5).tickFormat(d => `${d}%`));
    
    // Add engagement rate line
    const line = d3.line()
      .x(d => x(d.date))
      .y(d => y(d.value));
    
    svg.append('path')
      .datum(data)
      .attr('fill', 'none')
      .attr('stroke', '#8884d8')
      .attr('stroke-width', 1.5)
      .attr('d', line);
    
    // Add moving average line
    const movingAvgLine = d3.line()
      .x(d => x(d.date))
      .y(d => y(d.movingAvg));
    
    svg.append('path')
      .datum(data.filter(d => d.movingAvg))
      .attr('fill', 'none')
      .attr('stroke', '#ff7300')
      .attr('stroke-width', 2)
      .attr('stroke-dasharray', '5,5')
      .attr('d', movingAvgLine);
    
    // Add legend
    const legend = svg.append('g')
      .attr('transform', `translate(${width - 150}, 0)`);
    
    legend.append('rect')
      .attr('x', 0)
      .attr('y', 0)
      .attr('width', 10)
      .attr('height', 10)
      .attr('fill', '#8884d8');
    
    legend.append('text')
      .attr('x', 15)
      .attr('y', 10)
      .text('Engagement Rate')
      .style('font-size', '12px');
    
    legend.append('rect')
      .attr('x', 0)
      .attr('y', 20)
      .attr('width', 10)
      .attr('height', 10)
      .attr('fill', '#ff7300');
    
    legend.append('text')
      .attr('x', 15)
      .attr('y', 30)
      .text('7-Day Moving Avg')
      .style('font-size', '12px');
  }, [performanceData]);
  
  // Create platform performance chart
  useEffect(() => {
    if (!performanceData || !platformChartRef.current) return;
    
    const platformData = performanceData.platform_performance;
    if (!platformData || Object.keys(platformData).length === 0) return;
    
    // Clear previous chart
    d3.select(platformChartRef.current).selectAll('*').remove();
    
    // Prepare data
    const data = Object.entries(platformData).map(([platform, metrics]) => ({
      platform,
      engagementRate: metrics.engagement_rate,
      isSignificant: metrics.stats?.is_significant || false,
      isBetter: metrics.stats?.is_better || false
    }));
    
    // Sort by engagement rate
    data.sort((a, b) => b.engagementRate - a.engagementRate);
    
    // Set up dimensions
    const margin = { top: 20, right: 30, bottom: 60, left: 40 };
    const width = platformChartRef.current.clientWidth - margin.left - margin.right;
    const height = 300 - margin.top - margin.bottom;
    
    // Create SVG
    const svg = d3.select(platformChartRef.current)
      .append('svg')
      .attr('width', width + margin.left + margin.right)
      .attr('height', height + margin.top + margin.bottom)
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);
    
    // Set up scales
    const x = d3.scaleBand()
      .domain(data.map(d => d.platform))
      .range([0, width])
      .padding(0.3);
    
    const y = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.engagementRate) * 1.1])
      .range([height, 0]);
    
    // Add X axis
    svg.append('g')
      .attr('transform', `translate(0,${height})`)
      .call(d3.axisBottom(x))
      .selectAll('text')
      .attr('transform', 'translate(-10,0)rotate(-45)')
      .style('text-anchor', 'end');
    
    // Add Y axis
    svg.append('g')
      .call(d3.axisLeft(y).ticks(5).tickFormat(d => `${d}%`));
    
    // Add bars
    svg.selectAll('.bar')
      .data(data)
      .enter()
      .append('rect')
      .attr('class', 'bar')
      .attr('x', d => x(d.platform))
      .attr('y', d => y(d.engagementRate))
      .attr('width', x.bandwidth())
      .attr('height', d => height - y(d.engagementRate))
      .attr('fill', d => d.isSignificant ? (d.isBetter ? '#4caf50' : '#f44336') : '#2196f3');
    
    // Add significance indicators
    svg.selectAll('.significance')
      .data(data.filter(d => d.isSignificant))
      .enter()
      .append('text')
      .attr('class', 'significance')
      .attr('x', d => x(d.platform) + x.bandwidth() / 2)
      .attr('y', d => y(d.engagementRate) - 5)
      .attr('text-anchor', 'middle')
      .text('*')
      .style('font-size', '20px')
      .style('fill', d => d.isBetter ? '#4caf50' : '#f44336');
  }, [performanceData]);

  // Create content type performance chart
  useEffect(() => {
    if (!performanceData || !contentTypeChartRef.current) return;

    const contentTypeData = performanceData.content_type_performance;
    if (!contentTypeData || Object.keys(contentTypeData).length === 0) return;

    // Clear previous chart
    d3.select(contentTypeChartRef.current).selectAll('*').remove();

    // Prepare data
    const data = Object.entries(contentTypeData).map(([contentType, metrics]) => ({
      contentType: contentType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
      engagementRate: metrics.engagement_rate,
      isSignificant: metrics.stats?.is_significant || false,
      isBetter: metrics.stats?.is_better || false
    }));

    // Sort by engagement rate
    data.sort((a, b) => b.engagementRate - a.engagementRate);

    // Set up dimensions
    const margin = { top: 20, right: 30, bottom: 60, left: 40 };
    const width = contentTypeChartRef.current.clientWidth - margin.left - margin.right;
    const height = 300 - margin.top - margin.bottom;

    // Create SVG
    const svg = d3.select(contentTypeChartRef.current)
      .append('svg')
      .attr('width', width + margin.left + margin.right)
      .attr('height', height + margin.top + margin.bottom)
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Set up scales
    const x = d3.scaleBand()
      .domain(data.map(d => d.contentType))
      .range([0, width])
      .padding(0.3);

    const y = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.engagementRate) * 1.1])
      .range([height, 0]);

    // Add X axis
    svg.append('g')
      .attr('transform', `translate(0,${height})`)
      .call(d3.axisBottom(x))
      .selectAll('text')
      .attr('transform', 'translate(-10,0)rotate(-45)')
      .style('text-anchor', 'end');

    // Add Y axis
    svg.append('g')
      .call(d3.axisLeft(y).ticks(5).tickFormat(d => `${d}%`));

    // Add bars
    svg.selectAll('.bar')
      .data(data)
      .enter()
      .append('rect')
      .attr('class', 'bar')
      .attr('x', d => x(d.contentType))
      .attr('y', d => y(d.engagementRate))
      .attr('width', x.bandwidth())
      .attr('height', d => height - y(d.engagementRate))
      .attr('fill', d => d.isSignificant ? (d.isBetter ? '#4caf50' : '#f44336') : '#9c27b0');

    // Add significance indicators
    svg.selectAll('.significance')
      .data(data.filter(d => d.isSignificant))
      .enter()
      .append('text')
      .attr('class', 'significance')
      .attr('x', d => x(d.contentType) + x.bandwidth() / 2)
      .attr('y', d => y(d.engagementRate) - 5)
      .attr('text-anchor', 'middle')
      .text('*')
      .style('font-size', '20px')
      .style('fill', d => d.isBetter ? '#4caf50' : '#f44336');
  }, [performanceData]);
  
  // Handle refresh
  const handleRefresh = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await analyzeICPPerformance(icpId, true); // Force refresh
      setPerformanceData(data);
      showSuccessNotification('Performance data refreshed successfully');
      
      if (onDataLoaded) {
        onDataLoaded(data);
      }
    } catch (err) {
      console.error('Failed to refresh ICP performance data:', err);
      setError('Failed to refresh performance data. Please try again.');
      showErrorNotification('Failed to refresh ICP performance data');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle tab change
  const handleTabChange = useCallback((_, newValue) => {
    setActiveTab(newValue);
  }, []);

  // Handle export functionality
  const handleExport = useCallback(async () => {
    if (!performanceData) return;

    try {
      showSuccessNotification('Generating ICP performance report...');

      // Create CSV content
      let csvContent = "data:text/csv;charset=utf-8,";

      // Add summary data
      csvContent += "Metric,Value\r\n";
      csvContent += `ICP Name,${performanceData.icp_name}\r\n`;
      csvContent += `Average Engagement Rate,${performanceData.avg_engagement_rate.toFixed(2)}%\r\n`;
      csvContent += `Best Platform,${performanceData.best_performing_platform || 'N/A'}\r\n`;
      csvContent += `Best Content Type,${performanceData.best_performing_content_type || 'N/A'}\r\n`;
      csvContent += `Best Posting Time,${performanceData.best_performing_time || 'N/A'}\r\n`;

      // Add platform performance
      if (performanceData.platform_performance) {
        csvContent += "\r\nPlatform Performance\r\n";
        csvContent += "Platform,Engagement Rate,Is Significant,Is Better\r\n";
        Object.entries(performanceData.platform_performance).forEach(([platform, metrics]) => {
          csvContent += `${platform},${metrics.engagement_rate.toFixed(2)}%,${metrics.stats?.is_significant || false},${metrics.stats?.is_better || false}\r\n`;
        });
      }

      // Add content type performance
      if (performanceData.content_type_performance) {
        csvContent += "\r\nContent Type Performance\r\n";
        csvContent += "Content Type,Engagement Rate,Is Significant,Is Better\r\n";
        Object.entries(performanceData.content_type_performance).forEach(([contentType, metrics]) => {
          csvContent += `${contentType},${metrics.engagement_rate.toFixed(2)}%,${metrics.stats?.is_significant || false},${metrics.stats?.is_better || false}\r\n`;
        });
      }

      // Add time series data
      if (performanceData.time_series_data) {
        csvContent += "\r\nTime Series Data\r\n";
        csvContent += "Date,Engagement Rate,Moving Average\r\n";
        performanceData.time_series_data.forEach((dataPoint) => {
          csvContent += `${dataPoint.date},${dataPoint.engagement_rate.toFixed(2)}%,${(dataPoint.moving_avg_engagement || dataPoint.engagement_rate).toFixed(2)}%\r\n`;
        });
      }

      // Add recommendations
      if (performanceData.recommendations && performanceData.recommendations.length > 0) {
        csvContent += "\r\nRecommendations\r\n";
        performanceData.recommendations.forEach((recommendation, index) => {
          csvContent += `${index + 1},"${recommendation}"\r\n`;
        });
      }

      // Create and trigger download
      const encodedUri = encodeURI(csvContent);
      const link = document.createElement("a");
      link.setAttribute("href", encodedUri);
      link.setAttribute("download", `icp_performance_${performanceData.icp_name}_${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      showSuccessNotification('ICP performance report downloaded successfully');
    } catch (error) {
      console.error('Error exporting data:', error);
      showErrorNotification('Failed to export ICP performance data');
    }
  }, [performanceData, showSuccessNotification, showErrorNotification]);
  
  if (loading && !performanceData) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }
  
  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        {error}
        <Button 
          variant="outlined" 
          size="small" 
          startIcon={<RefreshIcon />} 
          onClick={handleRefresh}
          sx={{ ml: 2 }}
        >
          Retry
        </Button>
      </Alert>
    );
  }
  
  if (!performanceData) {
    return (
      <Alert severity="info" sx={{ mt: 2 }}>
        No performance data available for this ICP.
      </Alert>
    );
  }
  
  return (
    <Box sx={{ mt: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Performance Metrics for {performanceData.icp_name}
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={handleExport}
            disabled={!performanceData}
          >
            Export Data
          </Button>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            disabled={loading}
          >
            {loading ? 'Refreshing...' : 'Refresh Data'}
          </Button>
        </Box>
      </Box>
      
      {/* Summary Cards */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="textSecondary">
                Engagement Rate
              </Typography>
              <Typography variant="h4">
                {performanceData.avg_engagement_rate.toFixed(2)}%
              </Typography>
              {performanceData.engagement_statistics && (
                <Tooltip title={`Median: ${performanceData.engagement_statistics.median.toFixed(2)}%, StdDev: ${performanceData.engagement_statistics.stdev.toFixed(2)}%`}>
                  <Typography variant="body2" color="textSecondary">
                    Range: {performanceData.engagement_statistics.min.toFixed(2)}% - {performanceData.engagement_statistics.max.toFixed(2)}%
                  </Typography>
                </Tooltip>
              )}
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="textSecondary">
                Best Platform
              </Typography>
              <Typography variant="h5">
                {performanceData.best_performing_platform || 'N/A'}
              </Typography>
              {performanceData.platform_performance && performanceData.best_performing_platform && (
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <Typography variant="body2" color="textSecondary" sx={{ mr: 1 }}>
                    {performanceData.platform_performance[performanceData.best_performing_platform].engagement_rate.toFixed(2)}%
                  </Typography>
                  {performanceData.platform_performance[performanceData.best_performing_platform].stats?.is_significant && (
                    <SignificanceIndicator 
                      isSignificant={true}
                      isBetter={performanceData.platform_performance[performanceData.best_performing_platform].stats.is_better}
                      value={0}
                    />
                  )}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="textSecondary">
                Best Content Type
              </Typography>
              <Typography variant="h5">
                {performanceData.best_performing_content_type || 'N/A'}
              </Typography>
              {performanceData.content_type_performance && performanceData.best_performing_content_type && (
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <Typography variant="body2" color="textSecondary" sx={{ mr: 1 }}>
                    {performanceData.content_type_performance[performanceData.best_performing_content_type].engagement_rate.toFixed(2)}%
                  </Typography>
                  {performanceData.content_type_performance[performanceData.best_performing_content_type].stats?.is_significant && (
                    <SignificanceIndicator 
                      isSignificant={true}
                      isBetter={performanceData.content_type_performance[performanceData.best_performing_content_type].stats.is_better}
                      value={0}
                    />
                  )}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="textSecondary">
                Best Posting Time
              </Typography>
              <Typography variant="h5">
                {performanceData.best_performing_time || 'N/A'}
              </Typography>
              {performanceData.time_of_day_performance && performanceData.best_performing_time && (
                <Typography variant="body2" color="textSecondary">
                  {performanceData.time_of_day_performance[performanceData.best_performing_time].optimal_hour 
                    ? `Optimal hour: ${performanceData.time_of_day_performance[performanceData.best_performing_time].optimal_hour}:00` 
                    : ''}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      {/* Tabs for different charts */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={activeTab} onChange={handleTabChange} aria-label="performance metrics tabs">
          <Tab icon={<TimelineIcon />} label="Engagement Trends" />
          <Tab icon={<BarChartIcon />} label="Platform Performance" />
          <Tab icon={<PieChartIcon />} label="Content Analysis" />
        </Tabs>
      </Box>
      
      {/* Tab Panels */}
      <Box sx={{ mt: 2 }}>
        {/* Engagement Trends Tab */}
        {activeTab === 0 && (
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Engagement Rate Trends
            </Typography>
            <Box ref={engagementChartRef} sx={{ height: 300, width: '100%' }} />
          </Paper>
        )}
        
        {/* Platform Performance Tab */}
        {activeTab === 1 && (
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Platform Performance
            </Typography>
            <Box ref={platformChartRef} sx={{ height: 300, width: '100%' }} />
          </Paper>
        )}
        
        {/* Content Analysis Tab */}
        {activeTab === 2 && (
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Content Type Performance
            </Typography>
            <Box ref={contentTypeChartRef} sx={{ height: 300, width: '100%' }} />
          </Paper>
        )}
      </Box>
      
      {/* Recommendations */}
      <Paper sx={{ p: 2, mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          AI-Powered Recommendations
        </Typography>
        <Divider sx={{ mb: 2 }} />
        
        {performanceData.recommendations && performanceData.recommendations.length > 0 ? (
          <Box component="ul" sx={{ pl: 2 }}>
            {performanceData.recommendations.map((recommendation, index) => (
              <Typography component="li" key={index} sx={{ mb: 1 }}>
                {recommendation}
              </Typography>
            ))}
          </Box>
        ) : (
          <Alert severity="info">
            No recommendations available. Try generating more content for this ICP to get personalized recommendations.
          </Alert>
        )}
      </Paper>
    </Box>
  );
};

export default ICPPerformanceMetrics;
