"""
Schemas for social media AI response management.
Designed to match the data structures expected by aiResponseService.js frontend service.
@since 2024-1-1 to 2025-25-7
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, ConfigDict
from app.models.user import PyObjectId


class SocialMediaPostBase(BaseModel):
    """Base schema for social media posts with AI response capabilities."""
    platform: str = Field(..., description="Social media platform (linkedin, facebook, twitter, etc.)")
    post_id: str = Field(..., description="Platform-specific post ID")
    content_text: str = Field(..., description="Post content text")
    author_name: Optional[str] = Field(None, description="Post author name")
    published_at: Optional[datetime] = Field(None, description="Post publication timestamp")
    engagement_metrics: Optional[Dict[str, int]] = Field(default_factory=dict, description="Likes, comments, shares, etc.")


class SocialMediaPostWithPendingComments(SocialMediaPostBase):
    """Schema for posts with pending AI comment responses."""
    id: str = Field(..., description="Internal post ID")
    pending_comments_count: int = Field(0, description="Number of comments awaiting AI responses")
    total_comments_count: int = Field(0, description="Total number of comments on the post")
    last_comment_at: Optional[datetime] = Field(None, description="Timestamp of most recent comment")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "id": "64f8a1b2c3d4e5f6a7b8c9d0",
                "platform": "linkedin",
                "post_id": "urn:li:activity:7123456789",
                "content_text": "Excited to share our latest insights on digital marketing trends...",
                "author_name": "ACE Social",
                "published_at": "2024-01-15T10:30:00Z",
                "engagement_metrics": {"likes": 45, "comments": 12, "shares": 8},
                "pending_comments_count": 3,
                "total_comments_count": 12,
                "last_comment_at": "2024-01-15T14:22:00Z"
            }
        }
    )


class SocialMediaCommentBase(BaseModel):
    """Base schema for social media comments."""
    comment_id: str = Field(..., description="Platform-specific comment ID")
    post_id: str = Field(..., description="Platform-specific post ID")
    platform: str = Field(..., description="Social media platform")
    comment_text: str = Field(..., description="Original comment text")
    comment_author: str = Field(..., description="Comment author name/handle")
    comment_author_id: Optional[str] = Field(None, description="Platform-specific author ID")
    created_at: datetime = Field(..., description="Comment creation timestamp")
    sentiment: Optional[str] = Field(None, description="Comment sentiment (positive, negative, neutral)")
    parent_comment_id: Optional[str] = Field(None, description="Parent comment ID for replies")


class SocialMediaCommentWithAIResponse(SocialMediaCommentBase):
    """Schema for comments with AI response data."""
    id: str = Field(..., description="Internal comment response ID")
    ai_response_text: Optional[str] = Field(None, description="Generated AI response text")
    ai_response_status: str = Field("pending", description="AI response status (pending, approved, rejected, published)")
    ai_response_confidence: Optional[float] = Field(None, description="AI confidence score (0.0-1.0)")
    ai_response_generated_at: Optional[datetime] = Field(None, description="AI response generation timestamp")
    ai_response_approved_by: Optional[str] = Field(None, description="User who approved the response")
    ai_response_approved_at: Optional[datetime] = Field(None, description="Response approval timestamp")
    ai_response_published_at: Optional[datetime] = Field(None, description="Response publication timestamp")
    ai_response_edit_history: List[Dict[str, Any]] = Field(default_factory=list, description="History of edits to AI response")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "id": "64f8a1b2c3d4e5f6a7b8c9d1",
                "comment_id": "comment_123456",
                "post_id": "urn:li:activity:7123456789",
                "platform": "linkedin",
                "comment_text": "Great insights! How do you implement this in practice?",
                "comment_author": "John Doe",
                "comment_author_id": "john_doe_linkedin",
                "created_at": "2024-01-15T12:30:00Z",
                "sentiment": "positive",
                "parent_comment_id": None,
                "ai_response_text": "Thank you for your question, John! We implement this through a systematic approach...",
                "ai_response_status": "pending",
                "ai_response_confidence": 0.85,
                "ai_response_generated_at": "2024-01-15T12:35:00Z",
                "ai_response_approved_by": None,
                "ai_response_approved_at": None,
                "ai_response_published_at": None,
                "ai_response_edit_history": []
            }
        }
    )


class CommentApprovalRequest(BaseModel):
    """Schema for approving an AI response."""
    response_text: Optional[str] = Field(None, description="Modified response text (if edited during approval)")
    approval_notes: Optional[str] = Field(None, description="Notes about the approval decision")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "response_text": "Thank you for your question! We implement this through...",
                "approval_notes": "Approved with minor tone adjustment"
            }
        }
    )


class CommentRejectionRequest(BaseModel):
    """Schema for rejecting an AI response."""
    rejection_reason: str = Field(..., description="Reason for rejecting the AI response")
    rejection_notes: Optional[str] = Field(None, description="Additional notes about the rejection")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "rejection_reason": "tone_inappropriate",
                "rejection_notes": "Response tone doesn't match brand voice guidelines"
            }
        }
    )


class CommentRegenerationRequest(BaseModel):
    """Schema for regenerating an AI response."""
    regeneration_prompt: Optional[str] = Field(None, description="Additional context or instructions for regeneration")
    tone_adjustment: Optional[str] = Field(None, description="Tone adjustment (more_formal, more_casual, more_friendly, etc.)")
    length_preference: Optional[str] = Field(None, description="Length preference (shorter, longer, same)")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "regeneration_prompt": "Make the response more technical and include specific implementation details",
                "tone_adjustment": "more_formal",
                "length_preference": "longer"
            }
        }
    )


class CommentEditRequest(BaseModel):
    """Schema for editing an AI response."""
    edited_text: str = Field(..., description="Manually edited response text")
    edit_notes: Optional[str] = Field(None, description="Notes about the edit changes")
    preserve_ai_flag: bool = Field(True, description="Whether to maintain AI-generated flag after edit")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "edited_text": "Thank you for your excellent question! We implement this through a comprehensive approach...",
                "edit_notes": "Added more enthusiasm and detail to the opening",
                "preserve_ai_flag": True
            }
        }
    )


class AIResponseOperationResult(BaseModel):
    """Schema for AI response operation results."""
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Human-readable result message")
    comment_id: str = Field(..., description="Comment ID that was operated on")
    new_status: Optional[str] = Field(None, description="New AI response status after operation")
    updated_response_text: Optional[str] = Field(None, description="Updated response text (if applicable)")
    operation_timestamp: datetime = Field(default_factory=datetime.utcnow, description="Operation completion timestamp")
    correlation_id: Optional[str] = Field(None, description="Correlation ID for tracking")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "success": True,
                "message": "AI response approved successfully",
                "comment_id": "64f8a1b2c3d4e5f6a7b8c9d1",
                "new_status": "approved",
                "updated_response_text": "Thank you for your question! We implement this through...",
                "operation_timestamp": "2024-01-15T15:30:00Z",
                "correlation_id": "req_abc123def456"
            }
        }
    )


class BulkOperationRequest(BaseModel):
    """Schema for bulk operations on multiple comments."""
    comment_ids: List[str] = Field(..., description="List of comment IDs to operate on")
    operation_notes: Optional[str] = Field(None, description="Notes about the bulk operation")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "comment_ids": ["64f8a1b2c3d4e5f6a7b8c9d1", "64f8a1b2c3d4e5f6a7b8c9d2"],
                "operation_notes": "Bulk approval of positive sentiment responses"
            }
        }
    )


class BulkOperationResult(BaseModel):
    """Schema for bulk operation results."""
    total_requested: int = Field(..., description="Total number of items requested for operation")
    successful_operations: int = Field(..., description="Number of successful operations")
    failed_operations: int = Field(..., description="Number of failed operations")
    operation_details: List[AIResponseOperationResult] = Field(..., description="Detailed results for each operation")
    overall_success: bool = Field(..., description="Whether the overall bulk operation was successful")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "total_requested": 5,
                "successful_operations": 4,
                "failed_operations": 1,
                "operation_details": [],
                "overall_success": True
            }
        }
    )
