/**
 * Tests for UsageIndicator component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen } from '@testing-library/react';
import { expect, describe, test } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import UsageIndicator, { CompactUsageIndicator, RingUsageIndicator } from '../UsageIndicator';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

describe('UsageIndicator', () => {
  test('renders basic usage indicator', () => {
    render(
      <TestWrapper>
        <UsageIndicator current={25} total={100} label="Test Usage" />
      </TestWrapper>
    );

    expect(screen.getByText('Test Usage')).toBeInTheDocument();
    expect(screen.getByText('25 / 100')).toBeInTheDocument();
    expect(screen.getByText('75 remaining')).toBeInTheDocument();
    expect(screen.getByText('25%')).toBeInTheDocument();
  });

  test('handles unlimited usage', () => {
    render(
      <TestWrapper>
        <UsageIndicator current={0} total={-1} label="Unlimited Feature" />
      </TestWrapper>
    );

    expect(screen.getByText('Unlimited Feature: Unlimited')).toBeInTheDocument();
  });

  test('shows warning state at 75% usage', () => {
    render(
      <TestWrapper>
        <UsageIndicator current={75} total={100} label="Warning Usage" />
      </TestWrapper>
    );

    expect(screen.getByText('Running low')).toBeInTheDocument();
    expect(screen.getByText(/You've used 75.0% of your warning usage/)).toBeInTheDocument();
  });

  test('shows critical state at 95% usage', () => {
    render(
      <TestWrapper>
        <UsageIndicator current={95} total={100} label="Critical Usage" />
      </TestWrapper>
    );

    expect(screen.getByText('Almost depleted')).toBeInTheDocument();
    expect(screen.getByText(/You've used 95.0% of your critical usage/)).toBeInTheDocument();
  });

  test('handles zero total gracefully', () => {
    render(
      <TestWrapper>
        <UsageIndicator current={0} total={0} label="Zero Total" />
      </TestWrapper>
    );

    expect(screen.getByText('0%')).toBeInTheDocument();
  });

  test('respects showAlert prop', () => {
    render(
      <TestWrapper>
        <UsageIndicator current={80} total={100} label="No Alert" showAlert={false} />
      </TestWrapper>
    );

    expect(screen.queryByText(/You've used 80.0%/)).not.toBeInTheDocument();
  });

  test('formats large numbers correctly', () => {
    render(
      <TestWrapper>
        <UsageIndicator current={1500} total={2000000} label="Large Numbers" />
      </TestWrapper>
    );

    expect(screen.getByText('1.5K / 2.0M')).toBeInTheDocument();
  });
});

describe('CompactUsageIndicator', () => {
  test('renders compact indicator', () => {
    render(
      <TestWrapper>
        <CompactUsageIndicator current={30} total={100} label="Compact Test" />
      </TestWrapper>
    );

    expect(screen.getByText('30/100')).toBeInTheDocument();
  });

  test('handles unlimited in compact mode', () => {
    render(
      <TestWrapper>
        <CompactUsageIndicator current={0} total={-1} label="Unlimited Compact" />
      </TestWrapper>
    );

    expect(screen.getByText('∞')).toBeInTheDocument();
  });
});

describe('RingUsageIndicator', () => {
  test('renders ring indicator', () => {
    render(
      <TestWrapper>
        <RingUsageIndicator current={40} total={100} label="Ring Test" size={80} />
      </TestWrapper>
    );

    expect(screen.getByText('40%')).toBeInTheDocument();
    expect(screen.getByText('Ring Test')).toBeInTheDocument();
  });

  test('handles unlimited in ring mode', () => {
    render(
      <TestWrapper>
        <RingUsageIndicator current={0} total={-1} label="Unlimited Ring" />
      </TestWrapper>
    );

    expect(screen.getByText('∞')).toBeInTheDocument();
  });

  test('uses default size when not specified', () => {
    const { container } = render(
      <TestWrapper>
        <RingUsageIndicator current={50} total={100} label="Default Size" />
      </TestWrapper>
    );

    const svg = container.querySelector('svg');
    expect(svg).toHaveAttribute('width', '60');
    expect(svg).toHaveAttribute('height', '60');
  });
});
