/**
 * Tests for BrandProfileSelector component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { BrowserRouter } from 'react-router-dom';
import BrandProfileSelector from '../BrandProfileSelector';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

// Mock the useBrandProfiles hook
const mockUseBrandProfiles = {
  profiles: [
    {
      id: 'profile-1',
      name: 'Brand Profile 1',
      is_default: true,
      color_palette: {
        primary: '#4E40C5',
        secondary: '#00E4BC',
        accent: '#FF5733'
      },
      typography: {
        primary_font: 'Roboto',
        secondary_font: 'Open Sans'
      }
    },
    {
      id: 'profile-2',
      name: 'Brand Profile 2',
      is_default: false,
      color_palette: {
        primary: '#FF5733',
        secondary: '#FFC300'
      },
      typography: {
        primary_font: 'Arial'
      }
    }
  ],
  loading: false,
  error: null,
  fetchProfiles: vi.fn(),
  setDefaultProfile: vi.fn()
};

vi.mock('../../hooks/useBrandProfiles', () => ({
  useBrandProfiles: () => mockUseBrandProfiles
}));

describe('BrandProfileSelector', () => {
  const mockProps = {
    selectedProfileId: 'profile-1',
    onChange: vi.fn(),
    onError: vi.fn(),
    onProfileCreated: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders brand profile selector correctly', () => {
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Brand Profile')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Brand Profile 1')).toBeInTheDocument();
  });

  test('shows loading state when loading', () => {
    mockUseBrandProfiles.loading = true;
    
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    
    // Reset loading state
    mockUseBrandProfiles.loading = false;
  });

  test('shows error state when error is provided', () => {
    mockUseBrandProfiles.error = 'Failed to load profiles';
    
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} error="Failed to load profiles" />
      </TestWrapper>
    );

    expect(screen.getByText('Failed to load profiles')).toBeInTheDocument();
    
    // Reset error state
    mockUseBrandProfiles.error = null;
  });

  test('shows "No branding" option when allowNone is true', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} allowNone={true} />
      </TestWrapper>
    );

    const select = screen.getByLabelText('Brand Profile');
    await user.click(select);

    expect(screen.getByText('No branding')).toBeInTheDocument();
  });

  test('hides "No branding" option when allowNone is false', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} allowNone={false} />
      </TestWrapper>
    );

    const select = screen.getByLabelText('Brand Profile');
    await user.click(select);

    expect(screen.queryByText('No branding')).not.toBeInTheDocument();
  });

  test('calls onChange when selection changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} />
      </TestWrapper>
    );

    const select = screen.getByLabelText('Brand Profile');
    await user.click(select);

    const option = screen.getByText('Brand Profile 2');
    await user.click(option);

    expect(mockProps.onChange).toHaveBeenCalledWith('profile-2');
  });

  test('shows brand preview when showPreview is true', () => {
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} showPreview={true} />
      </TestWrapper>
    );

    expect(screen.getByText('Colors')).toBeInTheDocument();
    expect(screen.getByText('Typography')).toBeInTheDocument();
    expect(screen.getByText('Primary: Roboto')).toBeInTheDocument();
  });

  test('hides brand preview when showPreview is false', () => {
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} showPreview={false} />
      </TestWrapper>
    );

    expect(screen.queryByText('Colors')).not.toBeInTheDocument();
    expect(screen.queryByText('Typography')).not.toBeInTheDocument();
  });

  test('shows create button when showCreateButton is true', () => {
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} showCreateButton={true} />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Create new brand profile')).toBeInTheDocument();
  });

  test('hides create button when showCreateButton is false', () => {
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} showCreateButton={false} />
      </TestWrapper>
    );

    expect(screen.queryByLabelText('Create new brand profile')).not.toBeInTheDocument();
  });

  test('opens create dialog when create button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} />
      </TestWrapper>
    );

    const createButton = screen.getByLabelText('Create new brand profile');
    await user.click(createButton);

    expect(screen.getByText('Create New Brand Profile')).toBeInTheDocument();
    expect(screen.getByText('Would you like to create a new brand profile?')).toBeInTheDocument();
  });

  test('closes create dialog when cancel is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} />
      </TestWrapper>
    );

    const createButton = screen.getByLabelText('Create new brand profile');
    await user.click(createButton);

    const cancelButton = screen.getByText('Cancel');
    await user.click(cancelButton);

    expect(screen.queryByText('Create New Brand Profile')).not.toBeInTheDocument();
  });

  test('calls onProfileCreated when create profile is confirmed', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} />
      </TestWrapper>
    );

    const createButton = screen.getByLabelText('Create new brand profile');
    await user.click(createButton);

    const confirmButton = screen.getByLabelText('Create new brand profile');
    await user.click(confirmButton);

    expect(mockProps.onProfileCreated).toHaveBeenCalled();
  });

  test('shows default badge for default profile', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} />
      </TestWrapper>
    );

    const select = screen.getByLabelText('Brand Profile');
    await user.click(select);

    expect(screen.getByText('Default')).toBeInTheDocument();
  });

  test('calls setDefaultProfile when set default button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} />
      </TestWrapper>
    );

    const select = screen.getByLabelText('Brand Profile');
    await user.click(select);

    const setDefaultButton = screen.getByLabelText('Set Brand Profile 2 as default profile');
    await user.click(setDefaultButton);

    expect(mockUseBrandProfiles.setDefaultProfile).toHaveBeenCalledWith('profile-2');
  });

  test('disables selector when disabled prop is true', () => {
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} disabled={true} />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Brand Profile')).toBeDisabled();
  });

  test('shows helper text when provided', () => {
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} helperText="Select a brand profile to apply" />
      </TestWrapper>
    );

    expect(screen.getByText('Select a brand profile to apply')).toBeInTheDocument();
  });

  test('shows required indicator when required is true', () => {
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} required={true} />
      </TestWrapper>
    );

    const label = screen.getByText('Brand Profile');
    expect(label.textContent).toContain('*');
  });

  test('uses custom label when provided', () => {
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} label="Choose Brand" />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Choose Brand')).toBeInTheDocument();
  });

  test('uses custom placeholder when provided', () => {
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} placeholder="Choose your brand" allowNone={true} />
      </TestWrapper>
    );

    // The placeholder would be used in the "No branding" option
    expect(screen.getByDisplayValue('Brand Profile 1')).toBeInTheDocument();
  });

  test('handles error in onChange gracefully', async () => {
    const user = userEvent.setup();
    const onChangeMock = vi.fn().mockImplementation(() => {
      throw new Error('Change error');
    });
    
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} onChange={onChangeMock} />
      </TestWrapper>
    );

    const select = screen.getByLabelText('Brand Profile');
    await user.click(select);

    const option = screen.getByText('Brand Profile 2');
    await user.click(option);

    expect(mockProps.onError).toHaveBeenCalledWith(expect.any(Error));
  });

  test('handles error in setDefaultProfile gracefully', async () => {
    const user = userEvent.setup();
    mockUseBrandProfiles.setDefaultProfile.mockRejectedValue(new Error('Set default error'));
    
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} />
      </TestWrapper>
    );

    const select = screen.getByLabelText('Brand Profile');
    await user.click(select);

    const setDefaultButton = screen.getByLabelText('Set Brand Profile 2 as default profile');
    await user.click(setDefaultButton);

    await waitFor(() => {
      expect(mockProps.onError).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  test('passes through additional props', () => {
    render(
      <TestWrapper>
        <BrandProfileSelector 
          {...mockProps} 
          data-testid="test-profile-selector"
          className="custom-class"
        />
      </TestWrapper>
    );

    const component = screen.getByTestId('test-profile-selector');
    expect(component).toHaveClass('custom-class');
  });

  test('renders without crashing when no profiles are available', () => {
    mockUseBrandProfiles.profiles = [];
    
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Brand Profile')).toBeInTheDocument();
    
    // Reset profiles
    mockUseBrandProfiles.profiles = [
      {
        id: 'profile-1',
        name: 'Brand Profile 1',
        is_default: true,
        color_palette: { primary: '#4E40C5', secondary: '#00E4BC' },
        typography: { primary_font: 'Roboto' }
      }
    ];
  });

  test('shows loading skeleton for preview when loading', () => {
    mockUseBrandProfiles.loading = true;
    
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} showPreview={true} />
      </TestWrapper>
    );

    // Should show skeleton loading state
    expect(screen.getAllByTestId('skeleton')).toHaveLength(6); // 3 circles + 3 text skeletons
    
    // Reset loading state
    mockUseBrandProfiles.loading = false;
  });

  test('handles profiles without color_palette gracefully', () => {
    const profileWithoutColors = {
      id: 'profile-3',
      name: 'Profile Without Colors',
      is_default: false,
      typography: { primary_font: 'Arial' }
    };
    
    mockUseBrandProfiles.profiles = [profileWithoutColors];
    
    render(
      <TestWrapper>
        <BrandProfileSelector {...mockProps} selectedProfileId="profile-3" showPreview={true} />
      </TestWrapper>
    );

    expect(screen.getByText('Profile Without Colors')).toBeInTheDocument();
    expect(screen.getByText('Primary: Arial')).toBeInTheDocument();
  });
});
