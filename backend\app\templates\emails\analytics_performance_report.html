<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics Performance Report - ACE Social</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        :root {
            /* ACE Social Brand Colors */
            --ace-purple: #4E40C5;
            --ace-dark: #15110E;
            --ace-yellow: #EBAE1B;
            --ace-white: #FFFFFF;
            --ace-purple-light: #6C4BFA;
            --ace-purple-lighter: #8A72FF;
            --ace-purple-lightest: #B19FFF;
            
            /* Semantic Colors */
            --success-color: #4CAF50;
            --warning-color: #FF9800;
            --error-color: #F44336;
            --info-color: #2196F3;
            
            /* Text Colors */
            --text-primary: #1A1A2E;
            --text-secondary: #4A4A68;
            --text-muted: #AAAAAA;
            --background-light: #F0F4FF;
            --background-card: rgba(255, 255, 255, 0.85);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: var(--text-secondary);
            background: linear-gradient(135deg, var(--ace-purple-light) 0%, var(--ace-purple-lighter) 50%, var(--ace-purple-lightest) 100%);
            min-height: 100vh;
            padding: 20px 10px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--background-card);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            overflow: hidden;
            box-shadow: 
                0 4px 24px rgba(0, 0, 0, 0.08),
                0 1px 3px rgba(108, 75, 250, 0.1),
                0 1px 2px rgba(138, 114, 255, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, var(--ace-purple) 0%, var(--ace-purple-light) 100%);
            color: var(--ace-white);
            text-align: center;
            padding: 40px 40px 30px;
            position: relative;
        }
        
        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
        }
        
        .logo {
            max-width: 180px;
            height: auto;
            margin-bottom: 20px;
            filter: brightness(0) invert(1);
        }
        
        .report-title {
            font-size: 28px;
            font-weight: 700;
            letter-spacing: -0.5px;
            margin-bottom: 8px;
        }
        
        .report-subtitle {
            font-size: 16px;
            font-weight: 400;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .greeting {
            font-size: 18px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 20px;
        }
        
        .intro-text {
            font-size: 16px;
            color: var(--text-secondary);
            margin-bottom: 30px;
            line-height: 1.7;
        }
        
        .stat-box {
            background: var(--ace-white);
            border-radius: 16px;
            padding: 24px;
            margin: 20px 0;
            border: 1px solid rgba(108, 75, 250, 0.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .stat-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        }
        
        .stat-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: var(--ace-purple);
            margin: 8px 0 12px;
            line-height: 1.2;
        }
        
        .stat-description {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.5;
        }
        
        .performance-indicator {
            font-weight: 600;
            font-size: 14px;
        }
        
        .performance-indicator.up {
            color: var(--success-color);
        }
        
        .performance-indicator.down {
            color: var(--error-color);
        }
        
        .performance-indicator.neutral {
            color: var(--text-muted);
        }
        
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .section-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 40px 0 20px;
            letter-spacing: -0.5px;
        }
        
        .divider {
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, rgba(108, 75, 250, 0.2) 50%, transparent 100%);
            margin: 30px 0;
        }
        
        .info-box {
            background: var(--background-light);
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid var(--ace-purple);
        }
        
        .info-box h4 {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
        }
        
        .info-box p {
            font-size: 14px;
            color: var(--text-secondary);
            margin: 6px 0;
        }
        
        .button {
            display: inline-block;
            background: linear-gradient(135deg, var(--ace-purple) 0%, var(--ace-purple-light) 100%);
            color: var(--ace-white);
            padding: 14px 28px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(78, 64, 197, 0.3);
        }
        
        .button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(78, 64, 197, 0.4);
        }
        
        .footer {
            background: var(--background-light);
            text-align: center;
            padding: 30px 40px;
            border-top: 1px solid rgba(108, 75, 250, 0.1);
        }
        
        .footer p {
            color: var(--text-muted);
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .footer-links {
            font-size: 14px;
        }
        
        .footer-links a {
            color: var(--ace-purple);
            text-decoration: none;
            margin: 0 8px;
        }
        
        .footer-links a:hover {
            text-decoration: underline;
        }
        
        /* Responsive Design */
        @media only screen and (max-width: 640px) {
            body {
                padding: 10px 5px;
            }
            
            .container {
                border-radius: 16px;
                margin: 0;
            }
            
            .header {
                padding: 30px 20px 25px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .footer {
                padding: 25px 20px;
            }
            
            .logo {
                max-width: 140px;
            }
            
            .report-title {
                font-size: 24px;
            }
            
            .stat-value {
                font-size: 28px;
            }
            
            .stats-container {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
        
        @media only screen and (max-width: 400px) {
            .content {
                padding: 25px 15px;
            }
            
            .stat-box {
                padding: 20px;
            }
        }
        
        /* Print Styles */
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .container {
                box-shadow: none;
                border: 1px solid #ddd;
            }
            
            .button {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="{{frontend_url}}/assets/logo.svg" alt="ACE Social" class="logo">
            <h1 class="report-title">Analytics Performance Report</h1>
            <p class="report-subtitle">Generated on {{generated_at|date:"F j, Y \a\\t g:i A T"}}</p>
        </div>
        
        <div class="content">
            <h2 class="greeting">Hello {{user_name}},</h2>
            
            <p class="intro-text">
                Here's your comprehensive analytics performance report for the period from 
                <strong>{{report_period_start|date:"F j, Y"}}</strong> to 
                <strong>{{report_period_end|date:"F j, Y"}}</strong>. 
                This report provides insights into your content performance, engagement metrics, and growth trends.
            </p>
            
            <!-- Overall Performance Section -->
            <div class="stat-box">
                <div class="stat-label">Overall Engagement Rate</div>
                <div class="stat-value">{{overall_engagement_rate|floatformat:1}}%</div>
                <div class="stat-description">
                    {% if engagement_change > 0 %}
                    <span class="performance-indicator up">↑ {{engagement_change|floatformat:1}}%</span>
                    {% elif engagement_change < 0 %}
                    <span class="performance-indicator down">↓ {{engagement_change|floatformat:1|cut:"-"}}%</span>
                    {% else %}
                    <span class="performance-indicator neutral">No change</span>
                    {% endif %}
                    compared to the previous period
                </div>
            </div>
            
            <div class="divider"></div>
            
            <!-- Key Metrics Section -->
            <h3 class="section-title">Key Performance Metrics</h3>
            
            <div class="stats-container">
                <div class="stat-item">
                    <div class="stat-box">
                        <div class="stat-label">Total Content Published</div>
                        <div class="stat-value">{{total_content_published|default:"0"}}</div>
                        <div class="stat-description">Across all platforms</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-box">
                        <div class="stat-label">Total Impressions</div>
                        <div class="stat-value">{{total_impressions|floatformat:0|default:"0"}}</div>
                        <div class="stat-description">Content reach</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-box">
                        <div class="stat-label">Total Engagements</div>
                        <div class="stat-value">{{total_engagements|floatformat:0|default:"0"}}</div>
                        <div class="stat-description">Likes, comments, shares</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-box">
                        <div class="stat-label">Click-Through Rate</div>
                        <div class="stat-value">{{click_through_rate|floatformat:1|default:"0"}}%</div>
                        <div class="stat-description">Link clicks per impression</div>
                    </div>
                </div>
            </div>
            
            <div class="divider"></div>
            
            <!-- Top Performing Content Section -->
            <h3 class="section-title">Top Performing Content</h3>
            {% for content in top_performing_content %}
            <div class="info-box">
                <h4>{{content.title|truncatechars:60}}</h4>
                <p><strong>Platform:</strong> {{content.platform|title}}</p>
                <p><strong>Content Type:</strong> {{content.content_type|title}}</p>
                <p><strong>Engagement Rate:</strong> {{content.engagement_rate|floatformat:1}}%</p>
                <p><strong>Published:</strong> {{content.published_date|date:"F j, Y"}}</p>
                {% if content.impressions %}
                <p><strong>Impressions:</strong> {{content.impressions|floatformat:0}}</p>
                {% endif %}
            </div>
            {% empty %}
            <div class="info-box">
                <p>No content data available for this period.</p>
            </div>
            {% endfor %}
            
            <div class="divider"></div>
            
            <!-- Call to Action -->
            <p>View your complete analytics dashboard for detailed insights, trends, and actionable recommendations:</p>
            
            <a href="{{frontend_url}}/analytics" class="button">View Full Analytics Dashboard</a>
            
            {% if company_name %}
            <p style="margin-top: 30px; font-size: 14px; color: var(--text-muted);">
                This report was generated for <strong>{{company_name}}</strong> using ACE Social's advanced analytics engine.
            </p>
            {% endif %}
        </div>
        
        <div class="footer">
            <p>© {% now 'Y' %} ACE Social. All rights reserved.</p>
            <div class="footer-links">
                <a href="{{frontend_url}}/privacy-policy">Privacy Policy</a> •
                <a href="{{frontend_url}}/terms">Terms of Service</a> •
                <a href="{{frontend_url}}/contact">Contact Support</a>
            </div>
        </div>
    </div>
</body>
</html>
