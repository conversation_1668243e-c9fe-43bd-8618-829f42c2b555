"""
Production-ready Usage Tracking and Enforcement Service for ACEO Platform.

This service handles:
- Real-time usage tracking for all features
- Limit enforcement with proper error handling
- Usage analytics and reporting
- Integration with billing and add-on systems
@since 2024-1-1 to 2025-25-7
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta, timezone
from bson import ObjectId

from app.core.monitoring import monitoring
from app.core.config import settings
from app.services.ecommerce_addon_service import ecommerce_addon_service
import importlib

# Import with fallback for missing modules
mongodb = None
try:
    database_module = importlib.import_module("app.core.database")
    mongodb = database_module.db_manager
except ImportError:
    mongodb = None

logger = logging.getLogger(__name__)


class UsageTrackingService:
    """Production-ready service for tracking and enforcing usage limits."""
    
    def __init__(self):
        self.ecommerce_addon_service = ecommerce_addon_service
        
    async def check_and_consume_usage(
        self, 
        user_id: str, 
        feature: str, 
        amount: int = 1,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Check usage limits and consume usage if allowed.
        
        Args:
            user_id: User identifier
            feature: Feature being used
            amount: Amount of usage to consume
            metadata: Additional metadata for tracking
            
        Returns:
            Dict containing usage status and details
        """
        try:
            # Check if database is available
            if not mongodb:
                return {
                    "allowed": False,
                    "error": "Database not available",
                    "error_code": "DATABASE_UNAVAILABLE"
                }

            # Get user and subscription details
            user = await mongodb.users.find_one({"_id": ObjectId(user_id)})
            if not user:
                return {
                    "allowed": False,
                    "error": "User not found",
                    "error_code": "USER_NOT_FOUND"
                }
            
            # Check if this is an e-commerce feature
            if self._is_ecommerce_feature(feature):
                return await self._handle_ecommerce_usage(user_id, feature, amount, metadata)
            
            # Handle regular subscription features
            return await self._handle_subscription_usage(user, feature, amount, metadata)
            
        except Exception as e:
            logger.error(f"Error checking usage for user {user_id}, feature {feature}: {str(e)}")
            return {
                "allowed": False,
                "error": "Internal error checking usage",
                "error_code": "INTERNAL_ERROR"
            }
    
    def _is_ecommerce_feature(self, feature: str) -> bool:
        """Check if a feature is e-commerce related."""
        ecommerce_features = [
            "ecommerce_store_connections",
            "store_connections",
            "product_sync",
            "ecommerce_product_content",
            "product_content_generation",
            "ecommerce_icp_generation",
            "ecommerce_campaign_management"
        ]
        return feature in ecommerce_features
    
    async def _handle_ecommerce_usage(
        self, 
        user_id: str, 
        feature: str, 
        amount: int,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Handle usage for e-commerce features."""
        try:
            # Check e-commerce add-on access
            access_result = await self.ecommerce_addon_service.check_ecommerce_feature_access(
                user_id, feature, amount
            )
            
            if not access_result["has_access"]:
                return {
                    "allowed": False,
                    "error": access_result.get("error", "E-commerce feature access denied"),
                    "error_code": "ECOMMERCE_ACCESS_DENIED",
                    "requires_upgrade": access_result.get("requires_upgrade", True),
                    "recommended_addon": access_result.get("recommended_addon")
                }
            
            # Consume the usage
            consumption_result = await self.ecommerce_addon_service.consume_addon_usage(
                user_id, feature, amount
            )
            
            if not consumption_result["success"]:
                return {
                    "allowed": False,
                    "error": consumption_result.get("error", "Failed to consume e-commerce usage"),
                    "error_code": "ECOMMERCE_CONSUMPTION_FAILED"
                }
            
            # Record usage tracking
            await self._record_usage_event(user_id, feature, amount, "ecommerce", metadata)
            
            return {
                "allowed": True,
                "remaining_credits": consumption_result.get("remaining_credits", 0),
                "feature_type": "ecommerce"
            }
            
        except Exception as e:
            logger.error(f"Error handling e-commerce usage: {str(e)}")
            return {
                "allowed": False,
                "error": "Internal error handling e-commerce usage",
                "error_code": "ECOMMERCE_INTERNAL_ERROR"
            }
    
    async def _handle_subscription_usage(
        self, 
        user: Dict[str, Any], 
        feature: str, 
        amount: int,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Handle usage for regular subscription features."""
        try:
            subscription = user.get("subscription", {})
            plan_id = subscription.get("plan_id")
            
            if not plan_id:
                return {
                    "allowed": False,
                    "error": "No active subscription found",
                    "error_code": "NO_SUBSCRIPTION"
                }
            
            # Get plan limits
            plan_limits = await self._get_plan_limits(plan_id)
            if not plan_limits:
                return {
                    "allowed": False,
                    "error": "Invalid subscription plan",
                    "error_code": "INVALID_PLAN"
                }
            
            # Check feature limit
            feature_limit = plan_limits.get(feature)
            if feature_limit is None:
                return {
                    "allowed": False,
                    "error": f"Feature {feature} not available in current plan",
                    "error_code": "FEATURE_NOT_AVAILABLE"
                }
            
            # Get current usage
            current_usage = await self._get_current_usage(user["_id"], feature)
            
            # Check if usage would exceed limit
            if current_usage + amount > feature_limit:
                return {
                    "allowed": False,
                    "error": f"Usage limit exceeded for {feature}. Current: {current_usage}, Limit: {feature_limit}",
                    "error_code": "USAGE_LIMIT_EXCEEDED",
                    "current_usage": current_usage,
                    "limit": feature_limit,
                    "requires_upgrade": True
                }
            
            # Record the usage
            await self._record_usage_event(str(user["_id"]), feature, amount, "subscription", metadata)
            
            return {
                "allowed": True,
                "current_usage": current_usage + amount,
                "limit": feature_limit,
                "feature_type": "subscription"
            }
            
        except Exception as e:
            logger.error(f"Error handling subscription usage: {str(e)}")
            return {
                "allowed": False,
                "error": "Internal error handling subscription usage",
                "error_code": "SUBSCRIPTION_INTERNAL_ERROR"
            }
    
    async def _get_plan_limits(self, plan_id: str) -> Optional[Dict[str, Any]]:
        """Get feature limits for a subscription plan."""
        try:
            try:
                billing_module = importlib.import_module("app.services.billing")
                BillingService = billing_module.BillingService
                billing_service = BillingService()
            except ImportError:
                logger.warning("Billing service not available")
                return None

            plans = billing_service.get_subscription_plans()
            for plan in plans:
                if plan["id"] == plan_id:
                    return plan.get("feature_limits", {})

            return None

        except Exception as e:
            logger.error(f"Error getting plan limits: {str(e)}")
            return None
    
    async def _get_current_usage(self, user_id: ObjectId, feature: str) -> int:
        """Get current usage for a feature in the current billing period."""
        try:
            # Check if database is available
            if not mongodb:
                return 0

            # Calculate current billing period start
            now = datetime.now(timezone.utc)
            period_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

            # Get usage records for current period
            usage_records = await mongodb.usage_tracking.find({
                "user_id": user_id,
                "feature": feature,
                "created_at": {"$gte": period_start}
            }).to_list(None)

            # Sum up the usage
            total_usage = sum(record.get("amount", 0) for record in usage_records)
            return total_usage

        except Exception as e:
            logger.error(f"Error getting current usage: {str(e)}")
            return 0
    
    async def _record_usage_event(
        self,
        user_id: str,
        feature: str,
        amount: int,
        feature_type: str,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Record a usage event for tracking and analytics."""
        try:
            if not mongodb:
                return

            usage_record = {
                "user_id": ObjectId(user_id),
                "feature": feature,
                "feature_type": feature_type,
                "amount": amount,
                "metadata": metadata or {},
                "created_at": datetime.now(timezone.utc)
            }

            await mongodb.usage_tracking.insert_one(usage_record)

            # Record metrics for monitoring
            user = await mongodb.users.find_one({"_id": ObjectId(user_id)})
            subscription_tier = user.get("subscription", {}).get("plan_id", "unknown") if user else "unknown"

            monitoring.record_feature_access_attempt(
                feature=feature,
                result="granted",
                subscription_tier=subscription_tier
            )

        except Exception as e:
            logger.error(f"Error recording usage event: {str(e)}")
    
    async def get_user_usage_summary(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive usage summary for a user."""
        try:
            if not mongodb:
                return {"error": "Database not available"}

            user = await mongodb.users.find_one({"_id": ObjectId(user_id)})
            if not user:
                return {"error": "User not found"}

            # Get subscription limits
            subscription = user.get("subscription", {})
            plan_id = subscription.get("plan_id")
            plan_limits = await self._get_plan_limits(plan_id) if plan_id else {}

            # Get e-commerce limits
            ecommerce_limits = await self.ecommerce_addon_service.get_user_ecommerce_limits(user_id)

            # Get current usage for subscription features
            current_period_start = datetime.now(timezone.utc).replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            usage_records = await mongodb.usage_tracking.find({
                "user_id": ObjectId(user_id),
                "created_at": {"$gte": current_period_start}
            }).to_list(None)
            
            # Aggregate usage by feature
            usage_by_feature = {}
            for record in usage_records:
                feature = record["feature"]
                amount = record.get("amount", 0)
                usage_by_feature[feature] = usage_by_feature.get(feature, 0) + amount
            
            return {
                "user_id": user_id,
                "plan_id": plan_id,
                "subscription_limits": plan_limits,
                "ecommerce_limits": ecommerce_limits,
                "current_usage": usage_by_feature,
                "billing_period_start": current_period_start.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting usage summary: {str(e)}")
            return {"error": "Internal error getting usage summary"}


# Global instance
usage_tracking_service = UsageTrackingService()
