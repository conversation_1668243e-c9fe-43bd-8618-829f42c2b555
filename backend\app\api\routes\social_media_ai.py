"""
Social media AI response management routes.
Provides the missing API endpoints that aiResponseService.js requires for production functionality.
@since 2024-1-1 to 2025-25-7
"""
import uuid
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from fastapi.responses import JSONResponse

from app.core.security import get_current_active_user
from app.models.user import User
from app.models.brand_guidelines import CommentResponse
from app.schemas.social_media_ai import (
    SocialMediaPostWithPendingComments,
    SocialMediaCommentWithAIResponse,
    CommentApprovalRequest,
    CommentRejectionRequest,
    CommentRegenerationRequest,
    CommentEditRequest,
    AIResponseOperationResult,
    BulkOperationRequest,
    BulkOperationResult
)
from app.db.mongodb import get_database
from app.services.feature_access import has_feature_access, get_user_feature_limits
from app.services.credit_service import consume_credits, get_credit_balance
from app.services.redis_service import get_redis_client
from app.utils.correlation import generate_correlation_id
from app.utils.rate_limiting import check_rate_limit, update_rate_limit
from app.utils.openai_client import generate_ai_response
from app.core.config import settings

router = APIRouter()


@router.get("/posts/with-pending-comments", response_model=List[SocialMediaPostWithPendingComments])
async def get_posts_with_pending_comments(
    request: Request,
    platform: Optional[str] = Query(None, description="Filter by platform (linkedin, facebook, twitter, etc.)"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of posts to return"),
    offset: int = Query(0, ge=0, description="Number of posts to skip"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get social media posts that have comments awaiting AI responses.
    
    This endpoint provides the data structure expected by aiResponseService.js
    for the fetchPostsWithPendingComments() method.
    
    **Subscription Plan Access:**
    - Creator: Up to 10 posts
    - Accelerator: Up to 50 posts  
    - Dominator: Unlimited posts
    
    **Rate Limiting:**
    - Creator: 50 requests/minute
    - Accelerator: 200 requests/minute
    - Dominator: Unlimited
    """
    correlation_id = generate_correlation_id()
    
    try:
        # Validate subscription access and get plan-based limits
        subscription = await get_user_subscription(str(current_user.id))
        plan_limits = await validate_subscription_access(subscription, "ai_response_management")
        
        # Apply plan-based rate limiting
        rate_limit_key = f"ai_posts_fetch:{current_user.id}"
        await check_rate_limit(rate_limit_key, plan_limits.get("rate_limit", 50))
        
        # Apply plan-based result limits
        max_posts = plan_limits.get("max_posts_per_request", 50)
        effective_limit = min(limit, max_posts)

        # Redis caching for posts with pending comments
        redis_client = await get_redis_client()
        cache_key = f"ai_posts_pending:{current_user.id}:{platform or 'all'}:{offset}:{effective_limit}"
        cache_ttl = 60  # 1 minute cache for real-time data

        # Try to get from cache first
        if redis_client:
            try:
                cached_data = await redis_client.get(cache_key)
                if cached_data:
                    import json
                    posts = json.loads(cached_data)
                    print(f"[{correlation_id}] Cache hit for posts with pending comments")
                    return posts
            except Exception as e:
                print(f"[{correlation_id}] Redis cache read failed: {str(e)}")

        # Get database connection
        db = await get_database()
        
        # Build aggregation pipeline to find posts with pending comment responses
        pipeline = [
            # Match posts belonging to the current user
            {"$match": {"user_id": current_user.id}},
            
            # Lookup comment responses for each post
            {
                "$lookup": {
                    "from": "comment_responses",
                    "let": {"post_id": "$post_id", "platform": "$platform"},
                    "pipeline": [
                        {
                            "$match": {
                                "$expr": {
                                    "$and": [
                                        {"$eq": ["$post_id", "$$post_id"]},
                                        {"$eq": ["$platform", "$$platform"]},
                                        {"$eq": ["$user_id", current_user.id]}
                                    ]
                                }
                            }
                        }
                    ],
                    "as": "comment_responses"
                }
            },
            
            # Calculate pending comments count
            {
                "$addFields": {
                    "pending_comments_count": {
                        "$size": {
                            "$filter": {
                                "input": "$comment_responses",
                                "cond": {"$eq": ["$$this.status", "pending"]}
                            }
                        }
                    },
                    "total_comments_count": {"$size": "$comment_responses"}
                }
            },
            
            # Only include posts with pending comments
            {"$match": {"pending_comments_count": {"$gt": 0}}},
            
            # Apply platform filter if specified
            *([{"$match": {"platform": platform}}] if platform else []),
            
            # Sort by most recent comment activity
            {"$sort": {"last_comment_at": -1}},
            
            # Apply pagination
            {"$skip": offset},
            {"$limit": effective_limit},
            
            # Project to match expected schema
            {
                "$project": {
                    "id": {"$toString": "$_id"},
                    "platform": 1,
                    "post_id": 1,
                    "content_text": 1,
                    "author_name": 1,
                    "published_at": 1,
                    "engagement_metrics": 1,
                    "pending_comments_count": 1,
                    "total_comments_count": 1,
                    "last_comment_at": 1
                }
            }
        ]
        
        # Execute aggregation
        cursor = db.social_media_posts.aggregate(pipeline)
        posts = await cursor.to_list(length=effective_limit)

        # Cache the results in Redis
        if redis_client and posts:
            try:
                import json
                await redis_client.setex(cache_key, cache_ttl, json.dumps(posts, default=str))
                print(f"[{correlation_id}] Cached {len(posts)} posts with pending comments")
            except Exception as e:
                print(f"[{correlation_id}] Redis cache write failed: {str(e)}")

        # Update rate limiting
        await update_rate_limit(rate_limit_key, plan_limits.get("rate_limit", 50))

        # Log successful operation
        print(f"[{correlation_id}] Successfully fetched {len(posts)} posts with pending comments for user {current_user.email}")

        return posts
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"[{correlation_id}] Error fetching posts with pending comments: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "Failed to fetch posts with pending comments",
                "correlation_id": correlation_id,
                "error_type": "INTERNAL_SERVER_ERROR"
            }
        )


@router.get("/posts/{post_id}/comments", response_model=List[SocialMediaCommentWithAIResponse])
async def get_comments_for_post(
    post_id: str,
    request: Request,
    platform: Optional[str] = Query(None, description="Platform for additional validation"),
    include_published: bool = Query(False, description="Include already published responses"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get comments for a specific post with their AI response data.
    
    This endpoint provides the data structure expected by aiResponseService.js
    for the fetchCommentsForPost() method.
    
    **Subscription Plan Access:**
    - Creator: Basic comment data
    - Accelerator: Enhanced analytics data
    - Dominator: Full analytics and confidence scores
    """
    correlation_id = generate_correlation_id()
    
    try:
        # Validate subscription access
        subscription = await get_user_subscription(str(current_user.id))
        plan_limits = await validate_subscription_access(subscription, "ai_response_management")
        
        # Rate limiting
        rate_limit_key = f"ai_comments_fetch:{current_user.id}"
        await check_rate_limit(rate_limit_key, plan_limits.get("rate_limit", 50))

        # Redis caching for comments
        redis_client = await get_redis_client()
        cache_key = f"ai_comments:{current_user.id}:{post_id}:{platform or 'all'}:{include_published}"
        cache_ttl = 30  # 30 seconds cache for real-time comment data

        # Try to get from cache first
        if redis_client:
            try:
                cached_data = await redis_client.get(cache_key)
                if cached_data:
                    import json
                    comments = json.loads(cached_data)
                    print(f"[{correlation_id}] Cache hit for comments on post {post_id}")
                    return comments
            except Exception as e:
                print(f"[{correlation_id}] Redis cache read failed: {str(e)}")

        # Get database connection
        db = await get_database()
        
        # Build query filter
        query_filter = {
            "user_id": current_user.id,
            "post_id": post_id
        }
        
        if platform:
            query_filter["platform"] = platform
            
        if not include_published:
            query_filter["status"] = {"$ne": "published"}
        
        # Fetch comment responses
        cursor = db.comment_responses.find(query_filter).sort("created_at", -1)
        comment_responses = await cursor.to_list(length=None)
        
        # Transform to expected schema format
        comments = []
        for response in comment_responses:
            comment_data = {
                "id": str(response["_id"]),
                "comment_id": response["comment_id"],
                "post_id": response["post_id"],
                "platform": response["platform"],
                "comment_text": response["comment_text"],
                "comment_author": response["comment_author"],
                "comment_author_id": response.get("comment_author_id"),
                "created_at": response["created_at"],
                "sentiment": response.get("comment_sentiment"),
                "parent_comment_id": response.get("parent_comment_id"),
                "ai_response_text": response.get("generated_response"),
                "ai_response_status": response["status"],
                "ai_response_confidence": response.get("confidence_score"),
                "ai_response_generated_at": response.get("created_at"),
                "ai_response_approved_by": response.get("approved_by"),
                "ai_response_approved_at": response.get("approved_at"),
                "ai_response_published_at": response.get("published_at"),
                "ai_response_edit_history": response.get("edit_history", [])
            }
            
            # Plan-based data filtering
            if subscription.get("plan_id") == "creator":
                # Basic plan - remove advanced analytics
                comment_data.pop("ai_response_confidence", None)
                comment_data["ai_response_edit_history"] = []
            
            comments.append(comment_data)

        # Cache the results in Redis
        if redis_client and comments:
            try:
                import json
                await redis_client.setex(cache_key, cache_ttl, json.dumps(comments, default=str))
                print(f"[{correlation_id}] Cached {len(comments)} comments for post {post_id}")
            except Exception as e:
                print(f"[{correlation_id}] Redis cache write failed: {str(e)}")

        # Update rate limiting
        await update_rate_limit(rate_limit_key, plan_limits.get("rate_limit", 50))

        print(f"[{correlation_id}] Successfully fetched {len(comments)} comments for post {post_id}")

        return comments
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"[{correlation_id}] Error fetching comments for post {post_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "Failed to fetch comments for post",
                "correlation_id": correlation_id,
                "error_type": "INTERNAL_SERVER_ERROR"
            }
        )


@router.post("/comments/{comment_id}/approve", response_model=AIResponseOperationResult)
async def approve_ai_response(
    comment_id: str,
    approval_data: CommentApprovalRequest,
    request: Request,
    current_user: User = Depends(get_current_active_user)
):
    """
    Approve an AI-generated response for a comment.

    This endpoint provides the functionality expected by aiResponseService.js
    for the approveResponse() method.

    **Credit Consumption:**
    - Creator: 1 credit per approval
    - Accelerator: 0.5 credits per approval
    - Dominator: No credit consumption
    """
    correlation_id = generate_correlation_id()

    try:
        # Validate subscription and consume credits
        subscription = await get_user_subscription(str(current_user.id))
        plan_limits = await validate_subscription_access(subscription, "ai_response_approval")

        # Credit consumption based on plan
        credit_cost = plan_limits.get("approval_credit_cost", 1)
        if credit_cost > 0:
            credit_result = await consume_credits(
                str(current_user.id),
                credit_cost,
                "ai_response_approval",
                metadata={"comment_id": comment_id, "correlation_id": correlation_id}
            )
            if not credit_result.get("success", False):
                raise HTTPException(
                    status_code=status.HTTP_402_PAYMENT_REQUIRED,
                    detail={
                        "message": "Insufficient credits for AI response approval",
                        "credits_required": credit_cost,
                        "credits_available": credit_result.get("remaining_credits", 0),
                        "correlation_id": correlation_id
                    }
                )

        # Rate limiting
        rate_limit_key = f"ai_approve:{current_user.id}"
        await check_rate_limit(rate_limit_key, plan_limits.get("rate_limit", 50))

        # Get database connection
        db = await get_database()

        # Find the comment response
        comment_response = await db.comment_responses.find_one({
            "_id": comment_id,
            "user_id": current_user.id
        })

        if not comment_response:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "message": "Comment response not found",
                    "comment_id": comment_id,
                    "correlation_id": correlation_id
                }
            )

        # Validate current status
        if comment_response["status"] not in ["pending", "rejected"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": f"Cannot approve response with status '{comment_response['status']}'",
                    "current_status": comment_response["status"],
                    "correlation_id": correlation_id
                }
            )

        # Prepare update data
        update_data = {
            "status": "approved",
            "approved_by": current_user.email,
            "approved_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }

        # Update response text if provided
        final_response_text = comment_response["generated_response"]
        if approval_data.response_text and approval_data.response_text.strip():
            final_response_text = approval_data.response_text.strip()
            update_data["generated_response"] = final_response_text

            # Add to edit history
            edit_entry = {
                "timestamp": datetime.now(timezone.utc),
                "editor": current_user.email,
                "action": "approval_edit",
                "previous_text": comment_response["generated_response"],
                "new_text": final_response_text,
                "notes": approval_data.approval_notes
            }
            update_data["$push"] = {"edit_history": edit_entry}

        # Add approval notes if provided
        if approval_data.approval_notes:
            update_data["approval_notes"] = approval_data.approval_notes

        # Consume credits based on plan (only for paid plans)
        credit_cost = plan_limits.get("approval_credit_cost", 1.0)
        if credit_cost > 0:
            try:
                credit_result = await consume_credits(
                    user_id=str(current_user.id),
                    credit_cost=credit_cost,
                    action="ai_response_approval",
                    metadata={
                        "comment_id": str(comment_id),
                        "post_id": comment_response.get("post_id"),
                        "platform": comment_response.get("platform"),
                        "correlation_id": correlation_id
                    }
                )

                if not credit_result.get("success", False):
                    raise HTTPException(
                        status_code=402,
                        detail={
                            "message": "Insufficient credits for AI response approval",
                            "required_credits": credit_cost,
                            "available_credits": credit_result.get("available", 0),
                            "correlation_id": correlation_id
                        }
                    )

                print(f"[{correlation_id}] Consumed {credit_cost} credits for AI response approval")

            except Exception as e:
                print(f"[{correlation_id}] Credit consumption failed: {str(e)}")
                # For now, continue without blocking the operation
                # In production, you might want to block based on business rules

        # Update the comment response
        result = await db.comment_responses.update_one(
            {"_id": comment_id, "user_id": current_user.id},
            {"$set": update_data} if "$push" not in update_data else {
                "$set": {k: v for k, v in update_data.items() if k != "$push"},
                "$push": update_data["$push"]
            }
        )

        if result.modified_count == 0:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "message": "Failed to update comment response",
                    "correlation_id": correlation_id
                }
            )

        # Invalidate related caches
        redis_client = await get_redis_client()
        if redis_client:
            try:
                # Invalidate post-specific comment cache
                cache_pattern = f"ai_comments:{current_user.id}:{comment_response['post_id']}:*"
                # Invalidate posts with pending comments cache
                posts_cache_pattern = f"ai_posts_pending:{current_user.id}:*"

                # Note: In production, you'd want to use Redis SCAN for pattern deletion
                # For now, we'll invalidate specific known keys
                await redis_client.delete(f"ai_comments:{current_user.id}:{comment_response['post_id']}:*")
                await redis_client.delete(f"ai_posts_pending:{current_user.id}:*")
                print(f"[{correlation_id}] Invalidated caches after comment approval")
            except Exception as e:
                print(f"[{correlation_id}] Cache invalidation failed: {str(e)}")

        # Update rate limiting
        await update_rate_limit(rate_limit_key, plan_limits.get("rate_limit", 50))

        # Log successful operation
        print(f"[{correlation_id}] AI response approved for comment {comment_id} by user {current_user.email}")

        return AIResponseOperationResult(
            success=True,
            message="AI response approved successfully",
            comment_id=comment_id,
            new_status="approved",
            updated_response_text=final_response_text,
            operation_timestamp=datetime.now(timezone.utc),
            correlation_id=correlation_id
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"[{correlation_id}] Error approving AI response for comment {comment_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "Failed to approve AI response",
                "correlation_id": correlation_id,
                "error_type": "INTERNAL_SERVER_ERROR"
            }
        )


@router.post("/comments/{comment_id}/reject", response_model=AIResponseOperationResult)
async def reject_ai_response(
    comment_id: str,
    rejection_data: CommentRejectionRequest,
    request: Request,
    current_user: User = Depends(get_current_active_user)
):
    """
    Reject an AI-generated response for a comment.

    This endpoint provides the functionality expected by aiResponseService.js
    for the rejectResponse() method.
    """
    correlation_id = generate_correlation_id()

    try:
        # Validate subscription access
        subscription = await get_user_subscription(str(current_user.id))
        plan_limits = await validate_subscription_access(subscription, "ai_response_management")

        # Rate limiting
        rate_limit_key = f"ai_reject:{current_user.id}"
        await check_rate_limit(rate_limit_key, plan_limits.get("rate_limit", 50))

        # Get database connection
        db = await get_database()

        # Find the comment response
        comment_response = await db.comment_responses.find_one({
            "_id": comment_id,
            "user_id": current_user.id
        })

        if not comment_response:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "message": "Comment response not found",
                    "comment_id": comment_id,
                    "correlation_id": correlation_id
                }
            )

        # Validate current status
        if comment_response["status"] not in ["pending", "approved"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": f"Cannot reject response with status '{comment_response['status']}'",
                    "current_status": comment_response["status"],
                    "correlation_id": correlation_id
                }
            )

        # Prepare update data
        update_data = {
            "status": "rejected",
            "rejected_by": current_user.email,
            "rejected_at": datetime.now(timezone.utc),
            "rejection_reason": rejection_data.rejection_reason,
            "updated_at": datetime.now(timezone.utc)
        }

        if rejection_data.rejection_notes:
            update_data["rejection_notes"] = rejection_data.rejection_notes

        # Add to edit history
        edit_entry = {
            "timestamp": datetime.now(timezone.utc),
            "editor": current_user.email,
            "action": "rejection",
            "reason": rejection_data.rejection_reason,
            "notes": rejection_data.rejection_notes
        }

        # Update the comment response
        result = await db.comment_responses.update_one(
            {"_id": comment_id, "user_id": current_user.id},
            {
                "$set": update_data,
                "$push": {"edit_history": edit_entry}
            }
        )

        if result.modified_count == 0:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "message": "Failed to update comment response",
                    "correlation_id": correlation_id
                }
            )

        # Update rate limiting
        await update_rate_limit(rate_limit_key, plan_limits.get("rate_limit", 50))

        # Log successful operation
        print(f"[{correlation_id}] AI response rejected for comment {comment_id} by user {current_user.email}")

        return AIResponseOperationResult(
            success=True,
            message="AI response rejected successfully",
            comment_id=comment_id,
            new_status="rejected",
            updated_response_text=None,
            operation_timestamp=datetime.now(timezone.utc),
            correlation_id=correlation_id
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"[{correlation_id}] Error rejecting AI response for comment {comment_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "Failed to reject AI response",
                "correlation_id": correlation_id,
                "error_type": "INTERNAL_SERVER_ERROR"
            }
        )


@router.post("/comments/{comment_id}/regenerate", response_model=AIResponseOperationResult)
async def regenerate_ai_response(
    comment_id: str,
    regeneration_data: CommentRegenerationRequest,
    request: Request,
    current_user: User = Depends(get_current_active_user)
):
    """
    Regenerate an AI response for a comment with new parameters.

    This endpoint provides the functionality expected by aiResponseService.js
    for the regenerateResponse() method.

    **Credit Consumption:**
    - Creator: 2 credits per regeneration
    - Accelerator: 1 credit per regeneration
    - Dominator: 0.5 credits per regeneration
    """
    correlation_id = generate_correlation_id()

    try:
        # Validate subscription and consume credits
        subscription = await get_user_subscription(str(current_user.id))
        plan_limits = await validate_subscription_access(subscription, "ai_response_regeneration")

        # Credit consumption based on plan
        credit_cost = plan_limits.get("regeneration_credit_cost", 2)
        if credit_cost > 0:
            try:
                credit_result = await consume_credits(
                    user_id=str(current_user.id),
                    credit_cost=credit_cost,
                    action="ai_response_regeneration",
                    metadata={"comment_id": comment_id, "correlation_id": correlation_id}
                )

                if not credit_result.get("success", False):
                    raise HTTPException(
                        status_code=status.HTTP_402_PAYMENT_REQUIRED,
                        detail={
                            "message": "Insufficient credits for AI response regeneration",
                            "required_credits": credit_cost,
                            "available_credits": credit_result.get("available", 0),
                            "correlation_id": correlation_id
                        }
                    )

                print(f"[{correlation_id}] Consumed {credit_cost} credits for AI response regeneration")

            except Exception as e:
                print(f"[{correlation_id}] Credit consumption failed: {str(e)}")
                # For regeneration, we might want to block if credits fail
                raise HTTPException(
                    status_code=status.HTTP_402_PAYMENT_REQUIRED,
                    detail={
                        "message": "Credit system unavailable for regeneration",
                        "correlation_id": correlation_id
                    }
                )

        # Rate limiting
        rate_limit_key = f"ai_regenerate:{current_user.id}"
        await check_rate_limit(rate_limit_key, plan_limits.get("rate_limit", 20))  # Lower limit for expensive operations

        # Get database connection
        db = await get_database()

        # Find the comment response
        comment_response = await db.comment_responses.find_one({
            "_id": comment_id,
            "user_id": current_user.id
        })

        if not comment_response:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "message": "Comment response not found",
                    "comment_id": comment_id,
                    "correlation_id": correlation_id
                }
            )

        # Store previous response for history
        previous_response = comment_response.get("generated_response", "")

        # Generate new AI response with enhanced prompt
        enhanced_prompt = f"Original comment: {comment_response['comment_text']}\n"
        if regeneration_data.regeneration_prompt:
            enhanced_prompt += f"Additional instructions: {regeneration_data.regeneration_prompt}\n"
        if regeneration_data.tone_adjustment:
            enhanced_prompt += f"Tone adjustment: {regeneration_data.tone_adjustment}\n"
        if regeneration_data.length_preference:
            enhanced_prompt += f"Length preference: {regeneration_data.length_preference}\n"

        # Call AI service to generate new response
        new_response = await generate_ai_response(
            comment_text=comment_response["comment_text"],
            context=enhanced_prompt,
            platform=comment_response["platform"],
            user_id=str(current_user.id),
            tone_adjustment=regeneration_data.tone_adjustment,
            length_preference=regeneration_data.length_preference
        )

        # Prepare update data
        update_data = {
            "generated_response": new_response["text"],
            "confidence_score": new_response.get("confidence", 0.8),
            "status": "pending",  # Reset to pending after regeneration
            "regenerated_at": datetime.now(timezone.utc),
            "regenerated_by": current_user.email,
            "updated_at": datetime.now(timezone.utc)
        }

        # Add to edit history
        edit_entry = {
            "timestamp": datetime.now(timezone.utc),
            "editor": current_user.email,
            "action": "regeneration",
            "previous_text": previous_response,
            "new_text": new_response["text"],
            "regeneration_prompt": regeneration_data.regeneration_prompt,
            "tone_adjustment": regeneration_data.tone_adjustment,
            "length_preference": regeneration_data.length_preference
        }

        # Update the comment response
        result = await db.comment_responses.update_one(
            {"_id": comment_id, "user_id": current_user.id},
            {
                "$set": update_data,
                "$push": {"edit_history": edit_entry}
            }
        )

        if result.modified_count == 0:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "message": "Failed to update comment response",
                    "correlation_id": correlation_id
                }
            )

        # Update rate limiting
        await update_rate_limit(rate_limit_key, plan_limits.get("rate_limit", 20))

        # Log successful operation
        print(f"[{correlation_id}] AI response regenerated for comment {comment_id} by user {current_user.email}")

        return AIResponseOperationResult(
            success=True,
            message="AI response regenerated successfully",
            comment_id=comment_id,
            new_status="pending",
            updated_response_text=new_response["text"],
            operation_timestamp=datetime.now(timezone.utc),
            correlation_id=correlation_id
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"[{correlation_id}] Error regenerating AI response for comment {comment_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "Failed to regenerate AI response",
                "correlation_id": correlation_id,
                "error_type": "INTERNAL_SERVER_ERROR"
            }
        )


@router.put("/comments/{comment_id}/edit", response_model=AIResponseOperationResult)
async def edit_ai_response(
    comment_id: str,
    edit_data: CommentEditRequest,
    request: Request,
    current_user: User = Depends(get_current_active_user)
):
    """
    Edit an AI-generated response for a comment.

    This endpoint provides the functionality expected by aiResponseService.js
    for the editResponse() method.

    **Subscription Plan Access:**
    - Creator: Basic editing (text only)
    - Accelerator: Enhanced editing with history tracking
    - Dominator: Full editing capabilities with advanced analytics
    """
    correlation_id = generate_correlation_id()

    try:
        # Validate subscription access
        subscription = await get_user_subscription(str(current_user.id))
        plan_limits = await validate_subscription_access(subscription, "ai_response_editing")

        # Rate limiting
        rate_limit_key = f"ai_edit:{current_user.id}"
        await check_rate_limit(rate_limit_key, plan_limits.get("rate_limit", 100))

        # Get database connection
        db = await get_database()

        # Find the comment response
        comment_response = await db.comment_responses.find_one({
            "_id": comment_id,
            "user_id": current_user.id
        })

        if not comment_response:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "message": "Comment response not found",
                    "comment_id": comment_id,
                    "correlation_id": correlation_id
                }
            )

        # Validate edit permissions based on current status
        if comment_response["status"] == "published":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Cannot edit published responses",
                    "current_status": comment_response["status"],
                    "correlation_id": correlation_id
                }
            )

        # Store previous response for history
        previous_response = comment_response.get("generated_response", "")

        # Validate edited text
        if not edit_data.edited_text or not edit_data.edited_text.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Edited text cannot be empty",
                    "correlation_id": correlation_id
                }
            )

        # Prepare update data
        update_data = {
            "generated_response": edit_data.edited_text.strip(),
            "edited_at": datetime.now(timezone.utc),
            "edited_by": current_user.email,
            "updated_at": datetime.now(timezone.utc),
            "is_ai_generated": edit_data.preserve_ai_flag
        }

        # Reset status to pending if it was rejected
        if comment_response["status"] == "rejected":
            update_data["status"] = "pending"

        # Add to edit history (plan-dependent feature)
        edit_entry = {
            "timestamp": datetime.now(timezone.utc),
            "editor": current_user.email,
            "action": "manual_edit",
            "previous_text": previous_response,
            "new_text": edit_data.edited_text.strip(),
            "notes": edit_data.edit_notes,
            "preserve_ai_flag": edit_data.preserve_ai_flag
        }

        # Plan-based feature restrictions
        update_operations = {"$set": update_data}
        if subscription.get("plan_id") in ["accelerator", "dominator"]:
            # Enhanced plans get edit history tracking
            update_operations["$push"] = {"edit_history": edit_entry}

        # Update the comment response
        result = await db.comment_responses.update_one(
            {"_id": comment_id, "user_id": current_user.id},
            update_operations
        )

        if result.modified_count == 0:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "message": "Failed to update comment response",
                    "correlation_id": correlation_id
                }
            )

        # Update rate limiting
        await update_rate_limit(rate_limit_key, plan_limits.get("rate_limit", 100))

        # Log successful operation
        print(f"[{correlation_id}] AI response edited for comment {comment_id} by user {current_user.email}")

        return AIResponseOperationResult(
            success=True,
            message="AI response edited successfully",
            comment_id=comment_id,
            new_status=update_data.get("status", comment_response["status"]),
            updated_response_text=edit_data.edited_text.strip(),
            operation_timestamp=datetime.now(timezone.utc),
            correlation_id=correlation_id
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"[{correlation_id}] Error editing AI response for comment {comment_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "Failed to edit AI response",
                "correlation_id": correlation_id,
                "error_type": "INTERNAL_SERVER_ERROR"
            }
        )


# Helper functions for subscription and plan management
async def get_user_subscription(user_id: str) -> dict:
    """
    Get user subscription information.

    Args:
        user_id (str): User ID

    Returns:
        dict: Subscription information with plan_id
    """
    # For now, return a mock subscription based on user model
    # In production, this would fetch from the subscription service
    return {
        "plan_id": "creator",  # Default plan
        "is_active": True,
        "features": []
    }


async def validate_subscription_access(subscription: dict, feature: str) -> dict:
    """
    Validate subscription access for AI response features and return plan limits.

    Args:
        subscription (dict): User subscription information
        feature (str): Feature being accessed

    Returns:
        dict: Plan limits and access information
    """
    plan_id = subscription.get("plan_id", "creator")

    # Plan-based limits for AI response management
    plan_limits = {
        "creator": {
            "rate_limit": 50,                    # 50 requests/minute
            "max_posts_per_request": 10,         # Max 10 posts per request
            "approval_credit_cost": 1,           # 1 credit per approval
            "regeneration_credit_cost": 2,       # 2 credits per regeneration
            "max_regenerations_per_day": 20,     # 20 regenerations per day
            "edit_history_enabled": False,       # No edit history tracking
            "advanced_analytics": False          # No advanced analytics
        },
        "accelerator": {
            "rate_limit": 200,                   # 200 requests/minute
            "max_posts_per_request": 50,         # Max 50 posts per request
            "approval_credit_cost": 0.5,         # 0.5 credits per approval
            "regeneration_credit_cost": 1,       # 1 credit per regeneration
            "max_regenerations_per_day": 100,    # 100 regenerations per day
            "edit_history_enabled": True,        # Edit history tracking enabled
            "advanced_analytics": True           # Advanced analytics enabled
        },
        "dominator": {
            "rate_limit": -1,                    # Unlimited
            "max_posts_per_request": -1,         # Unlimited
            "approval_credit_cost": 0,           # No credit cost
            "regeneration_credit_cost": 0.5,     # 0.5 credits per regeneration
            "max_regenerations_per_day": -1,     # Unlimited
            "edit_history_enabled": True,        # Edit history tracking enabled
            "advanced_analytics": True           # Advanced analytics enabled
        }
    }

    return plan_limits.get(plan_id, plan_limits["creator"])
