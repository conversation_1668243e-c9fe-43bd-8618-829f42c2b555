// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import CustomCard from '../CustomCard';

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      error: {
        main: '#F44336',
      },
      warning: {
        main: '#FF9800',
      },
      success: {
        main: '#4CAF50',
      },
      info: {
        main: '#2196F3',
      },
      text: {
        secondary: '#666666',
      },
      divider: '#E0E0E0',
      background: {
        paper: '#FFFFFF',
      },
      grey: {
        900: '#212121',
      },
      common: {
        white: '#FFFFFF',
      },
    },
    spacing: (factor) => `${8 * factor}px`,
    shadows: [
      'none',
      '0px 2px 4px rgba(0,0,0,0.1)',
      '0px 4px 8px rgba(0,0,0,0.15)',
      '0px 8px 16px rgba(0,0,0,0.2)',
    ],
    transitions: {
      create: () => 'all 0.3s ease',
      duration: {
        short: 250,
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('CustomCard', () => {
  const mockProps = {
    children: <div>Card content</div>
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders card with children correctly', () => {
    render(
      <TestWrapper>
        <CustomCard {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Card content')).toBeInTheDocument();
  });

  test('applies different variants correctly', () => {
    const { rerender } = render(
      <TestWrapper>
        <CustomCard {...mockProps} variant="glass" />
      </TestWrapper>
    );

    let card = screen.getByRole('article');
    expect(card).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <CustomCard {...mockProps} variant="primary" />
      </TestWrapper>
    );

    card = screen.getByRole('article');
    expect(card).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <CustomCard {...mockProps} variant="error" />
      </TestWrapper>
    );

    card = screen.getByRole('article');
    expect(card).toBeInTheDocument();
  });

  test('handles click events', async () => {
    const user = userEvent.setup();
    const onClick = vi.fn();
    
    render(
      <TestWrapper>
        <CustomCard {...mockProps} onClick={onClick} />
      </TestWrapper>
    );

    const card = screen.getByRole('article');
    await user.click(card);

    expect(onClick).toHaveBeenCalled();
  });

  test('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    const onClick = vi.fn();
    
    render(
      <TestWrapper>
        <CustomCard {...mockProps} onClick={onClick} />
      </TestWrapper>
    );

    const card = screen.getByRole('article');
    card.focus();
    await user.keyboard('{Enter}');

    expect(onClick).toHaveBeenCalled();

    onClick.mockClear();
    await user.keyboard(' ');

    expect(onClick).toHaveBeenCalled();
  });

  test('shows loading state correctly', () => {
    render(
      <TestWrapper>
        <CustomCard {...mockProps} loading={true} loadingText="Loading data..." />
      </TestWrapper>
    );

    expect(screen.getByText('Loading data...')).toBeInTheDocument();
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('shows error state correctly', () => {
    const error = new Error('Test error');
    
    render(
      <TestWrapper>
        <CustomCard 
          {...mockProps} 
          error={error} 
          errorText="Failed to load"
          retryable={true}
          onRetry={vi.fn()}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Failed to load')).toBeInTheDocument();
    expect(screen.getByText('Test error')).toBeInTheDocument();
    expect(screen.getByLabelText('Retry loading content')).toBeInTheDocument();
  });

  test('handles retry functionality', async () => {
    const user = userEvent.setup();
    const onRetry = vi.fn();
    
    render(
      <TestWrapper>
        <CustomCard 
          {...mockProps} 
          error="Test error"
          retryable={true}
          onRetry={onRetry}
        />
      </TestWrapper>
    );

    const retryButton = screen.getByLabelText('Retry loading content');
    await user.click(retryButton);

    expect(onRetry).toHaveBeenCalled();
  });

  test('shows header when configured', () => {
    render(
      <TestWrapper>
        <CustomCard 
          {...mockProps} 
          showHeader={true}
          title="Card Title"
          subtitle="Card Subtitle"
        />
      </TestWrapper>
    );

    expect(screen.getByText('Card Title')).toBeInTheDocument();
    expect(screen.getByText('Card Subtitle')).toBeInTheDocument();
  });

  test('handles interactive features', async () => {
    const user = userEvent.setup();
    const onLike = vi.fn();
    const onBookmark = vi.fn();
    const onShare = vi.fn();
    
    render(
      <TestWrapper>
        <CustomCard 
          {...mockProps} 
          interactive={true}
          showActions={true}
          onLike={onLike}
          onBookmark={onBookmark}
          onShare={onShare}
          viewCount={42}
        />
      </TestWrapper>
    );

    // Test like functionality
    const likeButton = screen.getByLabelText('Like this content');
    await user.click(likeButton);
    expect(onLike).toHaveBeenCalledWith(true);

    // Test bookmark functionality
    const bookmarkButton = screen.getByLabelText('Bookmark this content');
    await user.click(bookmarkButton);
    expect(onBookmark).toHaveBeenCalledWith(true);

    // Test share functionality
    const shareButton = screen.getByLabelText('Share this content');
    await user.click(shareButton);
    expect(onShare).toHaveBeenCalled();

    // Check view count
    expect(screen.getByText('42')).toBeInTheDocument();
  });

  test('shows liked and bookmarked states', () => {
    render(
      <TestWrapper>
        <CustomCard 
          {...mockProps} 
          interactive={true}
          showActions={true}
          liked={true}
          bookmarked={true}
          onLike={vi.fn()}
          onBookmark={vi.fn()}
        />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Unlike this content')).toBeInTheDocument();
    expect(screen.getByLabelText('Remove bookmark')).toBeInTheDocument();
  });

  test('handles drag and drop', async () => {
    const onDragStart = vi.fn();
    const onDragEnd = vi.fn();
    
    render(
      <TestWrapper>
        <CustomCard 
          {...mockProps} 
          draggable={true}
          onDragStart={onDragStart}
          onDragEnd={onDragEnd}
        />
      </TestWrapper>
    );

    const card = screen.getByRole('article');
    
    fireEvent.dragStart(card);
    expect(onDragStart).toHaveBeenCalled();

    fireEvent.dragEnd(card);
    expect(onDragEnd).toHaveBeenCalled();
  });

  test('shows tags when provided', () => {
    const tags = ['React', 'JavaScript', 'UI'];
    
    render(
      <TestWrapper>
        <CustomCard {...mockProps} tags={tags} />
      </TestWrapper>
    );

    tags.forEach(tag => {
      expect(screen.getByText(tag)).toBeInTheDocument();
    });
  });

  test('handles fullscreen toggle', async () => {
    const user = userEvent.setup();
    const onFullscreenToggle = vi.fn();
    
    render(
      <TestWrapper>
        <CustomCard 
          {...mockProps} 
          interactive={true}
          showActions={true}
          onFullscreenToggle={onFullscreenToggle}
        />
      </TestWrapper>
    );

    const fullscreenButton = screen.getByLabelText('Enter fullscreen mode');
    await user.click(fullscreenButton);

    expect(onFullscreenToggle).toHaveBeenCalled();
  });

  test('shows fullscreen exit when in fullscreen', () => {
    render(
      <TestWrapper>
        <CustomCard 
          {...mockProps} 
          interactive={true}
          showActions={true}
          fullscreen={true}
          onFullscreenToggle={vi.fn()}
        />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Exit fullscreen mode')).toBeInTheDocument();
  });

  test('handles custom actions', async () => {
    const user = userEvent.setup();
    const customAction = vi.fn();
    
    const actions = [
      {
        icon: <span>Custom</span>,
        onClick: customAction,
        tooltip: 'Custom action',
        ariaLabel: 'Perform custom action'
      }
    ];

    render(
      <TestWrapper>
        <CustomCard 
          {...mockProps} 
          showHeader={true}
          actions={actions}
        />
      </TestWrapper>
    );

    const actionButton = screen.getByLabelText('Perform custom action');
    await user.click(actionButton);

    expect(customAction).toHaveBeenCalled();
  });

  test('handles focus and blur events', async () => {
    const user = userEvent.setup();
    const onFocus = vi.fn();
    const onBlur = vi.fn();
    
    render(
      <TestWrapper>
        <CustomCard 
          {...mockProps} 
          onClick={vi.fn()}
          onFocus={onFocus}
          onBlur={onBlur}
        />
      </TestWrapper>
    );

    const card = screen.getByRole('article');
    
    await user.click(card);
    card.focus();
    expect(onFocus).toHaveBeenCalled();

    card.blur();
    expect(onBlur).toHaveBeenCalled();
  });

  test('tracks analytics when enabled', async () => {
    const user = userEvent.setup();
    const onAnalytics = vi.fn();
    
    render(
      <TestWrapper>
        <CustomCard 
          {...mockProps} 
          onClick={vi.fn()}
          enableAnalytics={true}
          onAnalytics={onAnalytics}
        />
      </TestWrapper>
    );

    const card = screen.getByRole('article');
    await user.click(card);

    expect(onAnalytics).toHaveBeenCalledWith(
      expect.objectContaining({
        component: 'CustomCard',
        action: 'click'
      })
    );
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <CustomCard 
          {...mockProps} 
          title="Test Card"
          ariaLabel="Custom card label"
          onClick={vi.fn()}
        />
      </TestWrapper>
    );

    const card = screen.getByRole('article');
    expect(card).toHaveAttribute('aria-label', 'Custom card label');
    expect(card).toHaveAttribute('tabIndex', '0');
  });

  test('applies custom styles', () => {
    const customSx = { backgroundColor: 'red' };
    
    render(
      <TestWrapper>
        <CustomCard {...mockProps} sx={customSx} />
      </TestWrapper>
    );

    const card = screen.getByRole('article');
    expect(card).toBeInTheDocument();
  });

  test('handles different elevation levels', () => {
    const { rerender } = render(
      <TestWrapper>
        <CustomCard {...mockProps} elevation={0} />
      </TestWrapper>
    );

    let card = screen.getByRole('article');
    expect(card).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <CustomCard {...mockProps} elevation={8} />
      </TestWrapper>
    );

    card = screen.getByRole('article');
    expect(card).toBeInTheDocument();
  });

  test('disables hover effects when not hoverable', () => {
    render(
      <TestWrapper>
        <CustomCard {...mockProps} hoverable={false} />
      </TestWrapper>
    );

    const card = screen.getByRole('article');
    expect(card).toBeInTheDocument();
  });

  test('shows background image when provided', () => {
    render(
      <TestWrapper>
        <CustomCard 
          {...mockProps} 
          backgroundImage="https://example.com/image.jpg"
          blurStrength={5}
          opacity={0.8}
        />
      </TestWrapper>
    );

    const card = screen.getByRole('article');
    expect(card).toBeInTheDocument();
  });
});
