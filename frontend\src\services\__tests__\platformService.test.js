/**
 * Comprehensive unit tests for Enhanced Platform Service
 * Tests all platform configurations, methods, error handling, and new enhanced features
 *
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 * @since 2024-01-01
 @since 2024-1-1 to 2025-25-7
*/

import platformService from '../platformService';

// Mock dependencies for enhanced features
jest.mock('../fingerprint', () => ({
  getDeviceFingerprint: jest.fn().mockResolvedValue('mock-device-fingerprint')
}));

jest.mock('../../utils/PrometheusMetricsCollector', () => ({
  PrometheusMetricsCollector: jest.fn().mockImplementation(() => ({
    recordCustomMetric: jest.fn(),
    recordResponseTime: jest.fn(),
    recordError: jest.fn(),
    recordCacheHitRate: jest.fn(),
    recordErrorCount: jest.fn(),
    recordAverageResponseTime: jest.fn()
  }))
}));

// Mock WebSocket
global.WebSocket = jest.fn().mockImplementation(() => ({
  onopen: null,
  onmessage: null,
  onclose: null,
  onerror: null,
  close: jest.fn(),
  send: jest.fn()
}));

// Mock SubtleCrypto
Object.defineProperty(global, 'crypto', {
  value: {
    subtle: {
      digest: jest.fn().mockResolvedValue(new ArrayBuffer(32))
    }
  }
});

// Mock window for event dispatching
Object.defineProperty(global, 'window', {
  value: {
    location: {
      protocol: 'https:',
      host: 'localhost:3000'
    },
    dispatchEvent: jest.fn(),
    CustomEvent: jest.fn()
  }
});

describe('PlatformService', () => {
  describe('Platform Configuration', () => {
    test('should return correct platform configuration for supported platforms', () => {
      const facebook = platformService.getPlatform('facebook');
      expect(facebook.name).toBe('Facebook');
      expect(facebook.color).toBe('#1877F2');
      expect(facebook.maxCharacters).toBe(63206);
      expect(facebook.engagementTypes).toContain('likes');
      expect(facebook.engagementTypes).toContain('comments');
      expect(facebook.engagementTypes).toContain('shares');
    });

    test('should return default platform for unknown platforms', () => {
      const unknown = platformService.getPlatform('unknown-platform');
      expect(unknown.name).toBe('unknown-platform');
      expect(unknown.color).toBe('#666666');
      expect(unknown.maxCharacters).toBe(1000);
    });

    test('should handle null/undefined platform names', () => {
      const nullPlatform = platformService.getPlatform(null);
      expect(nullPlatform.name).toBe('Unknown Platform');
      
      const undefinedPlatform = platformService.getPlatform(undefined);
      expect(undefinedPlatform.name).toBe('Unknown Platform');
    });
  });

  describe('Platform Name Normalization', () => {
    test('should normalize platform names correctly', () => {
      expect(platformService.normalizePlatformName('Facebook')).toBe('facebook');
      expect(platformService.normalizePlatformName('TWITTER')).toBe('twitter');
      expect(platformService.normalizePlatformName('  LinkedIn  ')).toBe('linkedin');
    });

    test('should handle platform aliases', () => {
      expect(platformService.normalizePlatformName('fb')).toBe('facebook');
      expect(platformService.normalizePlatformName('ig')).toBe('instagram');
      expect(platformService.normalizePlatformName('yt')).toBe('youtube');
      expect(platformService.normalizePlatformName('li')).toBe('linkedin');
      expect(platformService.normalizePlatformName('tw')).toBe('twitter');
      expect(platformService.normalizePlatformName('tt')).toBe('tiktok');
      expect(platformService.normalizePlatformName('pin')).toBe('pinterest');
    });

    test('should handle invalid inputs', () => {
      expect(platformService.normalizePlatformName(null)).toBe('unknown');
      expect(platformService.normalizePlatformName(undefined)).toBe('unknown');
      expect(platformService.normalizePlatformName(123)).toBe('unknown');
      expect(platformService.normalizePlatformName('')).toBe('unknown');
    });
  });

  describe('Character Limits and Validation', () => {
    test('should return correct character limits', () => {
      expect(platformService.getCharacterLimit('twitter')).toBe(280);
      expect(platformService.getCharacterLimit('linkedin')).toBe(3000);
      expect(platformService.getCharacterLimit('facebook')).toBe(63206);
      expect(platformService.getCharacterLimit('instagram')).toBe(2200);
      expect(platformService.getCharacterLimit('tiktok')).toBe(2200);
      expect(platformService.getCharacterLimit('threads')).toBe(500);
    });

    test('should validate content length correctly', () => {
      const shortContent = 'Short content';
      const longContent = 'a'.repeat(300);

      const twitterValidation = platformService.validateContentLength('twitter', shortContent);
      expect(twitterValidation.valid).toBe(true);
      expect(twitterValidation.remaining).toBe(280 - shortContent.length);

      const twitterLongValidation = platformService.validateContentLength('twitter', longContent);
      expect(twitterLongValidation.valid).toBe(false);
      expect(twitterLongValidation.remaining).toBe(280 - 300);
    });

    test('should handle empty content validation', () => {
      const validation = platformService.validateContentLength('twitter', '');
      expect(validation.valid).toBe(true);
      expect(validation.remaining).toBe(280);
    });

    test('should handle null content validation', () => {
      const validation = platformService.validateContentLength('twitter', null);
      expect(validation.valid).toBe(true);
      expect(validation.remaining).toBe(280);
    });
  });

  describe('Engagement Formatting', () => {
    test('should format engagement numbers correctly', () => {
      expect(platformService.formatEngagement('facebook', 'likes', 1234)).toBe('1.2K');
      expect(platformService.formatEngagement('facebook', 'likes', 1234567)).toBe('1.2M');
      expect(platformService.formatEngagement('facebook', 'likes', 123)).toBe('123');
    });

    test('should handle platform-specific engagement formatting', () => {
      // YouTube views formatting
      expect(platformService.formatEngagement('youtube', 'views', 1234567)).toBe('1.23M views');
      expect(platformService.formatEngagement('youtube', 'views', 1234)).toBe('1.2K views');
      
      // Reddit upvotes formatting
      expect(platformService.formatEngagement('reddit', 'upvotes', 1234)).toBe('1.2K');
      expect(platformService.formatEngagement('reddit', 'upvotes', -1234)).toBe('-1.2K');
      
      // Pinterest saves formatting
      expect(platformService.formatEngagement('pinterest', 'saves', 1234)).toBe('1.2K saves');
      
      // LinkedIn reactions formatting
      expect(platformService.formatEngagement('linkedin', 'reactions', 1234)).toBe('1.2K');
      
      // Twitter retweets formatting
      expect(platformService.formatEngagement('twitter', 'retweets', 1234)).toBe('1.2K RT');
    });

    test('should handle invalid engagement values', () => {
      expect(platformService.formatEngagement('facebook', 'likes', null)).toBe('0');
      expect(platformService.formatEngagement('facebook', 'likes', undefined)).toBe('0');
      expect(platformService.formatEngagement('facebook', 'likes', 'invalid')).toBe('0');
    });
  });

  describe('Platform Features', () => {
    test('should check platform feature support correctly', () => {
      expect(platformService.supportsFeature('instagram', 'hasImages')).toBe(true);
      expect(platformService.supportsFeature('instagram', 'hasVideos')).toBe(true);
      expect(platformService.supportsFeature('instagram', 'hasStories')).toBe(true);
      expect(platformService.supportsFeature('instagram', 'hasReels')).toBe(true);
      
      expect(platformService.supportsFeature('twitter', 'hasPolls')).toBe(true);
      expect(platformService.supportsFeature('twitter', 'hasThreads')).toBe(true);
      
      expect(platformService.supportsFeature('linkedin', 'hasDocuments')).toBe(true);
      expect(platformService.supportsFeature('linkedin', 'hasArticles')).toBe(true);
      
      expect(platformService.supportsFeature('youtube', 'hasLiveStreaming')).toBe(true);
      expect(platformService.supportsFeature('youtube', 'hasShorts')).toBe(true);
    });

    test('should return false for unsupported features', () => {
      expect(platformService.supportsFeature('twitter', 'hasStories')).toBe(false);
      expect(platformService.supportsFeature('unknown-platform', 'hasImages')).toBe(true); // Default platform has images
    });
  });

  describe('Engagement Types and Labels', () => {
    test('should return correct engagement types for platforms', () => {
      const facebookTypes = platformService.getEngagementTypes('facebook');
      expect(facebookTypes).toEqual(['likes', 'comments', 'shares']);
      
      const twitterTypes = platformService.getEngagementTypes('twitter');
      expect(twitterTypes).toEqual(['likes', 'retweets', 'replies']);
      
      const linkedinTypes = platformService.getEngagementTypes('linkedin');
      expect(linkedinTypes).toEqual(['reactions', 'comments', 'shares']);
    });

    test('should return correct engagement labels', () => {
      expect(platformService.getEngagementLabel('facebook', 'likes')).toBe('Likes');
      expect(platformService.getEngagementLabel('twitter', 'retweets')).toBe('Retweets');
      expect(platformService.getEngagementLabel('linkedin', 'reactions')).toBe('Reactions');
      expect(platformService.getEngagementLabel('pinterest', 'saves')).toBe('Saves');
      expect(platformService.getEngagementLabel('reddit', 'upvotes')).toBe('Upvotes');
    });

    test('should handle engagement type aliases', () => {
      expect(platformService.getEngagementLabel('linkedin', 'likes')).toBe('Reactions');
      expect(platformService.getEngagementLabel('pinterest', 'likes')).toBe('Saves');
      expect(platformService.getEngagementLabel('reddit', 'likes')).toBe('Upvotes');
    });
  });

  describe('Platform Colors and Icons', () => {
    test('should return correct platform colors', () => {
      expect(platformService.getPlatformColor('facebook')).toBe('#1877F2');
      expect(platformService.getPlatformColor('twitter')).toBe('#1DA1F2');
      expect(platformService.getPlatformColor('linkedin')).toBe('#0A66C2');
      expect(platformService.getPlatformColor('instagram')).toBe('#E4405F');
      expect(platformService.getPlatformColor('youtube')).toBe('#FF0000');
      expect(platformService.getPlatformColor('tiktok')).toBe('#000000');
      expect(platformService.getPlatformColor('pinterest')).toBe('#BD081C');
      expect(platformService.getPlatformColor('reddit')).toBe('#FF4500');
    });

    test('should return platform icons', () => {
      const facebookIcon = platformService.getPlatformIcon('facebook');
      expect(facebookIcon).toBeDefined();
      expect(facebookIcon.props.sx.color).toBe('#1877F2');
    });
  });

  describe('Platform Support', () => {
    test('should correctly identify supported platforms', () => {
      expect(platformService.isSupported('facebook')).toBe(true);
      expect(platformService.isSupported('twitter')).toBe(true);
      expect(platformService.isSupported('linkedin')).toBe(true);
      expect(platformService.isSupported('instagram')).toBe(true);
      expect(platformService.isSupported('youtube')).toBe(true);
      expect(platformService.isSupported('tiktok')).toBe(true);
      expect(platformService.isSupported('pinterest')).toBe(true);
      expect(platformService.isSupported('reddit')).toBe(true);
      expect(platformService.isSupported('threads')).toBe(true);

      expect(platformService.isSupported('unknown-platform')).toBe(false);
    });

    test('should return all supported platforms', () => {
      const allPlatforms = platformService.getAllPlatforms();
      expect(allPlatforms).toHaveLength(9); // 8 main platforms + threads
      expect(allPlatforms.map(p => p.key)).toContain('facebook');
      expect(allPlatforms.map(p => p.key)).toContain('twitter');
      expect(allPlatforms.map(p => p.key)).toContain('linkedin');
      expect(allPlatforms.map(p => p.key)).toContain('instagram');
      expect(allPlatforms.map(p => p.key)).toContain('youtube');
      expect(allPlatforms.map(p => p.key)).toContain('tiktok');
      expect(allPlatforms.map(p => p.key)).toContain('pinterest');
      expect(allPlatforms.map(p => p.key)).toContain('reddit');
      expect(allPlatforms.map(p => p.key)).toContain('threads');
    });
  });

  describe('Hashtag Recommendations', () => {
    test('should return correct hashtag recommendations', () => {
      const twitterRec = platformService.getHashtagRecommendations('twitter');
      expect(twitterRec.max_hashtags).toBe(5);
      expect(twitterRec.description).toContain('1-2 highly relevant hashtags');

      const instagramRec = platformService.getHashtagRecommendations('instagram');
      expect(instagramRec.max_hashtags).toBe(30);
      expect(instagramRec.description).toContain('5-15 relevant hashtags');

      const linkedinRec = platformService.getHashtagRecommendations('linkedin');
      expect(linkedinRec.max_hashtags).toBe(5);
      expect(linkedinRec.description).toContain('3-5 relevant, professional hashtags');
    });

    test('should return default recommendations for unknown platforms', () => {
      const unknownRec = platformService.getHashtagRecommendations('unknown-platform');
      expect(unknownRec.max_hashtags).toBe(5); // LinkedIn default
    });
  });

  describe('Image Recommendations', () => {
    test('should return correct image recommendations', () => {
      const twitterRec = platformService.getImageRecommendations('twitter');
      expect(twitterRec.aspect_ratio).toBe('16:9');
      expect(twitterRec.supports_carousel).toBe(false);
      expect(twitterRec.supports_video).toBe(true);

      const instagramRec = platformService.getImageRecommendations('instagram');
      expect(instagramRec.aspect_ratio).toBe('1:1 (square), 4:5 (portrait)');
      expect(instagramRec.supports_carousel).toBe(true);
      expect(instagramRec.formats).toEqual(['JPG', 'PNG']);

      const linkedinRec = platformService.getImageRecommendations('linkedin');
      expect(linkedinRec.supports_pdf).toBe(true);
      expect(linkedinRec.supports_carousel).toBe(true);
    });

    test('should return default recommendations for unknown platforms', () => {
      const unknownRec = platformService.getImageRecommendations('unknown-platform');
      expect(unknownRec.aspect_ratio).toBe('1.91:1'); // LinkedIn default
    });
  });

  describe('Response Tone Suggestions', () => {
    test('should return platform-specific tone suggestions', () => {
      const linkedinTones = platformService.getResponseToneSuggestions('linkedin');
      expect(linkedinTones).toContain('professional');
      expect(linkedinTones).toContain('thought-leadership');

      const twitterTones = platformService.getResponseToneSuggestions('twitter');
      expect(twitterTones).toContain('conversational');
      expect(twitterTones).toContain('witty');

      const instagramTones = platformService.getResponseToneSuggestions('instagram');
      expect(instagramTones).toContain('visual-focused');
      expect(instagramTones).toContain('lifestyle');

      const tiktokTones = platformService.getResponseToneSuggestions('tiktok');
      expect(tiktokTones).toContain('trendy');
      expect(tiktokTones).toContain('fun');
    });

    test('should return base tones for unknown platforms', () => {
      const unknownTones = platformService.getResponseToneSuggestions('unknown-platform');
      expect(unknownTones).toContain('professional');
      expect(unknownTones).toContain('friendly');
      expect(unknownTones).toContain('casual');
      expect(unknownTones).toContain('helpful');
    });
  });

  describe('Content Guidelines', () => {
    test('should return platform-specific content guidelines', () => {
      const linkedinGuidelines = platformService.getContentGuidelines('linkedin');
      expect(linkedinGuidelines).toContain('Focus on professional value');
      expect(linkedinGuidelines).toContain('Share industry insights');

      const twitterGuidelines = platformService.getContentGuidelines('twitter');
      expect(twitterGuidelines).toContain('Keep responses concise');
      expect(twitterGuidelines).toContain('Use relevant hashtags sparingly');

      const instagramGuidelines = platformService.getContentGuidelines('instagram');
      expect(instagramGuidelines).toContain('Use high-quality visuals');
      expect(instagramGuidelines).toContain('Include relevant hashtags');
    });

    test('should include base guidelines for all platforms', () => {
      const guidelines = platformService.getContentGuidelines('facebook');
      expect(guidelines).toContain('Be respectful and professional');
      expect(guidelines).toContain('Avoid spam or promotional content');
      expect(guidelines).toContain('Respect community guidelines');
    });
  });

  // ===================================================================
  // ENHANCED FEATURES TESTS
  // ===================================================================

  describe('Enhanced Features - Message Deduplication', () => {
    test('should generate message fingerprint for platform', async () => {
      const fingerprint = await platformService.generateMessageFingerprint(
        'linkedin',
        'Hello world! This is a test message.',
        { conversationId: 'conv_123', userId: 'user_456' }
      );

      expect(fingerprint).toBeDefined();
      expect(typeof fingerprint).toBe('string');
      expect(fingerprint.length).toBeGreaterThan(0);
    });

    test('should generate consistent fingerprints for identical content', async () => {
      const content = 'Identical message content';
      const options = { conversationId: 'conv_123', userId: 'user_456' };

      const fingerprint1 = await platformService.generateMessageFingerprint('facebook', content, options);
      const fingerprint2 = await platformService.generateMessageFingerprint('facebook', content, options);

      expect(fingerprint1).toBe(fingerprint2);
    });

    test('should generate different fingerprints for different platforms', async () => {
      const content = 'Same message content';
      const options = { conversationId: 'conv_123', userId: 'user_456' };

      const linkedinFingerprint = await platformService.generateMessageFingerprint('linkedin', content, options);
      const twitterFingerprint = await platformService.generateMessageFingerprint('twitter', content, options);

      expect(linkedinFingerprint).not.toBe(twitterFingerprint);
    });

    test('should detect duplicate messages', async () => {
      const messages = [
        { platform: 'linkedin', content: 'Hello world!', conversationId: 'conv_1', userId: 'user_1' },
        { platform: 'linkedin', content: 'Hello world!', conversationId: 'conv_1', userId: 'user_1' },
        { platform: 'twitter', content: 'Different message', conversationId: 'conv_2', userId: 'user_2' }
      ];

      const result = await platformService.detectDuplicateMessages(messages);

      expect(result.duplicates).toHaveLength(1);
      expect(result.duplicates[0].type).toBe('exact');
      expect(result.totalMessages).toBe(3);
      expect(result.duplicateCount).toBe(1);
      expect(result.uniqueMessages).toBe(2);
    });

    test('should detect similar messages with high similarity', async () => {
      const messages = [
        { platform: 'facebook', content: 'Hello world how are you', conversationId: 'conv_1', userId: 'user_1' },
        { platform: 'facebook', content: 'Hello world how are you today', conversationId: 'conv_1', userId: 'user_1' }
      ];

      const result = await platformService.detectDuplicateMessages(messages, { similarityThreshold: 0.8 });

      expect(result.duplicates).toHaveLength(1);
      expect(result.duplicates[0].type).toBe('similar');
      expect(result.duplicates[0].similarity).toBeGreaterThan(0.8);
    });

    test('should handle empty message arrays', async () => {
      const result = await platformService.detectDuplicateMessages([]);

      expect(result.duplicates).toHaveLength(0);
      expect(result.totalMessages).toBe(0);
      expect(result.duplicateCount).toBe(0);
      expect(result.uniqueMessages).toBe(0);
    });
  });

  describe('Enhanced Features - Platform Fingerprinting', () => {
    test('should generate platform-specific device fingerprint', async () => {
      const fingerprint = await platformService.generatePlatformFingerprint('linkedin');

      expect(fingerprint).toBeDefined();
      expect(fingerprint.platform).toBeDefined();
      expect(fingerprint.platform.name).toBe('LinkedIn');
      expect(fingerprint.platform.maxCharacters).toBe(3000);
      expect(fingerprint.platform.supportedFeatures).toContain('hasImages');
      expect(fingerprint.security).toBeDefined();
      expect(fingerprint.security.timestamp).toBeDefined();
    });

    test('should include platform capabilities in fingerprint', async () => {
      const fingerprint = await platformService.generatePlatformFingerprint('instagram');

      expect(fingerprint.platform.capabilities).toBeDefined();
      expect(fingerprint.platform.capabilities.maxCharacters).toBe(2200);
      expect(fingerprint.platform.capabilities.engagementTypes).toBeGreaterThan(0);
      expect(fingerprint.platform.capabilities.features).toBeGreaterThan(0);
    });

    test('should generate different fingerprints for different platforms', async () => {
      const linkedinFingerprint = await platformService.generatePlatformFingerprint('linkedin');
      const twitterFingerprint = await platformService.generatePlatformFingerprint('twitter');

      expect(linkedinFingerprint.platform.name).toBe('LinkedIn');
      expect(twitterFingerprint.platform.name).toBe('Twitter');
      expect(linkedinFingerprint.platform.maxCharacters).not.toBe(twitterFingerprint.platform.maxCharacters);
    });

    test('should handle enhanced fingerprinting options', async () => {
      const fingerprint = await platformService.generatePlatformFingerprint('facebook', {
        enhanced: true,
        sessionId: 'test-session-123'
      });

      expect(fingerprint.enhanced).toBe(true);
      expect(fingerprint.security.sessionId).toBe('test-session-123');
    });
  });

  describe('Enhanced Features - Metrics Integration', () => {
    test('should track platform requests', () => {
      // Clear any existing metrics
      if (platformService.metrics) {
        platformService.metrics.platformRequests.clear();
      }

      // Make platform requests
      platformService.getPlatform('linkedin');
      platformService.getPlatform('twitter');
      platformService.getPlatform('linkedin'); // Duplicate

      // Check metrics tracking
      if (platformService.metrics?.platformRequests) {
        expect(platformService.metrics.platformRequests.get('linkedin')).toBe(2);
        expect(platformService.metrics.platformRequests.get('twitter')).toBe(1);
      }
    });

    test('should track engagement formatting requests', () => {
      // Clear any existing metrics
      if (platformService.metrics) {
        platformService.metrics.engagementFormatting.clear();
      }

      // Make formatting requests
      platformService.formatEngagement('facebook', 'likes', 1234);
      platformService.formatEngagement('twitter', 'retweets', 567);

      // Check metrics tracking
      if (platformService.metrics?.engagementFormatting) {
        expect(platformService.metrics.engagementFormatting.get('facebook_likes')).toBe(1);
        expect(platformService.metrics.engagementFormatting.get('twitter_retweets')).toBe(1);
      }
    });

    test('should track feature checks', () => {
      // Clear any existing metrics
      if (platformService.metrics) {
        platformService.metrics.featureChecks.clear();
      }

      // Make feature check requests
      platformService.supportsFeature('instagram', 'hasStories');
      platformService.supportsFeature('youtube', 'hasLiveStreaming');

      // Check metrics tracking
      if (platformService.metrics?.featureChecks) {
        expect(platformService.metrics.featureChecks.get('instagram_hasStories')).toBe(1);
        expect(platformService.metrics.featureChecks.get('youtube_hasLiveStreaming')).toBe(1);
      }
    });
  });

  describe('Enhanced Features - Security & Integrity', () => {
    test('should validate configuration integrity', () => {
      // This test verifies that the integrity checking system is working
      expect(platformService.configurationHash).toBeDefined();
      expect(typeof platformService.configurationHash).toBe('string');
      expect(platformService.configurationHash.length).toBeGreaterThan(0);
    });

    test('should handle security configuration', () => {
      expect(platformService.securityConfig).toBeDefined();
      expect(typeof platformService.securityConfig.integrityCheckEnabled).toBe('boolean');
    });

    test('should generate consistent configuration hashes', () => {
      const hash1 = platformService._generateConfigurationHash();
      const hash2 = platformService._generateConfigurationHash();

      expect(hash1).toBe(hash2);
    });
  });
});
