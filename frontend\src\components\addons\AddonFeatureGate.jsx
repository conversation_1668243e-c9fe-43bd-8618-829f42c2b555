/**
 * Feature gate component that checks both subscription limits AND add-on availability
 * Shows upgrade prompts when users hit limits
 @since 2024-1-1 to 2025-25-7
*/
import { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useAuth } from '../../hooks/useAuth';
import { useAddons } from '../../hooks/useAddons';
import { Button, Alert, Badge, Progress, Tooltip, Modal } from 'antd';
import {
  LockOutlined,
  RocketOutlined,
  TrophyOutlined,
  InfoCircleOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import './AddonFeatureGate.css';

const AddonFeatureGate = ({ 
  feature, 
  usageType, 
  currentUsage = 0, 
  baseLimit, 
  children, 
  fallbackComponent = null,
  showUpgradePrompt = true,
  className = ''
}) => {
  useAuth(); // Ensure user is authenticated
  const {
    checkFeatureAccess,
    getAddonEnhancedLimits,
    getRelevantAddons,
    trackFeatureAttempt
  } = useAddons();
  
  const [accessInfo, setAccessInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [relevantAddons, setRelevantAddons] = useState([]);

  const checkAccess = useCallback(async () => {
    try {
      setLoading(true);
      
      // Check feature access including add-ons
      const access = await checkFeatureAccess(feature, usageType);
      const enhancedLimits = await getAddonEnhancedLimits(usageType);
      const addons = await getRelevantAddons(usageType);
      
      setAccessInfo({
        hasAccess: access.hasAccess,
        totalLimit: enhancedLimits.totalLimit || baseLimit,
        baseLimit: baseLimit,
        addonBonus: enhancedLimits.addonBonus || 0,
        remainingUsage: Math.max(0, (enhancedLimits.totalLimit || baseLimit) - currentUsage),
        usagePercentage: ((currentUsage / (enhancedLimits.totalLimit || baseLimit)) * 100),
        blockingReason: access.blockingReason,
        suggestedAction: access.suggestedAction
      });
      
      setRelevantAddons(addons);
    } catch (error) {
      console.error('Error checking feature access:', error);
      setAccessInfo({
        hasAccess: false,
        totalLimit: baseLimit,
        baseLimit: baseLimit,
        addonBonus: 0,
        remainingUsage: Math.max(0, baseLimit - currentUsage),
        usagePercentage: (currentUsage / baseLimit) * 100,
        blockingReason: 'Error checking access',
        suggestedAction: 'Try again later'
      });
    } finally {
      setLoading(false);
    }
  }, [feature, usageType, currentUsage, checkFeatureAccess, getAddonEnhancedLimits, getRelevantAddons, baseLimit]);

  useEffect(() => {
    checkAccess();
  }, [checkAccess]);

  const handleFeatureAttempt = useCallback(async () => {
    if (!accessInfo?.hasAccess) {
      await trackFeatureAttempt(feature, usageType, 'blocked');
      
      if (showUpgradePrompt) {
        setShowUpgradeModal(true);
      }
      return false;
    }
    
    await trackFeatureAttempt(feature, usageType, 'allowed');
    return true;
  }, [accessInfo, feature, usageType, showUpgradePrompt, trackFeatureAttempt]);

  const renderUsageIndicator = () => {
    if (!accessInfo) return null;

    const { usagePercentage, remainingUsage, totalLimit, baseLimit, addonBonus } = accessInfo;
    
    let status = 'normal';
    let strokeColor = '#52c41a';
    
    if (usagePercentage >= 95) {
      status = 'exception';
      strokeColor = '#ff4d4f';
    } else if (usagePercentage >= 80) {
      status = 'active';
      strokeColor = '#faad14';
    }

    return (
      <div className="addon-usage-indicator">
        <div className="usage-header">
          <span className="usage-label">{feature} Usage</span>
          <span className="usage-count">
            {currentUsage} / {totalLimit}
            {addonBonus > 0 && (
              <Tooltip title={`Base limit: ${baseLimit}, Add-on bonus: +${addonBonus}`}>
                <Badge 
                  count={`+${addonBonus}`} 
                  style={{ backgroundColor: '#722ed1', marginLeft: 8 }}
                />
              </Tooltip>
            )}
          </span>
        </div>
        
        <Progress
          percent={Math.min(usagePercentage, 100)}
          status={status}
          strokeColor={strokeColor}
          size="small"
          showInfo={false}
        />
        
        <div className="usage-footer">
          <span className="remaining-text">
            {remainingUsage > 0 ? `${remainingUsage} remaining` : 'Limit reached'}
          </span>
          {usagePercentage >= 75 && (
            <Button 
              type="link" 
              size="small" 
              icon={<RocketOutlined />}
              onClick={() => setShowUpgradeModal(true)}
            >
              Upgrade
            </Button>
          )}
        </div>
      </div>
    );
  };

  const renderBlockedState = () => {
    const { blockingReason, suggestedAction } = accessInfo;
    
    return (
      <div className={`addon-feature-gate blocked ${className}`}>
        <div className="blocked-content">
          <div className="blocked-icon">
            <LockOutlined />
          </div>
          
          <div className="blocked-message">
            <h4>Feature Limited</h4>
            <p>{blockingReason}</p>
            
            {showUpgradePrompt && (
              <div className="upgrade-actions">
                <Button 
                  type="primary" 
                  icon={<ThunderboltOutlined />}
                  onClick={() => setShowUpgradeModal(true)}
                >
                  {suggestedAction || 'Upgrade Now'}
                </Button>
                
                {relevantAddons.length > 0 && (
                  <Button 
                    type="default" 
                    icon={<InfoCircleOutlined />}
                    onClick={() => setShowUpgradeModal(true)}
                  >
                    View Add-ons
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
        
        {fallbackComponent && (
          <div className="fallback-content">
            {fallbackComponent}
          </div>
        )}
      </div>
    );
  };

  const renderUpgradeModal = () => {
    return (
      <Modal
        title="Upgrade Your Limits"
        open={showUpgradeModal}
        onCancel={() => setShowUpgradeModal(false)}
        footer={null}
        width={600}
        className="addon-upgrade-modal"
      >
        <div className="upgrade-modal-content">
          <div className="current-status">
            <h4>Current Usage</h4>
            {renderUsageIndicator()}
          </div>
          
          {relevantAddons.length > 0 && (
            <div className="addon-recommendations">
              <h4>Recommended Add-ons</h4>
              <div className="addon-grid">
                {relevantAddons.map((addon) => (
                  <div key={addon.id} className="addon-card">
                    <div className="addon-header">
                      <h5>{addon.name}</h5>
                      {addon.is_popular && (
                        <Badge count="Popular" style={{ backgroundColor: '#722ed1' }} />
                      )}
                    </div>
                    
                    <p className="addon-description">{addon.description}</p>
                    
                    <div className="addon-features">
                      {addon.features.slice(0, 3).map((feature, index) => (
                        <div key={index} className="feature-item">
                          <TrophyOutlined /> {feature}
                        </div>
                      ))}
                    </div>
                    
                    <div className="addon-pricing">
                      {addon.pricing.basic && (
                        <div className="price-option">
                          <span className="price">${addon.pricing.basic.price}</span>
                          <span className="credits">+{addon.pricing.basic.credits} credits</span>
                        </div>
                      )}
                    </div>
                    
                    <Button 
                      type="primary" 
                      block
                      onClick={() => window.location.href = `/addons/${addon.id}`}
                    >
                      Get Add-on
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          <div className="plan-upgrade-option">
            <Alert
              message="Consider upgrading your plan"
              description="Higher plans include more generous limits and additional features."
              type="info"
              showIcon
              action={
                <Button size="small" onClick={() => window.location.href = '/billing'}>
                  View Plans
                </Button>
              }
            />
          </div>
        </div>
      </Modal>
    );
  };

  if (loading) {
    return (
      <div className={`addon-feature-gate loading ${className}`}>
        <div className="loading-content">
          Loading feature access...
        </div>
      </div>
    );
  }

  if (!accessInfo?.hasAccess) {
    return (
      <>
        {renderBlockedState()}
        {showUpgradeModal && renderUpgradeModal()}
      </>
    );
  }

  // Feature is accessible - render children with usage indicator
  return (
    <div className={`addon-feature-gate accessible ${className}`}>
      {accessInfo.usagePercentage >= 50 && renderUsageIndicator()}
      
      <div 
        className="feature-content"
        onClick={handleFeatureAttempt}
      >
        {children}
      </div>
      
      {showUpgradeModal && renderUpgradeModal()}
    </div>
  );
};

// PropTypes for type checking
AddonFeatureGate.propTypes = {
  feature: PropTypes.string.isRequired,
  usageType: PropTypes.string.isRequired,
  currentUsage: PropTypes.number,
  baseLimit: PropTypes.number.isRequired,
  children: PropTypes.node.isRequired,
  fallbackComponent: PropTypes.node,
  showUpgradePrompt: PropTypes.bool,
  className: PropTypes.string
};

// Default props
AddonFeatureGate.defaultProps = {
  currentUsage: 0,
  fallbackComponent: null,
  showUpgradePrompt: true,
  className: ''
};

export default AddonFeatureGate;
