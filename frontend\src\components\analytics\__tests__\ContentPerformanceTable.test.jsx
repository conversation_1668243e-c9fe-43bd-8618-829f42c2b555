/**
 * Tests for ContentPerformanceTable component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import ContentPerformanceTable from '../ContentPerformanceTable';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the hooks and API
vi.mock('../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showErrorNotification: vi.fn(),
    showInfoNotification: vi.fn()
  }))
}));

vi.mock('../../api', () => ({
  default: {
    get: vi.fn()
  }
}));

describe('ContentPerformanceTable', () => {
  const mockApi = require('../../api').default;
  const mockNotification = {
    showErrorNotification: vi.fn(),
    showInfoNotification: vi.fn()
  };

  const mockData = {
    content_performance: [
      {
        id: '1',
        title: 'Amazing Product Launch',
        platform: 'LinkedIn',
        content_type: 'Image',
        published_at: '2023-01-15T10:00:00Z',
        impressions: 15000,
        engagements: 750,
        engagement_rate: 5.0,
        engagement_rate_change: 0.5,
        image_url: 'https://example.com/image1.jpg',
        url: 'https://linkedin.com/post/1'
      },
      {
        id: '2',
        title: 'Industry Insights Report',
        platform: 'Twitter',
        content_type: 'Text',
        published_at: '2023-01-14T14:30:00Z',
        impressions: 8500,
        engagements: 425,
        engagement_rate: 5.0,
        engagement_rate_change: -0.2,
        image_url: null,
        url: 'https://twitter.com/post/2'
      },
      {
        id: '3',
        title: 'Behind the Scenes Video',
        platform: 'Instagram',
        content_type: 'Video',
        published_at: '2023-01-13T16:45:00Z',
        impressions: 22000,
        engagements: 1320,
        engagement_rate: 6.0,
        engagement_rate_change: 1.2,
        image_url: 'https://example.com/image3.jpg',
        url: 'https://instagram.com/post/3'
      }
    ]
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockApi.get.mockResolvedValue({ data: mockData });

    const { useNotification } = require('../../hooks/useNotification');
    useNotification.mockReturnValue(mockNotification);
  });

  test('renders content performance table', async () => {
    render(
      <TestWrapper>
        <ContentPerformanceTable />
      </TestWrapper>
    );

    expect(screen.getByText('Content Performance')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search content...')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('Amazing Product Launch')).toBeInTheDocument();
      expect(screen.getByText('Industry Insights Report')).toBeInTheDocument();
      expect(screen.getByText('Behind the Scenes Video')).toBeInTheDocument();
    });
  });

  test('displays content data correctly', async () => {
    render(
      <TestWrapper>
        <ContentPerformanceTable data={mockData} />
      </TestWrapper>
    );

    expect(screen.getByText('Amazing Product Launch')).toBeInTheDocument();
    expect(screen.getByText('LinkedIn')).toBeInTheDocument();
    expect(screen.getByText('Image')).toBeInTheDocument();
    expect(screen.getByText('15,000')).toBeInTheDocument(); // Impressions
    expect(screen.getByText('750')).toBeInTheDocument(); // Engagements
    expect(screen.getByText('5.00%')).toBeInTheDocument(); // Engagement rate
  });

  test('shows loading state when loading prop is true', () => {
    render(
      <TestWrapper>
        <ContentPerformanceTable loading={true} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('handles search functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ContentPerformanceTable data={mockData} />
      </TestWrapper>
    );

    const searchInput = screen.getByPlaceholderText('Search content...');
    await user.type(searchInput, 'Amazing');

    expect(screen.getByText('Amazing Product Launch')).toBeInTheDocument();
    expect(screen.queryByText('Industry Insights Report')).not.toBeInTheDocument();
    expect(screen.queryByText('Behind the Scenes Video')).not.toBeInTheDocument();
  });

  test('handles sorting functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ContentPerformanceTable data={mockData} />
      </TestWrapper>
    );

    // Click on Impressions column to sort
    const impressionsHeader = screen.getByText('Impressions');
    await user.click(impressionsHeader);

    // Should sort by impressions (ascending first click)
    const rows = screen.getAllByRole('row');
    expect(rows[1]).toHaveTextContent('Industry Insights Report'); // Lowest impressions first
  });

  test('handles pagination', async () => {
    const user = userEvent.setup();
    
    // Create data with more than 10 items to test pagination
    const largeData = {
      content_performance: Array.from({ length: 25 }, (_, i) => ({
        id: `${i + 1}`,
        title: `Content ${i + 1}`,
        platform: 'LinkedIn',
        content_type: 'Text',
        published_at: '2023-01-15T10:00:00Z',
        impressions: 1000 + i * 100,
        engagements: 50 + i * 5,
        engagement_rate: 5.0,
        engagement_rate_change: 0,
        image_url: null,
        url: `https://example.com/post/${i + 1}`
      }))
    };

    render(
      <TestWrapper>
        <ContentPerformanceTable data={largeData} />
      </TestWrapper>
    );

    // Should show pagination controls
    expect(screen.getByText('1–10 of 25')).toBeInTheDocument();

    // Click next page
    const nextPageButton = screen.getByLabelText('Go to next page');
    await user.click(nextPageButton);

    expect(screen.getByText('11–20 of 25')).toBeInTheDocument();
  });

  test('handles refresh functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ContentPerformanceTable />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledTimes(1);
    });

    // Clear previous calls
    vi.clearAllMocks();

    // Click refresh button
    const refreshButton = screen.getByLabelText('Refresh content performance data');
    await user.click(refreshButton);

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/analytics/content-performance', {
        params: { limit: 50, include_charts: false }
      });
    });
  });

  test('handles export functionality', async () => {
    const user = userEvent.setup();
    
    // Mock document.createElement and related methods
    const mockLink = {
      setAttribute: vi.fn(),
      click: vi.fn(),
      style: {}
    };
    const mockCreateElement = vi.spyOn(document, 'createElement').mockReturnValue(mockLink);
    const mockAppendChild = vi.spyOn(document.body, 'appendChild').mockImplementation(() => {});
    const mockRemoveChild = vi.spyOn(document.body, 'removeChild').mockImplementation(() => {});
    
    render(
      <TestWrapper>
        <ContentPerformanceTable data={mockData} />
      </TestWrapper>
    );

    // Click export button
    const exportButton = screen.getByLabelText('Export content performance data');
    await user.click(exportButton);

    expect(mockCreateElement).toHaveBeenCalledWith('a');
    expect(mockLink.click).toHaveBeenCalled();
    expect(mockNotification.showInfoNotification).toHaveBeenCalledWith(
      'Generating content performance report...'
    );

    // Cleanup mocks
    mockCreateElement.mockRestore();
    mockAppendChild.mockRestore();
    mockRemoveChild.mockRestore();
  });

  test('handles menu actions', async () => {
    const user = userEvent.setup();
    const mockOpen = vi.spyOn(window, 'open').mockImplementation(() => {});
    
    render(
      <TestWrapper>
        <ContentPerformanceTable data={mockData} />
      </TestWrapper>
    );

    // Click on more options for first row
    const moreButtons = screen.getAllByLabelText('More options');
    await user.click(moreButtons[0]);

    // Should show menu options
    expect(screen.getByText('View Content')).toBeInTheDocument();
    expect(screen.getByText('Export Data')).toBeInTheDocument();

    // Click view content
    await user.click(screen.getByText('View Content'));

    expect(mockOpen).toHaveBeenCalledWith('https://linkedin.com/post/1', '_blank');

    mockOpen.mockRestore();
  });

  test('shows no data message when content is empty', () => {
    render(
      <TestWrapper>
        <ContentPerformanceTable data={{ content_performance: [] }} loading={false} />
      </TestWrapper>
    );

    expect(screen.getByText('No content found')).toBeInTheDocument();
  });

  test('handles API error gracefully', async () => {
    mockApi.get.mockRejectedValue(new Error('API Error'));

    render(
      <TestWrapper>
        <ContentPerformanceTable />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockNotification.showErrorNotification).toHaveBeenCalledWith(
        'Failed to load content performance data'
      );
    });
  });

  test('works with external data prop', () => {
    render(
      <TestWrapper>
        <ContentPerformanceTable data={mockData} />
      </TestWrapper>
    );

    expect(screen.getByText('Amazing Product Launch')).toBeInTheDocument();
    expect(mockApi.get).not.toHaveBeenCalled(); // Should not fetch when external data provided
  });

  test('works with external loading prop', () => {
    render(
      <TestWrapper>
        <ContentPerformanceTable loading={true} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('calls onRefresh prop when provided', async () => {
    const mockOnRefresh = vi.fn();
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ContentPerformanceTable data={mockData} onRefresh={mockOnRefresh} />
      </TestWrapper>
    );

    const refreshButton = screen.getByLabelText('Refresh content performance data');
    await user.click(refreshButton);

    expect(mockOnRefresh).toHaveBeenCalled();
  });

  test('respects platform prop for API calls', async () => {
    render(
      <TestWrapper>
        <ContentPerformanceTable platform="linkedin" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/analytics/content-performance', {
        params: { limit: 50, include_charts: false, platform: 'linkedin' }
      });
    });
  });

  test('respects limit prop for API calls', async () => {
    render(
      <TestWrapper>
        <ContentPerformanceTable limit={25} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/analytics/content-performance', {
        params: { limit: 25, include_charts: false }
      });
    });
  });

  test('displays trend indicators correctly', () => {
    render(
      <TestWrapper>
        <ContentPerformanceTable data={mockData} />
      </TestWrapper>
    );

    // Should show trend icons for engagement rate changes
    const trendIcons = screen.getAllByTestId(/trending/i);
    expect(trendIcons.length).toBeGreaterThan(0);
  });

  test('handles missing image gracefully', () => {
    render(
      <TestWrapper>
        <ContentPerformanceTable data={mockData} />
      </TestWrapper>
    );

    // Should show placeholder for content without image
    expect(screen.getByText('I')).toBeInTheDocument(); // First letter of "Industry Insights Report"
  });

  test('disables export when no data', () => {
    render(
      <TestWrapper>
        <ContentPerformanceTable data={{ content_performance: [] }} />
      </TestWrapper>
    );

    const exportButton = screen.getByLabelText('Export content performance data');
    expect(exportButton).toBeDisabled();
  });

  test('disables refresh during loading', () => {
    render(
      <TestWrapper>
        <ContentPerformanceTable loading={true} />
      </TestWrapper>
    );

    const refreshButton = screen.getByLabelText('Refresh content performance data');
    expect(refreshButton).toBeDisabled();
  });

  test('handles auto refresh functionality', async () => {
    vi.useFakeTimers();
    
    render(
      <TestWrapper>
        <ContentPerformanceTable autoRefresh={true} refreshInterval={5} />
      </TestWrapper>
    );

    // Initial fetch
    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledTimes(1);
    });

    // Clear and advance timer
    vi.clearAllMocks();
    vi.advanceTimersByTime(5000);

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledTimes(1);
    });

    vi.useRealTimers();
  });
});
