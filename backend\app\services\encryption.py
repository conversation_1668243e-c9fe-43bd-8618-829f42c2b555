"""
Encryption Service

This service provides encryption and decryption functionality for sensitive data
such as email provider credentials and API keys.

@since 2024-1-1 to 2025-25-7
"""
import base64
import logging
from typing import Union, Optional
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from app.core.config import settings
from app.services.app_logging import get_logger

logger = get_logger(__name__)


class EncryptionService:
    """Service for encrypting and decrypting sensitive data."""
    
    def __init__(self):
        """Initialize the encryption service."""
        self._fernet = None
        self._initialize_encryption()
    
    def _initialize_encryption(self):
        """Initialize the Fernet encryption instance."""
        try:
            # Use a key from settings or generate one
            encryption_key = getattr(settings, 'ENCRYPTION_KEY', None)
            
            if not encryption_key:
                # Generate a key from a password (in production, use a proper secret)
                password = getattr(settings, 'SECRET_KEY', 'default-secret-key').encode()
                salt = b'salt_1234567890'  # In production, use a proper salt
                
                kdf = PBKDF2HMAC(
                    algorithm=hashes.SHA256(),
                    length=32,
                    salt=salt,
                    iterations=100000,
                )
                key = base64.urlsafe_b64encode(kdf.derive(password))
            else:
                key = encryption_key.encode() if isinstance(encryption_key, str) else encryption_key
            
            self._fernet = Fernet(key)
            logger.info("Encryption service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize encryption service: {str(e)}")
            # In case of failure, create a basic Fernet instance
            self._fernet = Fernet(Fernet.generate_key())
    
    def encrypt_data(self, data: Union[str, dict]) -> str:
        """
        Encrypt data.
        
        Args:
            data: Data to encrypt (string or dictionary)
            
        Returns:
            Encrypted data as base64 string
        """
        try:
            if not self._fernet:
                logger.warning("Encryption not available, returning data as-is")
                return str(data)
            
            # Convert data to string if it's a dict
            if isinstance(data, dict):
                import json
                data_str = json.dumps(data)
            else:
                data_str = str(data)
            
            # Encrypt the data
            encrypted_data = self._fernet.encrypt(data_str.encode())
            
            # Return as base64 string for storage
            return base64.urlsafe_b64encode(encrypted_data).decode()
            
        except Exception as e:
            logger.error(f"Error encrypting data: {str(e)}")
            # In case of error, return original data (not recommended for production)
            return str(data)
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """
        Decrypt data.
        
        Args:
            encrypted_data: Encrypted data as base64 string
            
        Returns:
            Decrypted data as string
        """
        try:
            if not self._fernet:
                logger.warning("Encryption not available, returning data as-is")
                return encrypted_data
            
            # Decode from base64
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            
            # Decrypt the data
            decrypted_data = self._fernet.decrypt(encrypted_bytes)
            
            return decrypted_data.decode()
            
        except Exception as e:
            logger.error(f"Error decrypting data: {str(e)}")
            # In case of error, return original data
            return encrypted_data
    
    def encrypt_dict(self, data_dict: dict) -> dict:
        """
        Encrypt specific fields in a dictionary.
        
        Args:
            data_dict: Dictionary with data to encrypt
            
        Returns:
            Dictionary with encrypted sensitive fields
        """
        if not isinstance(data_dict, dict):
            return data_dict
        
        # Fields that should be encrypted
        sensitive_fields = {
            'password', 'api_key', 'secret_key', 'access_key', 
            'secret_access_key', 'private_key', 'token'
        }
        
        encrypted_dict = {}
        for key, value in data_dict.items():
            if key.lower() in sensitive_fields and value:
                encrypted_dict[key] = self.encrypt_data(value)
            elif isinstance(value, dict):
                encrypted_dict[key] = self.encrypt_dict(value)
            else:
                encrypted_dict[key] = value
        
        return encrypted_dict
    
    def decrypt_dict(self, encrypted_dict: dict) -> dict:
        """
        Decrypt specific fields in a dictionary.
        
        Args:
            encrypted_dict: Dictionary with encrypted data
            
        Returns:
            Dictionary with decrypted sensitive fields
        """
        if not isinstance(encrypted_dict, dict):
            return encrypted_dict
        
        # Fields that should be decrypted
        sensitive_fields = {
            'password', 'api_key', 'secret_key', 'access_key', 
            'secret_access_key', 'private_key', 'token'
        }
        
        decrypted_dict = {}
        for key, value in encrypted_dict.items():
            if key.lower() in sensitive_fields and value:
                decrypted_dict[key] = self.decrypt_data(value)
            elif isinstance(value, dict):
                decrypted_dict[key] = self.decrypt_dict(value)
            else:
                decrypted_dict[key] = value
        
        return decrypted_dict


# Global encryption service instance
encryption_service = EncryptionService()


# Convenience functions
def encrypt_data(data: Union[str, dict]) -> str:
    """Encrypt data using the global encryption service."""
    return encryption_service.encrypt_data(data)


def decrypt_data(encrypted_data: str) -> str:
    """Decrypt data using the global encryption service."""
    return encryption_service.decrypt_data(encrypted_data)


def encrypt_dict(data_dict: dict) -> dict:
    """Encrypt sensitive fields in a dictionary."""
    return encryption_service.encrypt_dict(data_dict)


def decrypt_dict(encrypted_dict: dict) -> dict:
    """Decrypt sensitive fields in a dictionary."""
    return encryption_service.decrypt_dict(encrypted_dict)
