// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  Tabs,
  Tab,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  Snackbar,
  Fade,
  Skeleton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Email as EmailIcon,
  Settings as SettingsIcon,
  Dashboard as DashboardIcon,
  Send as SendIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  TestTube as TestIcon,
  CloudOff as OfflineIcon,
  Refresh as RefreshIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon
} from '@mui/icons-material';

import StablePageWrapper from '../components/StablePageWrapper';
import ErrorBoundary from '../components/common/ErrorBoundary';
import EmailProviderList from '../components/email/EmailProviderList';
import EmailProviderForm from '../components/email/EmailProviderForm';
import EmailProviderDashboard from '../components/email/EmailProviderDashboard';
import EmailProviderTest from '../components/email/EmailProviderTest';
import { useEmailProviderData } from '../hooks/useEmailProviderData';

const EmailConfiguration = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [selectedProvider, setSelectedProvider] = useState(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isTestOpen, setIsTestOpen] = useState(false);
  const [formMode, setFormMode] = useState('create'); // 'create', 'edit'
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [isRetrying, setIsRetrying] = useState(false);
  const [showOfflineMessage, setShowOfflineMessage] = useState(false);

  const {
    providers,
    dashboard,
    loading,
    error,
    fetchProviders,
    fetchDashboard,
    createProvider,
    updateProvider,
    deleteProvider,
    testProvider,
    isOnline
  } = useEmailProviderData();

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    setShowOfflineMessage(!isOnline);
  }, [isOnline]);

  const loadData = async () => {
    try {
      await Promise.all([
        fetchProviders(),
        fetchDashboard()
      ]);
    } catch (error) {
      console.error('Error loading email configuration data:', error);
      showSnackbar('Failed to load email configuration data', 'error');
    }
  };

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const handleCreateProvider = () => {
    setSelectedProvider(null);
    setFormMode('create');
    setIsFormOpen(true);
  };

  const handleEditProvider = (provider) => {
    setSelectedProvider(provider);
    setFormMode('edit');
    setIsFormOpen(true);
  };

  const handleTestProvider = (provider) => {
    setSelectedProvider(provider);
    setIsTestOpen(true);
  };

  const handleDeleteProvider = async (providerId) => {
    if (window.confirm('Are you sure you want to delete this email provider? This action cannot be undone.')) {
      try {
        await deleteProvider(providerId);
        showSnackbar('Email provider deleted successfully', 'success');
        await loadData();
      } catch (error) {
        console.error('Error deleting provider:', error);
        showSnackbar('Failed to delete email provider', 'error');
      }
    }
  };

  const handleFormSubmit = async (providerData) => {
    try {
      if (formMode === 'create') {
        await createProvider(providerData);
        showSnackbar('Email provider created successfully', 'success');
      } else {
        await updateProvider(selectedProvider.id, providerData);
        showSnackbar('Email provider updated successfully', 'success');
      }
      
      setIsFormOpen(false);
      setSelectedProvider(null);
      await loadData();
    } catch (error) {
      console.error('Error saving provider:', error);
      showSnackbar(
        formMode === 'create' 
          ? 'Failed to create email provider' 
          : 'Failed to update email provider', 
        'error'
      );
    }
  };

  const handleTestSubmit = async (testData) => {
    try {
      const result = await testProvider(selectedProvider.id, testData);
      
      if (result.success) {
        showSnackbar('Test email sent successfully', 'success');
      } else {
        showSnackbar(`Test failed: ${result.message}`, 'error');
      }
      
      setIsTestOpen(false);
      setSelectedProvider(null);
      await loadData();
    } catch (error) {
      console.error('Error testing provider:', error);
      showSnackbar('Failed to test email provider', 'error');
    }
  };

  const handleRetry = async () => {
    setIsRetrying(true);
    try {
      await loadData();
      showSnackbar('Data refreshed successfully', 'success');
    } catch (error) {
      showSnackbar('Failed to refresh data', 'error');
    } finally {
      setIsRetrying(false);
    }
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const tabLabels = [
    { label: 'Dashboard', icon: <DashboardIcon /> },
    { label: 'Providers', icon: <EmailIcon /> },
    { label: 'Settings', icon: <SettingsIcon /> }
  ];

  if (loading && !providers.length && !dashboard) {
    return (
      <StablePageWrapper>
        <Box sx={{ p: 3 }}>
          <Skeleton variant="text" width={300} height={40} />
          <Skeleton variant="rectangular" width="100%" height={200} sx={{ mt: 2 }} />
          <Grid container spacing={3} sx={{ mt: 2 }}>
            {[1, 2, 3].map((item) => (
              <Grid item xs={12} md={4} key={item}>
                <Skeleton variant="rectangular" height={150} />
              </Grid>
            ))}
          </Grid>
        </Box>
      </StablePageWrapper>
    );
  }

  return (
    <ErrorBoundary>
      <StablePageWrapper>
        <Box sx={{ p: 3 }}>
          {/* Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Box>
              <Typography variant="h4" component="h1" gutterBottom>
                Email Configuration
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Manage email providers, test configurations, and monitor delivery performance
              </Typography>
            </Box>
            
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="Refresh Data">
                <IconButton 
                  onClick={handleRetry} 
                  disabled={isRetrying}
                  color="primary"
                >
                  {isRetrying ? <CircularProgress size={24} /> : <RefreshIcon />}
                </IconButton>
              </Tooltip>
              
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleCreateProvider}
                sx={{ ml: 1 }}
              >
                Add Provider
              </Button>
            </Box>
          </Box>

          {/* Offline Message */}
          <Fade in={showOfflineMessage}>
            <Alert 
              severity="warning" 
              icon={<OfflineIcon />}
              sx={{ mb: 2 }}
              action={
                <Button color="inherit" size="small" onClick={handleRetry}>
                  Retry
                </Button>
              }
            >
              You're currently offline. Some features may not be available.
            </Alert>
          </Fade>

          {/* Error Message */}
          {error && (
            <Alert 
              severity="error" 
              sx={{ mb: 2 }}
              action={
                <Button color="inherit" size="small" onClick={handleRetry}>
                  Retry
                </Button>
              }
            >
              {error}
            </Alert>
          )}

          {/* Tabs */}
          <Card sx={{ mb: 3 }}>
            <Tabs
              value={currentTab}
              onChange={handleTabChange}
              variant="fullWidth"
              sx={{ borderBottom: 1, borderColor: 'divider' }}
            >
              {tabLabels.map((tab, index) => (
                <Tab
                  key={index}
                  icon={tab.icon}
                  label={tab.label}
                  iconPosition="start"
                />
              ))}
            </Tabs>
          </Card>

          {/* Tab Content */}
          <Box sx={{ mt: 3 }}>
            {currentTab === 0 && (
              <EmailProviderDashboard
                dashboard={dashboard}
                loading={loading}
                onRefresh={loadData}
                onTestProvider={handleTestProvider}
              />
            )}
            
            {currentTab === 1 && (
              <EmailProviderList
                providers={providers}
                loading={loading}
                onEdit={handleEditProvider}
                onDelete={handleDeleteProvider}
                onTest={handleTestProvider}
                onRefresh={loadData}
              />
            )}
            
            {currentTab === 2 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Email Configuration Settings
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Global email settings and preferences will be available here.
                </Typography>
              </Box>
            )}
          </Box>

          {/* Provider Form Dialog */}
          <EmailProviderForm
            open={isFormOpen}
            mode={formMode}
            provider={selectedProvider}
            onClose={() => {
              setIsFormOpen(false);
              setSelectedProvider(null);
            }}
            onSubmit={handleFormSubmit}
          />

          {/* Provider Test Dialog */}
          <EmailProviderTest
            open={isTestOpen}
            provider={selectedProvider}
            onClose={() => {
              setIsTestOpen(false);
              setSelectedProvider(null);
            }}
            onSubmit={handleTestSubmit}
          />

          {/* Snackbar */}
          <Snackbar
            open={snackbar.open}
            autoHideDuration={6000}
            onClose={handleCloseSnackbar}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          >
            <Alert 
              onClose={handleCloseSnackbar} 
              severity={snackbar.severity}
              variant="filled"
            >
              {snackbar.message}
            </Alert>
          </Snackbar>
        </Box>
      </StablePageWrapper>
    </ErrorBoundary>
  );
};

export default EmailConfiguration;
