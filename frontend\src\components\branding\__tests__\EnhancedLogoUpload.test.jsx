// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import EnhancedLogoUpload from '../EnhancedLogoUpload';

// Mock the notification hook
const mockShowSuccessNotification = vi.fn();
const mockShowErrorNotification = vi.fn();

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showSuccessNotification: mockShowSuccessNotification,
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Mock file reader
global.FileReader = class {
  constructor() {
    this.readAsDataURL = vi.fn(() => {
      this.onload({ target: { result: 'data:image/png;base64,test' } });
    });
  }
};

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('EnhancedLogoUpload', () => {
  const mockLogoData = {
    favicon: {
      file: new File(['favicon'], 'favicon.ico', { type: 'image/x-icon' }),
      preview: 'data:image/png;base64,favicon'
    },
    logo: {
      file: new File(['logo'], 'logo.png', { type: 'image/png' }),
      preview: 'data:image/png;base64,logo'
    },
    logo_url: 'https://example.com/logo.png',
    logo_settings: {
      position: 'bottom-right',
      opacity: 80,
      size: 25,
      margin: 16,
      rotation: 0,
      useWatermark: false,
      watermarkOpacity: 20,
      watermarkScale: 150
    },
    logo_templates: [
      {
        name: 'Social Media',
        settings: {
          position: 'bottom-right',
          opacity: 80,
          size: 20,
          margin: 10,
          rotation: 0,
          useWatermark: false,
          watermarkOpacity: 15,
          watermarkScale: 120
        }
      }
    ]
  };

  const mockProps = {
    logoData: mockLogoData,
    onChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders enhanced logo upload correctly', () => {
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Enhanced Logo Upload')).toBeInTheDocument();
    expect(screen.getByText(/Upload and manage your brand logos/)).toBeInTheDocument();
  });

  test('displays all tabs', () => {
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Upload')).toBeInTheDocument();
    expect(screen.getByText('Positioning & Settings')).toBeInTheDocument();
    expect(screen.getByText('Templates')).toBeInTheDocument();
    expect(screen.getByText('Preview')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
  });

  test('displays favicon upload section', () => {
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Favicon Upload')).toBeInTheDocument();
    expect(screen.getByText(/Upload a favicon for your brand/)).toBeInTheDocument();
    expect(screen.getByText('Upload Favicon')).toBeInTheDocument();
  });

  test('displays complete logo upload section', () => {
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Complete Logo Upload')).toBeInTheDocument();
    expect(screen.getByText(/Upload your complete brand logo/)).toBeInTheDocument();
    expect(screen.getByText('Upload Logo')).toBeInTheDocument();
  });

  test('handles tab navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    // Click on Positioning & Settings tab
    await user.click(screen.getByText('Positioning & Settings'));
    expect(screen.getByText('Logo Placement')).toBeInTheDocument();

    // Click on Templates tab
    await user.click(screen.getByText('Templates'));
    expect(screen.getByText('Logo Placement Templates')).toBeInTheDocument();

    // Click on Preview tab
    await user.click(screen.getByText('Preview'));
    expect(screen.getByText('Logo Preview')).toBeInTheDocument();

    // Click on Status tab
    await user.click(screen.getByText('Status'));
    expect(screen.getByText('Upload Status')).toBeInTheDocument();
  });

  test('displays favicon preview when uploaded', () => {
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    const faviconImage = screen.getByAltText('Favicon Preview');
    expect(faviconImage).toBeInTheDocument();
    expect(faviconImage).toHaveAttribute('src', 'data:image/png;base64,favicon');
  });

  test('displays logo preview when uploaded', () => {
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    const logoImage = screen.getByAltText('Logo Preview');
    expect(logoImage).toBeInTheDocument();
    expect(logoImage).toHaveAttribute('src', 'data:image/png;base64,logo');
  });

  test('handles favicon file upload', async () => {
    const user = userEvent.setup();
    const file = new File(['favicon'], 'favicon.ico', { type: 'image/x-icon' });
    
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    const faviconInput = screen.getByLabelText('Upload favicon file');
    await user.upload(faviconInput, file);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalled();
    });
  });

  test('handles logo file upload', async () => {
    const user = userEvent.setup();
    const file = new File(['logo'], 'logo.png', { type: 'image/png' });
    
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    const logoInput = screen.getByLabelText('Upload logo file');
    await user.upload(logoInput, file);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalled();
    });
  });

  test('handles favicon deletion', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    const deleteButtons = screen.getAllByLabelText(/delete/i);
    await user.click(deleteButtons[0]); // First delete button should be for favicon

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockLogoData,
        favicon: null
      });
    });
  });

  test('handles logo deletion', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    const deleteButtons = screen.getAllByLabelText(/delete/i);
    await user.click(deleteButtons[1]); // Second delete button should be for logo

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockLogoData,
        logo: null
      });
    });
  });

  test('displays positioning settings in positioning tab', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    await user.click(screen.getByText('Positioning & Settings'));

    expect(screen.getByText('Logo Placement')).toBeInTheDocument();
    expect(screen.getByText('Logo Size (%)')).toBeInTheDocument();
    expect(screen.getByText('Logo Opacity (%)')).toBeInTheDocument();
    expect(screen.getByText('Margin (px)')).toBeInTheDocument();
    expect(screen.getByText('Rotation (degrees)')).toBeInTheDocument();
    expect(screen.getByLabelText('Position')).toBeInTheDocument();
  });

  test('displays watermark settings in positioning tab', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    await user.click(screen.getByText('Positioning & Settings'));

    expect(screen.getByText('Watermark Settings')).toBeInTheDocument();
    expect(screen.getByText('Use Logo as Watermark')).toBeInTheDocument();
    expect(screen.getByText('Watermark Opacity (%)')).toBeInTheDocument();
    expect(screen.getByText('Watermark Scale (%)')).toBeInTheDocument();
  });

  test('handles logo size change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    await user.click(screen.getByText('Positioning & Settings'));

    const sizeSlider = screen.getByLabelText(/logo-size-slider/i);
    fireEvent.change(sizeSlider, { target: { value: '30' } });

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalled();
    });
  });

  test('handles logo opacity change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    await user.click(screen.getByText('Positioning & Settings'));

    const opacitySlider = screen.getByLabelText(/logo-opacity-slider/i);
    fireEvent.change(opacitySlider, { target: { value: '90' } });

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalled();
    });
  });

  test('handles position change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    await user.click(screen.getByText('Positioning & Settings'));

    const positionSelect = screen.getByLabelText('Position');
    await user.click(positionSelect);
    
    const topLeftOption = screen.getByText('Top Left');
    await user.click(topLeftOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalled();
    });
  });

  test('handles watermark toggle', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    await user.click(screen.getByText('Positioning & Settings'));

    const watermarkSwitch = screen.getByRole('checkbox', { name: /use logo as watermark/i });
    await user.click(watermarkSwitch);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalled();
    });
  });

  test('displays templates in templates tab', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    await user.click(screen.getByText('Templates'));

    expect(screen.getByText('Logo Placement Templates')).toBeInTheDocument();
    expect(screen.getByText('Social Media')).toBeInTheDocument();
    expect(screen.getByText('Apply Template')).toBeInTheDocument();
  });

  test('handles template application', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    await user.click(screen.getByText('Templates'));

    const applyButton = screen.getByText('Apply Template');
    await user.click(applyButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalled();
    });
  });

  test('displays preview in preview tab', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    await user.click(screen.getByText('Preview'));

    expect(screen.getByText('Logo Preview')).toBeInTheDocument();
    expect(screen.getByText(/This preview shows how your logo will appear/)).toBeInTheDocument();
  });

  test('displays status in status tab', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    await user.click(screen.getByText('Status'));

    expect(screen.getByText('Upload Status')).toBeInTheDocument();
    expect(screen.getByText('Favicon: Uploaded')).toBeInTheDocument();
    expect(screen.getByText('Logo: Uploaded')).toBeInTheDocument();
  });

  test('shows empty state when no logos uploaded', () => {
    const emptyProps = {
      logoData: {
        favicon: null,
        logo: null,
        logo_url: null,
        logo_settings: mockLogoData.logo_settings,
        logo_templates: []
      },
      onChange: vi.fn()
    };

    render(
      <TestWrapper>
        <EnhancedLogoUpload {...emptyProps} />
      </TestWrapper>
    );

    expect(screen.getByText(/No favicon uploaded yet/)).toBeInTheDocument();
    expect(screen.getByText(/No logo uploaded yet/)).toBeInTheDocument();
  });

  test('renders with default props when no logoData provided', () => {
    render(
      <TestWrapper>
        <EnhancedLogoUpload onChange={vi.fn()} />
      </TestWrapper>
    );

    expect(screen.getByText('Enhanced Logo Upload')).toBeInTheDocument();
    expect(screen.getByText(/No favicon uploaded yet/)).toBeInTheDocument();
    expect(screen.getByText(/No logo uploaded yet/)).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    // Check for proper file input labels
    expect(screen.getByLabelText('Upload favicon file')).toBeInTheDocument();
    expect(screen.getByLabelText('Upload logo file')).toBeInTheDocument();
    
    // Check for proper button roles
    expect(screen.getByRole('button', { name: /upload favicon/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /upload logo/i })).toBeInTheDocument();
  });

  test('validates file types for favicon', async () => {
    const user = userEvent.setup();
    const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });
    
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    const faviconInput = screen.getByLabelText('Upload favicon file');
    await user.upload(faviconInput, invalidFile);

    await waitFor(() => {
      expect(mockShowErrorNotification).toHaveBeenCalledWith(
        expect.stringContaining('Invalid file type')
      );
    });
  });

  test('validates file types for logo', async () => {
    const user = userEvent.setup();
    const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });
    
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    const logoInput = screen.getByLabelText('Upload logo file');
    await user.upload(logoInput, invalidFile);

    await waitFor(() => {
      expect(mockShowErrorNotification).toHaveBeenCalledWith(
        expect.stringContaining('Invalid file type')
      );
    });
  });

  test('validates file size limits', async () => {
    const user = userEvent.setup();
    // Create a large file (over 5MB)
    const largeFile = new File([new ArrayBuffer(6 * 1024 * 1024)], 'large.png', { type: 'image/png' });
    
    render(
      <TestWrapper>
        <EnhancedLogoUpload {...mockProps} />
      </TestWrapper>
    );

    const logoInput = screen.getByLabelText('Upload logo file');
    await user.upload(logoInput, largeFile);

    await waitFor(() => {
      expect(mockShowErrorNotification).toHaveBeenCalledWith(
        expect.stringContaining('File size too large')
      );
    });
  });
});
