/**
 * Tests for SentimentTrendChart component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import SentimentTrendChart from '../SentimentTrendChart';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the API
vi.mock('../../../api', () => ({
  default: {
    get: vi.fn()
  }
}));

// Mock Recharts
vi.mock('recharts', () => ({
  ResponsiveContainer: ({ children }) => <div data-testid="responsive-container">{children}</div>,
  LineChart: ({ children }) => <div data-testid="line-chart">{children}</div>,
  Line: () => <div data-testid="line" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="recharts-tooltip" />,
  Legend: () => <div data-testid="legend" />,
  ReferenceLine: () => <div data-testid="reference-line" />,
}));

// Mock date-fns
vi.mock('date-fns', () => ({
  format: vi.fn((date, formatStr) => {
    if (formatStr === 'yyyy-MM-dd') return '2023-01-01';
    if (formatStr === 'MMM dd') return 'Jan 01';
    if (formatStr === 'MMM dd, yyyy') return 'Jan 01, 2023';
    return '2023-01-01';
  }),
  subDays: vi.fn(() => new Date('2023-01-01')),
  subMonths: vi.fn(() => new Date('2023-01-01')),
  parseISO: vi.fn(() => new Date('2023-01-01')),
}));

describe('SentimentTrendChart', () => {
  const mockSentimentData = [
    {
      date: '2023-01-01',
      sentiment_score: 0.75,
      overall_sentiment: 'positive',
      title: 'Great product launch!',
      platforms: ['LinkedIn', 'Twitter']
    },
    {
      date: '2023-01-02',
      sentiment_score: -0.25,
      overall_sentiment: 'negative',
      title: 'Some issues reported',
      platforms: ['Twitter']
    },
    {
      date: '2023-01-03',
      sentiment_score: 0.1,
      overall_sentiment: 'neutral',
      title: 'Regular update',
      platforms: ['LinkedIn']
    }
  ];

  const mockProps = {
    userId: 'user123',
    contentIds: ['content1', 'content2'],
    platforms: ['LinkedIn', 'Twitter', 'Instagram'],
    onRefresh: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    
    // Mock document.createElement for export functionality
    global.document.createElement = vi.fn(() => ({
      setAttribute: vi.fn(),
      click: vi.fn(),
      style: {}
    }));

    global.document.body.appendChild = vi.fn();
    global.document.body.removeChild = vi.fn();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  test('renders sentiment trend chart component', () => {
    render(
      <TestWrapper>
        <SentimentTrendChart {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Sentiment Trend Analysis')).toBeInTheDocument();
    expect(screen.getByLabelText('Time Range')).toBeInTheDocument();
  });

  test('shows loading state correctly', async () => {
    const api = await import('../../../api');
    api.default.get.mockImplementation(() => new Promise(() => {})); // Never resolves
    
    render(
      <TestWrapper>
        <SentimentTrendChart {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('fetches and displays sentiment data', async () => {
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockSentimentData });
    
    render(
      <TestWrapper>
        <SentimentTrendChart {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    });

    expect(api.default.get).toHaveBeenCalledWith('/api/analytics/sentiment-trend', {
      params: expect.objectContaining({
        start_date: '2023-01-01',
        end_date: '2023-01-01',
        content_ids: 'content1,content2'
      })
    });
  });

  test('handles time range change', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockSentimentData });
    
    render(
      <TestWrapper>
        <SentimentTrendChart {...mockProps} />
      </TestWrapper>
    );

    const timeRangeSelect = screen.getByLabelText('Time Range');
    await user.click(timeRangeSelect);
    
    const sevenDaysOption = screen.getByText('Last 7 days');
    await user.click(sevenDaysOption);

    // Should trigger new API call
    await waitFor(() => {
      expect(api.default.get).toHaveBeenCalledTimes(2);
    });
  });

  test('handles platform filter change', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockSentimentData });
    
    render(
      <TestWrapper>
        <SentimentTrendChart {...mockProps} />
      </TestWrapper>
    );

    const linkedinButton = screen.getByLabelText('LinkedIn');
    await user.click(linkedinButton);

    // Should trigger new API call with platform filter
    await waitFor(() => {
      expect(api.default.get).toHaveBeenCalledWith('/api/analytics/sentiment-trend', {
        params: expect.objectContaining({
          platform: 'LinkedIn'
        })
      });
    });
  });

  test('shows error state when API fails', async () => {
    const api = await import('../../../api');
    api.default.get.mockRejectedValue(new Error('API Error'));
    
    render(
      <TestWrapper>
        <SentimentTrendChart {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Failed to fetch sentiment trend data. Please try again later.')).toBeInTheDocument();
    });
  });

  test('shows no data state when no sentiment data available', async () => {
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: [] });
    
    render(
      <TestWrapper>
        <SentimentTrendChart {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('No sentiment trend data available')).toBeInTheDocument();
    });
  });

  test('handles refresh functionality', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockSentimentData });
    
    render(
      <TestWrapper>
        <SentimentTrendChart {...mockProps} />
      </TestWrapper>
    );

    const refreshButton = screen.getByLabelText('Refresh sentiment data');
    await user.click(refreshButton);

    expect(mockProps.onRefresh).toHaveBeenCalled();
    expect(api.default.get).toHaveBeenCalledTimes(2); // Initial + refresh
  });

  test('handles export functionality', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockSentimentData });
    
    render(
      <TestWrapper>
        <SentimentTrendChart {...mockProps} />
      </TestWrapper>
    );

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    });

    const exportButton = screen.getByLabelText('Export sentiment data');
    await user.click(exportButton);

    // Should create download link
    expect(global.document.createElement).toHaveBeenCalledWith('a');
  });

  test('disables export when no data available', async () => {
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: [] });
    
    render(
      <TestWrapper>
        <SentimentTrendChart {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      const exportButton = screen.getByLabelText('Export sentiment data');
      expect(exportButton).toBeDisabled();
    });
  });

  test('hides action buttons when props are false', () => {
    render(
      <TestWrapper>
        <SentimentTrendChart 
          {...mockProps} 
          showRefresh={false}
          showExport={false}
        />
      </TestWrapper>
    );

    expect(screen.queryByLabelText('Refresh sentiment data')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('Export sentiment data')).not.toBeInTheDocument();
  });

  test('auto-refresh functionality works correctly', async () => {
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockSentimentData });
    
    render(
      <TestWrapper>
        <SentimentTrendChart 
          {...mockProps}
          autoRefresh={true}
          refreshInterval={1000}
        />
      </TestWrapper>
    );

    // Fast-forward time to trigger auto-refresh
    vi.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(api.default.get).toHaveBeenCalledTimes(2); // Initial + auto-refresh
    });
  });

  test('cleans up auto-refresh interval on unmount', () => {
    const { unmount } = render(
      <TestWrapper>
        <SentimentTrendChart 
          {...mockProps}
          autoRefresh={true}
          refreshInterval={1000}
        />
      </TestWrapper>
    );

    const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
    unmount();

    expect(clearIntervalSpy).toHaveBeenCalled();
  });

  test('does not fetch data when userId is not provided', () => {
    const api = require('../../../api');
    
    render(
      <TestWrapper>
        <SentimentTrendChart 
          {...mockProps}
          userId={null}
        />
      </TestWrapper>
    );

    expect(api.default.get).not.toHaveBeenCalled();
  });

  test('applies correct accessibility attributes', () => {
    render(
      <TestWrapper>
        <SentimentTrendChart {...mockProps} />
      </TestWrapper>
    );

    // Check ARIA labels
    expect(screen.getByLabelText('Export sentiment data')).toBeInTheDocument();
    expect(screen.getByLabelText('Refresh sentiment data')).toBeInTheDocument();
    expect(screen.getByLabelText('platform filter')).toBeInTheDocument();

    // Check form labels
    expect(screen.getByLabelText('Time Range')).toBeInTheDocument();
  });

  test('handles missing userId gracefully', () => {
    render(
      <TestWrapper>
        <SentimentTrendChart 
          contentIds={['content1']}
          platforms={['LinkedIn']}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Sentiment Trend Analysis')).toBeInTheDocument();
    // Should not crash and should show appropriate message
  });

  test('formats sentiment data correctly for chart', async () => {
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockSentimentData });
    
    render(
      <TestWrapper>
        <SentimentTrendChart {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    });

    // Should process data with formatted dates
    expect(api.default.get).toHaveBeenCalledWith('/api/analytics/sentiment-trend', {
      params: expect.objectContaining({
        start_date: '2023-01-01',
        end_date: '2023-01-01'
      })
    });
  });

  test('handles different time ranges correctly', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockSentimentData });
    
    render(
      <TestWrapper>
        <SentimentTrendChart {...mockProps} />
      </TestWrapper>
    );

    // Test different time ranges
    const timeRangeSelect = screen.getByLabelText('Time Range');
    
    await user.click(timeRangeSelect);
    await user.click(screen.getByText('Last 6 months'));

    await waitFor(() => {
      expect(api.default.get).toHaveBeenCalledTimes(2);
    });
  });

  test('shows loading spinner during export', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockSentimentData });
    
    render(
      <TestWrapper>
        <SentimentTrendChart {...mockProps} />
      </TestWrapper>
    );

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    });

    const exportButton = screen.getByLabelText('Export sentiment data');
    await user.click(exportButton);

    // Should briefly show loading spinner (this test might be flaky due to timing)
    // In a real scenario, you might want to mock the async behavior
  });
});
