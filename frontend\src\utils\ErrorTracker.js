/**
 * Enhanced Error Tracker with Detailed Reporting
 * Provides comprehensive error tracking with file-level details
 @since 2024-1-1 to 2025-25-7
*/

class ErrorTracker {
  constructor() {
    this.errors = [];
    this.errorCounts = new Map();
    this.sessionId = this._generateSessionId();
    this.startTime = Date.now();
    
    // Initialize error tracking
    this._initializeErrorTracking();
  }

  /**
   * Initialize comprehensive error tracking
   */
  _initializeErrorTracking() {
    // Global error handler
    window.addEventListener('error', (event) => {
      this._handleError({
        type: 'javascript',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error,
        stack: event.error?.stack,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent
      });
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      this._handleError({
        type: 'promise_rejection',
        message: event.reason?.message || 'Unhandled Promise Rejection',
        error: event.reason,
        stack: event.reason?.stack,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent
      });
    });

    // Console error override
    const originalConsoleError = console.error;
    console.error = (...args) => {
      this._handleConsoleError(args);
      originalConsoleError.apply(console, args);
    };
  }

  /**
   * Handle JavaScript errors with detailed analysis
   */
  _handleError(errorInfo) {
    const enhancedError = this._enhanceErrorInfo(errorInfo);
    this.errors.push(enhancedError);
    
    // Update error counts
    const errorKey = `${enhancedError.type}:${enhancedError.filename}:${enhancedError.lineno}`;
    this.errorCounts.set(errorKey, (this.errorCounts.get(errorKey) || 0) + 1);
    
    // Generate detailed error reference
    const errorReference = this._generateErrorReference(enhancedError);
    
    // Log detailed error information
    console.group(`🚨 JavaScript Error Detected - ${errorReference}`);
    console.error('Error Details:', enhancedError);
    console.error('Stack Trace:', enhancedError.stack);
    console.error('Component Stack:', this._getComponentStack());
    console.error('Environment Info:', this._getEnvironmentInfo());
    console.groupEnd();
    
    // Send to error reporting service if available
    this._reportError(enhancedError, errorReference);
    
    return errorReference;
  }

  /**
   * Handle console errors
   */
  _handleConsoleError(args) {
    const errorMessage = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
    ).join(' ');
    
    this._handleError({
      type: 'console_error',
      message: errorMessage,
      timestamp: Date.now(),
      url: window.location.href,
      stack: new Error().stack
    });
  }

  /**
   * Enhance error information with additional context
   */
  _enhanceErrorInfo(errorInfo) {
    return {
      ...errorInfo,
      sessionId: this.sessionId,
      errorId: this._generateErrorId(),
      component: this._identifyComponent(errorInfo.stack),
      module: this._identifyModule(errorInfo.filename),
      severity: this._calculateSeverity(errorInfo),
      context: this._getErrorContext(),
      browserInfo: this._getBrowserInfo(),
      performanceInfo: this._getPerformanceInfo()
    };
  }

  /**
   * Generate detailed error reference
   */
  _generateErrorReference(errorInfo) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '');
    const fileShort = this._getShortFilename(errorInfo.filename);
    const componentName = errorInfo.component || 'unknown';
    const errorType = errorInfo.type.toUpperCase();
    
    return `error_${timestamp}_${errorType}_${fileShort}_${componentName}_line${errorInfo.lineno || 'unknown'}`;
  }

  /**
   * Identify React component from stack trace
   */
  _identifyComponent(stack) {
    if (!stack) return null;
    
    const componentRegex = /at\s+(\w+)\s+\(/g;
    const matches = [...stack.matchAll(componentRegex)];
    
    // Look for React component names (usually start with uppercase)
    for (const match of matches) {
      const name = match[1];
      if (name && /^[A-Z]/.test(name) && name !== 'Object' && name !== 'Function') {
        return name;
      }
    }
    
    return null;
  }

  /**
   * Identify module from filename
   */
  _identifyModule(filename) {
    if (!filename) return null;
    
    const parts = filename.split('/');
    const file = parts[parts.length - 1];
    return file?.replace(/\?.*$/, '') || null;
  }

  /**
   * Calculate error severity
   */
  _calculateSeverity(errorInfo) {
    if (errorInfo.message?.includes('ChunkLoadError')) return 'high';
    if (errorInfo.message?.includes('Network Error')) return 'medium';
    if (errorInfo.type === 'promise_rejection') return 'medium';
    if (errorInfo.message?.includes('Warning')) return 'low';
    return 'medium';
  }

  /**
   * Get error context
   */
  _getErrorContext() {
    return {
      url: window.location.href,
      pathname: window.location.pathname,
      search: window.location.search,
      hash: window.location.hash,
      referrer: document.referrer,
      timestamp: new Date().toISOString(),
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    };
  }

  /**
   * Get browser information
   */
  _getBrowserInfo() {
    return {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine
    };
  }

  /**
   * Get performance information
   */
  _getPerformanceInfo() {
    if (!window.performance) return null;
    
    return {
      memory: window.performance.memory ? {
        usedJSHeapSize: window.performance.memory.usedJSHeapSize,
        totalJSHeapSize: window.performance.memory.totalJSHeapSize,
        jsHeapSizeLimit: window.performance.memory.jsHeapSizeLimit
      } : null,
      timing: window.performance.timing ? {
        loadEventEnd: window.performance.timing.loadEventEnd,
        navigationStart: window.performance.timing.navigationStart
      } : null
    };
  }

  /**
   * Get React component stack
   */
  _getComponentStack() {
    // Try to get React DevTools component stack if available
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      try {
        const fiber = window.__REACT_DEVTOOLS_GLOBAL_HOOK__.getFiberRoots?.(1);
        return this._extractComponentNames(fiber);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  /**
   * Extract component names from fiber
   */
  _extractComponentNames(fiber) {
    const components = [];
    let current = fiber;
    
    while (current && components.length < 10) {
      if (current.type?.name) {
        components.push(current.type.name);
      }
      current = current.child || current.sibling || current.return;
    }
    
    return components;
  }

  /**
   * Get environment information
   */
  _getEnvironmentInfo() {
    return {
      isDevelopment: process.env.NODE_ENV === 'development',
      isProduction: process.env.NODE_ENV === 'production',
      buildTime: process.env.REACT_APP_BUILD_TIME || 'unknown',
      version: process.env.REACT_APP_VERSION || 'unknown',
      hostname: window.location.hostname,
      protocol: window.location.protocol
    };
  }

  /**
   * Report error to external service
   */
  _reportError(errorInfo, errorReference) {
    // In development, just log
    if (process.env.NODE_ENV === 'development') {
      console.warn(`Error Reference: ${errorReference}`);
      return;
    }

    // In production, send to error reporting service
    try {
      fetch('/api/errors/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          errorReference,
          errorInfo,
          sessionId: this.sessionId
        })
      }).catch(err => {
        console.warn('Failed to report error:', err);
      });
    } catch (e) {
      console.warn('Error reporting failed:', e);
    }
  }

  /**
   * Generate session ID
   */
  _generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate error ID
   */
  _generateErrorId() {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get short filename
   */
  _getShortFilename(filename) {
    if (!filename) return 'unknown';
    const parts = filename.split('/');
    const file = parts[parts.length - 1];
    return file?.replace(/\.(js|jsx|ts|tsx).*$/, '') || 'unknown';
  }

  /**
   * Get error summary
   */
  getErrorSummary() {
    return {
      totalErrors: this.errors.length,
      errorCounts: Object.fromEntries(this.errorCounts),
      sessionId: this.sessionId,
      sessionDuration: Date.now() - this.startTime,
      recentErrors: this.errors.slice(-5)
    };
  }
}

// Create global instance
const errorTracker = new ErrorTracker();

export default errorTracker;
