"""
E-commerce webhook handling service for real-time product synchronization.
@since 2024-1-1 to 2025-25-7
"""

import logging
import json
from typing import Dict, Any, Optional
from datetime import datetime, timezone
from bson import ObjectId

from app.db.mongodb import get_database
from app.models.ecommerce import (
    EcommerceStore,
    SyncedProduct,
    EcommerceWebhook,
    SyncLog,
    SyncOperationEnum
)
from app.models.common import StatusEnum, EcommercePlatformEnum
from app.models.user import PyObjectId
from app.services.ecommerce.factory import EcommerceIntegrationFactory
from app.services.credential_manager import credential_manager
from app.utils.error_handling import ExternalServiceError

# Create ServiceError alias for consistency
ServiceError = ExternalServiceError

logger = logging.getLogger(__name__)

# Collection names
STORES_COLLECTION = "ecommerce_stores"
PRODUCTS_COLLECTION = "synced_products"
WEBHOOKS_COLLECTION = "ecommerce_webhooks"
SYNC_LOGS_COLLECTION = "ecommerce_sync_logs"


class EcommerceWebhookService:
    """
    Service for handling e-commerce platform webhooks.
    """
    
    async def handle_webhook(
        self,
        platform: str,
        payload: bytes,
        headers: Dict[str, str],
        signature: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Handle incoming webhook from e-commerce platform.
        
        Args:
            platform: E-commerce platform name
            payload: Raw webhook payload
            headers: Request headers
            signature: Webhook signature for verification
            
        Returns:
            Processing result
        """
        try:
            # Parse payload
            payload_data = json.loads(payload.decode('utf-8'))
            
            # Log webhook receipt
            webhook_id = await self._log_webhook(platform, payload_data, headers)
            
            try:
                # Get integration for platform
                integration = EcommerceIntegrationFactory.get_integration(platform)
                
                # Verify webhook signature if provided
                if signature and not await self._verify_webhook_signature(
                    platform, payload, signature, payload_data
                ):
                    await self._update_webhook_status(webhook_id, StatusEnum.FAILED, "Invalid signature")
                    return {"success": False, "error": "Invalid webhook signature"}
                
                # Process webhook based on event type
                result = await self._process_webhook_event(platform, payload_data, integration)
                
                # Update webhook status
                await self._update_webhook_status(
                    webhook_id, 
                    StatusEnum.COMPLETED if result["success"] else StatusEnum.FAILED,
                    result.get("error")
                )
                
                return result
                
            except Exception as e:
                await self._update_webhook_status(webhook_id, StatusEnum.FAILED, str(e))
                raise
                
        except Exception as e:
            logger.error(f"Failed to handle {platform} webhook: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _process_webhook_event(
        self,
        platform: str,
        payload: Dict[str, Any],
        integration
    ) -> Dict[str, Any]:
        """
        Process webhook event based on platform and event type.
        
        Args:
            platform: Platform name
            payload: Webhook payload
            integration: Platform integration instance
            
        Returns:
            Processing result
        """
        try:
            if platform == EcommercePlatformEnum.SHOPIFY:
                return await self._process_shopify_webhook(payload, integration)
            elif platform == EcommercePlatformEnum.WOOCOMMERCE:
                return await self._process_woocommerce_webhook(payload, integration)
            else:
                return {"success": False, "error": f"Unsupported platform: {platform}"}
                
        except Exception as e:
            logger.error(f"Failed to process webhook event: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _process_shopify_webhook(self, payload: Dict[str, Any], integration) -> Dict[str, Any]:
        """Process Shopify webhook events."""
        try:
            # Extract event type from headers (would be passed in real implementation)
            # For now, determine from payload structure
            if "id" in payload and "title" in payload:
                # Product webhook
                product_id = str(payload.get("id"))
                
                # Find stores that might have this product
                stores = await self._find_stores_for_product(EcommercePlatformEnum.SHOPIFY, product_id)
                
                results = []
                for store in stores:
                    try:
                        # Get updated product from Shopify
                        updated_product = await integration.get_product(store, product_id)
                        
                        if updated_product:
                            # Update in database
                            await self._update_product_in_db(updated_product)
                            results.append({"store_id": str(store.id), "success": True})
                        else:
                            # Product might be deleted
                            await self._delete_product_from_db(store.id, product_id)
                            results.append({"store_id": str(store.id), "success": True, "action": "deleted"})
                            
                    except Exception as e:
                        logger.error(f"Failed to process product update for store {store.id}: {str(e)}")
                        results.append({"store_id": str(store.id), "success": False, "error": str(e)})
                
                return {"success": True, "results": results}
            
            return {"success": False, "error": "Unknown webhook event type"}
            
        except Exception as e:
            logger.error(f"Failed to process Shopify webhook: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _process_woocommerce_webhook(self, payload: Dict[str, Any], integration) -> Dict[str, Any]:
        """Process WooCommerce webhook events."""
        try:
            # WooCommerce webhook structure
            if "id" in payload and "name" in payload:
                # Product webhook
                product_id = str(payload.get("id"))
                
                # Find stores that might have this product
                stores = await self._find_stores_for_product(EcommercePlatformEnum.WOOCOMMERCE, product_id)
                
                results = []
                for store in stores:
                    try:
                        # Get updated product from WooCommerce
                        updated_product = await integration.get_product(store, product_id)
                        
                        if updated_product:
                            # Update in database
                            await self._update_product_in_db(updated_product)
                            results.append({"store_id": str(store.id), "success": True})
                        else:
                            # Product might be deleted
                            await self._delete_product_from_db(store.id, product_id)
                            results.append({"store_id": str(store.id), "success": True, "action": "deleted"})
                            
                    except Exception as e:
                        logger.error(f"Failed to process product update for store {store.id}: {str(e)}")
                        results.append({"store_id": str(store.id), "success": False, "error": str(e)})
                
                return {"success": True, "results": results}
            
            return {"success": False, "error": "Unknown webhook event type"}
            
        except Exception as e:
            logger.error(f"Failed to process WooCommerce webhook: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _find_stores_for_product(self, platform: str, product_id: str) -> list[EcommerceStore]:
        """Find stores that might have a specific product."""
        try:
            db = await get_database()
            
            # First, try to find stores that have this product
            product_docs = await db[PRODUCTS_COLLECTION].find({
                "platform": platform,
                "external_product_id": product_id
            }).to_list(length=None)
            
            store_ids = [doc["store_id"] for doc in product_docs]
            
            if not store_ids:
                # If no products found, get all stores for this platform
                store_docs = await db[STORES_COLLECTION].find({
                    "platform": platform
                }).to_list(length=None)
            else:
                # Get specific stores
                store_docs = await db[STORES_COLLECTION].find({
                    "_id": {"$in": store_ids}
                }).to_list(length=None)
            
            stores = []
            for store_doc in store_docs:
                store = EcommerceStore(**store_doc)
                decrypted_store = await credential_manager.decrypt_ecommerce_store(store)
                stores.append(decrypted_store)
            
            return stores
            
        except Exception as e:
            logger.error(f"Failed to find stores for product: {str(e)}")
            return []
    
    async def _update_product_in_db(self, product: SyncedProduct) -> None:
        """Update product in database."""
        try:
            db = await get_database()
            
            # Update existing product or insert new one
            await db[PRODUCTS_COLLECTION].update_one(
                {
                    "store_id": product.store_id,
                    "external_product_id": product.external_product_id
                },
                {"$set": product.model_dump(by_alias=True, exclude={"id"})},
                upsert=True
            )
            
        except Exception as e:
            logger.error(f"Failed to update product in database: {str(e)}")
            raise
    
    async def _delete_product_from_db(self, store_id: PyObjectId, product_id: str) -> None:
        """Delete product from database."""
        try:
            db = await get_database()
            
            await db[PRODUCTS_COLLECTION].delete_one({
                "store_id": store_id,
                "external_product_id": product_id
            })
            
        except Exception as e:
            logger.error(f"Failed to delete product from database: {str(e)}")
            raise
    
    async def _verify_webhook_signature(
        self,
        platform: str,
        payload: bytes,
        signature: str,
        payload_data: Dict[str, Any]
    ) -> bool:
        """Verify webhook signature."""
        try:
            # Get integration
            integration = EcommerceIntegrationFactory.get_integration(platform)

            # For signature verification, we'd need the webhook secret
            # This would typically be stored with the store configuration
            # For now, return True (in production, implement proper verification)

            # Log parameters for debugging
            logger.debug(f"Verifying webhook for platform: {platform}")
            logger.debug(f"Payload size: {len(payload)} bytes")
            logger.debug(f"Signature: {signature[:20]}..." if signature else "No signature")
            logger.debug(f"Payload data keys: {list(payload_data.keys())}")
            logger.debug(f"Integration type: {type(integration).__name__}")

            return True

        except Exception as e:
            logger.error(f"Failed to verify webhook signature: {str(e)}")
            return False
    
    async def _log_webhook(
        self,
        platform: str,
        payload: Dict[str, Any],
        headers: Dict[str, str]
    ) -> PyObjectId:
        """Log webhook receipt."""
        try:
            db = await get_database()
            
            # Convert platform string to enum
            try:
                platform_enum = EcommercePlatformEnum(platform.lower())
            except ValueError:
                platform_enum = EcommercePlatformEnum.SHOPIFY  # Default fallback

            webhook = EcommerceWebhook(
                store_id=PyObjectId(),  # Will be updated when we identify the store
                event_type=headers.get("X-Event-Type", "unknown"),
                platform=platform_enum,
                external_id=payload.get("id"),
                payload=payload,
                headers=headers,
                status=StatusEnum.PENDING,
                processed_at=None,
                error_message=None
            )
            
            result = await db[WEBHOOKS_COLLECTION].insert_one(webhook.model_dump(by_alias=True))
            return PyObjectId(str(result.inserted_id))
            
        except Exception as e:
            logger.error(f"Failed to log webhook: {str(e)}")
            return PyObjectId()
    
    async def _update_webhook_status(
        self,
        webhook_id: PyObjectId,
        status: StatusEnum,
        error_message: Optional[str] = None
    ) -> None:
        """Update webhook processing status."""
        try:
            db = await get_database()
            
            update_data = {
                "status": status,
                "processed_at": datetime.now(timezone.utc)
            }
            
            if error_message:
                update_data["error_message"] = error_message
            
            await db[WEBHOOKS_COLLECTION].update_one(
                {"_id": ObjectId(str(webhook_id))},
                {"$set": update_data}
            )
            
        except Exception as e:
            logger.error(f"Failed to update webhook status: {str(e)}")


# Create singleton instance
ecommerce_webhook_service = EcommerceWebhookService()
