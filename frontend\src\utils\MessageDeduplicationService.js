/**
 * Message Deduplication Service for Social Media Messaging
 * 
 * Prevents duplicate message sending when users rapidly click send buttons
 * or network issues cause retries. Uses intelligent deduplication based on
 * message content, platform, conversation ID, and timestamp.
 * 
 * @version 1.0.0
 * <AUTHOR> Social Platform Team
 * @since 2024-01-01
 * 
 * @example
 * ```javascript
 * import { MessageDeduplicationService } from '../utils/MessageDeduplicationService';
 * 
 * const deduplication = new MessageDeduplicationService();
 * 
 * // Check if message is duplicate
 * const isDuplicate = await deduplication.isDuplicate({
 *   content: 'Hello world!',
 *   platform: 'linkedin',
 *   conversation_id: 'conv_123'
 * });
 * 
 * if (!isDuplicate) {
 *   // Send message
 *   await sendMessage(messageData);
 *   
 *   // Mark as sent
 *   await deduplication.markAsSent(messageData);
 * }
 * ```
 @since 2024-1-1 to 2025-25-7
*/

import axios from 'axios';

/**
 * Message Deduplication Service for Social Media Messaging
 * 
 * Provides intelligent deduplication for social media messages using Redis
 * caching infrastructure and content-based fingerprinting to prevent
 * duplicate sends during rapid user interactions or network issues.
 * 
 * Features:
 * - Content-based message fingerprinting
 * - Configurable deduplication windows
 * - Redis-backed persistence with fallback to memory
 * - Platform-specific deduplication policies
 * - Automatic cleanup of expired entries
 * - Integration with existing ACE Social caching patterns
 */
export class MessageDeduplicationService {
  /**
   * Initialize the deduplication service
   * 
   * @param {Object} options - Configuration options
   * @param {boolean} options.enabled - Whether deduplication is enabled
   * @param {number} options.windowMs - Deduplication window in milliseconds
   * @param {number} options.maxCacheSize - Maximum cache size for memory fallback
   * @param {string} options.cacheEndpoint - Cache endpoint for Redis operations
   * @param {boolean} options.useRedis - Whether to use Redis for persistence
   */
  constructor(options = {}) {
    this.enabled = options.enabled ?? true;
    this.windowMs = options.windowMs || 30000; // 30 seconds
    this.maxCacheSize = options.maxCacheSize || 1000;
    this.cacheEndpoint = options.cacheEndpoint || '/api/cache/deduplication';
    this.useRedis = options.useRedis ?? true;
    
    // Memory cache for fallback
    this.memoryCache = new Map();
    this.cacheStats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      cleanups: 0
    };
    
    // Platform-specific deduplication policies
    this.platformPolicies = {
      facebook: { windowMs: 30000, strictMode: true },
      instagram: { windowMs: 30000, strictMode: true },
      linkedin: { windowMs: 45000, strictMode: true }, // Longer window for professional content
      twitter: { windowMs: 15000, strictMode: false } // Shorter window for rapid conversations
    };
    
    // Content similarity thresholds
    this.similarityThresholds = {
      exact: 1.0,
      high: 0.95,
      medium: 0.85,
      low: 0.75
    };
    
    // Initialize cleanup timer
    if (this.enabled) {
      this._initializeCleanupTimer();
    }
    
    // Bind methods
    this.isDuplicate = this.isDuplicate.bind(this);
    this.markAsSent = this.markAsSent.bind(this);
    this.clearDuplicates = this.clearDuplicates.bind(this);
  }

  /**
   * Check if message is a duplicate
   * 
   * @param {Object} messageData - Message data to check
   * @param {string} messageData.content - Message content
   * @param {string} messageData.platform - Target platform
   * @param {string} messageData.conversation_id - Conversation ID
   * @param {Object} options - Check options
   * @returns {Promise<boolean>} Whether message is duplicate
   */
  async isDuplicate(messageData, options = {}) {
    if (!this.enabled) return false;
    
    try {
      const fingerprint = this._generateFingerprint(messageData);
      const cacheKey = this._generateCacheKey(messageData, fingerprint);
      
      // Check cache for existing entry
      const existingEntry = await this._getCacheEntry(cacheKey);
      
      if (existingEntry) {
        const policy = this.platformPolicies[messageData.platform];
        const windowMs = options.windowMs || policy?.windowMs || this.windowMs;
        const timeDiff = Date.now() - existingEntry.timestamp;
        
        if (timeDiff <= windowMs) {
          // Check content similarity
          const similarity = this._calculateSimilarity(
            messageData.content,
            existingEntry.content
          );
          
          const threshold = policy?.strictMode ? 
            this.similarityThresholds.exact : 
            this.similarityThresholds.high;
          
          if (similarity >= threshold) {
            this.cacheStats.hits++;
            
            // Log duplicate detection
            this._logDeduplicationEvent('duplicate_detected', messageData, {
              fingerprint,
              similarity,
              time_diff_ms: timeDiff
            });
            
            return true;
          }
        }
      }
      
      this.cacheStats.misses++;
      return false;
    } catch (error) {
      console.error('Error checking message duplication:', error);
      
      // Log error but don't block message sending
      this._logDeduplicationEvent('check_error', messageData, { error: error.message });
      
      return false; // Fail-open for availability
    }
  }

  /**
   * Mark message as sent to prevent future duplicates
   * 
   * @param {Object} messageData - Message data that was sent
   * @param {Object} options - Mark options
   * @returns {Promise<boolean>} Success status
   */
  async markAsSent(messageData, options = {}) {
    if (!this.enabled) return true;
    
    try {
      const fingerprint = this._generateFingerprint(messageData);
      const cacheKey = this._generateCacheKey(messageData, fingerprint);
      
      const entry = {
        content: messageData.content,
        platform: messageData.platform,
        conversation_id: messageData.conversation_id,
        fingerprint,
        timestamp: Date.now(),
        message_id: messageData.id || `temp-${Date.now()}`,
        user_id: messageData.user_id
      };
      
      // Store in cache
      const success = await this._setCacheEntry(cacheKey, entry);
      
      if (success) {
        this.cacheStats.sets++;
        
        // Log successful marking
        this._logDeduplicationEvent('marked_as_sent', messageData, {
          fingerprint,
          cache_key: cacheKey
        });
      }
      
      return success;
    } catch (error) {
      console.error('Error marking message as sent:', error);
      
      // Log error
      this._logDeduplicationEvent('mark_error', messageData, { error: error.message });
      
      return false;
    }
  }

  /**
   * Clear duplicates for a specific conversation or platform
   * 
   * @param {Object} filter - Filter criteria
   * @param {string} filter.platform - Platform to clear (optional)
   * @param {string} filter.conversation_id - Conversation ID to clear (optional)
   * @returns {Promise<number>} Number of entries cleared
   */
  async clearDuplicates(filter = {}) {
    if (!this.enabled) return 0;
    
    try {
      let clearedCount = 0;
      
      if (this.useRedis) {
        // Clear from Redis using pattern matching
        const pattern = this._generateClearPattern(filter);
        const response = await axios.delete(`${this.cacheEndpoint}/pattern`, {
          data: { pattern }
        });
        clearedCount = response.data?.cleared_count || 0;
      } else {
        // Clear from memory cache
        const keysToDelete = [];
        
        for (const [key, entry] of this.memoryCache.entries()) {
          if (this._matchesFilter(entry, filter)) {
            keysToDelete.push(key);
          }
        }
        
        keysToDelete.forEach(key => this.memoryCache.delete(key));
        clearedCount = keysToDelete.length;
      }
      
      this.cacheStats.deletes += clearedCount;
      
      // Log clearing operation
      this._logDeduplicationEvent('cleared_duplicates', filter, {
        cleared_count: clearedCount
      });
      
      return clearedCount;
    } catch (error) {
      console.error('Error clearing duplicates:', error);
      return 0;
    }
  }

  /**
   * Get deduplication statistics
   * 
   * @returns {Object} Deduplication statistics
   */
  getStats() {
    return {
      enabled: this.enabled,
      window_ms: this.windowMs,
      use_redis: this.useRedis,
      memory_cache_size: this.memoryCache.size,
      max_cache_size: this.maxCacheSize,
      platform_policies: this.platformPolicies,
      cache_stats: { ...this.cacheStats }
    };
  }

  /**
   * Update platform deduplication policy
   * 
   * @param {string} platform - Platform name
   * @param {Object} policy - Deduplication policy
   */
  updatePlatformPolicy(platform, policy) {
    this.platformPolicies[platform] = {
      ...this.platformPolicies[platform],
      ...policy
    };
  }

  /**
   * Disable deduplication
   */
  disable() {
    this.enabled = false;
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }

  /**
   * Enable deduplication
   */
  enable() {
    this.enabled = true;
    this._initializeCleanupTimer();
  }

  // Private methods

  /**
   * Generate content fingerprint
   * 
   * @private
   * @param {Object} messageData - Message data
   * @returns {string} Content fingerprint
   */
  _generateFingerprint(messageData) {
    const { content, platform, conversation_id } = messageData;
    
    // Normalize content for fingerprinting
    const normalizedContent = content
      .toLowerCase()
      .replace(/\s+/g, ' ')
      .trim();
    
    // Create fingerprint using simple hash
    const hashInput = `${platform}:${conversation_id}:${normalizedContent}`;
    return this._simpleHash(hashInput);
  }

  /**
   * Generate cache key
   * 
   * @private
   * @param {Object} messageData - Message data
   * @param {string} fingerprint - Content fingerprint
   * @returns {string} Cache key
   */
  _generateCacheKey(messageData, fingerprint) {
    return `sms_dedup:${messageData.platform}:${messageData.conversation_id}:${fingerprint}`;
  }

  /**
   * Calculate content similarity
   * 
   * @private
   * @param {string} content1 - First content
   * @param {string} content2 - Second content
   * @returns {number} Similarity score (0-1)
   */
  _calculateSimilarity(content1, content2) {
    if (content1 === content2) return 1.0;
    
    // Simple Jaccard similarity for now
    const words1 = new Set(content1.toLowerCase().split(/\s+/));
    const words2 = new Set(content2.toLowerCase().split(/\s+/));
    
    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);
    
    return intersection.size / union.size;
  }

  /**
   * Get cache entry
   * 
   * @private
   * @param {string} key - Cache key
   * @returns {Promise<Object|null>} Cache entry or null
   */
  async _getCacheEntry(key) {
    if (this.useRedis) {
      try {
        const response = await axios.get(`${this.cacheEndpoint}/${encodeURIComponent(key)}`);
        return response.data;
      } catch (error) {
        if (error.response?.status !== 404) {
          console.error('Error getting cache entry from Redis:', error);
        }
        return null;
      }
    } else {
      return this.memoryCache.get(key) || null;
    }
  }

  /**
   * Set cache entry
   * 
   * @private
   * @param {string} key - Cache key
   * @param {Object} entry - Cache entry
   * @returns {Promise<boolean>} Success status
   */
  async _setCacheEntry(key, entry) {
    if (this.useRedis) {
      try {
        await axios.put(`${this.cacheEndpoint}/${encodeURIComponent(key)}`, entry, {
          params: { ttl: Math.ceil(this.windowMs / 1000) }
        });
        return true;
      } catch (error) {
        console.error('Error setting cache entry in Redis:', error);
        return false;
      }
    } else {
      // Memory cache with size limit
      if (this.memoryCache.size >= this.maxCacheSize) {
        // Remove oldest entries
        const oldestKey = this.memoryCache.keys().next().value;
        this.memoryCache.delete(oldestKey);
      }
      
      this.memoryCache.set(key, entry);
      return true;
    }
  }

  /**
   * Generate clear pattern for Redis
   * 
   * @private
   * @param {Object} filter - Filter criteria
   * @returns {string} Redis pattern
   */
  _generateClearPattern(filter) {
    let pattern = 'sms_dedup:';
    
    if (filter.platform) {
      pattern += `${filter.platform}:`;
    } else {
      pattern += '*:';
    }
    
    if (filter.conversation_id) {
      pattern += `${filter.conversation_id}:*`;
    } else {
      pattern += '*';
    }
    
    return pattern;
  }

  /**
   * Check if entry matches filter
   * 
   * @private
   * @param {Object} entry - Cache entry
   * @param {Object} filter - Filter criteria
   * @returns {boolean} Whether entry matches filter
   */
  _matchesFilter(entry, filter) {
    if (filter.platform && entry.platform !== filter.platform) {
      return false;
    }
    
    if (filter.conversation_id && entry.conversation_id !== filter.conversation_id) {
      return false;
    }
    
    return true;
  }

  /**
   * Initialize cleanup timer
   * 
   * @private
   */
  _initializeCleanupTimer() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    
    // Clean up expired entries every 5 minutes
    this.cleanupTimer = setInterval(() => {
      this._cleanupExpiredEntries();
    }, 300000);
  }

  /**
   * Clean up expired entries from memory cache
   * 
   * @private
   */
  _cleanupExpiredEntries() {
    if (this.useRedis) return; // Redis handles TTL automatically
    
    const now = Date.now();
    const expiredKeys = [];
    
    for (const [key, entry] of this.memoryCache.entries()) {
      if (now - entry.timestamp > this.windowMs) {
        expiredKeys.push(key);
      }
    }
    
    expiredKeys.forEach(key => this.memoryCache.delete(key));
    
    if (expiredKeys.length > 0) {
      this.cacheStats.cleanups++;
      console.log(`[MessageDeduplication] Cleaned up ${expiredKeys.length} expired entries`);
    }
  }

  /**
   * Simple hash function
   * 
   * @private
   * @param {string} str - String to hash
   * @returns {string} Hash value
   */
  _simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Log deduplication event
   * 
   * @private
   * @param {string} operation - Operation type
   * @param {Object} messageData - Message data
   * @param {Object} metadata - Additional metadata
   */
  _logDeduplicationEvent(operation, messageData, metadata = {}) {
    const logData = {
      operation,
      platform: messageData.platform,
      conversation_id: messageData.conversation_id,
      timestamp: new Date().toISOString(),
      ...metadata
    };
    
    if (process.env.NODE_ENV === 'development') {
      console.log('[MessageDeduplication]', logData);
    }
  }
}

// Export singleton instance
export const messageDeduplication = new MessageDeduplicationService({
  enabled: process.env.NODE_ENV === 'production' || process.env.REACT_APP_DEDUPLICATION_ENABLED === 'true'
});

export default MessageDeduplicationService;
