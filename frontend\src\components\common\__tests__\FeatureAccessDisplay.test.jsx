/**
 * FeatureAccessDisplay Component Test Suite
 * Comprehensive testing for enterprise-grade feature access display
 @since 2024-1-1 to 2025-25-7
*/

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import FeatureAccessDisplay from '../FeatureAccessDisplay';

// Test theme
const theme = createTheme();

// Test wrapper component
const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

// Mock contexts
const mockAuth = {
  user: {
    id: 'test-user',
    subscription: {
      plan_id: 'creator',
      is_appsumo_lifetime: false
    }
  },
  hasFeature: jest.fn(),
  getFeatureLimit: jest.fn(),
  getFeatureDescription: jest.fn(),
  hasAddon: jest.fn(),
  getUserRole: jest.fn()
};

jest.mock('../../contexts/AuthContext', () => ({
  useAuth: () => mockAuth
}));

// Mock react-router-dom hooks
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => ({ pathname: '/test' })
}));

describe('FeatureAccessDisplay', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockAuth.hasFeature.mockReturnValue(false);
    mockAuth.getFeatureLimit.mockReturnValue(100);
    mockAuth.getFeatureDescription.mockReturnValue('Test feature description');
    mockAuth.hasAddon.mockReturnValue(false);
    mockAuth.getUserRole.mockReturnValue('viewer');
  });

  describe('Basic Functionality', () => {
    test('renders with default props', () => {
      render(
        <TestWrapper>
          <FeatureAccessDisplay />
        </TestWrapper>
      );

      expect(screen.getByText('Feature Access')).toBeInTheDocument();
    });

    test('renders features list when provided', () => {
      const features = ['advanced_analytics', 'premium_templates'];
      
      render(
        <TestWrapper>
          <FeatureAccessDisplay features={features} />
        </TestWrapper>
      );

      expect(screen.getByText('Advanced Analytics')).toBeInTheDocument();
      expect(screen.getByText('Premium Templates')).toBeInTheDocument();
    });

    test('renders usage limits when provided', () => {
      const limits = ['monthly_posts', 'social_accounts'];
      const usageData = { monthly_posts: 25, social_accounts: 2 };
      
      render(
        <TestWrapper>
          <FeatureAccessDisplay limits={limits} usageData={usageData} />
        </TestWrapper>
      );

      expect(screen.getByText('Monthly Posts')).toBeInTheDocument();
      expect(screen.getByText('Social Accounts')).toBeInTheDocument();
    });

    test('shows upgrade button by default', () => {
      render(
        <TestWrapper>
          <FeatureAccessDisplay />
        </TestWrapper>
      );

      expect(screen.getByText('Upgrade')).toBeInTheDocument();
    });

    test('hides upgrade button when showUpgradeButton is false', () => {
      render(
        <TestWrapper>
          <FeatureAccessDisplay showUpgradeButton={false} />
        </TestWrapper>
      );

      expect(screen.queryByText('Upgrade')).not.toBeInTheDocument();
    });
  });

  describe('Feature Display', () => {
    test('shows granted features with success icon', () => {
      mockAuth.hasFeature.mockReturnValue(true);
      
      render(
        <TestWrapper>
          <FeatureAccessDisplay features={['test_feature']} />
        </TestWrapper>
      );

      const successIcon = screen.getByTestId('CheckCircleIcon');
      expect(successIcon).toBeInTheDocument();
    });

    test('shows denied features with error icon', () => {
      mockAuth.hasFeature.mockReturnValue(false);
      
      render(
        <TestWrapper>
          <FeatureAccessDisplay features={['test_feature']} />
        </TestWrapper>
      );

      const errorIcon = screen.getByTestId('CancelIcon');
      expect(errorIcon).toBeInTheDocument();
    });

    test('shows addon features with shopping cart icon', () => {
      mockAuth.hasFeature.mockReturnValue(false);
      mockAuth.hasAddon.mockReturnValue(true);
      
      render(
        <TestWrapper>
          <FeatureAccessDisplay features={['test_feature']} />
        </TestWrapper>
      );

      expect(screen.getByText('Add-on')).toBeInTheDocument();
    });

    test('handles feature click when interactive mode enabled', async () => {
      const user = userEvent.setup();
      const onFeatureClick = jest.fn();
      
      render(
        <TestWrapper>
          <FeatureAccessDisplay 
            features={['test_feature']} 
            onFeatureClick={onFeatureClick}
            enableInteractiveMode
          />
        </TestWrapper>
      );

      const featureItem = screen.getByText('Test Feature');
      await user.click(featureItem);

      expect(onFeatureClick).toHaveBeenCalledWith(
        expect.objectContaining({
          key: 'test_feature',
          name: 'Test Feature'
        })
      );
    });
  });

  describe('Usage Limits Display', () => {
    test('shows usage progress correctly', () => {
      const limits = ['monthly_posts'];
      const usageData = { monthly_posts: 75 };
      mockAuth.getFeatureLimit.mockReturnValue(100);
      
      render(
        <TestWrapper>
          <FeatureAccessDisplay limits={limits} usageData={usageData} />
        </TestWrapper>
      );

      expect(screen.getByText('75 / 100')).toBeInTheDocument();
      expect(screen.getByText('75%')).toBeInTheDocument();
    });

    test('shows unlimited limits correctly', () => {
      const limits = ['monthly_posts'];
      const usageData = { monthly_posts: 75 };
      mockAuth.getFeatureLimit.mockReturnValue(-1);
      
      render(
        <TestWrapper>
          <FeatureAccessDisplay limits={limits} usageData={usageData} />
        </TestWrapper>
      );

      expect(screen.getByText('75 / ∞')).toBeInTheDocument();
    });

    test('shows warning for high usage', () => {
      const limits = ['monthly_posts'];
      const usageData = { monthly_posts: 95 };
      mockAuth.getFeatureLimit.mockReturnValue(100);
      
      render(
        <TestWrapper>
          <FeatureAccessDisplay limits={limits} usageData={usageData} />
        </TestWrapper>
      );

      const warningIcon = screen.getByTestId('WarningIcon');
      expect(warningIcon).toBeInTheDocument();
    });

    test('handles limit click when interactive mode enabled', async () => {
      const user = userEvent.setup();
      const onLimitClick = jest.fn();
      
      render(
        <TestWrapper>
          <FeatureAccessDisplay 
            limits={['monthly_posts']} 
            usageData={{ monthly_posts: 50 }}
            onLimitClick={onLimitClick}
            enableInteractiveMode
          />
        </TestWrapper>
      );

      const limitItem = screen.getByText('Monthly Posts');
      await user.click(limitItem);

      expect(onLimitClick).toHaveBeenCalledWith(
        expect.objectContaining({
          key: 'monthly_posts',
          name: 'Monthly Posts'
        })
      );
    });
  });

  describe('Display Variants', () => {
    test('renders compact variant correctly', () => {
      render(
        <TestWrapper>
          <FeatureAccessDisplay 
            variant="compact"
            features={['test_feature']}
          />
        </TestWrapper>
      );

      // Compact variant should still show features
      expect(screen.getByText('Test Feature')).toBeInTheDocument();
    });

    test('renders detailed variant with tabs', () => {
      render(
        <TestWrapper>
          <FeatureAccessDisplay 
            variant="detailed"
            features={['test_feature']}
            limits={['monthly_posts']}
            usageData={{ monthly_posts: 50 }}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Features')).toBeInTheDocument();
      expect(screen.getByText('Usage Limits')).toBeInTheDocument();
    });

    test('shows permission matrix tab when enabled', () => {
      render(
        <TestWrapper>
          <FeatureAccessDisplay 
            variant="detailed"
            features={['test_feature']}
            showPermissionMatrix
          />
        </TestWrapper>
      );

      expect(screen.getByText('Permission Matrix')).toBeInTheDocument();
    });
  });

  describe('AppSumo Integration', () => {
    test('shows AppSumo upgrade button for AppSumo users', () => {
      mockAuth.user.subscription.is_appsumo_lifetime = true;
      
      render(
        <TestWrapper>
          <FeatureAccessDisplay />
        </TestWrapper>
      );

      expect(screen.getByText('Upgrade AppSumo Tier')).toBeInTheDocument();
    });

    test('shows regular upgrade button for non-AppSumo users', () => {
      mockAuth.user.subscription.is_appsumo_lifetime = false;
      
      render(
        <TestWrapper>
          <FeatureAccessDisplay />
        </TestWrapper>
      );

      expect(screen.getByText('Upgrade')).toBeInTheDocument();
    });
  });

  describe('Analytics Integration', () => {
    test('tracks analytics events when enabled', () => {
      const onAnalytics = jest.fn();
      
      render(
        <TestWrapper>
          <FeatureAccessDisplay 
            features={['test_feature']}
            enableAnalytics 
            onAnalytics={onAnalytics}
          />
        </TestWrapper>
      );

      // Analytics should be tracked for component render
      expect(onAnalytics).toHaveBeenCalledWith(
        expect.objectContaining({
          component: 'FeatureAccessDisplay'
        })
      );
    });

    test('tracks tab changes in detailed view', async () => {
      const user = userEvent.setup();
      const onAnalytics = jest.fn();
      
      render(
        <TestWrapper>
          <FeatureAccessDisplay 
            variant="detailed"
            features={['test_feature']}
            limits={['monthly_posts']}
            usageData={{ monthly_posts: 50 }}
            enableAnalytics 
            onAnalytics={onAnalytics}
          />
        </TestWrapper>
      );

      const usageLimitsTab = screen.getByText('Usage Limits');
      await user.click(usageLimitsTab);

      expect(onAnalytics).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'tab_changed',
          tabIndex: 1
        })
      );
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA labels', () => {
      render(
        <TestWrapper>
          <FeatureAccessDisplay 
            ariaLabel="Custom accessibility label"
            features={['test_feature']}
          />
        </TestWrapper>
      );

      expect(screen.getByLabelText('Custom accessibility label')).toBeInTheDocument();
    });

    test('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <FeatureAccessDisplay 
            features={['test_feature']}
            enableInteractiveMode
          />
        </TestWrapper>
      );

      const featureItem = screen.getByText('Test Feature');
      featureItem.focus();
      
      expect(document.activeElement).toBe(featureItem);
      
      await user.keyboard('{Enter}');
      // Should trigger feature interaction
    });
  });

  describe('Error Handling', () => {
    test('handles missing feature descriptions gracefully', () => {
      mockAuth.getFeatureDescription.mockReturnValue('');
      
      render(
        <TestWrapper>
          <FeatureAccessDisplay features={['test_feature']} />
        </TestWrapper>
      );

      // Should still render the feature with formatted name
      expect(screen.getByText('Test Feature')).toBeInTheDocument();
    });

    test('handles missing usage data gracefully', () => {
      render(
        <TestWrapper>
          <FeatureAccessDisplay 
            limits={['monthly_posts']} 
            usageData={{}} 
          />
        </TestWrapper>
      );

      expect(screen.getByText('0 / 100')).toBeInTheDocument();
    });
  });
});
