# @since 2024-1-1 to 2025-25-7
version: '3.8'

# ACE Social Platform - Test Environment Docker Compose Configuration
# Version: 2.0.0
# Optimized for automated testing and CI/CD pipelines

services:
  # ================================
  # Backend API Service (Test)
  # ================================
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: ace-social-backend-test
    hostname: ace-backend-test
    ports:
      - "8000:8000"
    environment:
      # Application settings
      - ENVIRONMENT=test
      - DEBUG=true
      - SECRET_KEY=test-secret-key-for-ci-cd-only
      - PROJECT_NAME=ACE Social (Test)
      
      # Database connections
      - MONGODB_URL=********************************************************************************
      - REDIS_URL=redis://:test_redis_123@redis:6379/1
      
      # Mock external services for testing
      - OPENAI_API_KEY=test-openai-key
      - SENDGRID_API_KEY=test-sendgrid-key
      - LEMON_SQUEEZY_API_KEY=test-lemon-squeezy-key
      
      # Security settings (relaxed for testing)
      - CORS_ORIGINS=["http://localhost:3000","http://localhost:5173"]
      - JWT_SECRET_KEY=test-jwt-secret-key-for-ci-cd-only
      
      # Performance settings (optimized for testing)
      - WORKERS=1
      - MAX_UPLOAD_SIZE=10485760
      - RATE_LIMIT_ENABLED=false
      
      # Testing specific
      - LOG_LEVEL=DEBUG
      - ENABLE_METRICS=false
      - MOCK_EXTERNAL_APIS=true
    
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    
    networks:
      - ace-test-network
    
    restart: "no"
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health-minimal"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 20s
    
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 256M

  # ================================
  # MongoDB Database (Test)
  # ================================
  mongodb:
    image: mongo:7.0
    container_name: ace-social-mongodb-test
    hostname: ace-mongodb-test
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=test_password_123
      - MONGO_INITDB_DATABASE=ace_social_test
    volumes:
      - mongodb_test_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - ace-test-network
    restart: "no"
    
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # ================================
  # Redis Cache (Test)
  # ================================
  redis:
    image: redis:7.2-alpine
    container_name: ace-social-redis-test
    hostname: ace-redis-test
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass test_redis_123
    volumes:
      - redis_test_data:/data
    networks:
      - ace-test-network
    restart: "no"
    
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 5s
    
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M

  # ================================
  # Frontend (Test) - For E2E Testing
  # ================================
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: ace-social-frontend-test
    hostname: ace-frontend-test
    ports:
      - "3000:3000"
    environment:
      - VITE_API_URL=http://backend:8000
      - VITE_ENVIRONMENT=test
      - NODE_ENV=test
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - ace-test-network
    restart: "no"
    
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 256M

  # ================================
  # Admin App (Test) - For E2E Testing
  # ================================
  admin:
    build:
      context: ./admin-app
      dockerfile: Dockerfile
      target: development
    container_name: ace-social-admin-test
    hostname: ace-admin-test
    ports:
      - "3001:3001"
    environment:
      - VITE_API_URL=http://backend:8000
      - VITE_ENVIRONMENT=test
      - NODE_ENV=test
    volumes:
      - ./admin-app:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - ace-test-network
    restart: "no"
    
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M

  # ================================
  # Test Runner Service
  # ================================
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: ace-social-test-runner
    hostname: ace-test-runner
    environment:
      - ENVIRONMENT=test
      - MONGODB_URL=********************************************************************************
      - REDIS_URL=redis://:test_redis_123@redis:6379/1
    volumes:
      - ./backend:/app/backend
      - ./frontend:/app/frontend
      - ./admin-app:/app/admin-app
      - ./test-results:/app/test-results
    depends_on:
      backend:
        condition: service_healthy
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - ace-test-network
    restart: "no"
    
    command: ["tail", "-f", "/dev/null"]  # Keep container running for test execution

# ================================
# Volumes
# ================================
volumes:
  mongodb_test_data:
    driver: local
  redis_test_data:
    driver: local

# ================================
# Networks
# ================================
networks:
  ace-test-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ================================
# Test Environment Notes
# ================================
# This configuration is optimized for:
# 1. Fast startup and teardown
# 2. Minimal resource usage
# 3. Isolated test environment
# 4. Mock external services
# 5. Comprehensive test coverage
# 6. CI/CD pipeline integration
#
# Usage:
# - Start: docker-compose -f docker-compose.test.yml up -d
# - Run tests: docker-compose -f docker-compose.test.yml exec test-runner pytest
# - Stop: docker-compose -f docker-compose.test.yml down -v
#
# Environment Variables:
# All sensitive values use test-specific defaults
# No production secrets required
# Mock services enabled by default
