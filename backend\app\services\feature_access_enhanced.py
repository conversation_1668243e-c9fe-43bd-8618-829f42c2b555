"""
Enhanced feature access service that integrates subscription limits with add-on capabilities.
Provides comprehensive access control for ACEO platform features.
@since 2024-1-1 to 2025-25-7
"""
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta, timedelta
from enum import Enum

from app.models.user import User
from app.services.addon_usage_tracking import usage_tracker
from app.services.addon_catalog import addon_catalog
from app.core.monitoring import record_access_metrics

logger = logging.getLogger(__name__)


class AccessResult(str, Enum):
    """Feature access results."""
    ALLOWED = "allowed"
    BLOCKED_SUBSCRIPTION = "blocked_subscription"
    BLOCKED_USAGE_LIMIT = "blocked_usage_limit"
    BLOCKED_ADDON_REQUIRED = "blocked_addon_required"
    BLOCKED_EXPIRED = "blocked_expired"
    BLOCKED_PAYMENT = "blocked_payment"


class FeatureAccessManager:
    """Enhanced feature access manager with add-on integration."""
    
    def __init__(self):
        # Define feature mappings to usage types and requirements
        self.feature_mappings = {
            "content_regeneration": {
                "usage_type": "regeneration_credits",
                "base_limits": {"creator": 50, "accelerator": 200, "dominator": -1},  # -1 = unlimited
                "addon_enhancers": ["regeneration_booster"],
                "description": "Content regeneration and optimization"
            },
            "image_generation": {
                "usage_type": "image_generation",
                "base_limits": {"creator": 20, "accelerator": 100, "dominator": 500},
                "addon_enhancers": ["image_pack_premium"],
                "description": "AI image generation with DALL-E"
            },
            "sentiment_analysis": {
                "usage_type": "sentiment_analysis",
                "base_limits": {"creator": 500, "accelerator": 1500, "dominator": -1},
                "addon_enhancers": ["sentiment_analysis_pro"],
                "description": "Advanced sentiment analysis"
            },
            "auto_replies": {
                "usage_type": "auto_replies",
                "base_limits": {"creator": 200, "accelerator": 1000, "dominator": -1},
                "addon_enhancers": ["sentiment_analysis_pro"],
                "description": "AI-powered auto-replies"
            },
            "team_collaboration": {
                "usage_type": "team_seats",
                "base_limits": {"creator": 2, "accelerator": 5, "dominator": -1},
                "addon_enhancers": ["additional_user_seats"],
                "description": "Team collaboration features"
            },
            "priority_support": {
                "usage_type": "priority_support",
                "base_limits": {"creator": 0, "accelerator": 0, "dominator": 0},  # Addon only
                "addon_enhancers": ["priority_support"],
                "description": "Priority customer support"
            },
            "white_label": {
                "usage_type": "white_label_access",
                "base_limits": {"creator": 0, "accelerator": 0, "dominator": 0},  # Addon only
                "addon_enhancers": ["white_label_platform"],
                "description": "White-label platform customization"
            },
            "advanced_analytics": {
                "usage_type": "advanced_analytics",
                "base_limits": {"creator": 0, "accelerator": 1, "dominator": 1},
                "addon_enhancers": ["analytics_pro"],
                "description": "Advanced analytics and insights"
            }
        }
    
    async def check_feature_access(self, user: User, feature: str, 
                                 usage_amount: int = 1) -> Dict[str, Any]:
        """
        Comprehensive feature access check including subscription and add-ons.
        
        Returns:
            Dict with access information, limits, and suggestions
        """
        try:
            # Get feature configuration
            feature_config = self.feature_mappings.get(feature)
            if not feature_config:
                return self._create_access_result(
                    AccessResult.BLOCKED_SUBSCRIPTION,
                    f"Unknown feature: {feature}",
                    "Contact support"
                )
            
            # Get user's subscription plan
            user_plan = user.subscription.plan_id if user.subscription else "creator"
            
            # Check subscription status
            if not self._is_subscription_active(user):
                return self._create_access_result(
                    AccessResult.BLOCKED_PAYMENT,
                    "Subscription is not active",
                    "Update payment method"
                )
            
            # Get base limits and current usage
            base_limit = feature_config["base_limits"].get(user_plan, 0)
            current_usage = await self._get_current_usage(str(user.id), feature_config["usage_type"])
            
            # Calculate add-on enhancements
            addon_limits = await self._calculate_addon_limits(user, feature_config)
            total_limit = base_limit + addon_limits["bonus_credits"]
            
            # Handle unlimited features
            if total_limit == -1:
                return self._create_access_result(
                    AccessResult.ALLOWED,
                    "Unlimited access",
                    None,
                    {
                        "current_usage": current_usage,
                        "total_limit": -1,
                        "base_limit": base_limit,
                        "addon_bonus": addon_limits["bonus_credits"],
                        "remaining": -1,
                        "usage_percentage": 0
                    }
                )
            
            # Check if usage would exceed limits
            if current_usage + usage_amount > total_limit:
                # Check if add-ons could help
                available_addons = await self._get_enhancing_addons(user, feature_config)
                
                if available_addons:
                    return self._create_access_result(
                        AccessResult.BLOCKED_USAGE_LIMIT,
                        f"Usage limit reached ({current_usage}/{total_limit})",
                        f"Get {available_addons[0]['name']} for more credits",
                        {
                            "current_usage": current_usage,
                            "total_limit": total_limit,
                            "base_limit": base_limit,
                            "addon_bonus": addon_limits["bonus_credits"],
                            "remaining": max(0, total_limit - current_usage),
                            "usage_percentage": (current_usage / total_limit) * 100,
                            "suggested_addons": available_addons[:3]
                        }
                    )
                else:
                    return self._create_access_result(
                        AccessResult.BLOCKED_USAGE_LIMIT,
                        f"Usage limit reached ({current_usage}/{total_limit})",
                        "Upgrade your plan for higher limits",
                        {
                            "current_usage": current_usage,
                            "total_limit": total_limit,
                            "base_limit": base_limit,
                            "addon_bonus": addon_limits["bonus_credits"],
                            "remaining": max(0, total_limit - current_usage),
                            "usage_percentage": (current_usage / total_limit) * 100
                        }
                    )
            
            # Access allowed
            return self._create_access_result(
                AccessResult.ALLOWED,
                "Access granted",
                None,
                {
                    "current_usage": current_usage,
                    "total_limit": total_limit,
                    "base_limit": base_limit,
                    "addon_bonus": addon_limits["bonus_credits"],
                    "remaining": total_limit - current_usage,
                    "usage_percentage": (current_usage / total_limit) * 100 if total_limit > 0 else 0,
                    "active_addons": addon_limits["active_addons"]
                }
            )
            
        except Exception as e:
            logger.error(f"Error checking feature access for user {user.id}, feature {feature}: {str(e)}")
            return self._create_access_result(
                AccessResult.BLOCKED_SUBSCRIPTION,
                "Error checking access",
                "Try again later"
            )
    
    async def get_feature_limits_summary(self, user: User) -> Dict[str, Any]:
        """Get a summary of all feature limits for the user."""
        try:
            summary = {}
            user_plan = user.subscription.plan_id if user.subscription else "creator"
            
            for feature, config in self.feature_mappings.items():
                base_limit = config["base_limits"].get(user_plan, 0)
                current_usage = await self._get_current_usage(str(user.id), config["usage_type"])
                addon_limits = await self._calculate_addon_limits(user, config)
                total_limit = base_limit + addon_limits["bonus_credits"]
                
                summary[feature] = {
                    "description": config["description"],
                    "base_limit": base_limit,
                    "addon_bonus": addon_limits["bonus_credits"],
                    "total_limit": total_limit,
                    "current_usage": current_usage,
                    "remaining": max(0, total_limit - current_usage) if total_limit != -1 else -1,
                    "usage_percentage": (current_usage / total_limit) * 100 if total_limit > 0 else 0,
                    "status": "unlimited" if total_limit == -1 else "limited",
                    "active_addons": addon_limits["active_addons"]
                }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting feature limits summary for user {user.id}: {str(e)}")
            return {}
    
    async def predict_usage_exhaustion(self, user: User, feature: str) -> Optional[Dict[str, Any]]:
        """Predict when a user will exhaust their feature usage."""
        try:
            feature_config = self.feature_mappings.get(feature)
            if not feature_config:
                return None
            
            # Get usage analytics
            usage_analytics = await usage_tracker.get_usage_analytics(
                str(user.id),
                days=30
            )
            
            usage_type = feature_config["usage_type"]
            if usage_type not in usage_analytics.get("usage_by_type", {}):
                return None
            
            # Calculate daily average usage
            usage_data = usage_analytics["usage_by_type"][usage_type]
            daily_average = usage_data["total_amount"] / 30  # 30 days
            
            if daily_average <= 0:
                return None
            
            # Get current limits and usage
            access_info = await self.check_feature_access(user, feature, 0)
            if not access_info.get("limits"):
                return None
            
            limits = access_info["limits"]
            remaining = limits["remaining"]
            
            if remaining <= 0 or limits["total_limit"] == -1:
                return None
            
            # Predict exhaustion
            days_until_exhaustion = remaining / daily_average
            
            return {
                "feature": feature,
                "days_until_exhaustion": round(days_until_exhaustion, 1),
                "daily_average_usage": round(daily_average, 2),
                "remaining_credits": remaining,
                "predicted_exhaustion_date": (
                    datetime.now(timezone.utc).date() + 
                    timedelta(days=int(days_until_exhaustion))
                ).isoformat(),
                "confidence": "high" if usage_data["count"] >= 10 else "medium"
            }
            
        except Exception as e:
            logger.error(f"Error predicting usage exhaustion for user {user.id}, feature {feature}: {str(e)}")
            return None
    
    def _is_subscription_active(self, user: User) -> bool:
        """Check if user's subscription is active."""
        if not user.subscription:
            return False
        
        return (user.subscription.status == "active" and
                user.subscription.status != "canceled" and
                (not user.subscription.end_date or
                 user.subscription.end_date > datetime.now(timezone.utc)))
    
    async def _get_current_usage(self, user_id: str, usage_type: str) -> int:
        """Get current usage for a specific usage type."""
        try:
            # Get usage from the current billing period
            # This would typically be from the start of the current month
            
            # For now, return a simplified calculation
            # In production, this would query the usage tracking system
            analytics = await usage_tracker.get_usage_analytics(user_id, days=30)
            usage_by_type = analytics.get("usage_by_type", {})
            
            return usage_by_type.get(usage_type, {}).get("total_amount", 0)
            
        except Exception as e:
            logger.error(f"Error getting current usage for user {user_id}, type {usage_type}: {str(e)}")
            return 0
    
    async def _calculate_addon_limits(self, user: User, feature_config: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate additional limits provided by add-ons."""
        try:
            bonus_credits = 0
            active_addons = []
            
            # Get user's active add-ons
            user_addons = await usage_tracker.get_addon_status(str(user.id))
            
            for addon in user_addons:
                if (addon["status"] == "active" and 
                    addon["addon_id"] in feature_config["addon_enhancers"]):
                    
                    # Add remaining credits from this add-on
                    bonus_credits += addon["credits_remaining"]
                    active_addons.append({
                        "addon_id": addon["addon_id"],
                        "name": addon["name"],
                        "credits_remaining": addon["credits_remaining"],
                        "expires_at": addon["expires_at"]
                    })
            
            return {
                "bonus_credits": bonus_credits,
                "active_addons": active_addons
            }
            
        except Exception as e:
            logger.error(f"Error calculating addon limits for user {user.id}: {str(e)}")
            return {"bonus_credits": 0, "active_addons": []}
    
    async def _get_enhancing_addons(self, user: User, feature_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get available add-ons that could enhance this feature."""
        try:
            enhancing_addon_ids = feature_config["addon_enhancers"]
            available_addons = []
            
            for addon_id in enhancing_addon_ids:
                addon_config = addon_catalog.catalog.get(addon_id)
                if addon_config and addon_catalog._user_meets_requirements(user, addon_config):
                    available_addons.append(addon_config)
            
            return available_addons
            
        except Exception as e:
            logger.error(f"Error getting enhancing addons for user {user.id}: {str(e)}")
            return []
    
    def _create_access_result(self, result: AccessResult, message: str, 
                            suggested_action: Optional[str], 
                            limits: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Create a standardized access result."""
        return {
            "has_access": result == AccessResult.ALLOWED,
            "result": result.value,
            "message": message,
            "suggested_action": suggested_action,
            "limits": limits,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }


# Global feature access manager
feature_access_manager = FeatureAccessManager()


async def check_feature_access_with_addons(user: User, feature: str, 
                                         usage_amount: int = 1) -> Dict[str, Any]:
    """Check feature access including add-on enhancements."""
    result = await feature_access_manager.check_feature_access(user, feature, usage_amount)
    
    # Record metrics
    record_access_metrics(
        feature, 
        result["result"], 
        user.subscription.plan_id if user.subscription else "free"
    )
    
    return result


async def get_user_feature_limits(user: User) -> Dict[str, Any]:
    """Get comprehensive feature limits for a user."""
    return await feature_access_manager.get_feature_limits_summary(user)


async def predict_feature_exhaustion(user: User, feature: str) -> Optional[Dict[str, Any]]:
    """Predict when a user will exhaust their feature usage."""
    return await feature_access_manager.predict_usage_exhaustion(user, feature)
