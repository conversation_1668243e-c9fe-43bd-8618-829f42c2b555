"""
Example demonstrating adaptive text margins functionality.
Shows how your complex prompt would be enhanced with size-aware text margins.
@since 2024-1-1 to 2025-25-7
"""

from app.services.content_generator import _enhance_prompt_with_branding
from app.schemas.content import BrandingData


def demonstrate_text_margins():
    """Demonstrate text margins with your specific example."""
    
    # Your original prompt
    original_prompt = """Mission control dashboard: Central ACE Social logo (#4E40C5 glow) with <PERSON>ooCommerce (left) and Shopify (right) logos connected by pulsating data cables. Product thumbnails transform into ICP profiles above control panels: 1) Yoga mat → wellness influencer 2) Coffee beans → remote worker 3) Backpack → digital nomad. Campaign stations below generate content: DALL-E creates product visuals, AI writes platform-specific copy, scheduler plots calendars. #EBAE1B alert: 'Campaigns Live in 00:02:18'. Glass HUD interface with #15110E depth."""
    
    # Your branding data
    branding_data = BrandingData(
        colorSystem={
            "primary": "#4E40C5",
            "secondary": "#EBAE1B", 
            "accent": "#FFFFFF",
            "background": "#15110E"
        },
        visualStyle={
            "photographyStyle": "tech-command",
            "lighting": "holographic-interface"
        },
        imageComposition={
            "layout": "control-hierarchy",
            "subjectPosition": "central-command",
            "textMargins": {
                "top": "10%",
                "bottom": "8%", 
                "sides": "6%"
            }
        },
        style="ecom-command"
    )
    
    print("=== ORIGINAL PROMPT ===")
    print(original_prompt)
    print("\n" + "="*80 + "\n")
    
    # Test different image sizes
    sizes = [
        ("1792x1024", "Ultra-wide landscape"),
        ("1024x1024", "Square format"),
        ("1024x1792", "Portrait format"),
        ("512x512", "Small square")
    ]
    
    for size, description in sizes:
        print(f"=== {description.upper()} ({size}) ===")
        
        enhanced_prompt = _enhance_prompt_with_branding(
            original_prompt, 
            branding_data, 
            size
        )
        
        print(f"Enhanced prompt for {size}:")
        print("-" * 60)
        print(enhanced_prompt)
        print("\n" + "="*80 + "\n")


def show_text_margin_calculations():
    """Show how text margins are calculated for different sizes."""
    
    from app.services.content_generator import _build_adaptive_text_margins
    
    margins = {
        "top": "10%",
        "bottom": "8%",
        "sides": "6%"
    }
    
    sizes = [
        ("1792x1024", "Ultra-wide (1.75:1)"),
        ("1600x900", "Wide (1.78:1)"),
        ("1024x1024", "Square (1:1)"),
        ("900x1600", "Portrait (0.56:1)"),
        ("1024x1792", "Tall Portrait (0.57:1)")
    ]
    
    print("=== TEXT MARGIN CALCULATIONS ===\n")
    
    for size, description in sizes:
        print(f"{description} - {size}")
        print("-" * 40)
        
        result = _build_adaptive_text_margins(margins, size)
        print(result)
        print("\n")


def show_branding_enhancement_breakdown():
    """Show how each branding element is enhanced."""
    
    from app.services.content_generator import (
        _build_color_system_enhancement,
        _build_visual_style_enhancement,
        _build_composition_enhancement,
        _build_brand_style_enhancement
    )
    
    # Your branding components
    color_system = {
        "primary": "#4E40C5",
        "secondary": "#EBAE1B",
        "accent": "#FFFFFF", 
        "background": "#15110E"
    }
    
    visual_style = {
        "photographyStyle": "tech-command",
        "lighting": "holographic-interface"
    }
    
    composition = {
        "layout": "control-hierarchy",
        "subjectPosition": "central-command",
        "textMargins": {
            "top": "10%",
            "bottom": "8%",
            "sides": "6%"
        }
    }
    
    print("=== BRANDING ENHANCEMENT BREAKDOWN ===\n")
    
    print("1. COLOR SYSTEM ENHANCEMENT:")
    print("-" * 40)
    color_enhancement = _build_color_system_enhancement(color_system)
    print(color_enhancement)
    print()
    
    print("2. VISUAL STYLE ENHANCEMENT:")
    print("-" * 40)
    style_enhancement = _build_visual_style_enhancement(visual_style)
    print(style_enhancement)
    print()
    
    print("3. COMPOSITION ENHANCEMENT (with text margins):")
    print("-" * 40)
    composition_enhancement = _build_composition_enhancement(composition, "1792x1024")
    print(composition_enhancement)
    print()
    
    print("4. BRAND STYLE ENHANCEMENT:")
    print("-" * 40)
    brand_style_enhancement = _build_brand_style_enhancement("ecom-command")
    print(brand_style_enhancement)
    print()


if __name__ == "__main__":
    print("🎨 ADAPTIVE TEXT MARGINS DEMONSTRATION\n")
    
    demonstrate_text_margins()
    
    print("\n" + "🔢 TEXT MARGIN CALCULATIONS\n")
    show_text_margin_calculations()
    
    print("\n" + "🎯 BRANDING ENHANCEMENT BREAKDOWN\n")
    show_branding_enhancement_breakdown()
