// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import CompositionEditor from '../CompositionEditor';

// Mock the notification hook
const mockShowSuccessNotification = vi.fn();
const mockShowErrorNotification = vi.fn();

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showSuccessNotification: mockShowSuccessNotification,
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn().mockResolvedValue(undefined),
  },
});

// Mock URL.createObjectURL for export functionality
global.URL.createObjectURL = vi.fn(() => 'mock-url');
global.URL.revokeObjectURL = vi.fn();

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      error: {
        main: '#d32f2f',
      },
      warning: {
        main: '#ed6c02',
      },
      success: {
        main: '#2e7d32',
      },
      grey: {
        300: '#e0e0e0',
        400: '#bdbdbd',
        500: '#9e9e9e',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('CompositionEditor', () => {
  const mockComposition = {
    layout: 'rule-of-thirds',
    spacing: 'normal',
    typography: 'minor-third',
    grid: '12-column',
    aspectRatio: '16:9',
    subjectPosition: 'center',
    negativeSpace: 'balanced',
    depthOfField: 'medium',
    templates: [
      {
        id: '1',
        name: 'Test Template',
        category: 'Custom',
        layout: 'golden-ratio',
        spacing: 'loose',
        typography: 'major-third',
        grid: '16-column',
        aspectRatio: '4:3',
        description: 'Test template description',
        preview: 'test-image.jpg',
        difficulty: 'Intermediate',
        created: '2023-01-01T00:00:00Z'
      }
    ]
  };

  const mockProps = {
    composition: mockComposition,
    onChange: vi.fn(),
    onError: vi.fn(),
    onExport: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders composition editor correctly', () => {
    render(
      <TestWrapper>
        <CompositionEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Image Composition')).toBeInTheDocument();
    expect(screen.getByText('Define how your brand\'s visual content should be composed. Set layout guidelines, subject positioning, and aspect ratios.')).toBeInTheDocument();
  });

  test('shows loading state when no composition provided', () => {
    render(
      <TestWrapper>
        <CompositionEditor composition={null} onChange={vi.fn()} />
      </TestWrapper>
    );

    // Component should still render with default values
    expect(screen.getByText('Image Composition')).toBeInTheDocument();
  });

  test('handles layout change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CompositionEditor {...mockProps} />
      </TestWrapper>
    );

    // Find and change layout
    const layoutSelect = screen.getByDisplayValue('Rule of Thirds');
    await user.click(layoutSelect);
    
    const goldenRatioOption = screen.getByText('Golden Ratio');
    await user.click(goldenRatioOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith(
        expect.objectContaining({
          layout: 'golden-ratio'
        })
      );
    });
  });

  test('handles spacing change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CompositionEditor {...mockProps} />
      </TestWrapper>
    );

    // Find and change spacing (this would need to be implemented in the UI)
    // For now, test that the component renders without errors
    expect(screen.getByText('Composition Settings')).toBeInTheDocument();
  });

  test('handles template saving', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CompositionEditor {...mockProps} />
      </TestWrapper>
    );

    // Find template name input
    const templateNameInput = screen.getByLabelText(/template name/i);
    await user.type(templateNameInput, 'New Template');

    // Find and click save button
    const saveButton = screen.getByRole('button', { name: /save/i });
    await user.click(saveButton);

    await waitFor(() => {
      expect(mockShowSuccessNotification).toHaveBeenCalledWith(
        expect.stringContaining('Template "New Template" saved successfully!')
      );
    });
  });

  test('handles template application', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CompositionEditor {...mockProps} />
      </TestWrapper>
    );

    // Find and click apply button for existing template
    const applyButtons = screen.getAllByText('Apply');
    if (applyButtons.length > 0) {
      await user.click(applyButtons[0]);

      await waitFor(() => {
        expect(mockProps.onChange).toHaveBeenCalled();
      });
    }
  });

  test('handles template deletion', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CompositionEditor {...mockProps} />
      </TestWrapper>
    );

    // Find and click delete button for existing template
    const deleteButtons = screen.getAllByTestId('DeleteIcon');
    if (deleteButtons.length > 0) {
      await user.click(deleteButtons[0]);

      await waitFor(() => {
        expect(mockProps.onChange).toHaveBeenCalledWith(
          expect.objectContaining({
            templates: []
          })
        );
      });
    }
  });

  test('handles grid toggle', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CompositionEditor {...mockProps} />
      </TestWrapper>
    );

    // Find and toggle grid switch
    const gridSwitch = screen.getByRole('checkbox', { name: /show grid/i });
    await user.click(gridSwitch);

    // Grid should be toggled (this is internal state, so we just verify no errors)
    expect(gridSwitch).toBeInTheDocument();
  });

  test('handles aspect ratio change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CompositionEditor {...mockProps} />
      </TestWrapper>
    );

    // Find and change aspect ratio
    const aspectRatioSelect = screen.getByDisplayValue('16:9 - Landscape');
    await user.click(aspectRatioSelect);
    
    const squareOption = screen.getByText('1:1 - Square');
    await user.click(squareOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith(
        expect.objectContaining({
          aspectRatio: '1:1'
        })
      );
    });
  });

  test('displays composition preview correctly', () => {
    render(
      <TestWrapper>
        <CompositionEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Composition Preview')).toBeInTheDocument();
    expect(screen.getByAltText('Composition Preview')).toBeInTheDocument();
  });

  test('shows saved templates section', () => {
    render(
      <TestWrapper>
        <CompositionEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Saved Templates')).toBeInTheDocument();
    expect(screen.getByText('Test Template')).toBeInTheDocument();
  });

  test('handles empty templates gracefully', () => {
    const propsWithoutTemplates = {
      ...mockProps,
      composition: {
        ...mockComposition,
        templates: []
      }
    };

    render(
      <TestWrapper>
        <CompositionEditor {...propsWithoutTemplates} />
      </TestWrapper>
    );

    expect(screen.getByText('No templates saved yet. Create your first template by configuring the settings and saving it.')).toBeInTheDocument();
  });

  test('validates template name before saving', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CompositionEditor {...mockProps} />
      </TestWrapper>
    );

    // Try to save without entering a name
    const saveButton = screen.getByRole('button', { name: /save/i });
    await user.click(saveButton);

    // Save button should be disabled when no name is entered
    expect(saveButton).toBeDisabled();
  });

  test('handles disabled state correctly', () => {
    render(
      <TestWrapper>
        <CompositionEditor {...mockProps} disabled />
      </TestWrapper>
    );

    // All form controls should be disabled
    const layoutSelect = screen.getByDisplayValue('Rule of Thirds');
    expect(layoutSelect.closest('.MuiSelect-root')).toHaveClass('Mui-disabled');
  });

  test('handles read-only state correctly', () => {
    render(
      <TestWrapper>
        <CompositionEditor {...mockProps} readOnly />
      </TestWrapper>
    );

    // Save button should be disabled in read-only mode
    const saveButton = screen.getByRole('button', { name: /save/i });
    expect(saveButton).toBeDisabled();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <CompositionEditor {...mockProps} />
      </TestWrapper>
    );

    // Check for proper labels
    expect(screen.getByLabelText(/layout/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/aspect ratio/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/template name/i)).toBeInTheDocument();
  });

  test('handles error scenarios gracefully', async () => {
    const user = userEvent.setup();
    const onChangeWithError = vi.fn(() => {
      throw new Error('Test error');
    });
    
    render(
      <TestWrapper>
        <CompositionEditor {...mockProps} onChange={onChangeWithError} />
      </TestWrapper>
    );

    // Try to change layout which should trigger an error
    const layoutSelect = screen.getByDisplayValue('Rule of Thirds');
    await user.click(layoutSelect);
    
    const goldenRatioOption = screen.getByText('Golden Ratio');
    await user.click(goldenRatioOption);

    await waitFor(() => {
      expect(mockShowErrorNotification).toHaveBeenCalled();
    });
  });
});
