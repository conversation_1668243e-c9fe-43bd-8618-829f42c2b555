<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Competitor Analysis Report - ACE Social</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        :root {
            /* ACE Social Brand Colors */
            --ace-purple: #4E40C5;
            --ace-dark: #15110E;
            --ace-yellow: #EBAE1B;
            --ace-white: #FFFFFF;
            --ace-purple-light: #6C4BFA;
            --ace-purple-lighter: #8A72FF;
            --ace-purple-lightest: #B19FFF;
            
            /* Semantic Colors */
            --success-color: #4CAF50;
            --warning-color: #FF9800;
            --error-color: #F44336;
            --info-color: #2196F3;
            
            /* Text Colors */
            --text-primary: #1A1A2E;
            --text-secondary: #4A4A68;
            --text-muted: #AAAAAA;
            --background-light: #F0F4FF;
            --background-card: rgba(255, 255, 255, 0.85);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-secondary);
            background: linear-gradient(135deg, var(--ace-purple-light) 0%, var(--ace-purple-lighter) 50%, var(--ace-purple-lightest) 100%);
            min-height: 100vh;
            padding: 20px 10px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--background-card);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            overflow: hidden;
            box-shadow: 
                0 4px 24px rgba(0, 0, 0, 0.08),
                0 1px 3px rgba(108, 75, 250, 0.1),
                0 1px 2px rgba(138, 114, 255, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, var(--ace-purple) 0%, var(--ace-purple-light) 100%);
            color: var(--ace-white);
            text-align: center;
            padding: 40px 40px 30px;
            position: relative;
        }
        
        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
        }
        
        .logo {
            max-width: 180px;
            height: auto;
            margin-bottom: 20px;
            filter: brightness(0) invert(1);
        }
        
        .report-title {
            font-size: 28px;
            font-weight: 700;
            letter-spacing: -0.5px;
            margin-bottom: 8px;
        }
        
        .report-subtitle {
            font-size: 16px;
            font-weight: 400;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .greeting {
            font-size: 18px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 20px;
        }
        
        .intro-text {
            font-size: 16px;
            color: var(--text-secondary);
            margin-bottom: 30px;
            line-height: 1.7;
        }
        
        .competitor-card {
            background: var(--ace-white);
            border-radius: 16px;
            padding: 24px;
            margin: 20px 0;
            border: 1px solid rgba(108, 75, 250, 0.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transition: transform 0.2s ease;
        }
        
        .competitor-card:hover {
            transform: translateY(-2px);
        }
        
        .competitor-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(108, 75, 250, 0.1);
        }
        
        .competitor-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: var(--ace-purple);
            color: var(--ace-white);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 18px;
            margin-right: 16px;
        }
        
        .competitor-info h3 {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .competitor-info p {
            font-size: 14px;
            color: var(--text-muted);
        }
        
        .metrics-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 16px;
            margin: 16px 0;
        }
        
        .metric-item {
            text-align: center;
            padding: 12px;
            background: var(--background-light);
            border-radius: 8px;
        }
        
        .metric-value {
            font-size: 20px;
            font-weight: 700;
            color: var(--ace-purple);
            margin-bottom: 4px;
        }
        
        .metric-label {
            font-size: 12px;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .performance-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .performance-badge.high {
            background: rgba(76, 175, 80, 0.1);
            color: var(--success-color);
            border: 1px solid var(--success-color);
        }
        
        .performance-badge.medium {
            background: rgba(255, 152, 0, 0.1);
            color: var(--warning-color);
            border: 1px solid var(--warning-color);
        }
        
        .performance-badge.low {
            background: rgba(244, 67, 54, 0.1);
            color: var(--error-color);
            border: 1px solid var(--error-color);
        }
        
        .insights-box {
            background: var(--background-light);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid var(--ace-yellow);
        }
        
        .insights-box h4 {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }
        
        .insights-box h4::before {
            content: '💡';
            margin-right: 8px;
        }
        
        .insights-list {
            list-style: none;
            padding: 0;
        }
        
        .insights-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(108, 75, 250, 0.1);
            font-size: 14px;
            color: var(--text-secondary);
        }
        
        .insights-list li:last-child {
            border-bottom: none;
        }
        
        .insights-list li::before {
            content: '→';
            color: var(--ace-purple);
            font-weight: bold;
            margin-right: 8px;
        }
        
        .section-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 40px 0 20px;
            letter-spacing: -0.5px;
        }
        
        .divider {
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, rgba(108, 75, 250, 0.2) 50%, transparent 100%);
            margin: 30px 0;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .summary-card {
            background: var(--ace-white);
            border-radius: 12px;
            padding: 24px;
            text-align: center;
            border: 1px solid rgba(108, 75, 250, 0.1);
        }
        
        .summary-card .value {
            font-size: 28px;
            font-weight: 700;
            color: var(--ace-purple);
            margin-bottom: 8px;
        }
        
        .summary-card .label {
            font-size: 14px;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .button {
            display: inline-block;
            background: linear-gradient(135deg, var(--ace-purple) 0%, var(--ace-purple-light) 100%);
            color: var(--ace-white);
            padding: 14px 28px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(78, 64, 197, 0.3);
        }
        
        .button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(78, 64, 197, 0.4);
        }
        
        .footer {
            background: var(--background-light);
            text-align: center;
            padding: 30px 40px;
            border-top: 1px solid rgba(108, 75, 250, 0.1);
        }
        
        .footer p {
            color: var(--text-muted);
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .footer-links {
            font-size: 14px;
        }
        
        .footer-links a {
            color: var(--ace-purple);
            text-decoration: none;
            margin: 0 8px;
        }
        
        .footer-links a:hover {
            text-decoration: underline;
        }
        
        /* Responsive Design */
        @media only screen and (max-width: 640px) {
            body {
                padding: 10px 5px;
            }
            
            .container {
                border-radius: 16px;
                margin: 0;
            }
            
            .header {
                padding: 30px 20px 25px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .footer {
                padding: 25px 20px;
            }
            
            .logo {
                max-width: 140px;
            }
            
            .report-title {
                font-size: 24px;
            }
            
            .competitor-header {
                flex-direction: column;
                text-align: center;
            }
            
            .competitor-avatar {
                margin-right: 0;
                margin-bottom: 12px;
            }
            
            .metrics-row {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }
            
            .summary-stats {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
        
        @media only screen and (max-width: 400px) {
            .content {
                padding: 25px 15px;
            }
            
            .competitor-card {
                padding: 20px;
            }
            
            .metrics-row {
                grid-template-columns: 1fr;
            }
        }
        
        /* Print Styles */
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .container {
                box-shadow: none;
                border: 1px solid #ddd;
            }
            
            .button {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="{{frontend_url}}/assets/logo.svg" alt="ACE Social" class="logo">
            <h1 class="report-title">Competitor Analysis Report</h1>
            <p class="report-subtitle">Generated on {{generated_at|date:"F j, Y \a\\t g:i A T"}}</p>
        </div>
        
        <div class="content">
            <h2 class="greeting">Hello {{user_name}},</h2>
            
            <p class="intro-text">
                Here's your comprehensive competitor analysis report for the period from 
                <strong>{{report_period_start|date:"F j, Y"}}</strong> to 
                <strong>{{report_period_end|date:"F j, Y"}}</strong>. 
                This analysis provides insights into your competitive landscape and opportunities for growth.
            </p>
            
            <!-- Summary Statistics -->
            <div class="summary-stats">
                <div class="summary-card">
                    <div class="value">{{total_competitors|default:"0"}}</div>
                    <div class="label">Competitors Tracked</div>
                </div>
                <div class="summary-card">
                    <div class="value">{{avg_engagement_rate|floatformat:1|default:"0"}}%</div>
                    <div class="label">Avg Engagement Rate</div>
                </div>
                <div class="summary-card">
                    <div class="value">{{total_content_analyzed|default:"0"}}</div>
                    <div class="label">Content Pieces Analyzed</div>
                </div>
            </div>
            
            <div class="divider"></div>
            
            <!-- Top Competitors Section -->
            <h3 class="section-title">Top Performing Competitors</h3>
            
            {% for competitor in top_competitors %}
            <div class="competitor-card">
                <div class="competitor-header">
                    <div class="competitor-avatar">
                        {{competitor.name|first|upper}}
                    </div>
                    <div class="competitor-info">
                        <h3>{{competitor.name}}</h3>
                        <p>{{competitor.industry|default:"Industry not specified"}}</p>
                        <span class="performance-badge {% if competitor.performance_score >= 80 %}high{% elif competitor.performance_score >= 60 %}medium{% else %}low{% endif %}">
                            {% if competitor.performance_score >= 80 %}High Performer{% elif competitor.performance_score >= 60 %}Medium Performer{% else %}Low Performer{% endif %}
                        </span>
                    </div>
                </div>
                
                <div class="metrics-row">
                    <div class="metric-item">
                        <div class="metric-value">{{competitor.followers|floatformat:0|default:"N/A"}}</div>
                        <div class="metric-label">Followers</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">{{competitor.engagement_rate|floatformat:1|default:"N/A"}}%</div>
                        <div class="metric-label">Engagement</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">{{competitor.content_frequency|default:"N/A"}}</div>
                        <div class="metric-label">Posts/Week</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">{{competitor.avg_likes|floatformat:0|default:"N/A"}}</div>
                        <div class="metric-label">Avg Likes</div>
                    </div>
                </div>
                
                {% if competitor.top_content %}
                <div style="margin-top: 16px;">
                    <strong>Top Content:</strong> "{{competitor.top_content|truncatechars:80}}"
                    <br>
                    <small style="color: var(--text-muted);">{{competitor.top_content_engagement|floatformat:0}} engagements</small>
                </div>
                {% endif %}
            </div>
            {% empty %}
            <div class="competitor-card">
                <p>No competitor data available for this period.</p>
            </div>
            {% endfor %}
            
            <div class="divider"></div>
            
            <!-- Key Insights Section -->
            <h3 class="section-title">Key Insights & Recommendations</h3>
            
            <div class="insights-box">
                <h4>Strategic Insights</h4>
                <ul class="insights-list">
                    {% for insight in key_insights %}
                    <li>{{insight}}</li>
                    {% empty %}
                    <li>Competitors are posting {{avg_posting_frequency|default:"3-5"}} times per week on average</li>
                    <li>Video content shows {{video_engagement_boost|default:"40"}}% higher engagement rates</li>
                    <li>Peak posting times are between {{peak_posting_time|default:"9-11 AM and 2-4 PM"}}</li>
                    <li>Hashtag usage averages {{avg_hashtags|default:"5-8"}} per post for optimal reach</li>
                    {% endfor %}
                </ul>
            </div>
            
            {% if content_gaps %}
            <div class="insights-box">
                <h4>Content Gap Opportunities</h4>
                <ul class="insights-list">
                    {% for gap in content_gaps %}
                    <li>{{gap}}</li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
            
            <div class="divider"></div>
            
            <!-- Call to Action -->
            <p>Access your complete competitor analysis dashboard for real-time tracking, detailed comparisons, and strategic recommendations:</p>
            
            <a href="{{frontend_url}}/competitor-analysis" class="button">View Full Competitor Dashboard</a>
            
            {% if company_name %}
            <p style="margin-top: 30px; font-size: 14px; color: var(--text-muted);">
                This competitor analysis was generated for <strong>{{company_name}}</strong> using ACE Social's advanced competitive intelligence engine.
            </p>
            {% endif %}
        </div>
        
        <div class="footer">
            <p>© {% now 'Y' %} ACE Social. All rights reserved.</p>
            <div class="footer-links">
                <a href="{{frontend_url}}/privacy-policy">Privacy Policy</a> •
                <a href="{{frontend_url}}/terms">Terms of Service</a> •
                <a href="{{frontend_url}}/contact">Contact Support</a>
            </div>
        </div>
    </div>
</body>
</html>
