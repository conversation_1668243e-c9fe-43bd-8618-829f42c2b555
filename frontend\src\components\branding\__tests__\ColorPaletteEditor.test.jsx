// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import ColorPaletteEditor from '../ColorPaletteEditor';

// Mock the notification hook
const mockShowSuccessNotification = vi.fn();
const mockShowErrorNotification = vi.fn();

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showSuccessNotification: mockShowSuccessNotification,
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Mock the accessibility validation utility
vi.mock('../../../utils/accessibilityValidation', () => ({
  validateColorContrast: vi.fn((textColor, backgroundColor) => ({
    ratio: 4.5,
    level: 'AA',
    passesAA: true,
    passesAAA: false
  }))
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn().mockResolvedValue(undefined),
  },
});

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#1976d2',
      },
      secondary: {
        main: '#dc004e',
      },
      error: {
        main: '#d32f2f',
      },
      warning: {
        main: '#ed6c02',
      },
      success: {
        main: '#2e7d32',
      },
      grey: {
        300: '#e0e0e0',
        400: '#bdbdbd',
        500: '#9e9e9e',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('ColorPaletteEditor', () => {
  const mockColorPalette = {
    primary: '#4E40C5',
    secondary: '#00E4BC',
    accent: '#FF5733',
    background: '#F8F9FA',
    text: '#333333',
    relationship: 'complementary',
    additional_colors: ['#FF6B6B', '#4ECDC4']
  };

  const mockProps = {
    colorPalette: mockColorPalette,
    onChange: vi.fn(),
    errors: {},
    onError: vi.fn(),
    onExport: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders color palette editor correctly', () => {
    render(
      <TestWrapper>
        <ColorPaletteEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Color Palette Editor')).toBeInTheDocument();
    expect(screen.getByText('Main Colors')).toBeInTheDocument();
    expect(screen.getByText('Background & Text Colors')).toBeInTheDocument();
    expect(screen.getByText('Additional Colors & Preview')).toBeInTheDocument();
  });

  test('displays accessibility score when enabled', () => {
    render(
      <TestWrapper>
        <ColorPaletteEditor {...mockProps} showAccessibility={true} />
      </TestWrapper>
    );

    expect(screen.getByText('Accessibility Score')).toBeInTheDocument();
    expect(screen.getByText('Contrast ratio: 4.5:1 (AA)')).toBeInTheDocument();
  });

  test('shows placeholder when no color palette provided', () => {
    render(
      <TestWrapper>
        <ColorPaletteEditor colorPalette={null} onChange={vi.fn()} />
      </TestWrapper>
    );

    expect(screen.getByText('No color palette defined')).toBeInTheDocument();
    expect(screen.getByText('Configure color palette settings to establish your brand colors')).toBeInTheDocument();
  });

  test('handles primary color change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorPaletteEditor {...mockProps} />
      </TestWrapper>
    );

    // Expand main colors section
    const mainColorsAccordion = screen.getByText('Primary & Secondary Colors');
    await user.click(mainColorsAccordion);

    // Change primary color
    const primaryColorInput = screen.getByDisplayValue('#4E40C5');
    await user.clear(primaryColorInput);
    await user.type(primaryColorInput, '#FF0000');

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockColorPalette,
        primary: '#FF0000'
      });
    });
  });

  test('handles secondary color change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorPaletteEditor {...mockProps} />
      </TestWrapper>
    );

    // Expand main colors section
    const mainColorsAccordion = screen.getByText('Primary & Secondary Colors');
    await user.click(mainColorsAccordion);

    // Change secondary color
    const secondaryColorInput = screen.getByDisplayValue('#00E4BC');
    await user.clear(secondaryColorInput);
    await user.type(secondaryColorInput, '#00FF00');

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockColorPalette,
        secondary: '#00FF00'
      });
    });
  });

  test('handles color relationship change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorPaletteEditor {...mockProps} />
      </TestWrapper>
    );

    // Expand main colors section
    const mainColorsAccordion = screen.getByText('Primary & Secondary Colors');
    await user.click(mainColorsAccordion);

    // Change color relationship
    const relationshipSelect = screen.getByRole('combobox', { name: /color relationship/i });
    await user.click(relationshipSelect);
    
    const analogousOption = screen.getByText('Analogous');
    await user.click(analogousOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith(
        expect.objectContaining({
          relationship: 'analogous'
        })
      );
    });
  });

  test('handles background color change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorPaletteEditor {...mockProps} />
      </TestWrapper>
    );

    // Expand background colors section
    const backgroundColorsAccordion = screen.getByText('Background & Text Settings');
    await user.click(backgroundColorsAccordion);

    // Change background color
    const backgroundColorInput = screen.getByDisplayValue('#F8F9FA');
    await user.clear(backgroundColorInput);
    await user.type(backgroundColorInput, '#FFFFFF');

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockColorPalette,
        background: '#FFFFFF'
      });
    });
  });

  test('handles copy color functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorPaletteEditor {...mockProps} />
      </TestWrapper>
    );

    // Expand main colors section
    const mainColorsAccordion = screen.getByText('Primary & Secondary Colors');
    await user.click(mainColorsAccordion);

    // Click copy button for primary color
    const copyButtons = screen.getAllByLabelText(/copy color/i);
    await user.click(copyButtons[0]);

    await waitFor(() => {
      expect(navigator.clipboard.writeText).toHaveBeenCalledWith('#4E40C5');
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Copied #4E40C5 to clipboard');
    });
  });

  test('handles adding additional colors', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorPaletteEditor {...mockProps} />
      </TestWrapper>
    );

    // Switch to additional colors tab
    const additionalColorsTab = screen.getByText('Additional Colors');
    await user.click(additionalColorsTab);

    // Add a new color
    const newColorInput = screen.getByPlaceholderText('Enter color value');
    await user.type(newColorInput, '#PURPLE');
    
    const addButton = screen.getByRole('button', { name: /add color/i });
    await user.click(addButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockColorPalette,
        additional_colors: [...mockColorPalette.additional_colors, '#PURPLE']
      });
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Added color #PURPLE');
    });
  });

  test('handles removing additional colors', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorPaletteEditor {...mockProps} />
      </TestWrapper>
    );

    // Switch to additional colors tab
    const additionalColorsTab = screen.getByText('Additional Colors');
    await user.click(additionalColorsTab);

    // Remove first additional color
    const deleteButtons = screen.getAllByTestId('CancelIcon');
    if (deleteButtons.length > 0) {
      await user.click(deleteButtons[0]);

      await waitFor(() => {
        expect(mockProps.onChange).toHaveBeenCalledWith({
          ...mockColorPalette,
          additional_colors: ['#4ECDC4']
        });
      });
    }
  });

  test('handles export functionality', async () => {
    // Mock URL.createObjectURL and related APIs
    global.URL.createObjectURL = vi.fn(() => 'mock-url');
    global.URL.revokeObjectURL = vi.fn();
    
    const mockLink = {
      href: '',
      download: '',
      click: vi.fn(),
    };
    
    vi.spyOn(document, 'createElement').mockReturnValue(mockLink);
    vi.spyOn(document.body, 'appendChild').mockImplementation(() => {});
    vi.spyOn(document.body, 'removeChild').mockImplementation(() => {});

    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorPaletteEditor {...mockProps} />
      </TestWrapper>
    );

    const exportButton = screen.getByLabelText('Export color palette');
    await user.click(exportButton);

    await waitFor(() => {
      expect(mockProps.onExport).toHaveBeenCalled();
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Exported palette as JSON');
    });
  });

  test('handles auto-generate secondary color', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorPaletteEditor {...mockProps} />
      </TestWrapper>
    );

    // Expand main colors section
    const mainColorsAccordion = screen.getByText('Primary & Secondary Colors');
    await user.click(mainColorsAccordion);

    // Click auto-generate button
    const autoGenerateButton = screen.getByLabelText('Auto-generate from primary');
    await user.click(autoGenerateButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalled();
    });
  });

  test('handles disabled state correctly', () => {
    render(
      <TestWrapper>
        <ColorPaletteEditor {...mockProps} disabled />
      </TestWrapper>
    );

    const exportButton = screen.getByLabelText('Export color palette');
    expect(exportButton).toBeDisabled();
  });

  test('handles read-only state correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorPaletteEditor {...mockProps} readOnly />
      </TestWrapper>
    );

    // Switch to additional colors tab
    const additionalColorsTab = screen.getByText('Additional Colors');
    await user.click(additionalColorsTab);

    // Should not show add controls in read-only mode
    expect(screen.queryByPlaceholderText('Enter color value')).not.toBeInTheDocument();
  });

  test('displays color history correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorPaletteEditor {...mockProps} />
      </TestWrapper>
    );

    // Switch to color history tab
    const historyTab = screen.getByText('Color History');
    await user.click(historyTab);

    expect(screen.getByText('Recent Colors')).toBeInTheDocument();
  });

  test('displays preview correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorPaletteEditor {...mockProps} />
      </TestWrapper>
    );

    // Switch to preview tab
    const previewTab = screen.getByText('Preview');
    await user.click(previewTab);

    expect(screen.getByText('Color Palette Preview')).toBeInTheDocument();
    expect(screen.getByText('Sample Heading')).toBeInTheDocument();
    expect(screen.getByText('Primary Button')).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <ColorPaletteEditor {...mockProps} />
      </TestWrapper>
    );

    // Check for proper ARIA labels
    expect(screen.getByLabelText('Export color palette')).toBeInTheDocument();

    // Check for proper headings
    const headings = screen.getAllByRole('heading');
    expect(headings.length).toBeGreaterThan(0);
  });

  test('validates color format correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorPaletteEditor {...mockProps} />
      </TestWrapper>
    );

    // Expand main colors section
    const mainColorsAccordion = screen.getByText('Primary & Secondary Colors');
    await user.click(mainColorsAccordion);

    // Try to enter invalid color
    const primaryColorInput = screen.getByDisplayValue('#4E40C5');
    await user.clear(primaryColorInput);
    await user.type(primaryColorInput, 'invalid-color');

    await waitFor(() => {
      expect(mockShowErrorNotification).toHaveBeenCalledWith(
        expect.stringContaining('Color validation failed')
      );
    });
  });
});
