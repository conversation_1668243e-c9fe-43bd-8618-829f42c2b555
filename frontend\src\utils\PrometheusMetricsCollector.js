/**
 * Prometheus Metrics Collector for Social Media Messaging
 * 
 * Provides comprehensive metrics collection for social media messaging operations
 * following existing ACE Social monitoring patterns and integrating with backend
 * Prometheus infrastructure.
 * 
 * @version 1.0.0
 * <AUTHOR> Social Platform Team
 * @since 2024-01-01
 * 
 * @example
 * ```javascript
 * import { PrometheusMetricsCollector } from '../utils/PrometheusMetricsCollector';
 * 
 * const metrics = new PrometheusMetricsCollector();
 * 
 * // Record message send attempt
 * metrics.recordMessageSend('linkedin', 'creator', 'success', 150);
 * 
 * // Record rate limit violation
 * metrics.recordRateLimitViolation('facebook', 'creator');
 * 
 * // Record circuit breaker state change
 * metrics.recordCircuitBreakerState('twitter', 'open');
 * ```
 @since 2024-1-1 to 2025-25-7
*/

import axios from 'axios';
import { shouldUseFallbackMode } from './environmentDetection';

/**
 * Prometheus Metrics Collector for Social Media Messaging Operations
 * 
 * Integrates with existing ACE Social monitoring infrastructure to provide
 * comprehensive metrics collection for social media messaging operations.
 * 
 * Features:
 * - Platform-specific message success/failure rates
 * - Rate limit violation tracking
 * - Circuit breaker state monitoring
 * - Queue processing time metrics
 * - Subscription tier-based analytics
 * - Real-time performance monitoring
 */
export class PrometheusMetricsCollector {
  /**
   * Initialize the metrics collector
   * 
   * @param {Object} options - Configuration options
   * @param {boolean} options.enabled - Whether metrics collection is enabled
   * @param {string} options.endpoint - Metrics endpoint URL
   * @param {number} options.batchSize - Number of metrics to batch before sending
   * @param {number} options.flushInterval - Interval to flush metrics (ms)
   */
  constructor(options = {}) {
    try {
      this.enabled = options.enabled ?? true;
      this.endpoint = options.endpoint || '/api/metrics/social-media-messaging';
      this.batchSize = options.batchSize || 50;
      this.flushInterval = options.flushInterval || 30000; // 30 seconds

      // Metrics buffer for batching
      this.metricsBuffer = [];
      this.lastFlush = Date.now();

      // Correlation ID for tracking
      this.correlationId = this._generateCorrelationId();

      // Performance tracking
      this.startTime = Date.now();
      this.operationCounts = new Map();

      // Initialize flush timer with error handling
      if (this.enabled) {
        try {
          this._initializeFlushTimer();
        } catch (error) {
          console.warn('[PrometheusMetricsCollector] Error initializing flush timer:', error);
          this.enabled = false; // Disable if timer initialization fails
        }
      }

      // Bind methods
      this.recordMessageSend = this.recordMessageSend.bind(this);
      this.recordRateLimitViolation = this.recordRateLimitViolation.bind(this);
      this.recordCircuitBreakerState = this.recordCircuitBreakerState.bind(this);
      this.recordQueueProcessing = this.recordQueueProcessing.bind(this);
      this.recordPlatformStatus = this.recordPlatformStatus.bind(this);
    } catch (error) {
      console.error('[PrometheusMetricsCollector] Error in constructor:', error);
      // Set safe defaults if constructor fails
      this.enabled = false;
      this.metricsBuffer = [];
      this.operationCounts = new Map();
    }
  }

  /**
   * Record message send operation metrics
   * 
   * @param {string} platform - Social media platform (facebook, linkedin, etc.)
   * @param {string} subscriptionTier - User subscription tier (creator, accelerator, dominator)
   * @param {string} status - Operation status (success, failure, queued)
   * @param {number} duration - Operation duration in milliseconds
   * @param {Object} metadata - Additional metadata
   */
  recordMessageSend(platform, subscriptionTier, status, duration, metadata = {}) {
    if (!this.enabled) return;
    
    try {
      const metric = {
        type: 'social_media_message_send',
        timestamp: new Date().toISOString(),
        labels: {
          platform: platform.toLowerCase(),
          subscription_tier: subscriptionTier.toLowerCase(),
          status: status.toLowerCase()
        },
        values: {
          duration_ms: duration,
          count: 1
        },
        metadata: {
          correlation_id: this.correlationId,
          ...metadata
        }
      };
      
      this._addMetric(metric);
      this._incrementOperationCount('message_send');
      
      // Log for development
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Metrics] Message send: ${platform} (${status}) - ${duration}ms`);
      }
    } catch (error) {
      console.error('Error recording message send metric:', error);
    }
  }

  /**
   * Record rate limit violation
   * 
   * @param {string} platform - Social media platform
   * @param {string} subscriptionTier - User subscription tier
   * @param {Object} rateLimitInfo - Rate limit information
   */
  recordRateLimitViolation(platform, subscriptionTier, rateLimitInfo = {}) {
    if (!this.enabled) return;
    
    try {
      const metric = {
        type: 'social_media_rate_limit_violation',
        timestamp: new Date().toISOString(),
        labels: {
          platform: platform.toLowerCase(),
          subscription_tier: subscriptionTier.toLowerCase()
        },
        values: {
          count: 1,
          remaining: rateLimitInfo.remaining || 0,
          reset_time_seconds: rateLimitInfo.resetTime ? 
            Math.floor((rateLimitInfo.resetTime - Date.now()) / 1000) : 0
        },
        metadata: {
          correlation_id: this.correlationId,
          rate_limit_window: rateLimitInfo.window,
          rate_limit_max: rateLimitInfo.limit
        }
      };
      
      this._addMetric(metric);
      this._incrementOperationCount('rate_limit_violation');
    } catch (error) {
      console.error('Error recording rate limit violation metric:', error);
    }
  }

  /**
   * Record circuit breaker state change
   * 
   * @param {string} platform - Social media platform
   * @param {string} state - Circuit breaker state (open, closed, half-open)
   * @param {Object} stateInfo - Additional state information
   */
  recordCircuitBreakerState(platform, state, stateInfo = {}) {
    if (!this.enabled) return;
    
    try {
      const metric = {
        type: 'social_media_circuit_breaker_state',
        timestamp: new Date().toISOString(),
        labels: {
          platform: platform.toLowerCase(),
          state: state.toLowerCase()
        },
        values: {
          state_value: state === 'open' ? 1 : 0,
          error_count: stateInfo.errorCount || 0,
          reset_time_seconds: stateInfo.resetTime ? 
            Math.floor((stateInfo.resetTime - Date.now()) / 1000) : 0
        },
        metadata: {
          correlation_id: this.correlationId,
          failure_threshold: stateInfo.failureThreshold,
          last_error: stateInfo.lastError
        }
      };
      
      this._addMetric(metric);
      this._incrementOperationCount('circuit_breaker_state');
    } catch (error) {
      console.error('Error recording circuit breaker state metric:', error);
    }
  }

  /**
   * Record queue processing metrics
   * 
   * @param {number} queueSize - Current queue size
   * @param {number} processingTime - Time to process queue in milliseconds
   * @param {number} successCount - Number of successful operations
   * @param {number} failureCount - Number of failed operations
   */
  recordQueueProcessing(queueSize, processingTime, successCount, failureCount) {
    if (!this.enabled) return;
    
    try {
      const metric = {
        type: 'social_media_queue_processing',
        timestamp: new Date().toISOString(),
        labels: {
          operation: 'queue_processing'
        },
        values: {
          queue_size: queueSize,
          processing_time_ms: processingTime,
          success_count: successCount,
          failure_count: failureCount,
          total_processed: successCount + failureCount
        },
        metadata: {
          correlation_id: this.correlationId,
          success_rate: successCount / (successCount + failureCount) || 0
        }
      };
      
      this._addMetric(metric);
      this._incrementOperationCount('queue_processing');
    } catch (error) {
      console.error('Error recording queue processing metric:', error);
    }
  }

  /**
   * Record platform status update
   * 
   * @param {string} platform - Social media platform
   * @param {boolean} available - Platform availability status
   * @param {number} responseTime - Platform response time in milliseconds
   * @param {Object} statusInfo - Additional status information
   */
  recordPlatformStatus(platform, available, responseTime, statusInfo = {}) {
    if (!this.enabled) return;
    
    try {
      const metric = {
        type: 'social_media_platform_status',
        timestamp: new Date().toISOString(),
        labels: {
          platform: platform.toLowerCase(),
          status: available ? 'available' : 'unavailable'
        },
        values: {
          availability: available ? 1 : 0,
          response_time_ms: responseTime,
          uptime_percentage: statusInfo.uptimePercentage || 0
        },
        metadata: {
          correlation_id: this.correlationId,
          last_check: statusInfo.lastCheck,
          error_message: statusInfo.errorMessage
        }
      };
      
      this._addMetric(metric);
      this._incrementOperationCount('platform_status');
    } catch (error) {
      console.error('Error recording platform status metric:', error);
    }
  }

  /**
   * Get current metrics summary
   * 
   * @returns {Object} Metrics summary
   */
  getMetricsSummary() {
    const uptime = Date.now() - this.startTime;
    const operationCounts = Object.fromEntries(this.operationCounts);
    
    return {
      enabled: this.enabled,
      uptime_ms: uptime,
      correlation_id: this.correlationId,
      buffer_size: this.metricsBuffer.length,
      operation_counts: operationCounts,
      last_flush: this.lastFlush,
      total_operations: Array.from(this.operationCounts.values()).reduce((a, b) => a + b, 0)
    };
  }

  /**
   * Flush metrics buffer to backend
   * 
   * @returns {Promise<boolean>} Success status
   */
  async flushMetrics() {
    if (!this.enabled || this.metricsBuffer.length === 0) {
      return true;
    }

    try {
      const metricsToSend = [...this.metricsBuffer];
      this.metricsBuffer = [];

      // Safely check fallback mode
      let useFallback = false;
      try {
        useFallback = shouldUseFallbackMode();
      } catch (error) {
        console.warn('[PrometheusMetricsCollector] Error checking fallback mode, using default:', error);
        useFallback = true; // Default to fallback mode if check fails
      }

      const headers = {
        'Content-Type': 'application/json'
      };

      if (useFallback) {
        headers['X-Fallback-Mode'] = 'true';
      }

      const response = await axios.post(this.endpoint, {
        metrics: metricsToSend,
        correlation_id: this.correlationId,
        timestamp: new Date().toISOString(),
        source: 'enhanced_social_media_messaging'
      }, { headers });
      
      this.lastFlush = Date.now();
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Metrics] Flushed ${metricsToSend.length} metrics to backend`);
      }
      
      return response.status === 200;
    } catch (error) {
      console.error('Error flushing metrics to backend:', error);
      return false;
    }
  }

  /**
   * Disable metrics collection
   */
  disable() {
    this.enabled = false;
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
  }

  /**
   * Enable metrics collection
   */
  enable() {
    this.enabled = true;
    this._initializeFlushTimer();
  }

  // Private methods

  /**
   * Add metric to buffer
   * 
   * @private
   * @param {Object} metric - Metric to add
   */
  _addMetric(metric) {
    this.metricsBuffer.push(metric);
    
    // Auto-flush if buffer is full
    if (this.metricsBuffer.length >= this.batchSize) {
      this.flushMetrics().catch(error => {
        console.error('Error auto-flushing metrics:', error);
      });
    }
  }

  /**
   * Increment operation count
   * 
   * @private
   * @param {string} operation - Operation name
   */
  _incrementOperationCount(operation) {
    const current = this.operationCounts.get(operation) || 0;
    this.operationCounts.set(operation, current + 1);
  }

  /**
   * Initialize flush timer
   * 
   * @private
   */
  _initializeFlushTimer() {
    try {
      if (this.flushTimer) {
        clearInterval(this.flushTimer);
      }

      // Check if we're in a browser environment
      if (typeof setInterval === 'undefined') {
        console.warn('[PrometheusMetricsCollector] setInterval not available, disabling flush timer');
        return;
      }

      this.flushTimer = setInterval(() => {
        this.flushMetrics().catch(error => {
          console.error('[PrometheusMetricsCollector] Error in scheduled metrics flush:', error);
        });
      }, this.flushInterval);
    } catch (error) {
      console.error('[PrometheusMetricsCollector] Error initializing flush timer:', error);
      throw error; // Re-throw to be caught by constructor
    }
  }

  /**
   * Generate correlation ID for tracking
   * 
   * @private
   * @returns {string} Correlation ID
   */
  _generateCorrelationId() {
    return `sms-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }
}

// Export singleton instance with safe initialization
let _socialMediaMetrics = null;

export const getSocialMediaMetrics = () => {
  if (!_socialMediaMetrics) {
    try {
      _socialMediaMetrics = new PrometheusMetricsCollector({
        enabled: process.env.NODE_ENV === 'production' || process.env.REACT_APP_METRICS_ENABLED === 'true'
      });
    } catch (error) {
      console.warn('[PrometheusMetricsCollector] Error creating singleton instance:', error);
      // Return a disabled instance as fallback
      _socialMediaMetrics = new PrometheusMetricsCollector({ enabled: false });
    }
  }
  return _socialMediaMetrics;
};

// Backward compatibility
export const socialMediaMetrics = getSocialMediaMetrics();

export default PrometheusMetricsCollector;
