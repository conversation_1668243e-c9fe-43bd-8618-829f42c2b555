// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Typography,
  Box,
  Chip,
  Alert,
  Tabs,
  Tab,
  Paper,
  Divider,
  Switch,
  FormControlLabel,
  Autocomplete,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Save as SaveIcon,
  Preview as PreviewIcon,
  Code as CodeIcon,
  Visibility as VisibilityIcon,
  Settings as SettingsIcon,
  Info as InfoIcon,
  Upload as UploadIcon,
  Close as CloseIcon,
  Add as AddIcon
} from '@mui/icons-material';

const KnowledgeBaseArticleEditor = ({ 
  open, 
  mode = 'create', 
  article = null, 
  templates = [],
  onClose, 
  onSubmit 
}) => {
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    summary: '',
    content_format: 'rich_text',
    category: 'general',
    article_type: 'help_article',
    tags: [],
    difficulty_level: 'beginner',
    status: 'draft',
    is_featured: false,
    is_internal: false,
    scheduled_publish_at: null,
    template_id: null,
    seo: {
      meta_title: '',
      meta_description: '',
      keywords: [],
      slug: ''
    },
    search_keywords: [],
    related_articles: [],
    featured_image: '',
    email_template_variables: {},
    can_embed_in_emails: true,
    email_snippet: '',
    change_summary: ''
  });

  const [currentTab, setCurrentTab] = useState(0);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [newTag, setNewTag] = useState('');
  const [newKeyword, setNewKeyword] = useState('');

  useEffect(() => {
    if (mode === 'edit' && article) {
      setFormData({
        title: article.title || '',
        content: article.content || '',
        summary: article.summary || '',
        content_format: article.content_format || 'rich_text',
        category: article.category || 'general',
        article_type: article.article_type || 'help_article',
        tags: article.tags || [],
        difficulty_level: article.difficulty_level || 'beginner',
        status: article.status || 'draft',
        is_featured: article.is_featured || false,
        is_internal: article.is_internal || false,
        scheduled_publish_at: article.scheduled_publish_at || null,
        template_id: article.template_id || null,
        seo: article.seo || {
          meta_title: '',
          meta_description: '',
          keywords: [],
          slug: ''
        },
        search_keywords: article.search_keywords || [],
        related_articles: article.related_articles || [],
        featured_image: article.featured_image || '',
        email_template_variables: article.email_template_variables || {},
        can_embed_in_emails: article.can_embed_in_emails !== false,
        email_snippet: article.email_snippet || '',
        change_summary: ''
      });
    } else if (mode === 'duplicate' && article) {
      setFormData({
        title: `Copy of ${article.title}`,
        content: article.content || '',
        summary: article.summary || '',
        content_format: article.content_format || 'rich_text',
        category: article.category || 'general',
        article_type: article.article_type || 'help_article',
        tags: article.tags || [],
        difficulty_level: article.difficulty_level || 'beginner',
        status: 'draft',
        is_featured: false,
        is_internal: article.is_internal || false,
        scheduled_publish_at: null,
        template_id: article.template_id || null,
        seo: {
          meta_title: '',
          meta_description: '',
          keywords: [],
          slug: ''
        },
        search_keywords: article.search_keywords || [],
        related_articles: [],
        featured_image: article.featured_image || '',
        email_template_variables: article.email_template_variables || {},
        can_embed_in_emails: article.can_embed_in_emails !== false,
        email_snippet: article.email_snippet || '',
        change_summary: 'Duplicated from existing article'
      });
    } else {
      // Reset for create mode
      setFormData({
        title: '',
        content: '',
        summary: '',
        content_format: 'rich_text',
        category: 'general',
        article_type: 'help_article',
        tags: [],
        difficulty_level: 'beginner',
        status: 'draft',
        is_featured: false,
        is_internal: false,
        scheduled_publish_at: null,
        template_id: null,
        seo: {
          meta_title: '',
          meta_description: '',
          keywords: [],
          slug: ''
        },
        search_keywords: [],
        related_articles: [],
        featured_image: '',
        email_template_variables: {},
        can_embed_in_emails: true,
        email_snippet: '',
        change_summary: ''
      });
    }
  }, [mode, article, open]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const handleNestedChange = (section, field, value) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleAddKeyword = () => {
    if (newKeyword.trim() && !formData.search_keywords.includes(newKeyword.trim())) {
      setFormData(prev => ({
        ...prev,
        search_keywords: [...prev.search_keywords, newKeyword.trim()]
      }));
      setNewKeyword('');
    }
  };

  const handleRemoveKeyword = (keywordToRemove) => {
    setFormData(prev => ({
      ...prev,
      search_keywords: prev.search_keywords.filter(keyword => keyword !== keywordToRemove)
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Article title is required';
    }

    if (!formData.content.trim()) {
      newErrors.content = 'Article content is required';
    }

    if (mode === 'edit' && !formData.change_summary.trim()) {
      newErrors.change_summary = 'Change summary is required for updates';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting article:', error);
    } finally {
      setLoading(false);
    }
  };

  const getDialogTitle = () => {
    switch (mode) {
      case 'create':
        return 'Create Knowledge Base Article';
      case 'edit':
        return 'Edit Knowledge Base Article';
      case 'duplicate':
        return 'Duplicate Knowledge Base Article';
      default:
        return 'Knowledge Base Article';
    }
  };

  const tabLabels = [
    { label: 'Basic Info', icon: <InfoIcon /> },
    { label: 'Content', icon: <CodeIcon /> },
    { label: 'Preview', icon: <VisibilityIcon /> },
    { label: 'SEO & Meta', icon: <SettingsIcon /> },
    { label: 'Email Integration', icon: null }
  ];

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="lg" 
      fullWidth
      PaperProps={{
        sx: { minHeight: '80vh' }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          {getDialogTitle()}
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      
      <DialogContent dividers>
        {/* Tabs */}
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}
        >
          {tabLabels.map((tab, index) => (
            <Tab
              key={index}
              icon={tab.icon}
              label={tab.label}
              iconPosition="start"
            />
          ))}
        </Tabs>

        {/* Tab Content */}
        {currentTab === 0 && (
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <TextField
                fullWidth
                label="Article Title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                error={!!errors.title}
                helperText={errors.title}
                placeholder="How to connect your Instagram account"
              />
            </Grid>
            
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Template</InputLabel>
                <Select
                  value={formData.template_id || ''}
                  label="Template"
                  onChange={(e) => handleInputChange('template_id', e.target.value || null)}
                >
                  <MenuItem value="">No Template</MenuItem>
                  {templates.map((template) => (
                    <MenuItem key={template.id} value={template.id}>
                      {template.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Summary"
                multiline
                rows={3}
                value={formData.summary}
                onChange={(e) => handleInputChange('summary', e.target.value)}
                placeholder="Brief description of this article"
                helperText="A short summary that appears in search results and article lists"
              />
            </Grid>
            
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  value={formData.category}
                  label="Category"
                  onChange={(e) => handleInputChange('category', e.target.value)}
                >
                  <MenuItem value="faq">FAQ</MenuItem>
                  <MenuItem value="tutorials">Tutorials</MenuItem>
                  <MenuItem value="api_documentation">API Documentation</MenuItem>
                  <MenuItem value="user_guides">User Guides</MenuItem>
                  <MenuItem value="troubleshooting">Troubleshooting</MenuItem>
                  <MenuItem value="announcements">Announcements</MenuItem>
                  <MenuItem value="technical">Technical</MenuItem>
                  <MenuItem value="billing">Billing</MenuItem>
                  <MenuItem value="account">Account</MenuItem>
                  <MenuItem value="integrations">Integrations</MenuItem>
                  <MenuItem value="features">Features</MenuItem>
                  <MenuItem value="general">General</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Article Type</InputLabel>
                <Select
                  value={formData.article_type}
                  label="Article Type"
                  onChange={(e) => handleInputChange('article_type', e.target.value)}
                >
                  <MenuItem value="help_article">Help Article</MenuItem>
                  <MenuItem value="blog_post">Blog Post</MenuItem>
                  <MenuItem value="announcement">Announcement</MenuItem>
                  <MenuItem value="technical_doc">Technical Documentation</MenuItem>
                  <MenuItem value="faq">FAQ</MenuItem>
                  <MenuItem value="tutorial">Tutorial</MenuItem>
                  <MenuItem value="troubleshooting">Troubleshooting</MenuItem>
                  <MenuItem value="user_guide">User Guide</MenuItem>
                  <MenuItem value="api_documentation">API Documentation</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Difficulty Level</InputLabel>
                <Select
                  value={formData.difficulty_level}
                  label="Difficulty Level"
                  onChange={(e) => handleInputChange('difficulty_level', e.target.value)}
                >
                  <MenuItem value="beginner">Beginner</MenuItem>
                  <MenuItem value="intermediate">Intermediate</MenuItem>
                  <MenuItem value="advanced">Advanced</MenuItem>
                  <MenuItem value="expert">Expert</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Tags
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                {formData.tags.map((tag) => (
                  <Chip
                    key={tag}
                    label={tag}
                    onDelete={() => handleRemoveTag(tag)}
                    size="small"
                  />
                ))}
              </Box>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <TextField
                  size="small"
                  placeholder="Add tag"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddTag();
                    }
                  }}
                />
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleAddTag}
                  startIcon={<AddIcon />}
                >
                  Add
                </Button>
              </Box>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={formData.status}
                  label="Status"
                  onChange={(e) => handleInputChange('status', e.target.value)}
                >
                  <MenuItem value="draft">Draft</MenuItem>
                  <MenuItem value="published">Published</MenuItem>
                  <MenuItem value="archived">Archived</MenuItem>
                  <MenuItem value="scheduled">Scheduled</MenuItem>
                  <MenuItem value="under_review">Under Review</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.is_featured}
                      onChange={(e) => handleInputChange('is_featured', e.target.checked)}
                    />
                  }
                  label="Featured Article"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.is_internal}
                      onChange={(e) => handleInputChange('is_internal', e.target.checked)}
                    />
                  }
                  label="Internal Only"
                />
              </Box>
            </Grid>
            
            {mode === 'edit' && (
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Change Summary"
                  value={formData.change_summary}
                  onChange={(e) => handleInputChange('change_summary', e.target.value)}
                  error={!!errors.change_summary}
                  helperText={errors.change_summary || "Describe what changes you made to this article"}
                  placeholder="Updated installation instructions"
                />
              </Grid>
            )}
          </Grid>
        )}

        {currentTab === 1 && (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Article Content
                </Typography>
                <FormControl size="small">
                  <InputLabel>Format</InputLabel>
                  <Select
                    value={formData.content_format}
                    label="Format"
                    onChange={(e) => handleInputChange('content_format', e.target.value)}
                  >
                    <MenuItem value="rich_text">Rich Text</MenuItem>
                    <MenuItem value="markdown">Markdown</MenuItem>
                    <MenuItem value="html">HTML</MenuItem>
                  </Select>
                </FormControl>
              </Box>
              <TextField
                fullWidth
                multiline
                rows={20}
                value={formData.content}
                onChange={(e) => handleInputChange('content', e.target.value)}
                error={!!errors.content}
                helperText={errors.content || `Write your article content in ${formData.content_format} format`}
                placeholder="Start writing your article content here..."
                sx={{ fontFamily: formData.content_format === 'markdown' ? 'monospace' : 'inherit' }}
              />
            </Grid>
          </Grid>
        )}

        {currentTab === 2 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Article Preview
            </Typography>
            <Paper 
              variant="outlined" 
              sx={{ 
                p: 3, 
                minHeight: 400, 
                bgcolor: 'grey.50',
                border: '1px solid',
                borderColor: 'grey.300'
              }}
            >
              <Typography variant="h4" gutterBottom>
                {formData.title || 'Article Title'}
              </Typography>
              
              {formData.summary && (
                <Typography variant="subtitle1" color="text.secondary" sx={{ mb: 2, fontStyle: 'italic' }}>
                  {formData.summary}
                </Typography>
              )}
              
              <Divider sx={{ mb: 2 }} />
              
              {formData.content ? (
                <Box
                  sx={{ 
                    '& *': { 
                      maxWidth: '100% !important',
                      wordBreak: 'break-word'
                    }
                  }}
                >
                  {formData.content_format === 'html' ? (
                    <div dangerouslySetInnerHTML={{ __html: formData.content }} />
                  ) : (
                    <Typography component="div" sx={{ whiteSpace: 'pre-wrap' }}>
                      {formData.content}
                    </Typography>
                  )}
                </Box>
              ) : (
                <Typography color="text.secondary" style={{ fontStyle: 'italic' }}>
                  No content to preview. Add content in the Content tab.
                </Typography>
              )}
            </Paper>
          </Box>
        )}

        {currentTab === 3 && (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                SEO & Meta Information
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Meta Title"
                value={formData.seo.meta_title}
                onChange={(e) => handleNestedChange('seo', 'meta_title', e.target.value)}
                placeholder={formData.title}
                helperText="Recommended: 50-60 characters"
                inputProps={{ maxLength: 60 }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="URL Slug"
                value={formData.seo.slug}
                onChange={(e) => handleNestedChange('seo', 'slug', e.target.value)}
                placeholder="auto-generated-from-title"
                helperText="URL-friendly version of the title"
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Meta Description"
                multiline
                rows={3}
                value={formData.seo.meta_description}
                onChange={(e) => handleNestedChange('seo', 'meta_description', e.target.value)}
                placeholder="Brief description for search engines"
                helperText="Recommended: 150-160 characters"
                inputProps={{ maxLength: 160 }}
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                SEO Keywords
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                {formData.search_keywords.map((keyword) => (
                  <Chip
                    key={keyword}
                    label={keyword}
                    onDelete={() => handleRemoveKeyword(keyword)}
                    size="small"
                  />
                ))}
              </Box>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <TextField
                  size="small"
                  placeholder="Add SEO keyword"
                  value={newKeyword}
                  onChange={(e) => setNewKeyword(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddKeyword();
                    }
                  }}
                />
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleAddKeyword}
                  startIcon={<AddIcon />}
                >
                  Add
                </Button>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Featured Image URL"
                value={formData.featured_image}
                onChange={(e) => handleInputChange('featured_image', e.target.value)}
                placeholder="https://example.com/image.jpg"
                helperText="URL to the featured image for this article"
              />
            </Grid>
          </Grid>
        )}

        {currentTab === 4 && (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Email Integration Settings
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.can_embed_in_emails}
                    onChange={(e) => handleInputChange('can_embed_in_emails', e.target.checked)}
                  />
                }
                label="Allow embedding in emails"
              />
              <Typography variant="body2" color="text.secondary">
                When enabled, this article can be automatically included in email campaigns and templates
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Email Snippet"
                multiline
                rows={4}
                value={formData.email_snippet}
                onChange={(e) => handleInputChange('email_snippet', e.target.value)}
                placeholder="Brief version of this article for email inclusion..."
                helperText="Short version of the article content for email templates (optional)"
                disabled={!formData.can_embed_in_emails}
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Email Template Variables
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Define custom variables that can be used in email templates to reference this article
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Article Link Variable"
                    value={formData.email_template_variables.article_link || ''}
                    onChange={(e) => handleNestedChange('email_template_variables', 'article_link', e.target.value)}
                    placeholder="{{help_article_url}}"
                    helperText="Variable name for the article URL"
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Article Title Variable"
                    value={formData.email_template_variables.article_title || ''}
                    onChange={(e) => handleNestedChange('email_template_variables', 'article_title', e.target.value)}
                    placeholder="{{help_article_title}}"
                    helperText="Variable name for the article title"
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Article Summary Variable"
                    value={formData.email_template_variables.article_summary || ''}
                    onChange={(e) => handleNestedChange('email_template_variables', 'article_summary', e.target.value)}
                    placeholder="{{help_article_summary}}"
                    helperText="Variable name for the article summary"
                  />
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={12}>
              <Alert severity="info">
                <Typography variant="body2">
                  <strong>Email Integration Tips:</strong>
                  <br />
                  • Use descriptive variable names that are easy to remember
                  • Keep email snippets concise (2-3 sentences)
                  • Test email templates with these variables before sending campaigns
                </Typography>
              </Alert>
            </Grid>
          </Grid>
        )}

        {Object.keys(errors).length > 0 && (
          <Alert severity="error" sx={{ mt: 2 }}>
            Please fix the errors above before submitting.
          </Alert>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained"
          disabled={loading}
          startIcon={<SaveIcon />}
        >
          {loading ? 'Saving...' : (mode === 'create' ? 'Create Article' : 'Update Article')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default KnowledgeBaseArticleEditor;
