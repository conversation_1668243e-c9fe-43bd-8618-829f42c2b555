// @since 2024-1-1 to 2025-25-7
import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  Grid,
  Button,
  Alert,
  Avatar,
  Switch
} from '@mui/material';
import {
  MoreVert as MoreIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PlayArrow as EnableIcon,
  Pause as DisableIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Trigger as TriggerIcon,
  Person as UserIcon,
  Business as BusinessIcon,
  Schedule as ScheduleIcon,
  Extension as CustomIcon,
  CheckCircle as ActiveIcon,
  Cancel as InactiveIcon
} from '@mui/icons-material';

const EmailTriggerList = ({ 
  triggers = [], 
  templates = [],
  loading = false, 
  onEdit, 
  onDelete, 
  onRefresh 
}) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedTrigger, setSelectedTrigger] = useState(null);

  // Filter triggers based on search and filters
  const filteredTriggers = triggers.filter(trigger => {
    const matchesSearch = !searchTerm || 
      trigger.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      trigger.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      trigger.event_name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = typeFilter === 'all' || trigger.trigger_type === typeFilter;
    const matchesStatus = statusFilter === 'all' || 
      (statusFilter === 'active' && trigger.is_active) ||
      (statusFilter === 'inactive' && !trigger.is_active);
    
    return matchesSearch && matchesType && matchesStatus;
  });

  // Paginated triggers
  const paginatedTriggers = filteredTriggers.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleMenuOpen = (event, trigger) => {
    setAnchorEl(event.currentTarget);
    setSelectedTrigger(trigger);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedTrigger(null);
  };

  const handleEdit = () => {
    if (selectedTrigger && onEdit) {
      onEdit(selectedTrigger);
    }
    handleMenuClose();
  };

  const handleDelete = () => {
    if (selectedTrigger && onDelete) {
      onDelete(selectedTrigger.id);
    }
    handleMenuClose();
  };

  const getTriggerTypeIcon = (type) => {
    switch (type) {
      case 'user_lifecycle':
        return <UserIcon color="primary" />;
      case 'behavioral':
        return <UserIcon color="secondary" />;
      case 'business_event':
        return <BusinessIcon color="info" />;
      case 'custom_event':
        return <CustomIcon color="warning" />;
      case 'scheduled':
        return <ScheduleIcon color="success" />;
      default:
        return <TriggerIcon color="default" />;
    }
  };

  const getTriggerTypeColor = (type) => {
    switch (type) {
      case 'user_lifecycle':
        return 'primary';
      case 'behavioral':
        return 'secondary';
      case 'business_event':
        return 'info';
      case 'custom_event':
        return 'warning';
      case 'scheduled':
        return 'success';
      default:
        return 'default';
    }
  };

  const formatTriggerType = (type) => {
    return type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString();
  };

  const getTemplateName = (templateId) => {
    const template = templates.find(t => t.id === templateId);
    return template ? template.name : 'Unknown Template';
  };

  if (triggers.length === 0 && !loading) {
    return (
      <Card>
        <CardContent>
          <Alert severity="info">
            No email triggers found. Click "Create Trigger" to create your first email trigger.
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        {/* Filters */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Search triggers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Type</InputLabel>
              <Select
                value={typeFilter}
                label="Type"
                onChange={(e) => setTypeFilter(e.target.value)}
              >
                <MenuItem value="all">All Types</MenuItem>
                <MenuItem value="user_lifecycle">User Lifecycle</MenuItem>
                <MenuItem value="behavioral">Behavioral</MenuItem>
                <MenuItem value="business_event">Business Event</MenuItem>
                <MenuItem value="custom_event">Custom Event</MenuItem>
                <MenuItem value="scheduled">Scheduled</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                label="Status"
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <MenuItem value="all">All Statuses</MenuItem>
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="inactive">Inactive</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<FilterIcon />}
              onClick={onRefresh}
              sx={{ height: '56px' }}
            >
              Refresh
            </Button>
          </Grid>
        </Grid>

        {/* Results Summary */}
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Showing {filteredTriggers.length} of {triggers.length} triggers
        </Typography>

        {/* Table */}
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Trigger</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Event</TableCell>
                <TableCell>Template</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Usage</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedTriggers.map((trigger) => (
                <TableRow key={trigger.id} hover>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar sx={{ bgcolor: getTriggerTypeColor(trigger.trigger_type) + '.main' }}>
                        {getTriggerTypeIcon(trigger.trigger_type)}
                      </Avatar>
                      <Box>
                        <Typography variant="subtitle2" fontWeight="medium">
                          {trigger.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {trigger.description || 'No description'}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  
                  <TableCell>
                    <Chip
                      icon={getTriggerTypeIcon(trigger.trigger_type)}
                      label={formatTriggerType(trigger.trigger_type)}
                      color={getTriggerTypeColor(trigger.trigger_type)}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {trigger.event_name}
                    </Typography>
                    {trigger.delay_minutes > 0 && (
                      <Typography variant="caption" color="text.secondary">
                        Delay: {trigger.delay_minutes} min
                      </Typography>
                    )}
                  </TableCell>
                  
                  <TableCell>
                    <Typography variant="body2">
                      {getTemplateName(trigger.template_id)}
                    </Typography>
                  </TableCell>
                  
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {trigger.is_active ? (
                        <ActiveIcon color="success" />
                      ) : (
                        <InactiveIcon color="error" />
                      )}
                      <Chip
                        label={trigger.is_active ? 'Active' : 'Inactive'}
                        color={trigger.is_active ? 'success' : 'default'}
                        size="small"
                        variant="outlined"
                      />
                    </Box>
                  </TableCell>
                  
                  <TableCell>
                    <Box>
                      <Typography variant="body2" fontWeight="medium">
                        {trigger.trigger_count || 0} times
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Last: {formatDate(trigger.last_triggered)}
                      </Typography>
                    </Box>
                  </TableCell>
                  
                  <TableCell align="right">
                    <Tooltip title="More actions">
                      <IconButton
                        onClick={(e) => handleMenuOpen(e, trigger)}
                        size="small"
                      >
                        <MoreIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={filteredTriggers.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />

        {/* Action Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={handleEdit}>
            <ListItemIcon>
              <EditIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Edit Trigger</ListItemText>
          </MenuItem>
          
          <MenuItem onClick={() => {
            // Toggle trigger status
            handleMenuClose();
          }}>
            <ListItemIcon>
              {selectedTrigger?.is_active ? (
                <DisableIcon fontSize="small" />
              ) : (
                <EnableIcon fontSize="small" />
              )}
            </ListItemIcon>
            <ListItemText>
              {selectedTrigger?.is_active ? 'Disable' : 'Enable'} Trigger
            </ListItemText>
          </MenuItem>
          
          <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
            <ListItemIcon>
              <DeleteIcon fontSize="small" color="error" />
            </ListItemIcon>
            <ListItemText>Delete Trigger</ListItemText>
          </MenuItem>
        </Menu>
      </CardContent>
    </Card>
  );
};

export default EmailTriggerList;
