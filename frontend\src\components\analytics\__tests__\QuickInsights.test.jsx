/**
 * Tests for QuickInsights component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import QuickInsights from '../QuickInsights';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the API
vi.mock('../../../api', () => ({
  default: {
    get: vi.fn()
  }
}));

describe('QuickInsights', () => {
  const mockData = {
    total_impressions: 150000,
    total_engagements: 12500,
    total_followers: 8500,
    engagement_rate: 8.33,
    platforms: ['LinkedIn', 'Twitter', 'Instagram'],
    top_content: [
      { id: 1, title: 'Post 1', engagement: 500 },
      { id: 2, title: 'Post 2', engagement: 400 }
    ]
  };

  const mockCallbacks = {
    onRefresh: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  test('renders quick insights component', () => {
    render(
      <TestWrapper>
        <QuickInsights data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    expect(screen.getByText('Quick Insights')).toBeInTheDocument();
    expect(screen.getByLabelText('Expand insights')).toBeInTheDocument();
    expect(screen.getByLabelText('Refresh insights')).toBeInTheDocument();
  });

  test('shows loading state correctly', () => {
    render(
      <TestWrapper>
        <QuickInsights data={mockData} loading={true} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    expect(screen.getByText('Quick Insights')).toBeInTheDocument();
    // Should show loading spinner in refresh button
    const refreshButton = screen.getByLabelText('Refresh insights');
    expect(refreshButton).toBeDisabled();
  });

  test('generates and displays insights when data is provided', async () => {
    render(
      <TestWrapper>
        <QuickInsights data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    // Wait for insights to be generated
    await waitFor(() => {
      expect(screen.getByText('Engagement is up 15% this week')).toBeInTheDocument();
    }, { timeout: 2000 });

    expect(screen.getByText('Best posting time identified')).toBeInTheDocument();
    expect(screen.getByText('Audience growth slowing down')).toBeInTheDocument();
  });

  test('handles expand/collapse functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <QuickInsights data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    // Initially expanded
    expect(screen.getByLabelText('Collapse insights')).toBeInTheDocument();

    // Click to collapse
    const expandButton = screen.getByLabelText('Collapse insights');
    await user.click(expandButton);

    // Should now show expand button
    expect(screen.getByLabelText('Expand insights')).toBeInTheDocument();
  });

  test('handles refresh functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <QuickInsights data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    const refreshButton = screen.getByLabelText('Refresh insights');
    await user.click(refreshButton);

    expect(mockCallbacks.onRefresh).toHaveBeenCalled();
  });

  test('shows error state when error prop is provided', () => {
    const errorMessage = 'Failed to load insights';
    
    render(
      <TestWrapper>
        <QuickInsights 
          data={mockData} 
          loading={false} 
          onRefresh={mockCallbacks.onRefresh}
          error={errorMessage}
        />
      </TestWrapper>
    );

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
    expect(screen.getByText('Try Again')).toBeInTheDocument();
  });

  test('shows no data state when no insights are available', async () => {
    render(
      <TestWrapper>
        <QuickInsights data={null} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('No insights available. Try refreshing the data.')).toBeInTheDocument();
    });

    expect(screen.getByText('Refresh Insights')).toBeInTheDocument();
  });

  test('limits insights to maxInsights prop', async () => {
    render(
      <TestWrapper>
        <QuickInsights 
          data={mockData} 
          loading={false} 
          onRefresh={mockCallbacks.onRefresh}
          maxInsights={2}
        />
      </TestWrapper>
    );

    // Wait for insights to be generated
    await waitFor(() => {
      expect(screen.getByText('Engagement is up 15% this week')).toBeInTheDocument();
    }, { timeout: 2000 });

    // Should only show 2 insights
    const insights = screen.getAllByText(/\+\d+%|\-\d+%/);
    expect(insights.length).toBeLessThanOrEqual(4); // 2 insights * 2 occurrences each (title and chip)
  });

  test('hides refresh button when showRefresh is false', () => {
    render(
      <TestWrapper>
        <QuickInsights 
          data={mockData} 
          loading={false} 
          onRefresh={mockCallbacks.onRefresh}
          showRefresh={false}
        />
      </TestWrapper>
    );

    expect(screen.queryByLabelText('Refresh insights')).not.toBeInTheDocument();
  });

  test('handles insight action buttons correctly', async () => {
    const user = userEvent.setup();
    
    // Mock window.location.href
    delete window.location;
    window.location = { href: '' };
    
    render(
      <TestWrapper>
        <QuickInsights data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    // Wait for insights to be generated
    await waitFor(() => {
      expect(screen.getByText('View top performing content')).toBeInTheDocument();
    }, { timeout: 2000 });

    const actionButton = screen.getByText('View top performing content');
    await user.click(actionButton);

    expect(window.location.href).toBe('/analytics/content');
  });

  test('displays different insight types with correct styling', async () => {
    render(
      <TestWrapper>
        <QuickInsights data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    // Wait for insights to be generated
    await waitFor(() => {
      expect(screen.getByText('Engagement is up 15% this week')).toBeInTheDocument();
    }, { timeout: 2000 });

    // Should show positive insight
    expect(screen.getByText('+15%')).toBeInTheDocument();
    
    // Should show negative insight
    expect(screen.getByText('-5%')).toBeInTheDocument();
    
    // Should show regular insight
    expect(screen.getByText('+23%')).toBeInTheDocument();
  });

  test('handles API error gracefully', async () => {
    const api = await import('../../../api');
    api.default.get.mockRejectedValue(new Error('API Error'));
    
    render(
      <TestWrapper>
        <QuickInsights data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    // Should still show fallback insights
    await waitFor(() => {
      expect(screen.getByText('Engagement is up 15% this week')).toBeInTheDocument();
    }, { timeout: 2000 });
  });

  test('auto-refresh functionality works correctly', async () => {
    render(
      <TestWrapper>
        <QuickInsights 
          data={mockData} 
          loading={false} 
          onRefresh={mockCallbacks.onRefresh}
          autoRefresh={true}
          refreshInterval={1000}
        />
      </TestWrapper>
    );

    // Fast-forward time to trigger auto-refresh
    vi.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(mockCallbacks.onRefresh).toHaveBeenCalled();
    });
  });

  test('cleans up auto-refresh interval on unmount', () => {
    const { unmount } = render(
      <TestWrapper>
        <QuickInsights 
          data={mockData} 
          loading={false} 
          onRefresh={mockCallbacks.onRefresh}
          autoRefresh={true}
          refreshInterval={1000}
        />
      </TestWrapper>
    );

    const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
    unmount();

    expect(clearIntervalSpy).toHaveBeenCalled();
  });

  test('handles missing data gracefully', () => {
    render(
      <TestWrapper>
        <QuickInsights data={null} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    expect(screen.getByText('Quick Insights')).toBeInTheDocument();
    // Should not crash and should show appropriate message
  });

  test('applies correct accessibility attributes', () => {
    render(
      <TestWrapper>
        <QuickInsights data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    // Check ARIA labels
    expect(screen.getByLabelText('Collapse insights')).toBeInTheDocument();
    expect(screen.getByLabelText('Refresh insights')).toBeInTheDocument();

    // Check aria-expanded
    const expandButton = screen.getByLabelText('Collapse insights');
    expect(expandButton).toHaveAttribute('aria-expanded', 'true');
  });

  test('shows loading state during insight generation', async () => {
    render(
      <TestWrapper>
        <QuickInsights data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    // Should show loading initially
    expect(screen.getByRole('progressbar')).toBeInTheDocument();

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 2000 });
  });

  test('handles insight categories correctly', async () => {
    render(
      <TestWrapper>
        <QuickInsights data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    // Wait for insights to be generated
    await waitFor(() => {
      expect(screen.getByText('engagement')).toBeInTheDocument();
    }, { timeout: 2000 });

    expect(screen.getByText('timing')).toBeInTheDocument();
    expect(screen.getByText('audience')).toBeInTheDocument();
    expect(screen.getByText('content')).toBeInTheDocument();
  });
});
