// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import StyleGuideExport from '../StyleGuideExport';

// Mock the notification hook
const mockShowSuccessNotification = vi.fn();
const mockShowErrorNotification = vi.fn();

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showSuccessNotification: mockShowSuccessNotification,
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn(),
  },
});

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('StyleGuideExport', () => {
  const mockBrandingData = {
    colorSystem: {
      primary: '#4E40C5',
      secondary: '#00E4BC',
      accent: '#FF5733',
      background: '#F8F9FA',
      text: '#333333'
    },
    typography: {
      primary_font: 'Inter',
      secondary_font: 'Roboto',
      style: 'professional'
    },
    visualStyle: {
      photography_style: 'lifestyle',
      lighting: 'bright-airy',
      filters: []
    },
    imageComposition: {
      layout: 'rule-of-thirds',
      aspectRatio: '16:9'
    },
    visualElements: {
      patterns: [
        {
          id: 'pattern1',
          name: 'Dots',
          url: 'https://example.com/dots.png'
        }
      ],
      textures: [],
      shapes: {
        type: 'rounded',
        size: 'medium'
      }
    },
    logo_url: 'https://example.com/logo.png',
    brand_voice: {
      tone: 'professional',
      personality_traits: ['trustworthy', 'innovative'],
      writing_style: 'clear and concise'
    }
  };

  const mockProps = {
    brandingData: mockBrandingData,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders style guide export correctly', () => {
    render(
      <TestWrapper>
        <StyleGuideExport {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Brand Style Guide Export')).toBeInTheDocument();
    expect(screen.getByText(/Generate a shareable brand style guide/)).toBeInTheDocument();
  });

  test('displays style guide settings form', () => {
    render(
      <TestWrapper>
        <StyleGuideExport {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Style Guide Settings')).toBeInTheDocument();
    expect(screen.getByLabelText('Company Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Company Description')).toBeInTheDocument();
    expect(screen.getByLabelText('Export Format')).toBeInTheDocument();
  });

  test('shows sections to include with switches', () => {
    render(
      <TestWrapper>
        <StyleGuideExport {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Sections to Include')).toBeInTheDocument();
    expect(screen.getByText('Color System')).toBeInTheDocument();
    expect(screen.getByText('Visual Style')).toBeInTheDocument();
    expect(screen.getByText('Typography')).toBeInTheDocument();
    expect(screen.getByText('Image Composition')).toBeInTheDocument();
    expect(screen.getByText('Patterns & Textures')).toBeInTheDocument();
    expect(screen.getByText('Logo Guidelines')).toBeInTheDocument();
  });

  test('handles company name input', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StyleGuideExport {...mockProps} />
      </TestWrapper>
    );

    const companyNameInput = screen.getByLabelText('Company Name');
    await user.type(companyNameInput, 'Test Company');

    expect(companyNameInput).toHaveValue('Test Company');
  });

  test('handles company description input', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StyleGuideExport {...mockProps} />
      </TestWrapper>
    );

    const descriptionInput = screen.getByLabelText('Company Description');
    await user.type(descriptionInput, 'Test company description');

    expect(descriptionInput).toHaveValue('Test company description');
  });

  test('handles export format change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StyleGuideExport {...mockProps} />
      </TestWrapper>
    );

    const formatSelect = screen.getByLabelText('Export Format');
    await user.click(formatSelect);
    
    const htmlOption = screen.getByText('HTML Webpage');
    await user.click(htmlOption);

    expect(formatSelect).toHaveTextContent('HTML Webpage');
  });

  test('handles section toggle switches', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StyleGuideExport {...mockProps} />
      </TestWrapper>
    );

    const colorSystemSwitch = screen.getByRole('checkbox', { name: /color system/i });
    expect(colorSystemSwitch).toBeChecked();

    await user.click(colorSystemSwitch);
    expect(colorSystemSwitch).not.toBeChecked();
  });

  test('shows validation error when company name is missing on preview', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StyleGuideExport {...mockProps} />
      </TestWrapper>
    );

    const previewButton = screen.getByText('Preview');
    await user.click(previewButton);

    await waitFor(() => {
      expect(screen.getByText('Please enter your company name')).toBeInTheDocument();
    });
  });

  test('shows validation error when company name is missing on export', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StyleGuideExport {...mockProps} />
      </TestWrapper>
    );

    const exportButton = screen.getByRole('button', { name: /export as pdf/i });
    await user.click(exportButton);

    await waitFor(() => {
      expect(screen.getByText('Please enter your company name')).toBeInTheDocument();
    });
  });

  test('opens preview dialog when company name is provided', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StyleGuideExport {...mockProps} />
      </TestWrapper>
    );

    const companyNameInput = screen.getByLabelText('Company Name');
    await user.type(companyNameInput, 'Test Company');

    const previewButton = screen.getByText('Preview');
    await user.click(previewButton);

    await waitFor(() => {
      expect(screen.getByText('Style Guide Preview: Test Company')).toBeInTheDocument();
    });
  });

  test('displays color system in preview when enabled', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StyleGuideExport {...mockProps} />
      </TestWrapper>
    );

    const companyNameInput = screen.getByLabelText('Company Name');
    await user.type(companyNameInput, 'Test Company');

    const previewButton = screen.getByText('Preview');
    await user.click(previewButton);

    await waitFor(() => {
      expect(screen.getByText('Color System')).toBeInTheDocument();
      expect(screen.getByText('Primary')).toBeInTheDocument();
      expect(screen.getByText('Secondary')).toBeInTheDocument();
      expect(screen.getByText('#4E40C5')).toBeInTheDocument();
      expect(screen.getByText('#00E4BC')).toBeInTheDocument();
    });
  });

  test('handles color copying to clipboard', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StyleGuideExport {...mockProps} />
      </TestWrapper>
    );

    const companyNameInput = screen.getByLabelText('Company Name');
    await user.type(companyNameInput, 'Test Company');

    const previewButton = screen.getByText('Preview');
    await user.click(previewButton);

    await waitFor(async () => {
      const copyButtons = screen.getAllByRole('button', { name: /copy/i });
      if (copyButtons.length > 0) {
        await user.click(copyButtons[0]);
        expect(navigator.clipboard.writeText).toHaveBeenCalled();
      }
    });
  });

  test('handles export process', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StyleGuideExport {...mockProps} />
      </TestWrapper>
    );

    const companyNameInput = screen.getByLabelText('Company Name');
    await user.type(companyNameInput, 'Test Company');

    const exportButton = screen.getByRole('button', { name: /export as pdf/i });
    await user.click(exportButton);

    expect(screen.getByText('Generating...')).toBeInTheDocument();

    await waitFor(() => {
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Style guide exported as PDF');
    }, { timeout: 3000 });
  });

  test('disables export button when company name is empty', () => {
    render(
      <TestWrapper>
        <StyleGuideExport {...mockProps} />
      </TestWrapper>
    );

    const exportButton = screen.getByRole('button', { name: /export as pdf/i });
    expect(exportButton).toBeDisabled();
  });

  test('enables export button when company name is provided', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StyleGuideExport {...mockProps} />
      </TestWrapper>
    );

    const companyNameInput = screen.getByLabelText('Company Name');
    await user.type(companyNameInput, 'Test Company');

    const exportButton = screen.getByRole('button', { name: /export as pdf/i });
    expect(exportButton).not.toBeDisabled();
  });

  test('closes preview dialog', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StyleGuideExport {...mockProps} />
      </TestWrapper>
    );

    const companyNameInput = screen.getByLabelText('Company Name');
    await user.type(companyNameInput, 'Test Company');

    const previewButton = screen.getByText('Preview');
    await user.click(previewButton);

    await waitFor(() => {
      expect(screen.getByText('Style Guide Preview: Test Company')).toBeInTheDocument();
    });

    const closeButton = screen.getByText('Close');
    await user.click(closeButton);

    await waitFor(() => {
      expect(screen.queryByText('Style Guide Preview: Test Company')).not.toBeInTheDocument();
    });
  });

  test('handles logo upload button click', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StyleGuideExport {...mockProps} />
      </TestWrapper>
    );

    const uploadButton = screen.getByText('Upload Logo');
    await user.click(uploadButton);

    // Should trigger file input click (tested via implementation)
    expect(uploadButton).toBeInTheDocument();
  });

  test('shows logo status correctly', () => {
    render(
      <TestWrapper>
        <StyleGuideExport {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Logo uploaded')).toBeInTheDocument();
  });

  test('shows no logo status when logo_url is missing', () => {
    const propsWithoutLogo = {
      brandingData: {
        ...mockBrandingData,
        logo_url: null
      }
    };

    render(
      <TestWrapper>
        <StyleGuideExport {...propsWithoutLogo} />
      </TestWrapper>
    );

    expect(screen.getByText('No logo uploaded')).toBeInTheDocument();
  });

  test('renders with default props when no brandingData provided', () => {
    render(
      <TestWrapper>
        <StyleGuideExport />
      </TestWrapper>
    );

    expect(screen.getByText('Brand Style Guide Export')).toBeInTheDocument();
    expect(screen.getByText('No logo uploaded')).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <StyleGuideExport {...mockProps} />
      </TestWrapper>
    );

    // Check for proper labels and roles
    expect(screen.getByRole('textbox', { name: /company name/i })).toBeInTheDocument();
    expect(screen.getByRole('textbox', { name: /company description/i })).toBeInTheDocument();
    expect(screen.getByRole('combobox', { name: /export format/i })).toBeInTheDocument();
  });

  test('shows company description in preview when provided', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StyleGuideExport {...mockProps} />
      </TestWrapper>
    );

    const companyNameInput = screen.getByLabelText('Company Name');
    await user.type(companyNameInput, 'Test Company');

    const descriptionInput = screen.getByLabelText('Company Description');
    await user.type(descriptionInput, 'Test company description');

    const previewButton = screen.getByText('Preview');
    await user.click(previewButton);

    await waitFor(() => {
      expect(screen.getByText('About Test Company')).toBeInTheDocument();
      expect(screen.getByText('Test company description')).toBeInTheDocument();
    });
  });
});
