/**
 * Tests for CancelSubscriptionDialog component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CancelSubscriptionDialog from '../CancelSubscriptionDialog';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the API
vi.mock('../../../api', () => ({
  default: {
    post: vi.fn()
  }
}));

// Mock useNotification hook
vi.mock('../../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showSuccessNotification: vi.fn(),
    showErrorNotification: vi.fn()
  }))
}));

// Mock window.open
global.window.open = vi.fn();

describe('CancelSubscriptionDialog', () => {
  const mockSubscription = {
    plan_name: 'Creator Plan',
    current_period_start: '2024-01-01T00:00:00Z',
    current_period_end: '2024-02-01T00:00:00Z',
    is_appsumo_lifetime: false
  };

  const mockProps = {
    open: true,
    onClose: vi.fn(),
    subscription: mockSubscription,
    onCancelled: vi.fn(),
    onError: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders cancel subscription dialog', () => {
    render(
      <TestWrapper>
        <CancelSubscriptionDialog {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Cancel Subscription')).toBeInTheDocument();
    expect(screen.getByText('Are you sure you want to cancel your Creator Plan subscription?')).toBeInTheDocument();
    expect(screen.getByText('Current Billing Period')).toBeInTheDocument();
    expect(screen.getByText('Cancellation Options')).toBeInTheDocument();
  });

  test('shows AppSumo lifetime subscription warning', () => {
    const appsumoSubscription = {
      ...mockSubscription,
      is_appsumo_lifetime: true
    };

    render(
      <TestWrapper>
        <CancelSubscriptionDialog {...mockProps} subscription={appsumoSubscription} />
      </TestWrapper>
    );

    expect(screen.getByText('AppSumo lifetime subscriptions cannot be cancelled through this interface.')).toBeInTheDocument();
    expect(screen.getByText('If you need to cancel your AppSumo lifetime subscription, please contact AppSumo support directly.')).toBeInTheDocument();
  });

  test('disables cancel button for AppSumo subscriptions', () => {
    const appsumoSubscription = {
      ...mockSubscription,
      is_appsumo_lifetime: true
    };

    render(
      <TestWrapper>
        <CancelSubscriptionDialog {...mockProps} subscription={appsumoSubscription} />
      </TestWrapper>
    );

    const cancelButton = screen.getByText('Confirm Cancellation');
    expect(cancelButton).toBeDisabled();
  });

  test('handles cancellation option changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CancelSubscriptionDialog {...mockProps} />
      </TestWrapper>
    );

    // Default should be "Cancel at the end of the billing period"
    const endOfPeriodOption = screen.getByLabelText(/Cancel at the end of the billing period/);
    expect(endOfPeriodOption).toBeChecked();

    // Switch to immediate cancellation
    const immediateOption = screen.getByLabelText(/Cancel immediately/);
    await user.click(immediateOption);

    expect(immediateOption).toBeChecked();
    expect(endOfPeriodOption).not.toBeChecked();
  });

  test('handles cancellation reason selection', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CancelSubscriptionDialog {...mockProps} />
      </TestWrapper>
    );

    const reasonOption = screen.getByLabelText("It's too expensive");
    await user.click(reasonOption);

    expect(reasonOption).toBeChecked();
  });

  test('shows other reason text field when "Other reason" is selected', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CancelSubscriptionDialog {...mockProps} />
      </TestWrapper>
    );

    const otherReasonOption = screen.getByLabelText('Other reason');
    await user.click(otherReasonOption);

    expect(otherReasonOption).toBeChecked();
    expect(screen.getByPlaceholderText('Please tell us more...')).toBeInTheDocument();
  });

  test('validates other reason text input', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CancelSubscriptionDialog {...mockProps} />
      </TestWrapper>
    );

    // Select "Other reason" but don't provide text
    const otherReasonOption = screen.getByLabelText('Other reason');
    await user.click(otherReasonOption);

    const cancelButton = screen.getByText('Confirm Cancellation');
    expect(cancelButton).toBeDisabled();

    // Add text to enable the button
    const textField = screen.getByPlaceholderText('Please tell us more...');
    await user.type(textField, 'Custom reason');

    expect(cancelButton).not.toBeDisabled();
  });

  test('handles successful cancellation', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    const { useNotification } = await import('../../../hooks/useNotification');
    
    const mockShowSuccess = vi.fn();
    useNotification.mockReturnValue({
      showSuccessNotification: mockShowSuccess,
      showErrorNotification: vi.fn()
    });

    api.default.post.mockResolvedValue({
      data: {
        customer_portal_url: 'https://portal.lemonsqueezy.com/customer/123'
      }
    });
    
    render(
      <TestWrapper>
        <CancelSubscriptionDialog {...mockProps} />
      </TestWrapper>
    );

    // Select a reason
    const reasonOption = screen.getByLabelText("It's too expensive");
    await user.click(reasonOption);

    // Click cancel button
    const cancelButton = screen.getByText('Confirm Cancellation');
    await user.click(cancelButton);

    await waitFor(() => {
      expect(api.default.post).toHaveBeenCalledWith('/api/billing/subscription/cancel', {
        cancel_at_period_end: true,
        reason: 'too_expensive'
      });
    });

    expect(mockShowSuccess).toHaveBeenCalledWith('You will be redirected to the customer portal to complete cancellation');
    expect(window.open).toHaveBeenCalledWith('https://portal.lemonsqueezy.com/customer/123', '_blank');
    expect(mockProps.onCancelled).toHaveBeenCalled();
    expect(mockProps.onClose).toHaveBeenCalled();
  });

  test('handles cancellation with other reason', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.post.mockResolvedValue({
      data: {
        customer_portal_url: 'https://portal.lemonsqueezy.com/customer/123'
      }
    });
    
    render(
      <TestWrapper>
        <CancelSubscriptionDialog {...mockProps} />
      </TestWrapper>
    );

    // Select "Other reason"
    const otherReasonOption = screen.getByLabelText('Other reason');
    await user.click(otherReasonOption);

    // Enter custom reason
    const textField = screen.getByPlaceholderText('Please tell us more...');
    await user.type(textField, 'Custom cancellation reason');

    // Click cancel button
    const cancelButton = screen.getByText('Confirm Cancellation');
    await user.click(cancelButton);

    await waitFor(() => {
      expect(api.default.post).toHaveBeenCalledWith('/api/billing/subscription/cancel', {
        cancel_at_period_end: true,
        reason: 'Custom cancellation reason'
      });
    });
  });

  test('handles API errors gracefully', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    const { useNotification } = await import('../../../hooks/useNotification');
    
    const mockShowError = vi.fn();
    useNotification.mockReturnValue({
      showSuccessNotification: vi.fn(),
      showErrorNotification: mockShowError
    });

    api.default.post.mockRejectedValue({
      response: {
        data: {
          detail: 'Subscription not found'
        }
      }
    });
    
    render(
      <TestWrapper>
        <CancelSubscriptionDialog {...mockProps} />
      </TestWrapper>
    );

    // Select a reason
    const reasonOption = screen.getByLabelText("It's too expensive");
    await user.click(reasonOption);

    // Click cancel button
    const cancelButton = screen.getByText('Confirm Cancellation');
    await user.click(cancelButton);

    await waitFor(() => {
      expect(screen.getByText('Failed to cancel subscription. Please try again or contact support.')).toBeInTheDocument();
    });

    expect(mockShowError).toHaveBeenCalledWith('Failed to cancel subscription');
    expect(mockProps.onError).toHaveBeenCalledWith('Failed to cancel subscription. Please try again or contact support.');
  });

  test('handles missing customer portal URL', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.post.mockResolvedValue({
      data: {} // No customer_portal_url
    });
    
    render(
      <TestWrapper>
        <CancelSubscriptionDialog {...mockProps} />
      </TestWrapper>
    );

    // Select a reason
    const reasonOption = screen.getByLabelText("It's too expensive");
    await user.click(reasonOption);

    // Click cancel button
    const cancelButton = screen.getByText('Confirm Cancellation');
    await user.click(cancelButton);

    await waitFor(() => {
      expect(screen.getByText('Failed to cancel subscription. Please try again or contact support.')).toBeInTheDocument();
    });
  });

  test('shows loading state during cancellation', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.post.mockImplementation(() => new Promise(() => {})); // Never resolves
    
    render(
      <TestWrapper>
        <CancelSubscriptionDialog {...mockProps} />
      </TestWrapper>
    );

    // Select a reason
    const reasonOption = screen.getByLabelText("It's too expensive");
    await user.click(reasonOption);

    // Click cancel button
    const cancelButton = screen.getByText('Confirm Cancellation');
    await user.click(cancelButton);

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(screen.getByText('Processing...')).toBeInTheDocument();
    expect(cancelButton).toBeDisabled();
  });

  test('prevents dialog close during loading', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.post.mockImplementation(() => new Promise(() => {})); // Never resolves
    
    render(
      <TestWrapper>
        <CancelSubscriptionDialog {...mockProps} />
      </TestWrapper>
    );

    // Select a reason and start cancellation
    const reasonOption = screen.getByLabelText("It's too expensive");
    await user.click(reasonOption);

    const cancelButton = screen.getByText('Confirm Cancellation');
    await user.click(cancelButton);

    // Try to close dialog - should be disabled
    const keepButton = screen.getByText('Keep Subscription');
    expect(keepButton).toBeDisabled();
  });

  test('formats dates correctly', () => {
    render(
      <TestWrapper>
        <CancelSubscriptionDialog {...mockProps} />
      </TestWrapper>
    );

    // Check if dates are formatted and displayed
    expect(screen.getByText(/1\/1\/2024 - 2\/1\/2024/)).toBeInTheDocument();
  });

  test('handles immediate cancellation option', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.post.mockResolvedValue({
      data: {
        customer_portal_url: 'https://portal.lemonsqueezy.com/customer/123'
      }
    });
    
    render(
      <TestWrapper>
        <CancelSubscriptionDialog {...mockProps} />
      </TestWrapper>
    );

    // Select immediate cancellation
    const immediateOption = screen.getByLabelText(/Cancel immediately/);
    await user.click(immediateOption);

    // Select a reason
    const reasonOption = screen.getByLabelText("It's too expensive");
    await user.click(reasonOption);

    // Click cancel button
    const cancelButton = screen.getByText('Confirm Cancellation');
    await user.click(cancelButton);

    await waitFor(() => {
      expect(api.default.post).toHaveBeenCalledWith('/api/billing/subscription/cancel', {
        cancel_at_period_end: false,
        reason: 'too_expensive'
      });
    });
  });

  test('applies correct accessibility attributes', () => {
    render(
      <TestWrapper>
        <CancelSubscriptionDialog {...mockProps} />
      </TestWrapper>
    );

    // Check dialog accessibility
    const dialog = screen.getByRole('dialog');
    expect(dialog).toBeInTheDocument();

    // Check radio groups
    const cancellationOptions = screen.getByRole('radiogroup');
    expect(cancellationOptions).toBeInTheDocument();

    // Check form controls
    const reasonOptions = screen.getAllByRole('radio');
    expect(reasonOptions.length).toBeGreaterThan(0);
  });

  test('passes through additional props', () => {
    render(
      <TestWrapper>
        <CancelSubscriptionDialog 
          {...mockProps} 
          data-testid="test-cancel-dialog"
          aria-describedby="custom-description"
        />
      </TestWrapper>
    );

    const dialog = screen.getByTestId('test-cancel-dialog');
    expect(dialog).toHaveAttribute('aria-describedby', 'custom-description');
  });

  test('handles dialog close', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CancelSubscriptionDialog {...mockProps} />
      </TestWrapper>
    );

    const keepButton = screen.getByText('Keep Subscription');
    await user.click(keepButton);

    expect(mockProps.onClose).toHaveBeenCalled();
  });
});
