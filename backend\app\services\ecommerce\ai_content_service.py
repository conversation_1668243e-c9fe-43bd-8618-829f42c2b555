"""
AI-Powered Content Generation Service for E-commerce Social Media.
Provides intelligent content creation with platform-specific optimization.
@since 2024-1-1 to 2025-25-7
"""

import logging
import asyncio
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from bson import ObjectId
import openai
from dataclasses import dataclass, asdict

from app.models.ecommerce import SyncedProduct
from app.models.user import User
from app.db.mongodb import get_database
from app.core.redis import get_redis_client
from app.core.monitoring import monitor_performance, log_audit_event, OperationType
from app.core.config import settings
from app.services.content_generation.content_generator import content_generator

logger = logging.getLogger(__name__)

# Collection names
PRODUCTS_COLLECTION = "synced_products"
GENERATED_CONTENT_COLLECTION = "generated_content"
CONTENT_TEMPLATES_COLLECTION = "content_templates"
CONTENT_ANALYTICS_COLLECTION = "content_analytics"

# Redis keys
CONTENT_CACHE_KEY = "ai_content:{product_id}:{platform}"
TEMPLATE_CACHE_KEY = "content_templates:{user_id}"
GENERATION_QUEUE_KEY = "content_generation_queue"

# Platform specifications
PLATFORM_SPECS = {
    "instagram": {
        "max_caption_length": 2200,
        "max_hashtags": 30,
        "optimal_hashtags": 11,
        "image_aspect_ratios": ["1:1", "4:5", "9:16"],
        "supports_carousel": True,
        "supports_video": True,
        "supports_stories": True
    },
    "facebook": {
        "max_caption_length": 63206,
        "max_hashtags": 30,
        "optimal_hashtags": 5,
        "image_aspect_ratios": ["16:9", "1:1", "4:5"],
        "supports_carousel": True,
        "supports_video": True,
        "supports_stories": True
    },
    "twitter": {
        "max_caption_length": 280,
        "max_hashtags": 10,
        "optimal_hashtags": 3,
        "image_aspect_ratios": ["16:9", "1:1"],
        "supports_carousel": False,
        "supports_video": True,
        "supports_stories": False
    },
    "linkedin": {
        "max_caption_length": 3000,
        "max_hashtags": 30,
        "optimal_hashtags": 5,
        "image_aspect_ratios": ["1.91:1", "1:1"],
        "supports_carousel": True,
        "supports_video": True,
        "supports_stories": False
    },
    "tiktok": {
        "max_caption_length": 2200,
        "max_hashtags": 100,
        "optimal_hashtags": 5,
        "image_aspect_ratios": ["9:16"],
        "supports_carousel": False,
        "supports_video": True,
        "supports_stories": False
    }
}


@dataclass
class ContentGenerationRequest:
    """Content generation request structure."""
    product_id: str
    store_id: str
    user_id: str
    platforms: List[str]
    content_type: str  # product_showcase, promotion, story, educational
    tone: str  # professional, casual, enthusiastic, luxury
    target_audience: str
    include_hashtags: bool = True
    include_cta: bool = True
    custom_prompt: Optional[str] = None
    template_id: Optional[str] = None


@dataclass
class GeneratedContent:
    """Generated content structure."""
    content_id: str
    product_id: str
    platform: str
    content_type: str
    caption: str
    hashtags: List[str]
    call_to_action: str
    image_suggestions: List[str]
    video_suggestions: List[str]
    optimal_posting_times: List[str]
    engagement_predictions: Dict[str, float]
    created_at: datetime
    expires_at: datetime


class AIContentService:
    """
    Comprehensive AI-powered content generation service.
    """
    
    def __init__(self):
        self.redis_client = None
        self.db = None
        
        # Initialize OpenAI if API key is available
        if hasattr(settings, 'OPENAI_API_KEY') and settings.OPENAI_API_KEY:
            openai.api_key = settings.OPENAI_API_KEY
        
    async def _get_redis_client(self):
        """Get Redis client for caching."""
        if not self.redis_client:
            self.redis_client = await get_redis_client()
        return self.redis_client
    
    async def _get_database(self):
        """Get MongoDB database."""
        if not self.db:
            self.db = await get_database()
        return self.db
    
    @monitor_performance("generate_product_content")
    async def generate_product_content(
        self,
        request: ContentGenerationRequest
    ) -> Dict[str, Any]:
        """
        Generate AI-powered content for product across multiple platforms.
        
        Args:
            request: Content generation request
            
        Returns:
            Generated content for all specified platforms
        """
        try:
            db = await self._get_database()
            
            # Get product data
            product = await db[PRODUCTS_COLLECTION].find_one({
                "_id": ObjectId(request.product_id),
                "user_id": ObjectId(request.user_id),
                "store_id": ObjectId(request.store_id)
            })
            
            if not product:
                return {
                    "success": False,
                    "error": "Product not found or access denied"
                }
            
            # Generate content for each platform
            generated_contents = []
            
            for platform in request.platforms:
                if platform not in PLATFORM_SPECS:
                    logger.warning(f"Unsupported platform: {platform}")
                    continue
                
                # Check cache first
                cache_key = CONTENT_CACHE_KEY.format(
                    product_id=request.product_id,
                    platform=platform
                )
                
                redis = await self._get_redis_client()
                cached_content = None
                
                if redis:
                    cached_data = await redis.get(cache_key)
                    if cached_data:
                        cached_content = json.loads(cached_data)
                
                if not cached_content:
                    # Generate new content
                    content = await self._generate_platform_content(
                        product, platform, request
                    )
                    
                    if content:
                        generated_contents.append(content)
                        
                        # Cache the content
                        if redis:
                            await redis.setex(
                                cache_key,
                                3600,  # 1 hour cache
                                json.dumps(asdict(content), default=str)
                            )
                else:
                    # Use cached content
                    cached_content["from_cache"] = True
                    generated_contents.append(GeneratedContent(**cached_content))
            
            # Save generated content to database
            if generated_contents:
                content_docs = []
                for content in generated_contents:
                    content_doc = {
                        **asdict(content),
                        "_id": ObjectId(content.content_id),
                        "product_id": ObjectId(content.product_id),
                        "user_id": ObjectId(request.user_id),
                        "store_id": ObjectId(request.store_id),
                        "generation_request": asdict(request)
                    }
                    content_docs.append(content_doc)
                
                await db[GENERATED_CONTENT_COLLECTION].insert_many(content_docs)
            
            # Log audit event
            log_audit_event(
                operation_type=OperationType.CREATE,
                resource_type="ai_generated_content",
                resource_id=request.product_id,
                user_id=request.user_id,
                details={
                    "platforms": request.platforms,
                    "content_type": request.content_type,
                    "contents_generated": len(generated_contents)
                }
            )
            
            return {
                "success": True,
                "product_id": request.product_id,
                "platforms": request.platforms,
                "generated_contents": [asdict(content) for content in generated_contents],
                "total_generated": len(generated_contents),
                "generation_time": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating product content: {str(e)}")
            return {
                "success": False,
                "error": f"Content generation failed: {str(e)}",
                "product_id": request.product_id
            }
    
    async def _generate_platform_content(
        self,
        product: Dict[str, Any],
        platform: str,
        request: ContentGenerationRequest
    ) -> Optional[GeneratedContent]:
        """Generate content for a specific platform."""
        try:
            platform_spec = PLATFORM_SPECS[platform]
            
            # Build content generation prompt
            prompt = await self._build_content_prompt(
                product, platform, request, platform_spec
            )
            
            # Generate content using AI
            ai_response = await self._call_ai_service(prompt, platform_spec)
            
            if not ai_response:
                return None
            
            # Parse AI response
            parsed_content = await self._parse_ai_response(
                ai_response, platform, platform_spec
            )
            
            # Generate additional suggestions
            image_suggestions = await self._generate_image_suggestions(
                product, platform, request.content_type
            )
            
            video_suggestions = await self._generate_video_suggestions(
                product, platform, request.content_type
            )
            
            optimal_times = await self._get_optimal_posting_times(
                request.user_id, platform
            )
            
            engagement_predictions = await self._predict_engagement(
                parsed_content, platform, product
            )
            
            # Create content object
            content = GeneratedContent(
                content_id=str(ObjectId()),
                product_id=request.product_id,
                platform=platform,
                content_type=request.content_type,
                caption=parsed_content.get("caption", ""),
                hashtags=parsed_content.get("hashtags", []),
                call_to_action=parsed_content.get("cta", ""),
                image_suggestions=image_suggestions,
                video_suggestions=video_suggestions,
                optimal_posting_times=optimal_times,
                engagement_predictions=engagement_predictions,
                created_at=datetime.now(timezone.utc),
                expires_at=datetime.now(timezone.utc) + timedelta(days=7)
            )
            
            return content
            
        except Exception as e:
            logger.error(f"Error generating content for platform {platform}: {str(e)}")
            return None
    
    async def _build_content_prompt(
        self,
        product: Dict[str, Any],
        platform: str,
        request: ContentGenerationRequest,
        platform_spec: Dict[str, Any]
    ) -> str:
        """Build AI prompt for content generation."""
        
        # Base product information
        product_info = f"""
Product: {product.get('title', 'Unknown Product')}
Description: {product.get('description', 'No description available')}
Price: ${product.get('price', 0)}
Category: {product.get('category', 'General')}
Tags: {', '.join(product.get('tags', '').split(',') if product.get('tags') else [])}
"""
        
        # Platform constraints
        constraints = f"""
Platform: {platform.title()}
Max caption length: {platform_spec['max_caption_length']} characters
Optimal hashtags: {platform_spec['optimal_hashtags']}
Max hashtags: {platform_spec['max_hashtags']}
"""
        
        # Content requirements
        requirements = f"""
Content Type: {request.content_type}
Tone: {request.tone}
Target Audience: {request.target_audience}
Include Hashtags: {request.include_hashtags}
Include Call-to-Action: {request.include_cta}
"""
        
        # Custom prompt if provided
        custom_section = ""
        if request.custom_prompt:
            custom_section = f"\nCustom Instructions: {request.custom_prompt}"
        
        # Build final prompt
        prompt = f"""
Create engaging social media content for the following product:

{product_info}

Platform Requirements:
{constraints}

Content Requirements:
{requirements}

{custom_section}

Please generate:
1. An engaging caption that fits the platform's character limit
2. Relevant hashtags (optimal number for the platform)
3. A compelling call-to-action
4. Content that matches the specified tone and appeals to the target audience

Format the response as JSON with keys: "caption", "hashtags" (array), "cta"
"""
        
        return prompt
    
    async def _call_ai_service(
        self,
        prompt: str,
        platform_spec: Dict[str, Any]
    ) -> Optional[str]:
        """Call AI service to generate content."""
        try:
            # Use existing content generator service
            result = await content_generator.generate_content(
                prompt=prompt,
                content_type="social_media_post",
                max_length=platform_spec["max_caption_length"],
                tone="engaging"
            )
            
            if result.get("success"):
                return result.get("content", "")
            
            return None
            
        except Exception as e:
            logger.error(f"Error calling AI service: {str(e)}")
            return None


    async def _parse_ai_response(
        self,
        ai_response: str,
        platform: str,
        platform_spec: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Parse AI response into structured content."""
        try:
            # Try to parse as JSON first
            try:
                parsed = json.loads(ai_response)
                if isinstance(parsed, dict):
                    return parsed
            except json.JSONDecodeError:
                pass

            # Fallback: parse text response
            lines = ai_response.strip().split('\n')
            content = {
                "caption": "",
                "hashtags": [],
                "cta": ""
            }

            current_section = None
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                if line.lower().startswith('caption:'):
                    current_section = 'caption'
                    content['caption'] = line[8:].strip()
                elif line.lower().startswith('hashtags:'):
                    current_section = 'hashtags'
                    hashtag_text = line[9:].strip()
                    content['hashtags'] = [tag.strip('#').strip() for tag in hashtag_text.split() if tag.startswith('#')]
                elif line.lower().startswith('cta:') or line.lower().startswith('call-to-action:'):
                    current_section = 'cta'
                    content['cta'] = line.split(':', 1)[1].strip()
                elif current_section == 'caption':
                    content['caption'] += ' ' + line
                elif current_section == 'hashtags' and line.startswith('#'):
                    content['hashtags'].extend([tag.strip('#').strip() for tag in line.split() if tag.startswith('#')])
                elif current_section == 'cta':
                    content['cta'] += ' ' + line

            # Validate and clean content
            content['caption'] = content['caption'][:platform_spec['max_caption_length']]
            content['hashtags'] = content['hashtags'][:platform_spec['max_hashtags']]

            return content

        except Exception as e:
            logger.error(f"Error parsing AI response: {str(e)}")
            return {
                "caption": "Check out this amazing product!",
                "hashtags": ["product", "shopping", "deals"],
                "cta": "Shop now!"
            }

    async def _generate_image_suggestions(
        self,
        product: Dict[str, Any],
        platform: str,
        content_type: str
    ) -> List[str]:
        """Generate image composition suggestions."""
        suggestions = []

        platform_spec = PLATFORM_SPECS.get(platform, {})
        aspect_ratios = platform_spec.get("image_aspect_ratios", ["1:1"])

        base_suggestions = [
            f"Product hero shot in {aspect_ratios[0]} aspect ratio",
            "Lifestyle image showing product in use",
            "Flat lay composition with complementary items",
            "Close-up detail shot highlighting key features"
        ]

        if content_type == "promotion":
            base_suggestions.extend([
                "Sale badge overlay on product image",
                "Before/after comparison if applicable",
                "Bundle or collection showcase"
            ])
        elif content_type == "story":
            base_suggestions.extend([
                "Behind-the-scenes creation process",
                "User-generated content style",
                "Authentic, candid product moments"
            ])
        elif content_type == "educational":
            base_suggestions.extend([
                "Step-by-step usage demonstration",
                "Infographic-style feature breakdown",
                "Comparison with similar products"
            ])

        # Add platform-specific suggestions
        if platform == "instagram" and platform_spec.get("supports_carousel"):
            suggestions.append("Multi-image carousel showing different angles")

        if platform == "tiktok":
            suggestions.append("Vertical video-style product reveal")

        return base_suggestions + suggestions

    async def _generate_video_suggestions(
        self,
        product: Dict[str, Any],
        platform: str,
        content_type: str
    ) -> List[str]:
        """Generate video content suggestions."""
        suggestions = []

        platform_spec = PLATFORM_SPECS.get(platform, {})

        if not platform_spec.get("supports_video"):
            return suggestions

        base_suggestions = [
            "Product unboxing experience",
            "Quick product demonstration",
            "360-degree product rotation",
            "Time-lapse setup or assembly"
        ]

        if content_type == "promotion":
            base_suggestions.extend([
                "Flash sale countdown timer",
                "Limited-time offer announcement",
                "Customer testimonial compilation"
            ])
        elif content_type == "educational":
            base_suggestions.extend([
                "How-to tutorial featuring the product",
                "Tips and tricks for best usage",
                "Common mistakes to avoid"
            ])

        # Platform-specific video suggestions
        if platform == "tiktok":
            suggestions.extend([
                "Trending audio with product showcase",
                "Quick transformation or reveal",
                "Dance or movement incorporating product"
            ])
        elif platform == "instagram":
            suggestions.extend([
                "Reels-style quick cuts and transitions",
                "Story highlights compilation",
                "IGTV longer-form content"
            ])

        return base_suggestions + suggestions

    async def _get_optimal_posting_times(
        self,
        user_id: str,
        platform: str
    ) -> List[str]:
        """Get optimal posting times for user and platform."""
        try:
            # This would analyze user's audience and engagement patterns
            # For now, return general best practices

            optimal_times = {
                "instagram": [
                    "Tuesday 11:00 AM",
                    "Wednesday 2:00 PM",
                    "Friday 5:00 PM"
                ],
                "facebook": [
                    "Tuesday 9:00 AM",
                    "Wednesday 1:00 PM",
                    "Thursday 3:00 PM"
                ],
                "twitter": [
                    "Monday 9:00 AM",
                    "Tuesday 10:00 AM",
                    "Wednesday 9:00 AM"
                ],
                "linkedin": [
                    "Tuesday 10:00 AM",
                    "Wednesday 12:00 PM",
                    "Thursday 2:00 PM"
                ],
                "tiktok": [
                    "Tuesday 6:00 AM",
                    "Thursday 7:00 AM",
                    "Friday 9:00 AM"
                ]
            }

            return optimal_times.get(platform, ["Tuesday 12:00 PM"])

        except Exception as e:
            logger.error(f"Error getting optimal posting times: {str(e)}")
            return ["Tuesday 12:00 PM"]

    async def _predict_engagement(
        self,
        content: Dict[str, Any],
        platform: str,
        product: Dict[str, Any]
    ) -> Dict[str, float]:
        """Predict engagement metrics for the content."""
        try:
            # This would use ML models to predict engagement
            # For now, return estimated values based on content analysis

            base_engagement = {
                "likes": 0.05,  # 5% of followers
                "comments": 0.01,  # 1% of followers
                "shares": 0.005,  # 0.5% of followers
                "saves": 0.02,  # 2% of followers (Instagram)
                "clicks": 0.03  # 3% of followers
            }

            # Adjust based on content quality indicators
            caption_length = len(content.get("caption", ""))
            hashtag_count = len(content.get("hashtags", []))
            has_cta = bool(content.get("cta", ""))

            # Platform-specific adjustments
            platform_multipliers = {
                "instagram": 1.2,
                "facebook": 0.8,
                "twitter": 1.0,
                "linkedin": 0.6,
                "tiktok": 2.0
            }

            multiplier = platform_multipliers.get(platform, 1.0)

            # Content quality adjustments
            if caption_length > 100:  # Longer captions tend to perform better
                multiplier *= 1.1
            if hashtag_count >= 5:  # Good hashtag usage
                multiplier *= 1.15
            if has_cta:  # Clear call-to-action
                multiplier *= 1.2

            # Apply multipliers
            predictions = {}
            for metric, base_rate in base_engagement.items():
                predictions[metric] = round(base_rate * multiplier, 4)

            return predictions

        except Exception as e:
            logger.error(f"Error predicting engagement: {str(e)}")
            return {
                "likes": 0.05,
                "comments": 0.01,
                "shares": 0.005,
                "saves": 0.02,
                "clicks": 0.03
            }


# Create singleton instance
ai_content_service = AIContentService()
