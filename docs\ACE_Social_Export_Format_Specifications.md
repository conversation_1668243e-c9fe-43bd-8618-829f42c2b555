# ACE Social Export Format Specifications

## Overview

This document provides comprehensive technical specifications for all export formats supported by the ACE Social platform. These specifications ensure consistency, reliability, and compatibility across all report types and export formats.

## Supported Export Formats

### 1. CSV (Comma-Separated Values)
- **MIME Type**: `text/csv`
- **File Extension**: `.csv`
- **Encoding**: UTF-8 with BOM
- **Line Endings**: CRLF (`\r\n`)

### 2. PDF (Portable Document Format)
- **MIME Type**: `application/pdf`
- **File Extension**: `.pdf`
- **Version**: PDF 1.4 or higher
- **Compression**: Enabled

### 3. Excel/XLSX (Microsoft Excel)
- **MIME Type**: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- **File Extension**: `.xlsx`
- **Version**: Office Open XML format
- **Compatibility**: Excel 2007+

### 4. <PERSON><PERSON><PERSON> (JavaScript Object Notation)
- **MIME Type**: `application/json`
- **File Extension**: `.json`
- **Encoding**: UTF-8
- **Schema**: JSON Schema Draft 2020-12

## CSV Format Specifications

### File Structure

```csv
# Metadata Section (Optional)
"Report Title","Content Performance Analytics"
"Generated At","2024-01-15 14:30:00 UTC"
"Report Period","2024-01-01 to 2024-01-31"
"User","John Doe (<EMAIL>)"
"Company","Acme Corporation"
""
# Data Section
"Column Header 1","Column Header 2","Column Header 3"
"Data Row 1 Col 1","Data Row 1 Col 2","Data Row 1 Col 3"
"Data Row 2 Col 1","Data Row 2 Col 2","Data Row 2 Col 3"
```

### Data Type Formatting

#### Dates and Times
```javascript
// ISO 8601 format with UTC timezone
const formatDate = (date) => {
    return new Date(date).toISOString().replace('T', ' ').replace('Z', ' UTC');
};
// Example: "2024-01-15 14:30:00 UTC"
```

#### Numbers
```javascript
// Integers: No formatting, raw numbers
const formatInteger = (value) => String(value);
// Example: "1234"

// Decimals: Up to 2 decimal places, no thousands separators
const formatDecimal = (value) => Number(value).toFixed(2);
// Example: "1234.56"

// Percentages: Decimal format with % suffix
const formatPercentage = (value) => (Number(value) * 100).toFixed(2) + '%';
// Example: "15.75%"

// Currency: Numeric value without currency symbol
const formatCurrency = (value) => Number(value).toFixed(2);
// Example: "1234.56" (not "$1,234.56")
```

#### Text Escaping
```javascript
function escapeCSVValue(value) {
    if (value === null || value === undefined) {
        return '';
    }
    
    const stringValue = String(value);
    
    // Escape if contains comma, quote, newline, or carriage return
    if (stringValue.includes(',') || 
        stringValue.includes('"') || 
        stringValue.includes('\n') || 
        stringValue.includes('\r')) {
        return `"${stringValue.replace(/"/g, '""')}"`;
    }
    
    return stringValue;
}
```

### Column Naming Conventions

```javascript
const columnNamingRules = {
    // Use descriptive, human-readable names
    good: ["Content Title", "Engagement Rate", "Published Date"],
    bad: ["title", "eng_rate", "pub_dt"],
    
    // Include units where applicable
    withUnits: ["Duration (seconds)", "Amount (USD)", "Size (MB)"],
    
    // Use consistent capitalization
    capitalization: "Title Case", // "Content Performance Rate"
    
    // Avoid special characters except parentheses for units
    specialChars: "Avoid: @#$%^&*+=[]{}|\\:;\"'<>?,./`~",
    allowed: "Parentheses for units: (USD), (%), (seconds)"
};
```

## PDF Format Specifications

### Page Layout

```javascript
const pdfConfig = {
    // Page dimensions
    format: 'A4', // 210 × 297 mm
    orientation: 'portrait', // or 'landscape' for wide tables
    unit: 'mm',
    
    // Margins
    margins: {
        top: 20,
        right: 15,
        bottom: 20,
        left: 15
    },
    
    // Font settings
    defaultFont: 'Inter',
    fallbackFonts: ['Arial', 'Helvetica', 'sans-serif'],
    
    // Compression
    compress: true,
    
    // Metadata
    title: 'ACE Social Report',
    author: 'ACE Social Platform',
    subject: 'Analytics Report',
    creator: 'ACE Social Export Engine'
};
```

### Typography Specifications

```javascript
const pdfTypography = {
    // Font sizes (in points)
    title: 20,
    sectionTitle: 16,
    subsectionTitle: 14,
    bodyText: 11,
    caption: 9,
    footer: 8,
    
    // Line heights
    titleLineHeight: 1.2,
    bodyLineHeight: 1.4,
    captionLineHeight: 1.3,
    
    // Colors (RGB values)
    colors: {
        primary: [78, 64, 197],      // ACE Purple
        text: [26, 26, 46],          // Text Primary
        secondary: [74, 74, 104],    // Text Secondary
        muted: [170, 170, 170],      // Text Muted
        success: [76, 175, 80],      // Success Green
        warning: [255, 152, 0],      // Warning Orange
        error: [244, 67, 54]         // Error Red
    }
};
```

### Multi-page Handling

```javascript
function generatePDFReport(data, template) {
    const pdf = new jsPDF(pdfConfig);
    
    // Track page position
    let currentY = pdfConfig.margins.top + 20; // Start after header
    const pageHeight = pdf.internal.pageSize.height;
    const marginBottom = pdfConfig.margins.bottom;
    const usableHeight = pageHeight - pdfConfig.margins.top - marginBottom;
    
    // Add content with page breaks
    data.sections.forEach((section, index) => {
        const sectionHeight = calculateSectionHeight(section);
        
        // Check if section fits on current page
        if (currentY + sectionHeight > pageHeight - marginBottom) {
            pdf.addPage();
            addPDFHeader(pdf, data.reportTitle, data.generatedAt);
            currentY = pdfConfig.margins.top + 20;
        }
        
        currentY = addPDFSection(pdf, section, currentY);
    });
    
    // Add page numbers and footers
    addPDFFooters(pdf);
    
    return pdf;
}
```

## Excel/XLSX Format Specifications

### Workbook Structure

```javascript
const excelStructure = {
    workbook: {
        // Workbook properties
        title: 'ACE Social Report',
        subject: 'Analytics Data',
        creator: 'ACE Social Platform',
        created: new Date(),
        
        // Worksheets
        worksheets: [
            {
                name: 'Summary',
                maxLength: 31, // Excel limit
                data: summaryData,
                formatting: 'summary'
            },
            {
                name: 'Detailed Data',
                data: detailedData,
                formatting: 'data'
            },
            {
                name: 'Charts',
                data: chartData,
                formatting: 'charts'
            }
        ]
    }
};
```

### Cell Formatting Standards

```javascript
const excelStyles = {
    // Header row styling
    header: {
        font: { 
            bold: true, 
            color: { rgb: '4E40C5' }, 
            size: 12,
            name: 'Inter'
        },
        fill: { 
            fgColor: { rgb: 'F0F4FF' } 
        },
        border: {
            top: { style: 'thin', color: { rgb: '4E40C5' } },
            bottom: { style: 'thin', color: { rgb: '4E40C5' } },
            left: { style: 'thin', color: { rgb: 'E0E0E0' } },
            right: { style: 'thin', color: { rgb: 'E0E0E0' } }
        },
        alignment: { 
            horizontal: 'center', 
            vertical: 'center',
            wrapText: true
        }
    },
    
    // Data cell styling
    data: {
        font: { 
            color: { rgb: '4A4A68' }, 
            size: 11,
            name: 'Inter'
        },
        alignment: { 
            horizontal: 'left', 
            vertical: 'center' 
        },
        border: {
            bottom: { style: 'thin', color: { rgb: 'E0E0E0' } }
        }
    },
    
    // Number formatting
    numberFormats: {
        currency: '$#,##0.00',
        percentage: '0.00%',
        integer: '#,##0',
        decimal: '#,##0.00',
        date: 'yyyy-mm-dd',
        datetime: 'yyyy-mm-dd hh:mm:ss',
        time: 'hh:mm:ss'
    },
    
    // Conditional formatting for performance indicators
    conditionalFormatting: {
        positive: {
            font: { color: { rgb: '4CAF50' } },
            fill: { fgColor: { rgb: 'E8F5E8' } }
        },
        negative: {
            font: { color: { rgb: 'F44336' } },
            fill: { fgColor: { rgb: 'FFEBEE' } }
        },
        neutral: {
            font: { color: { rgb: 'AAAAAA' } }
        }
    }
};
```

### Column Width Auto-sizing

```javascript
function autoSizeColumns(worksheet, data) {
    const columnWidths = {};
    
    // Calculate optimal column widths
    data.forEach((row, rowIndex) => {
        Object.keys(row).forEach((key, colIndex) => {
            const cellValue = String(row[key] || '');
            const cellWidth = Math.min(
                Math.max(cellValue.length * 1.2, 10), // Min 10, scale by 1.2
                50 // Max width of 50 characters
            );
            
            columnWidths[colIndex] = Math.max(
                columnWidths[colIndex] || 0,
                cellWidth
            );
        });
    });
    
    // Apply column widths
    Object.keys(columnWidths).forEach(colIndex => {
        worksheet.getColumn(parseInt(colIndex) + 1).width = columnWidths[colIndex];
    });
}
```

## JSON Format Specifications

### Schema Definition

```json
{
    "$schema": "https://json-schema.org/draft/2020-12/schema",
    "$id": "https://acesocial.com/schemas/report-export.json",
    "title": "ACE Social Report Export",
    "description": "Standard schema for ACE Social report exports",
    "type": "object",
    "properties": {
        "report_metadata": {
            "type": "object",
            "properties": {
                "report_id": {
                    "type": "string",
                    "format": "uuid",
                    "description": "Unique identifier for the report"
                },
                "report_type": {
                    "type": "string",
                    "enum": ["analytics", "financial", "competitor_analysis", "content_performance", "icp_metrics"],
                    "description": "Type of report being exported"
                },
                "generated_at": {
                    "type": "string",
                    "format": "date-time",
                    "description": "ISO 8601 timestamp when report was generated"
                },
                "user_id": {
                    "type": "string",
                    "description": "ID of the user who generated the report"
                },
                "company_name": {
                    "type": "string",
                    "description": "Name of the company the report belongs to"
                },
                "period_start": {
                    "type": "string",
                    "format": "date-time",
                    "description": "Start date of the report period"
                },
                "period_end": {
                    "type": "string",
                    "format": "date-time",
                    "description": "End date of the report period"
                },
                "export_format": {
                    "type": "string",
                    "enum": ["json", "csv", "pdf", "xlsx"],
                    "description": "Format of the exported report"
                },
                "version": {
                    "type": "string",
                    "pattern": "^\\d+\\.\\d+$",
                    "default": "1.0",
                    "description": "Schema version"
                }
            },
            "required": ["report_id", "report_type", "generated_at", "user_id"]
        },
        "summary": {
            "type": "object",
            "properties": {
                "total_records": {
                    "type": "integer",
                    "minimum": 0,
                    "description": "Total number of data records in the report"
                },
                "key_metrics": {
                    "type": "object",
                    "description": "Summary of key performance metrics"
                },
                "performance_indicators": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "metric_name": { "type": "string" },
                            "current_value": { "type": "number" },
                            "previous_value": { "type": "number" },
                            "change_percentage": { "type": "number" },
                            "trend": { "type": "string", "enum": ["up", "down", "neutral"] }
                        }
                    }
                }
            }
        },
        "data": {
            "type": "array",
            "items": {
                "type": "object",
                "description": "Individual data records"
            },
            "description": "Main data payload of the report"
        },
        "charts": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "chart_id": { "type": "string" },
                    "chart_type": { "type": "string", "enum": ["line", "bar", "pie", "area", "scatter"] },
                    "title": { "type": "string" },
                    "data": { "type": "array" },
                    "config": { "type": "object" }
                },
                "required": ["chart_id", "chart_type", "title", "data"]
            },
            "description": "Chart data and configuration"
        }
    },
    "required": ["report_metadata", "data"]
}
```

### Field Naming Conventions

```javascript
const namingConventions = {
    // Use snake_case for all field names
    casing: "snake_case",
    examples: {
        good: ["user_id", "created_at", "engagement_rate", "total_revenue"],
        bad: ["userId", "createdAt", "engagementRate", "totalRevenue"]
    },
    
    // Include units in field names where applicable
    withUnits: {
        time: ["duration_seconds", "response_time_ms"],
        money: ["amount_usd", "revenue_eur"],
        size: ["file_size_bytes", "image_width_px"],
        rate: ["engagement_rate_percent", "conversion_rate_decimal"]
    },
    
    // Use consistent date field suffixes
    dateFields: {
        timestamps: "_at", // created_at, updated_at, published_at
        dates: "_date", // birth_date, start_date, end_date
        times: "_time" // start_time, end_time
    },
    
    // Boolean field prefixes
    booleanPrefixes: ["is_", "has_", "can_", "should_", "will_"],
    examples: ["is_active", "has_premium", "can_edit", "should_notify"],
    
    // Collection field suffixes
    collections: {
        arrays: "_list", // tag_list, category_list
        counts: "_count", // follower_count, post_count
        totals: "_total" // revenue_total, engagement_total
    }
};
```

## File Naming Conventions

### Standard Naming Pattern

```javascript
const generateFileName = (reportType, userId, timestamp, format) => {
    const datePart = new Date(timestamp).toISOString().split('T')[0]; // YYYY-MM-DD
    const timePart = new Date(timestamp).toISOString().split('T')[1].split('.')[0].replace(/:/g, '-'); // HH-MM-SS
    
    return `ace-social-${reportType}-${userId}-${datePart}-${timePart}.${format}`;
};

// Examples:
// ace-social-analytics-user123-2024-01-15-14-30-00.pdf
// ace-social-financial-user456-2024-01-15-14-30-00.xlsx
// ace-social-competitor-analysis-user789-2024-01-15-14-30-00.csv
```

### File Size Limits

```javascript
const fileSizeLimits = {
    csv: '50MB',      // Large datasets
    pdf: '25MB',      // Rich formatting with images
    xlsx: '100MB',    // Complex spreadsheets with multiple sheets
    json: '50MB'      // Structured data with metadata
};
```

## Error Handling and Validation

### Data Validation Rules

```javascript
const validationRules = {
    // Required fields validation
    requiredFields: ['report_id', 'report_type', 'generated_at', 'user_id'],
    
    // Data type validation
    dataTypes: {
        dates: /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z$/,
        emails: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        uuids: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
        numbers: /^-?\d+(\.\d+)?$/,
        percentages: /^-?\d+(\.\d+)?%?$/
    },
    
    // Value range validation
    ranges: {
        percentages: { min: 0, max: 100 },
        ratings: { min: 1, max: 5 },
        engagement_rates: { min: 0, max: 100 }
    }
};
```

### Error Response Format

```json
{
    "error": {
        "code": "EXPORT_VALIDATION_ERROR",
        "message": "Data validation failed",
        "details": [
            {
                "field": "engagement_rate",
                "value": "150%",
                "error": "Value exceeds maximum allowed percentage (100%)"
            }
        ],
        "timestamp": "2024-01-15T14:30:00Z",
        "request_id": "req_123456789"
    }
}
```

## Performance Optimization

### Large Dataset Handling

```javascript
const optimizationStrategies = {
    // Streaming for large CSV exports
    csvStreaming: {
        chunkSize: 1000, // Process 1000 rows at a time
        memoryLimit: '512MB',
        useStreaming: true
    },
    
    // PDF optimization
    pdfOptimization: {
        imageCompression: 0.8, // 80% quality
        fontSubsetting: true,
        objectCompression: true
    },
    
    // Excel optimization
    excelOptimization: {
        sharedStrings: true, // Reduce file size for repeated text
        compression: 'fast',
        maxRowsPerSheet: 1048576 // Excel limit
    },
    
    // JSON optimization
    jsonOptimization: {
        prettyPrint: false, // Minified JSON for production
        nullValueHandling: 'omit', // Omit null values to reduce size
        numberPrecision: 2 // Limit decimal places
    }
};
```

---

*This specification document is maintained by the ACE Social Engineering team and is updated with each platform release.*
