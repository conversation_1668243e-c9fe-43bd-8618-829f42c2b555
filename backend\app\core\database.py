"""
MongoDB database configuration and connection management for the Social Media Platform.
@since 2024-1-1 to 2025-25-7
"""

import asyncio
from typing import Optional, Dict, Any
import logging
import os

# Handle optional imports gracefully
try:
    from motor.motor_asyncio import AsyncIOMotorClient
    MOTOR_AVAILABLE = True
except ImportError:
    MOTOR_AVAILABLE = False
    AsyncIOMotorClient = None

# Note: pymongo is a dependency of motor, so we don't need to import it separately

# Configure logging
logger = logging.getLogger(__name__)

class MongoDBConfig:
    """MongoDB configuration settings."""

    def __init__(self):
        # Get MongoDB connection settings from environment variables
        self.MONGODB_URL = os.getenv(
            "MONGODB_URL",
            "mongodb://localhost:27017"
        )
        self.DATABASE_NAME = os.getenv(
            "DATABASE_NAME",
            "social_media_platform"
        )
        self.MAX_CONNECTIONS = int(os.getenv("MONGODB_MAX_CONNECTIONS", "100"))
        self.MIN_CONNECTIONS = int(os.getenv("MONGODB_MIN_CONNECTIONS", "10"))
        self.CONNECTION_TIMEOUT = int(os.getenv("MONGODB_CONNECTION_TIMEOUT", "10000"))
        self.SERVER_SELECTION_TIMEOUT = int(os.getenv("MONGODB_SERVER_SELECTION_TIMEOUT", "5000"))

# Global configuration instance
config = MongoDBConfig()

class DatabaseManager:
    """MongoDB connection and database manager."""

    def __init__(self):
        self.client: Optional[Any] = None
        self.database: Optional[Any] = None
        self._is_connected = False

    async def connect(self) -> None:
        """Establish connection to MongoDB."""
        if not MOTOR_AVAILABLE or AsyncIOMotorClient is None:
            logger.error("Motor (MongoDB async driver) is not installed. Please install it with: pip install motor")
            raise ImportError("Motor is required for MongoDB connection")

        try:
            # Create MongoDB client with connection pooling
            self.client = AsyncIOMotorClient(
                config.MONGODB_URL,
                maxPoolSize=config.MAX_CONNECTIONS,
                minPoolSize=config.MIN_CONNECTIONS,
                connectTimeoutMS=config.CONNECTION_TIMEOUT,
                serverSelectionTimeoutMS=config.SERVER_SELECTION_TIMEOUT,
                retryWrites=True,
                retryReads=True
            )

            # Get database instance
            if self.client:
                self.database = self.client[config.DATABASE_NAME]

                # Test the connection
                await self.client.admin.command('ping')
                self._is_connected = True

                logger.info(f"Successfully connected to MongoDB: {config.DATABASE_NAME}")

        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            self._is_connected = False
            raise

    async def disconnect(self) -> None:
        """Close MongoDB connection."""
        if self.client:
            self.client.close()
            self._is_connected = False
            logger.info("Disconnected from MongoDB")

    def get_database(self) -> Any:
        """Get the database instance."""
        if not self._is_connected or not self.database:
            raise RuntimeError("Database not connected. Call connect() first.")
        return self.database

    def get_collection(self, collection_name: str) -> Any:
        """Get a specific collection."""
        database = self.get_database()
        return database[collection_name]

    async def health_check(self) -> Dict[str, Any]:
        """Perform database health check."""
        try:
            if not self._is_connected or not self.client:
                return {
                    "status": "disconnected",
                    "database_name": config.DATABASE_NAME,
                    "error": "Not connected to database"
                }

            # Test connection with ping
            await self.client.admin.command('ping')

            # Get server info
            server_info = await self.client.server_info()

            return {
                "status": "healthy",
                "database_name": config.DATABASE_NAME,
                "mongodb_version": server_info.get("version", "unknown"),
                "connection_pool_size": config.MAX_CONNECTIONS,
                "is_connected": self._is_connected
            }

        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                "status": "unhealthy",
                "database_name": config.DATABASE_NAME,
                "error": str(e),
                "is_connected": self._is_connected
            }

# Global database manager instance
db_manager = DatabaseManager()

# Dependency for FastAPI
async def get_database() -> Any:
    """
    Dependency to get database instance for FastAPI routes.

    Returns:
        MongoDB database instance
    """
    return db_manager.get_database()

async def get_collection(collection_name: str) -> Any:
    """
    Dependency to get a specific collection.

    Args:
        collection_name: Name of the collection

    Returns:
        Collection instance
    """
    return db_manager.get_collection(collection_name)

# Legacy compatibility function for existing code
def get_db():
    """
    Legacy compatibility function.
    For new code, use get_database() instead.
    """
    return db_manager.get_database()

# Database initialization and cleanup
async def init_database():
    """Initialize database connection."""
    await db_manager.connect()

async def close_database():
    """Close database connection."""
    await db_manager.disconnect()

# Health check function
async def check_database_health() -> bool:
    """
    Simple health check function.

    Returns:
        bool: True if database is healthy, False otherwise
    """
    try:
        health_status = await db_manager.health_check()
        return health_status["status"] == "healthy"
    except Exception:
        return False
