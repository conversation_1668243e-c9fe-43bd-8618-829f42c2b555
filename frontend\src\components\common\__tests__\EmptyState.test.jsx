// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import EmptyState from '../EmptyState';

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      text: {
        primary: '#333333',
        secondary: '#666666',
      },
      divider: '#E0E0E0',
      background: {
        paper: '#FFFFFF',
      },
    },
    spacing: (factor) => `${8 * factor}px`,
    breakpoints: {
      down: () => '@media (max-width: 600px)',
    },
  });
  
  return (
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  );
};

describe('EmptyState', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders with default props', () => {
    render(
      <TestWrapper>
        <EmptyState />
      </TestWrapper>
    );

    expect(screen.getByText('No data found')).toBeInTheDocument();
    expect(screen.getByText('There is no data to display at the moment.')).toBeInTheDocument();
  });

  test('renders custom title and description', () => {
    render(
      <TestWrapper>
        <EmptyState
          title="Custom Title"
          description="Custom description text"
        />
      </TestWrapper>
    );

    expect(screen.getByText('Custom Title')).toBeInTheDocument();
    expect(screen.getByText('Custom description text')).toBeInTheDocument();
  });

  test('renders with custom illustration', () => {
    render(
      <TestWrapper>
        <EmptyState
          illustration="/test-image.png"
          title="Test Title"
        />
      </TestWrapper>
    );

    const image = screen.getByAltText('Empty state illustration');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', '/test-image.png');
  });

  test('renders action button and handles click', async () => {
    const user = userEvent.setup();
    const onAction = vi.fn();

    render(
      <TestWrapper>
        <EmptyState
          actionText="Create New"
          onAction={onAction}
        />
      </TestWrapper>
    );

    const actionButton = screen.getByText('Create New');
    expect(actionButton).toBeInTheDocument();

    await user.click(actionButton);
    expect(onAction).toHaveBeenCalledTimes(1);
  });

  test('renders secondary action button', async () => {
    const user = userEvent.setup();
    const onSecondaryAction = vi.fn();

    render(
      <TestWrapper>
        <EmptyState
          secondaryActionText="Learn More"
          onSecondaryAction={onSecondaryAction}
        />
      </TestWrapper>
    );

    const secondaryButton = screen.getByText('Learn More');
    expect(secondaryButton).toBeInTheDocument();

    await user.click(secondaryButton);
    expect(onSecondaryAction).toHaveBeenCalledTimes(1);
  });

  test('renders steps when provided', () => {
    const steps = [
      'Step 1: Create an account',
      'Step 2: Set up your profile',
      'Step 3: Start creating content'
    ];

    render(
      <TestWrapper>
        <EmptyState steps={steps} />
      </TestWrapper>
    );

    expect(screen.getByText('Get started with these steps:')).toBeInTheDocument();
    steps.forEach(step => {
      expect(screen.getByText(step)).toBeInTheDocument();
    });
  });

  test('renders without paper wrapper when withPaper is false', () => {
    const { container } = render(
      <TestWrapper>
        <EmptyState withPaper={false} />
      </TestWrapper>
    );

    // Should not have Paper component wrapper
    expect(container.querySelector('.MuiPaper-root')).not.toBeInTheDocument();
  });

  test('renders with paper wrapper by default', () => {
    const { container } = render(
      <TestWrapper>
        <EmptyState />
      </TestWrapper>
    );

    // Should have Paper component wrapper
    expect(container.querySelector('.MuiPaper-root')).toBeInTheDocument();
  });

  test('handles different empty state types', () => {
    const { rerender } = render(
      <TestWrapper>
        <EmptyState type="no_results" />
      </TestWrapper>
    );

    expect(screen.getByText('No results found')).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <EmptyState type="no_content" />
      </TestWrapper>
    );

    expect(screen.getByText('No content yet')).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <EmptyState type="first_time" />
      </TestWrapper>
    );

    expect(screen.getByText('Welcome! Let\'s get started')).toBeInTheDocument();
  });

  test('shows loading state', () => {
    render(
      <TestWrapper>
        <EmptyState loading={true} />
      </TestWrapper>
    );

    // Should show skeleton loaders
    expect(screen.getByTestId('skeleton-circular') || document.querySelector('.MuiSkeleton-circular')).toBeInTheDocument();
  });

  test('shows progress indicator when enabled', () => {
    render(
      <TestWrapper>
        <EmptyState
          showProgress={true}
          progressValue={50}
        />
      </TestWrapper>
    );

    expect(screen.getByText('50% complete')).toBeInTheDocument();
  });

  test('handles analytics tracking', async () => {
    const user = userEvent.setup();
    const onAnalytics = vi.fn();
    const onAction = vi.fn();

    render(
      <TestWrapper>
        <EmptyState
          actionText="Test Action"
          onAction={onAction}
          enableAnalytics={true}
          onAnalytics={onAnalytics}
        />
      </TestWrapper>
    );

    const actionButton = screen.getByText('Test Action');
    await user.click(actionButton);

    expect(onAnalytics).toHaveBeenCalledWith(
      expect.objectContaining({
        component: 'EmptyState',
        action: 'primary_clicked'
      })
    );
  });

  test('renders custom actions', async () => {
    const user = userEvent.setup();
    const customAction = vi.fn();
    const customActions = [
      {
        text: 'Custom Action',
        onClick: customAction,
        variant: 'outlined',
        color: 'primary'
      }
    ];

    render(
      <TestWrapper>
        <EmptyState customActions={customActions} />
      </TestWrapper>
    );

    const customButton = screen.getByText('Custom Action');
    expect(customButton).toBeInTheDocument();

    await user.click(customButton);
    expect(customAction).toHaveBeenCalledTimes(1);
  });

  test('renders filter button when enabled', async () => {
    const user = userEvent.setup();
    const onFilter = vi.fn();

    render(
      <TestWrapper>
        <EmptyState
          enableFilters={true}
          onFilter={onFilter}
        />
      </TestWrapper>
    );

    const filterButton = screen.getByText('Filters');
    expect(filterButton).toBeInTheDocument();

    await user.click(filterButton);
    expect(onFilter).toHaveBeenCalledTimes(1);
  });

  test('renders refresh button when enabled', async () => {
    const user = userEvent.setup();
    const onRefresh = vi.fn();

    render(
      <TestWrapper>
        <EmptyState
          showRefresh={true}
          onRefresh={onRefresh}
        />
      </TestWrapper>
    );

    const refreshButton = screen.getByLabelText('Refresh');
    expect(refreshButton).toBeInTheDocument();

    await user.click(refreshButton);
    expect(onRefresh).toHaveBeenCalledTimes(1);
  });

  test('handles compact variant', () => {
    render(
      <TestWrapper>
        <EmptyState
          variant="compact"
          title="Compact Title"
        />
      </TestWrapper>
    );

    // Title should be h6 for compact variant
    const title = screen.getByText('Compact Title');
    expect(title).toBeInTheDocument();
  });

  test('handles spacious variant', () => {
    render(
      <TestWrapper>
        <EmptyState
          variant="spacious"
          title="Spacious Title"
        />
      </TestWrapper>
    );

    const title = screen.getByText('Spacious Title');
    expect(title).toBeInTheDocument();
  });

  test('handles illustration error gracefully', () => {
    const onAnalytics = vi.fn();

    render(
      <TestWrapper>
        <EmptyState
          illustration="/broken-image.png"
          enableAnalytics={true}
          onAnalytics={onAnalytics}
        />
      </TestWrapper>
    );

    const image = screen.getByAltText('Empty state illustration');
    
    // Simulate image error
    fireEvent.error(image);

    expect(onAnalytics).toHaveBeenCalledWith(
      expect.objectContaining({
        action: 'illustration_error',
        illustration: '/broken-image.png'
      })
    );
  });

  test('applies correct ARIA attributes', () => {
    render(
      <TestWrapper>
        <EmptyState
          ariaLabel="Empty state container"
          role="status"
        />
      </TestWrapper>
    );

    const container = screen.getByRole('status');
    expect(container).toHaveAttribute('aria-label', 'Empty state container');
  });

  test('handles mobile responsive design', () => {
    // Mock mobile breakpoint
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation(query => ({
        matches: query.includes('max-width'),
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      })),
    });

    render(
      <TestWrapper>
        <EmptyState title="Mobile Test" />
      </TestWrapper>
    );

    expect(screen.getByText('Mobile Test')).toBeInTheDocument();
  });

  test('renders contextual icons based on type', () => {
    const { rerender } = render(
      <TestWrapper>
        <EmptyState type="no_results" />
      </TestWrapper>
    );

    // Should render search icon for no_results type
    expect(document.querySelector('[data-testid="SearchIcon"]')).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <EmptyState type="no_content" />
      </TestWrapper>
    );

    // Should render add icon for no_content type
    expect(document.querySelector('[data-testid="AddIcon"]')).toBeInTheDocument();
  });

  test('handles animation timeouts', async () => {
    render(
      <TestWrapper>
        <EmptyState illustration="/test-image.png" />
      </TestWrapper>
    );

    // Wait for fade animation
    await waitFor(() => {
      expect(screen.getByAltText('Empty state illustration')).toBeInTheDocument();
    }, { timeout: 1000 });
  });
});
