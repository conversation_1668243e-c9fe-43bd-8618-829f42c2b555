"""
E-commerce ICP generation service for creating customer personas based on product data.
@since 2024-1-1 to 2025-25-7
"""

import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
from bson import ObjectId
from decimal import Decimal

from app.utils.openai_client import generate_content
from app.models.icp import ICP, Demographics, DecisionMaker, PainPoint, Goal, Objection, ContentPreference
from app.models.user import PyObjectId
from app.models.ecommerce import SyncedProduct
from app.services.ecommerce_service import ecommerce_service
from app.db.mongodb import get_database

logger = logging.getLogger(__name__)


class EcommerceICPGenerator:
    """
    Service for generating ICPs based on e-commerce product data.
    """
    
    async def generate_product_icps(
        self,
        user_id: str,
        store_id: str,
        product_ids: Optional[List[str]] = None,
        count: int = 3
    ) -> List[ICP]:
        """
        Generate ICPs based on product data from an e-commerce store.
        
        Args:
            user_id: User ID
            store_id: Store ID
            product_ids: Specific product IDs to analyze (optional)
            count: Number of ICPs to generate
            
        Returns:
            List of generated ICPs
        """
        try:
            # Get store products
            if product_ids:
                products = await self._get_specific_products(store_id, user_id, product_ids)
            else:
                # Get a sample of products for analysis
                products_data = await ecommerce_service.get_store_products(
                    store_id, user_id, limit=50
                )
                products = products_data.get("products", [])
            
            if not products:
                raise ValueError("No products found for ICP generation")
            
            # Analyze products to extract insights
            product_insights = self._analyze_products(products)
            
            # Generate ICPs using AI
            icps = await self._generate_icps_from_products(
                product_insights, count, user_id, store_id
            )
            
            return icps
            
        except Exception as e:
            logger.error(f"Failed to generate product ICPs: {str(e)}")
            raise
    
    async def generate_category_icps(
        self,
        user_id: str,
        store_id: str,
        category: str,
        count: int = 3
    ) -> List[ICP]:
        """
        Generate ICPs for a specific product category.
        
        Args:
            user_id: User ID
            store_id: Store ID
            category: Product category
            count: Number of ICPs to generate
            
        Returns:
            List of generated ICPs
        """
        try:
            # Get products in the category
            products_data = await ecommerce_service.get_store_products(
                store_id, user_id, limit=100
            )
            products = products_data.get("products", [])
            
            # Filter by category
            category_products = [
                p for p in products 
                if p.category and category.lower() in p.category.lower()
            ]
            
            if not category_products:
                raise ValueError(f"No products found in category: {category}")
            
            # Analyze category-specific products
            product_insights = self._analyze_products(category_products)
            product_insights["category_focus"] = category
            
            # Generate category-specific ICPs
            icps = await self._generate_icps_from_products(
                product_insights, count, user_id, store_id
            )
            
            return icps
            
        except Exception as e:
            logger.error(f"Failed to generate category ICPs: {str(e)}")
            raise
    
    def _analyze_products(self, products: List[SyncedProduct]) -> Dict[str, Any]:
        """
        Analyze products to extract insights for ICP generation.
        
        Args:
            products: List of products to analyze
            
        Returns:
            Product insights dictionary
        """
        insights = {
            "total_products": len(products),
            "price_ranges": {},
            "categories": {},
            "vendors": {},
            "common_tags": {},
            "product_types": {},
            "average_price": 0,
            "price_distribution": {
                "budget": 0,      # < $50
                "mid_range": 0,   # $50-$200
                "premium": 0,     # $200-$500
                "luxury": 0       # > $500
            }
        }
        
        total_price = Decimal("0")
        
        for product in products:
            # Price analysis
            price = product.price
            total_price += price
            
            if price < 50:
                insights["price_distribution"]["budget"] += 1
            elif price < 200:
                insights["price_distribution"]["mid_range"] += 1
            elif price < 500:
                insights["price_distribution"]["premium"] += 1
            else:
                insights["price_distribution"]["luxury"] += 1
            
            # Category analysis
            if product.category:
                category = product.category
                insights["categories"][category] = insights["categories"].get(category, 0) + 1
            
            # Vendor analysis
            if product.vendor:
                vendor = product.vendor
                insights["vendors"][vendor] = insights["vendors"].get(vendor, 0) + 1
            
            # Tag analysis
            for tag in product.tags:
                insights["common_tags"][tag] = insights["common_tags"].get(tag, 0) + 1
            
            # Product type analysis
            if product.product_type:
                ptype = product.product_type
                insights["product_types"][ptype] = insights["product_types"].get(ptype, 0) + 1
        
        # Calculate averages
        if len(products) > 0:
            insights["average_price"] = float(total_price / len(products))
        
        # Get top items
        insights["top_categories"] = sorted(
            insights["categories"].items(), key=lambda x: x[1], reverse=True
        )[:5]
        
        insights["top_vendors"] = sorted(
            insights["vendors"].items(), key=lambda x: x[1], reverse=True
        )[:5]
        
        insights["top_tags"] = sorted(
            insights["common_tags"].items(), key=lambda x: x[1], reverse=True
        )[:10]
        
        return insights
    
    async def _generate_icps_from_products(
        self,
        product_insights: Dict[str, Any],
        count: int,
        user_id: str,
        store_id: str
    ) -> List[ICP]:
        """
        Generate ICPs using AI based on product insights.
        
        Args:
            product_insights: Analyzed product data
            count: Number of ICPs to generate
            user_id: User ID
            store_id: Store ID (used as service_id for compatibility)
            
        Returns:
            List of generated ICPs
        """
        # Create prompt for ICP generation
        prompt = self._create_product_icp_prompt(product_insights, count)
        
        # Generate ICPs using OpenAI
        response = await generate_content(prompt, max_tokens=3000, temperature=0.7)
        
        # Parse the response into ICP objects
        icps = self._parse_product_icp_response(
            response, PyObjectId(store_id), PyObjectId(user_id)
        )
        
        return icps
    
    def _create_product_icp_prompt(self, insights: Dict[str, Any], count: int) -> str:
        """
        Create a prompt for generating ICPs based on product insights.
        
        Args:
            insights: Product analysis insights
            count: Number of ICPs to generate
            
        Returns:
            Formatted prompt string
        """
        # Build product analysis summary
        top_categories = [cat[0] for cat in insights.get("top_categories", [])]
        top_vendors = [vendor[0] for vendor in insights.get("top_vendors", [])]
        top_tags = [tag[0] for tag in insights.get("top_tags", [])]
        
        price_dist = insights.get("price_distribution", {})
        avg_price = insights.get("average_price", 0)
        
        prompt = f"""
Based on the following e-commerce product analysis, generate {count} distinct Ideal Customer Profiles (ICPs) in JSON format.

PRODUCT ANALYSIS:
- Total Products: {insights.get("total_products", 0)}
- Average Price: ${avg_price:.2f}
- Price Distribution:
  * Budget (< $50): {price_dist.get("budget", 0)} products
  * Mid-range ($50-$200): {price_dist.get("mid_range", 0)} products
  * Premium ($200-$500): {price_dist.get("premium", 0)} products
  * Luxury (> $500): {price_dist.get("luxury", 0)} products

- Top Categories: {", ".join(top_categories[:3])}
- Top Brands: {", ".join(top_vendors[:3])}
- Common Features/Tags: {", ".join(top_tags[:5])}

Generate {count} ICPs that would be interested in these products. Each ICP should be returned as a JSON object with the following structure:

{{
  "name": "ICP Name",
  "description": "Brief description of this customer segment",
  "demographics": {{
    "age_range": "25-35",
    "gender": "Mixed",
    "income_range": "$50,000-$75,000",
    "education": "Bachelor's degree",
    "location": "Urban areas",
    "occupation": "Professional"
  }},
  "decision_maker": {{
    "title": "Primary decision maker",
    "department": "Relevant department",
    "influence_level": "High/Medium/Low",
    "decision_criteria": ["criteria1", "criteria2"]
  }},
  "pain_points": [
    {{"description": "Pain point 1", "severity": "High", "frequency": "Daily"}},
    {{"description": "Pain point 2", "severity": "Medium", "frequency": "Weekly"}}
  ],
  "goals": [
    {{"description": "Goal 1", "priority": "High", "timeline": "Short-term"}},
    {{"description": "Goal 2", "priority": "Medium", "timeline": "Long-term"}}
  ],
  "objections": [
    {{"objection": "Price concern", "response": "Value-based response"}},
    {{"objection": "Quality concern", "response": "Quality assurance response"}}
  ],
  "content_preferences": [
    {{"type": "Video", "platform": "YouTube", "engagement_level": "High"}},
    {{"type": "Article", "platform": "Blog", "engagement_level": "Medium"}}
  ],
  "buying_process": "Detailed buying process description"
}}

Focus on creating realistic customer personas that would actually purchase these products. Consider the price points, categories, and features when defining demographics and behaviors.

Return only the JSON array of ICPs, no additional text:
"""
        
        return prompt
    
    def _parse_product_icp_response(
        self, 
        response: str, 
        service_id: PyObjectId, 
        user_id: PyObjectId
    ) -> List[ICP]:
        """
        Parse the AI response into ICP objects.
        
        Args:
            response: AI response containing JSON ICPs
            service_id: Service ID (store ID)
            user_id: User ID
            
        Returns:
            List of ICP objects
        """
        try:
            # Extract JSON from response
            json_start = response.find('[')
            json_end = response.rfind(']') + 1
            
            if json_start == -1 or json_end == 0:
                # Try to find single object
                json_start = response.find('{')
                json_end = response.rfind('}') + 1
                if json_start != -1 and json_end != 0:
                    json_str = response[json_start:json_end]
                    icp_data = [json.loads(json_str)]
                else:
                    raise ValueError("No valid JSON found in response")
            else:
                json_str = response[json_start:json_end]
                icp_data = json.loads(json_str)
            
            icps = []
            for icp_json in icp_data:
                icp = self._create_icp_from_json(icp_json, service_id, user_id)
                icps.append(icp)
            
            return icps
            
        except Exception as e:
            logger.error(f"Failed to parse ICP response: {str(e)}")
            # Return a default ICP if parsing fails
            return [self._create_default_icp(service_id, user_id)]
    
    def _create_icp_from_json(
        self, 
        icp_json: Dict[str, Any], 
        service_id: PyObjectId, 
        user_id: PyObjectId
    ) -> ICP:
        """Create an ICP object from JSON data."""
        # Create demographics
        demo_data = icp_json.get("demographics", {})
        demographics = Demographics(
            industry=demo_data.get("industry", "E-commerce"),
            company_size=demo_data.get("company_size", "Small"),
            annual_revenue=demo_data.get("annual_revenue", "$100K - $1M"),
            employee_count=demo_data.get("employee_count", "1-10"),
            location=demo_data.get("location", ["United States", "Canada"])
        )
        
        # Create decision maker
        dm_data = icp_json.get("decision_maker", {})
        decision_maker = DecisionMaker(
            title=dm_data.get("title", "Primary buyer"),
            department=dm_data.get("department", "Personal"),
            age_range=dm_data.get("age_range", "25-45"),
            years_of_experience=dm_data.get("years_of_experience", "5+"),
            gender=dm_data.get("gender", "Any"),
            education_level=dm_data.get("education_level", "College educated"),
            reporting_to=dm_data.get("reporting_to", "Self")
        )
        
        # Create pain points
        pain_points = []
        for pp_data in icp_json.get("pain_points", []):
            pain_point = PainPoint(
                description=pp_data.get("description", "Finding quality products"),
                severity=pp_data.get("severity", "Medium")
            )
            pain_points.append(pain_point)
        
        # Create goals
        goals = []
        for goal_data in icp_json.get("goals", []):
            goal = Goal(
                description=goal_data.get("description", "Make informed purchasing decisions"),
                priority=goal_data.get("priority", "Medium")
            )
            goals.append(goal)
        
        # Create objections
        objections = []
        for obj_data in icp_json.get("objections", []):
            objection = Objection(
                description=obj_data.get("objection", "Price concerns"),
                counter_point=obj_data.get("response", "Highlight value and quality benefits")
            )
            objections.append(objection)
        
        # Create content preferences
        content_preferences = []
        for cp_data in icp_json.get("content_preferences", []):
            content_pref = ContentPreference(
                content_type=cp_data.get("type", "Social Media"),
                preferred_platforms=cp_data.get("platform", ["Instagram"]) if isinstance(cp_data.get("platform"), list) else [cp_data.get("platform", "Instagram")],
                tone=cp_data.get("tone", "Professional"),
                content_length=cp_data.get("content_length", "Medium")
            )
            content_preferences.append(content_pref)
        
        # Create ICP
        icp = ICP(
            user_id=user_id,
            service_id=service_id,
            name=icp_json.get("name", "E-commerce Customer"),
            description=icp_json.get("description", "Generated from product analysis"),
            demographics=demographics,
            decision_maker=decision_maker,
            pain_points=pain_points,
            goals=goals,
            objections=objections,
            content_preferences=content_preferences,
            buying_process=icp_json.get("buying_process", "Research online, compare options, purchase"),
            success_metrics=["Conversion rate", "Customer satisfaction", "Repeat purchases"],
            is_ai_generated=True
        )
        
        return icp
    
    def _create_default_icp(self, service_id: PyObjectId, user_id: PyObjectId) -> ICP:
        """Create a default ICP when parsing fails."""
        return ICP(
            user_id=user_id,
            service_id=service_id,
            name="E-commerce Customer",
            description="Default customer profile for e-commerce products",
            demographics=Demographics(
                industry="E-commerce",
                company_size="Small",
                annual_revenue="$100K - $1M",
                employee_count="1-10",
                location=["United States", "Canada"]
            ),
            decision_maker=DecisionMaker(
                title="Primary buyer",
                department="Personal",
                age_range="25-45",
                years_of_experience="5+",
                gender="Any",
                education_level="College educated",
                reporting_to="Self"
            ),
            pain_points=[
                PainPoint(
                    description="Finding quality products at reasonable prices",
                    severity="Medium"
                )
            ],
            goals=[
                Goal(
                    description="Make informed purchasing decisions",
                    priority="High"
                )
            ],
            objections=[
                Objection(
                    description="Price concerns",
                    counter_point="Highlight value and quality benefits"
                )
            ],
            content_preferences=[
                ContentPreference(
                    content_type="Social Media",
                    preferred_platforms=["Instagram"],
                    tone="Professional",
                    content_length="Medium"
                )
            ],
            buying_process="Research online, read reviews, compare prices, purchase",
            success_metrics=["Conversion rate", "Customer satisfaction"],
            is_ai_generated=True
        )
    
    async def _get_specific_products(
        self, 
        store_id: str, 
        user_id: str, 
        product_ids: List[str]
    ) -> List[SyncedProduct]:
        """Get specific products by their IDs."""
        # This would need to be implemented in the ecommerce_service
        # For now, get all products and filter
        products_data = await ecommerce_service.get_store_products(
            store_id, user_id, limit=1000
        )
        products = products_data.get("products", [])
        
        # Filter by product IDs
        filtered_products = [
            p for p in products 
            if p.external_product_id in product_ids
        ]
        
        return filtered_products

    async def generate_behavioral_segments(
        self,
        user_id: str,
        store_id: str,
        behavioral_data: Dict[str, Any],
        subscription_tier: str = "creator"
    ) -> Dict[str, Any]:
        """
        Generate customer segments based on behavioral data and purchase patterns.

        Args:
            user_id: User ID
            store_id: E-commerce store ID
            behavioral_data: Customer behavioral data including purchase history, browsing patterns
            subscription_tier: User's subscription tier

        Returns:
            Behavioral segmentation analysis with customer segments
        """
        try:
            # Extract behavioral metrics
            purchase_history = behavioral_data.get("purchase_history", [])
            browsing_patterns = behavioral_data.get("browsing_patterns", {})
            engagement_data = behavioral_data.get("engagement_data", {})

            # Analyze purchase patterns
            purchase_analysis = await self._analyze_purchase_patterns(purchase_history, subscription_tier)

            # Analyze browsing behavior
            browsing_analysis = await self._analyze_browsing_behavior(browsing_patterns, subscription_tier)

            # Generate customer segments
            segments = await self._generate_customer_segments(
                purchase_analysis,
                browsing_analysis,
                engagement_data,
                subscription_tier
            )

            # Create behavioral insights
            behavioral_insights = await self._create_behavioral_insights(
                segments,
                purchase_analysis,
                browsing_analysis,
                subscription_tier
            )

            # Generate targeting recommendations
            targeting_recommendations = await self._generate_targeting_recommendations(
                segments,
                behavioral_insights,
                subscription_tier
            )

            # Store analysis in database
            db = await get_database()
            segmentation_result = {
                "user_id": user_id,
                "store_id": store_id,
                "segments": segments,
                "behavioral_insights": behavioral_insights,
                "targeting_recommendations": targeting_recommendations,
                "purchase_analysis": purchase_analysis,
                "browsing_analysis": browsing_analysis,
                "analysis_timestamp": datetime.now(timezone.utc),
                "subscription_tier": subscription_tier,
                "data_quality_score": self._calculate_data_quality_score(behavioral_data)
            }

            await db.behavioral_segmentation.insert_one(segmentation_result)

            return segmentation_result

        except Exception as e:
            logger.error(f"Error generating behavioral segments: {str(e)}")
            raise

    async def _analyze_purchase_patterns(self, purchase_history: List[Dict], subscription_tier: str) -> Dict[str, Any]:
        """Analyze customer purchase patterns."""
        try:
            if not purchase_history:
                return {
                    "total_purchases": 0,
                    "average_order_value": 0,
                    "purchase_frequency": "unknown",
                    "seasonal_patterns": {},
                    "category_preferences": {},
                    "price_sensitivity": "unknown"
                }

            # Basic purchase metrics
            total_purchases = len(purchase_history)
            total_value = sum(purchase.get("amount", 0) for purchase in purchase_history)
            average_order_value = total_value / total_purchases if total_purchases > 0 else 0

            # Calculate purchase frequency
            if total_purchases >= 5:
                purchase_frequency = "high"
            elif total_purchases >= 2:
                purchase_frequency = "medium"
            else:
                purchase_frequency = "low"

            # Analyze category preferences
            category_counts = {}
            for purchase in purchase_history:
                categories = purchase.get("categories", [])
                for category in categories:
                    category_counts[category] = category_counts.get(category, 0) + 1

            # Sort categories by preference
            category_preferences = dict(sorted(category_counts.items(), key=lambda x: x[1], reverse=True)[:5])

            # Advanced analysis for higher tiers
            seasonal_patterns = {}
            price_sensitivity = "unknown"

            if subscription_tier in ["accelerator", "dominator"]:
                # Seasonal pattern analysis
                seasonal_counts = {"spring": 0, "summer": 0, "fall": 0, "winter": 0}
                for purchase in purchase_history:
                    purchase_date = purchase.get("date")
                    if purchase_date:
                        try:
                            month = datetime.fromisoformat(purchase_date.replace('Z', '+00:00')).month
                            if month in [3, 4, 5]:
                                seasonal_counts["spring"] += 1
                            elif month in [6, 7, 8]:
                                seasonal_counts["summer"] += 1
                            elif month in [9, 10, 11]:
                                seasonal_counts["fall"] += 1
                            else:
                                seasonal_counts["winter"] += 1
                        except (ValueError, AttributeError):
                            continue

                seasonal_patterns = seasonal_counts

                # Price sensitivity analysis
                if purchase_history:
                    prices = [p.get("amount", 0) for p in purchase_history]
                    avg_price = sum(prices) / len(prices)
                    if avg_price < 50:
                        price_sensitivity = "high"
                    elif avg_price < 200:
                        price_sensitivity = "medium"
                    else:
                        price_sensitivity = "low"

            return {
                "total_purchases": total_purchases,
                "average_order_value": average_order_value,
                "purchase_frequency": purchase_frequency,
                "seasonal_patterns": seasonal_patterns,
                "category_preferences": category_preferences,
                "price_sensitivity": price_sensitivity
            }

        except Exception as e:
            logger.error(f"Error analyzing purchase patterns: {str(e)}")
            return {}

    async def _analyze_browsing_behavior(self, browsing_patterns: Dict, subscription_tier: str) -> Dict[str, Any]:
        """Analyze customer browsing behavior."""
        try:
            page_views = browsing_patterns.get("page_views", [])
            session_duration = browsing_patterns.get("average_session_duration", 0)
            bounce_rate = browsing_patterns.get("bounce_rate", 0)

            # Basic browsing metrics
            total_page_views = len(page_views)

            # Analyze page categories
            category_views = {}
            for page in page_views:
                category = page.get("category", "unknown")
                category_views[category] = category_views.get(category, 0) + 1

            # Determine browsing behavior type
            if session_duration > 300 and bounce_rate < 0.3:  # 5+ minutes, low bounce
                behavior_type = "engaged_browser"
            elif session_duration > 120 and total_page_views > 5:
                behavior_type = "active_researcher"
            elif bounce_rate > 0.7:
                behavior_type = "quick_visitor"
            else:
                behavior_type = "casual_browser"

            # Advanced analysis for higher tiers
            time_patterns = {}
            device_preferences = {}

            if subscription_tier in ["accelerator", "dominator"]:
                # Time-based browsing patterns
                hour_counts = {}
                for page in page_views:
                    timestamp = page.get("timestamp")
                    if timestamp:
                        try:
                            hour = datetime.fromisoformat(timestamp.replace('Z', '+00:00')).hour
                            hour_counts[hour] = hour_counts.get(hour, 0) + 1
                        except (ValueError, AttributeError):
                            continue

                # Find peak browsing hours
                if hour_counts:
                    peak_hour = max(hour_counts.keys(), key=lambda x: hour_counts[x])
                    time_patterns = {
                        "peak_hour": peak_hour,
                        "hourly_distribution": hour_counts
                    }

                # Device preference analysis
                devices = [page.get("device", "unknown") for page in page_views]
                device_counts = {}
                for device in devices:
                    device_counts[device] = device_counts.get(device, 0) + 1
                device_preferences = device_counts

            return {
                "total_page_views": total_page_views,
                "average_session_duration": session_duration,
                "bounce_rate": bounce_rate,
                "behavior_type": behavior_type,
                "category_interests": category_views,
                "time_patterns": time_patterns,
                "device_preferences": device_preferences
            }

        except Exception as e:
            logger.error(f"Error analyzing browsing behavior: {str(e)}")
            return {}

    async def _generate_customer_segments(
        self,
        purchase_analysis: Dict,
        browsing_analysis: Dict,
        engagement_data: Dict,
        subscription_tier: str
    ) -> List[Dict[str, Any]]:
        """Generate customer segments based on behavioral analysis."""
        try:
            segments = []

            # Basic segmentation for all tiers
            purchase_freq = purchase_analysis.get("purchase_frequency", "unknown")
            behavior_type = browsing_analysis.get("behavior_type", "casual_browser")
            avg_order_value = purchase_analysis.get("average_order_value", 0)

            # High-value customers
            if avg_order_value > 200 and purchase_freq in ["high", "medium"]:
                segments.append({
                    "name": "High-Value Customers",
                    "description": "Customers with high purchase value and frequency",
                    "characteristics": {
                        "purchase_behavior": "frequent_high_value",
                        "engagement_level": "high",
                        "price_sensitivity": "low"
                    },
                    "size_percentage": 15,
                    "targeting_priority": "high"
                })

            # Engaged browsers
            if behavior_type == "engaged_browser":
                segments.append({
                    "name": "Engaged Browsers",
                    "description": "Users who spend significant time browsing but may need conversion optimization",
                    "characteristics": {
                        "purchase_behavior": "research_heavy",
                        "engagement_level": "high",
                        "conversion_potential": "medium"
                    },
                    "size_percentage": 25,
                    "targeting_priority": "medium"
                })

            # Price-sensitive customers
            price_sensitivity = purchase_analysis.get("price_sensitivity", "unknown")
            if price_sensitivity == "high":
                segments.append({
                    "name": "Price-Conscious Shoppers",
                    "description": "Customers who are sensitive to pricing and look for deals",
                    "characteristics": {
                        "purchase_behavior": "deal_seeking",
                        "engagement_level": "medium",
                        "price_sensitivity": "high"
                    },
                    "size_percentage": 30,
                    "targeting_priority": "medium"
                })

            # Advanced segmentation for higher tiers
            if subscription_tier in ["accelerator", "dominator"]:
                # Seasonal shoppers
                seasonal_patterns = purchase_analysis.get("seasonal_patterns", {})
                if seasonal_patterns:
                    peak_season = max(seasonal_patterns.keys(), key=lambda x: seasonal_patterns[x]) if seasonal_patterns else None
                    if peak_season:
                        segments.append({
                            "name": f"{peak_season.title()} Shoppers",
                            "description": f"Customers who primarily shop during {peak_season}",
                            "characteristics": {
                                "purchase_behavior": "seasonal",
                                "peak_season": peak_season,
                                "engagement_level": "variable"
                            },
                            "size_percentage": 20,
                            "targeting_priority": "medium"
                        })

                # Device-specific segments
                device_preferences = browsing_analysis.get("device_preferences", {})
                if device_preferences:
                    primary_device = max(device_preferences.keys(), key=lambda x: device_preferences[x]) if device_preferences else None
                    if primary_device and primary_device != "unknown":
                        segments.append({
                            "name": f"{primary_device.title()} Users",
                            "description": f"Customers who primarily browse on {primary_device}",
                            "characteristics": {
                                "purchase_behavior": "device_specific",
                                "primary_device": primary_device,
                                "engagement_level": "medium"
                            },
                            "size_percentage": 10,
                            "targeting_priority": "low"
                        })

            return segments

        except Exception as e:
            logger.error(f"Error generating customer segments: {str(e)}")
            return []

    async def _create_behavioral_insights(
        self,
        segments: List[Dict],
        purchase_analysis: Dict,
        browsing_analysis: Dict,
        subscription_tier: str
    ) -> Dict[str, Any]:
        """Create behavioral insights from analysis data."""
        try:
            insights = {
                "customer_lifecycle": {
                    "new_customers": 0,
                    "returning_customers": 0,
                    "loyal_customers": 0
                },
                "engagement_patterns": {
                    "high_engagement": 0,
                    "medium_engagement": 0,
                    "low_engagement": 0
                },
                "conversion_opportunities": [],
                "retention_risks": []
            }

            # Analyze customer lifecycle based on purchase frequency
            purchase_freq = purchase_analysis.get("purchase_frequency", "unknown")
            total_purchases = purchase_analysis.get("total_purchases", 0)

            if total_purchases == 1:
                insights["customer_lifecycle"]["new_customers"] = 100
            elif total_purchases <= 3:
                insights["customer_lifecycle"]["returning_customers"] = 100
            else:
                insights["customer_lifecycle"]["loyal_customers"] = 100

            # Analyze engagement patterns
            behavior_type = browsing_analysis.get("behavior_type", "casual_browser")
            if behavior_type == "engaged_browser":
                insights["engagement_patterns"]["high_engagement"] = 100
            elif behavior_type == "active_researcher":
                insights["engagement_patterns"]["medium_engagement"] = 100
            else:
                insights["engagement_patterns"]["low_engagement"] = 100

            # Identify conversion opportunities
            if behavior_type == "engaged_browser" and purchase_freq == "low":
                insights["conversion_opportunities"].append({
                    "opportunity": "High engagement, low conversion",
                    "recommendation": "Implement targeted offers for engaged browsers",
                    "potential_impact": "medium"
                })

            # Identify retention risks
            if purchase_freq == "low" and behavior_type == "quick_visitor":
                insights["retention_risks"].append({
                    "risk": "Low engagement and purchase frequency",
                    "recommendation": "Implement re-engagement campaigns",
                    "severity": "high"
                })

            return insights

        except Exception as e:
            logger.error(f"Error creating behavioral insights: {str(e)}")
            return {}

    async def _generate_targeting_recommendations(
        self,
        segments: List[Dict],
        behavioral_insights: Dict,
        subscription_tier: str
    ) -> List[Dict[str, Any]]:
        """Generate targeting recommendations based on segments and insights."""
        try:
            recommendations = []

            # Basic recommendations for all tiers
            for segment in segments:
                if segment.get("targeting_priority") == "high":
                    recommendations.append({
                        "segment": segment["name"],
                        "strategy": "Premium targeting",
                        "channels": ["email", "social_media", "paid_ads"],
                        "message_focus": "value_proposition",
                        "budget_allocation": "high"
                    })
                elif segment.get("targeting_priority") == "medium":
                    recommendations.append({
                        "segment": segment["name"],
                        "strategy": "Nurture campaigns",
                        "channels": ["email", "content_marketing"],
                        "message_focus": "education_and_value",
                        "budget_allocation": "medium"
                    })

            # Advanced recommendations for higher tiers
            if subscription_tier in ["accelerator", "dominator"]:
                # Conversion opportunity recommendations
                opportunities = behavioral_insights.get("conversion_opportunities", [])
                for opportunity in opportunities:
                    recommendations.append({
                        "segment": "Conversion Opportunity",
                        "strategy": opportunity["recommendation"],
                        "channels": ["retargeting", "email", "push_notifications"],
                        "message_focus": "conversion_optimization",
                        "budget_allocation": "medium"
                    })

                # Retention risk recommendations
                risks = behavioral_insights.get("retention_risks", [])
                for risk in risks:
                    recommendations.append({
                        "segment": "At-Risk Customers",
                        "strategy": risk["recommendation"],
                        "channels": ["email", "sms", "personalized_offers"],
                        "message_focus": "retention_and_reactivation",
                        "budget_allocation": "high"
                    })

            return recommendations

        except Exception as e:
            logger.error(f"Error generating targeting recommendations: {str(e)}")
            return []

    def _calculate_data_quality_score(self, behavioral_data: Dict[str, Any]) -> float:
        """Calculate data quality score based on available behavioral data."""
        try:
            score = 0.0
            max_score = 100.0

            # Check for purchase history
            purchase_history = behavioral_data.get("purchase_history", [])
            if purchase_history:
                score += 40.0  # 40% for having purchase data
                if len(purchase_history) >= 5:
                    score += 10.0  # Bonus for sufficient purchase history

            # Check for browsing patterns
            browsing_patterns = behavioral_data.get("browsing_patterns", {})
            if browsing_patterns:
                score += 30.0  # 30% for having browsing data
                if browsing_patterns.get("page_views"):
                    score += 10.0  # Bonus for detailed page view data

            # Check for engagement data
            engagement_data = behavioral_data.get("engagement_data", {})
            if engagement_data:
                score += 20.0  # 20% for having engagement data

            return min(score / max_score, 1.0)  # Normalize to 0-1 range

        except Exception as e:
            logger.error(f"Error calculating data quality score: {str(e)}")
            return 0.0


# Create singleton instance
ecommerce_icp_generator = EcommerceICPGenerator()
