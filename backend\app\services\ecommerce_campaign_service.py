"""
Enhanced campaign service with e-commerce product integration.
@since 2024-1-1 to 2025-25-7
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
from decimal import Decimal
from bson import ObjectId

from app.db.mongodb import get_database
from app.models.user import User, PyObjectId
from app.models.campaign import (
    Campaign, 
    ProductCampaignTarget, 
    EcommerceCampaignMetrics,
    CampaignGoal
)
from app.models.ecommerce import SyncedProduct
from app.services.ecommerce_service import ecommerce_service
from app.services.campaign import create_campaign, get_campaign
from app.services.content_generator import generate_content_service
from app.schemas.content import GenerateContentRequest, ProductDataInput
from app.utils.error_handling import ExternalServiceError

# Create ServiceError alias for consistency
ServiceError = ExternalServiceError

logger = logging.getLogger(__name__)

# Collection names
CAMPAIGNS_COLLECTION = "campaigns"
PRODUCTS_COLLECTION = "synced_products"


class EcommerceCampaignService:
    """
    Service for managing e-commerce integrated campaigns.
    """
    
    async def create_product_campaign(
        self,
        user: User,
        campaign_data: Dict[str, Any],
        product_ids: List[str],
        store_id: str
    ) -> Campaign:
        """
        Create a new campaign with product integration.
        
        Args:
            user: User creating the campaign
            campaign_data: Basic campaign information
            product_ids: List of product IDs to include
            store_id: E-commerce store ID
            
        Returns:
            Created campaign
        """
        try:
            # Get products from store
            products = await self._get_campaign_products(store_id, str(user.id), product_ids)
            
            if not products:
                raise ServiceError("No valid products found for campaign")
            
            # Create product targets
            target_products = []
            for i, product in enumerate(products):
                target = ProductCampaignTarget(
                    product_id=product.id,
                    external_product_id=product.external_product_id,
                    product_title=product.title,
                    target_audience=f"Customers interested in {product.category or 'products'}",
                    custom_messaging=f"Discover our amazing {product.title}",
                    budget_allocation=100.0 / len(products),  # Equal budget allocation
                    priority=i + 1
                )
                target_products.append(target)
            
            # Create campaign with e-commerce integration
            start_date_str = campaign_data.get("start_date")
            end_date_str = campaign_data.get("end_date")

            # Validate required fields
            if not start_date_str:
                raise ServiceError("start_date is required")

            name = campaign_data.get("name")
            description = campaign_data.get("description")
            icp_id = campaign_data.get("icp_id")

            if not name:
                raise ServiceError("Campaign name is required")
            if not description:
                raise ServiceError("Campaign description is required")
            if not icp_id:
                raise ServiceError("ICP ID is required")

            campaign = Campaign(
                user_id=user.id,
                icp_id=PyObjectId(icp_id),
                name=name,
                description=description,
                start_date=datetime.fromisoformat(start_date_str),
                end_date=datetime.fromisoformat(end_date_str) if end_date_str else None,
                platforms=campaign_data.get("platforms", []),
                goals=[CampaignGoal(**goal) for goal in campaign_data.get("goals", [])],
                is_ecommerce_campaign=True,
                store_id=PyObjectId(store_id),
                target_products=target_products,
                campaign_budget=Decimal(str(campaign_data.get("budget", "0"))),
                cost_per_click=Decimal(str(campaign_data.get("cost_per_click", "0.50"))),  # Default $0.50 CPC
                target_roas=campaign_data.get("target_roas", 300.0)  # 300% default ROAS
            )
            
            # Save campaign
            db = await get_database()
            result = await db[CAMPAIGNS_COLLECTION].insert_one(campaign.model_dump(by_alias=True))
            
            # Get created campaign
            created_campaign = await db[CAMPAIGNS_COLLECTION].find_one({"_id": result.inserted_id})
            if not created_campaign:
                raise ServiceError("Failed to retrieve created campaign")
            
            return Campaign(**created_campaign)
            
        except Exception as e:
            logger.error(f"Failed to create product campaign: {str(e)}")
            raise ServiceError(f"Failed to create campaign: {str(e)}")
    
    async def generate_product_content(
        self,
        campaign_id: str,
        user_id: str,
        content_requests: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Generate content for campaign products.
        
        Args:
            campaign_id: Campaign ID
            user_id: User ID
            content_requests: List of content generation requests
            
        Returns:
            List of generated content
        """
        try:
            # Get campaign
            campaign = await self._get_campaign(campaign_id, user_id)
            if not campaign or not campaign.is_ecommerce_campaign:
                raise ServiceError("Campaign not found or not an e-commerce campaign")
            
            # Get campaign products
            products = await self._get_campaign_products_by_campaign(campaign)
            
            generated_content = []
            
            for request_data in content_requests:
                product_id = request_data.get("product_id")
                
                # Find the product
                product = next((p for p in products if str(p.id) == product_id), None)
                if not product:
                    logger.warning(f"Product {product_id} not found in campaign")
                    continue
                
                # Create product data input
                product_data = ProductDataInput(
                    product_id=product.external_product_id,
                    title=product.title,
                    description=product.description,
                    price=product.price,
                    compare_at_price=product.compare_at_price,
                    sku=product.sku,
                    vendor=product.vendor,
                    category=product.category,
                    tags=product.tags,
                    images=[img.url for img in product.images],
                    featured_image=product.featured_image,
                    product_url=f"https://store.example.com/products/{product.external_product_id}",  # Generate product URL
                    inventory_quantity=product.inventory_quantity
                )
                
                # Create content generation request
                content_request = GenerateContentRequest(
                    topic=request_data.get("topic", f"Promote {product.title}"),
                    tone=request_data.get("tone", "engaging"),
                    platform=request_data.get("platform", "instagram"),
                    content_type=request_data.get("content_type", "post"),
                    length=request_data.get("length", "medium"),
                    include_hashtags=request_data.get("include_hashtags", True),
                    target_audience=request_data.get("target_audience"),
                    generate_image=request_data.get("generate_image", True),
                    product_data=product_data,
                    include_product_details=True,
                    include_pricing=request_data.get("include_pricing", True),
                    include_call_to_action=True,
                    call_to_action_text=request_data.get("call_to_action", "Shop now")
                )
                
                # Generate content
                content = await generate_content_service(content_request, ObjectId(user_id))
                
                # Add product context to content
                content_dict = content.model_dump()
                content_dict["product_id"] = str(product.id)
                content_dict["external_product_id"] = product.external_product_id
                content_dict["campaign_id"] = campaign_id
                
                generated_content.append(content_dict)
            
            return generated_content
            
        except Exception as e:
            logger.error(f"Failed to generate product content: {str(e)}")
            raise ServiceError(f"Failed to generate content: {str(e)}")
    
    async def update_campaign_metrics(
        self,
        campaign_id: str,
        user_id: str,
        metrics_data: Dict[str, Any]
    ) -> bool:
        """
        Update e-commerce metrics for a campaign.
        
        Args:
            campaign_id: Campaign ID
            user_id: User ID
            metrics_data: Metrics data to update
            
        Returns:
            True if successful
        """
        try:
            db = await get_database()
            
            # Build update data
            update_data = {
                "updated_at": datetime.now(timezone.utc)
            }
            
            # Update e-commerce metrics
            ecommerce_metrics = {}
            if "product_views" in metrics_data:
                ecommerce_metrics["product_views"] = metrics_data["product_views"]
            if "add_to_cart" in metrics_data:
                ecommerce_metrics["add_to_cart"] = metrics_data["add_to_cart"]
            if "purchases" in metrics_data:
                ecommerce_metrics["purchases"] = metrics_data["purchases"]
            if "revenue" in metrics_data:
                ecommerce_metrics["revenue"] = Decimal(str(metrics_data["revenue"]))
            
            # Calculate derived metrics
            if "purchases" in metrics_data and "product_views" in metrics_data:
                if metrics_data["product_views"] > 0:
                    ecommerce_metrics["conversion_rate"] = (
                        metrics_data["purchases"] / metrics_data["product_views"]
                    ) * 100
            
            if "revenue" in metrics_data and "purchases" in metrics_data:
                if metrics_data["purchases"] > 0:
                    ecommerce_metrics["average_order_value"] = Decimal(
                        str(metrics_data["revenue"])
                    ) / metrics_data["purchases"]
            
            # Update metrics in database
            for key, value in ecommerce_metrics.items():
                update_data[f"ecommerce_metrics.{key}"] = value
            
            result = await db[CAMPAIGNS_COLLECTION].update_one(
                {
                    "_id": ObjectId(campaign_id),
                    "user_id": ObjectId(user_id),
                    "is_ecommerce_campaign": True
                },
                {"$set": update_data}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Failed to update campaign metrics: {str(e)}")
            return False
    
    async def get_campaign_performance(
        self,
        campaign_id: str,
        user_id: str
    ) -> Dict[str, Any]:
        """
        Get comprehensive performance data for an e-commerce campaign.
        
        Args:
            campaign_id: Campaign ID
            user_id: User ID
            
        Returns:
            Performance data
        """
        try:
            campaign = await self._get_campaign(campaign_id, user_id)
            if not campaign or not campaign.is_ecommerce_campaign:
                raise ServiceError("Campaign not found or not an e-commerce campaign")
            
            # Get basic performance data
            performance_data = {
                "campaign_id": campaign_id,
                "campaign_name": campaign.name,
                "status": campaign.status,
                "start_date": campaign.start_date.isoformat(),
                "end_date": campaign.end_date.isoformat() if campaign.end_date else None,
                "platforms": campaign.platforms,
                "total_products": len(campaign.target_products),
                "budget": float(campaign.campaign_budget) if campaign.campaign_budget else 0,
                "target_roas": campaign.target_roas
            }
            
            # Add e-commerce metrics
            metrics = campaign.ecommerce_metrics
            performance_data["ecommerce_metrics"] = {
                "product_views": metrics.product_views,
                "add_to_cart": metrics.add_to_cart,
                "purchases": metrics.purchases,
                "revenue": float(metrics.revenue),
                "conversion_rate": metrics.conversion_rate,
                "average_order_value": float(metrics.average_order_value),
                "return_on_ad_spend": metrics.return_on_ad_spend,
                "cost_per_acquisition": float(metrics.cost_per_acquisition)
            }
            
            # Add product performance breakdown
            product_performance = []
            for target_product in campaign.target_products:
                product_perf = {
                    "product_id": str(target_product.product_id),
                    "external_product_id": target_product.external_product_id,
                    "product_title": target_product.product_title,
                    "priority": target_product.priority,
                    "budget_allocation": target_product.budget_allocation
                }
                product_performance.append(product_perf)
            
            performance_data["product_performance"] = product_performance
            
            return performance_data
            
        except Exception as e:
            logger.error(f"Failed to get campaign performance: {str(e)}")
            raise ServiceError(f"Failed to get performance data: {str(e)}")
    
    async def _get_campaign(self, campaign_id: str, user_id: str) -> Optional[Campaign]:
        """Get campaign by ID and user ID."""
        try:
            db = await get_database()
            campaign_data = await db[CAMPAIGNS_COLLECTION].find_one({
                "_id": ObjectId(campaign_id),
                "user_id": ObjectId(user_id)
            })
            
            if campaign_data:
                return Campaign(**campaign_data)
            return None
            
        except Exception as e:
            logger.error(f"Failed to get campaign: {str(e)}")
            return None
    
    async def _get_campaign_products(
        self, 
        store_id: str, 
        user_id: str, 
        product_ids: List[str]
    ) -> List[SyncedProduct]:
        """Get products for campaign creation."""
        try:
            db = await get_database()
            
            # Get products by external IDs
            products_data = await db[PRODUCTS_COLLECTION].find({
                "store_id": ObjectId(store_id),
                "user_id": ObjectId(user_id),
                "external_product_id": {"$in": product_ids}
            }).to_list(length=None)
            
            products = [SyncedProduct(**product_data) for product_data in products_data]
            return products
            
        except Exception as e:
            logger.error(f"Failed to get campaign products: {str(e)}")
            return []
    
    async def _get_campaign_products_by_campaign(self, campaign: Campaign) -> List[SyncedProduct]:
        """Get products associated with a campaign."""
        try:
            if not campaign.target_products:
                return []
            
            product_ids = [str(target.product_id) for target in campaign.target_products]
            
            db = await get_database()
            products_data = await db[PRODUCTS_COLLECTION].find({
                "_id": {"$in": [ObjectId(pid) for pid in product_ids]}
            }).to_list(length=None)
            
            products = [SyncedProduct(**product_data) for product_data in products_data]
            return products
            
        except Exception as e:
            logger.error(f"Failed to get campaign products: {str(e)}")
            return []


# Create singleton instance
ecommerce_campaign_service = EcommerceCampaignService()
