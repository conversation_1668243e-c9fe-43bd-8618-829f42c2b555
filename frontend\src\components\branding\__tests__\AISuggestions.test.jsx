/**
 * Tests for AISuggestions component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import AISuggestions from '../AISuggestions';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the API
vi.mock('../../../api', () => ({
  default: {
    post: vi.fn()
  }
}));

// Mock hooks
vi.mock('../../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showSuccessNotification: vi.fn(),
    showErrorNotification: vi.fn()
  }))
}));

vi.mock('../../../hooks/useBranding', () => ({
  default: vi.fn(() => ({
    brandingData: {
      colorSystem: { primary: '#4E40C5' },
      visualStyle: { photographyStyle: 'lifestyle' },
      imageComposition: { layout: 'rule-of-thirds' }
    }
  }))
}));

describe('AISuggestions', () => {
  const mockProps = {
    onApplySuggestion: vi.fn(),
    onSuggestionsGenerated: vi.fn(),
    onError: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders AI suggestions component', () => {
    render(
      <TestWrapper>
        <AISuggestions {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('AI Branding Suggestions')).toBeInTheDocument();
    expect(screen.getByText('Get AI-powered branding recommendations based on your industry, target audience, and brand personality.')).toBeInTheDocument();
    expect(screen.getByText('Tell us about your brand')).toBeInTheDocument();
  });

  test('loads initial data when provided', () => {
    const initialData = {
      industry: 'Technology',
      audience: 'IT Decision Makers',
      personality: ['Professional', 'Innovative'],
      competitors: 'Microsoft, Google'
    };

    render(
      <TestWrapper>
        <AISuggestions {...mockProps} initialData={initialData} />
      </TestWrapper>
    );

    expect(screen.getByDisplayValue('Technology')).toBeInTheDocument();
    expect(screen.getByDisplayValue('IT Decision Makers')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Microsoft, Google')).toBeInTheDocument();
  });

  test('validates required fields before generating suggestions', async () => {
    const user = userEvent.setup();
    const { useNotification } = await import('../../../hooks/useNotification');
    
    const mockShowError = vi.fn();
    useNotification.mockReturnValue({
      showSuccessNotification: vi.fn(),
      showErrorNotification: mockShowError
    });

    render(
      <TestWrapper>
        <AISuggestions {...mockProps} />
      </TestWrapper>
    );

    const generateButton = screen.getByLabelText('Generate AI suggestions');
    await user.click(generateButton);

    expect(mockShowError).toHaveBeenCalledWith('Please fill in all required fields');
  });

  test('generates AI suggestions with valid input', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.post.mockResolvedValue({
      data: {
        success: true,
        content: JSON.stringify({
          colorSystem: { primary: '#0078D7', secondary: '#00B294' },
          visualStyle: { photographyStyle: 'corporate' },
          typography: { fonts: ['Roboto', 'Open Sans'] }
        })
      }
    });

    render(
      <TestWrapper>
        <AISuggestions {...mockProps} />
      </TestWrapper>
    );

    // Fill in required fields
    const industrySelect = screen.getByLabelText('Select industry');
    const audienceSelect = screen.getByLabelText('Select target audience');
    const personalitySelect = screen.getByLabelText('Select brand personality traits');

    await user.click(industrySelect);
    await user.click(screen.getByText('Technology'));

    await user.click(audienceSelect);
    await user.click(screen.getByText('IT Decision Makers'));

    await user.click(personalitySelect);
    await user.click(screen.getByText('Professional'));

    // Generate suggestions
    const generateButton = screen.getByLabelText('Generate AI suggestions');
    await user.click(generateButton);

    await waitFor(() => {
      expect(api.default.post).toHaveBeenCalledWith('/api/ai-content/generate', expect.objectContaining({
        content_type: 'branding_suggestions',
        target_audience: 'IT Decision Makers'
      }));
    });

    expect(mockProps.onSuggestionsGenerated).toHaveBeenCalled();
  });

  test('handles API errors gracefully', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.post.mockRejectedValue(new Error('API Error'));

    render(
      <TestWrapper>
        <AISuggestions {...mockProps} />
      </TestWrapper>
    );

    // Fill in required fields
    const industrySelect = screen.getByLabelText('Select industry');
    await user.click(industrySelect);
    await user.click(screen.getByText('Technology'));

    const audienceSelect = screen.getByLabelText('Select target audience');
    await user.click(audienceSelect);
    await user.click(screen.getByText('IT Decision Makers'));

    const personalitySelect = screen.getByLabelText('Select brand personality traits');
    await user.click(personalitySelect);
    await user.click(screen.getByText('Professional'));

    const generateButton = screen.getByLabelText('Generate AI suggestions');
    await user.click(generateButton);

    await waitFor(() => {
      expect(mockProps.onError).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  test('uses fallback suggestions when API response is invalid', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.post.mockResolvedValue({
      data: {
        success: true,
        content: 'invalid json'
      }
    });

    render(
      <TestWrapper>
        <AISuggestions {...mockProps} />
      </TestWrapper>
    );

    // Fill in required fields and generate
    const industrySelect = screen.getByLabelText('Select industry');
    await user.click(industrySelect);
    await user.click(screen.getByText('Technology'));

    const audienceSelect = screen.getByLabelText('Select target audience');
    await user.click(audienceSelect);
    await user.click(screen.getByText('IT Decision Makers'));

    const personalitySelect = screen.getByLabelText('Select brand personality traits');
    await user.click(personalitySelect);
    await user.click(screen.getByText('Professional'));

    const generateButton = screen.getByLabelText('Generate AI suggestions');
    await user.click(generateButton);

    await waitFor(() => {
      expect(mockProps.onSuggestionsGenerated).toHaveBeenCalled();
    });
  });

  test('displays suggestions after generation', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.post.mockResolvedValue({
      data: {
        success: true,
        content: JSON.stringify({
          colorSystem: {
            primary: '#0078D7',
            secondary: '#00B294',
            explanation: 'Technology color palette'
          },
          visualStyle: {
            photographyStyle: 'corporate',
            explanation: 'Corporate photography style'
          }
        })
      }
    });

    render(
      <TestWrapper>
        <AISuggestions {...mockProps} />
      </TestWrapper>
    );

    // Fill in required fields and generate
    const industrySelect = screen.getByLabelText('Select industry');
    await user.click(industrySelect);
    await user.click(screen.getByText('Technology'));

    const audienceSelect = screen.getByLabelText('Select target audience');
    await user.click(audienceSelect);
    await user.click(screen.getByText('IT Decision Makers'));

    const personalitySelect = screen.getByLabelText('Select brand personality traits');
    await user.click(personalitySelect);
    await user.click(screen.getByText('Professional'));

    const generateButton = screen.getByLabelText('Generate AI suggestions');
    await user.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText('AI Branding Suggestions')).toBeInTheDocument();
      expect(screen.getByText('Apply All Suggestions')).toBeInTheDocument();
    });
  });

  test('applies individual suggestions', async () => {
    const user = userEvent.setup();
    
    // Mock component with existing suggestions
    const ComponentWithSuggestions = () => {
      const [suggestions] = React.useState({
        colorSystem: { primary: '#0078D7', secondary: '#00B294' }
      });
      
      return (
        <TestWrapper>
          <AISuggestions {...mockProps} />
        </TestWrapper>
      );
    };

    render(<ComponentWithSuggestions />);

    // This test would need the component to have suggestions already loaded
    // In a real scenario, we'd need to generate suggestions first
  });

  test('disables form when disabled prop is true', () => {
    render(
      <TestWrapper>
        <AISuggestions {...mockProps} disabled={true} />
      </TestWrapper>
    );

    const industrySelect = screen.getByLabelText('Select industry');
    const audienceSelect = screen.getByLabelText('Select target audience');
    const personalitySelect = screen.getByLabelText('Select brand personality traits');
    const competitorsInput = screen.getByLabelText('Enter key competitors');
    const generateButton = screen.getByLabelText('Generate AI suggestions');

    expect(industrySelect).toBeDisabled();
    expect(audienceSelect).toBeDisabled();
    expect(personalitySelect).toBeDisabled();
    expect(competitorsInput).toBeDisabled();
    expect(generateButton).toBeDisabled();
  });

  test('shows loading state during generation', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    // Mock API to never resolve
    api.default.post.mockImplementation(() => new Promise(() => {}));

    render(
      <TestWrapper>
        <AISuggestions {...mockProps} />
      </TestWrapper>
    );

    // Fill in required fields
    const industrySelect = screen.getByLabelText('Select industry');
    await user.click(industrySelect);
    await user.click(screen.getByText('Technology'));

    const audienceSelect = screen.getByLabelText('Select target audience');
    await user.click(audienceSelect);
    await user.click(screen.getByText('IT Decision Makers'));

    const personalitySelect = screen.getByLabelText('Select brand personality traits');
    await user.click(personalitySelect);
    await user.click(screen.getByText('Professional'));

    const generateButton = screen.getByLabelText('Generate AI suggestions');
    await user.click(generateButton);

    expect(screen.getByText('Generating Suggestions...')).toBeInTheDocument();
    expect(screen.getByText('Generating Branding Suggestions')).toBeInTheDocument();
  });

  test('applies correct accessibility attributes', () => {
    render(
      <TestWrapper>
        <AISuggestions {...mockProps} />
      </TestWrapper>
    );

    const industrySelect = screen.getByLabelText('Select industry');
    const audienceSelect = screen.getByLabelText('Select target audience');
    const personalitySelect = screen.getByLabelText('Select brand personality traits');
    const competitorsInput = screen.getByLabelText('Enter key competitors');

    expect(industrySelect).toHaveAttribute('aria-label', 'Select industry');
    expect(audienceSelect).toHaveAttribute('aria-label', 'Select target audience');
    expect(personalitySelect).toHaveAttribute('aria-label', 'Select brand personality traits');
    expect(competitorsInput).toHaveAttribute('aria-label', 'Enter key competitors');
  });

  test('passes through additional props', () => {
    render(
      <TestWrapper>
        <AISuggestions 
          {...mockProps} 
          data-testid="test-ai-suggestions"
          className="custom-class"
        />
      </TestWrapper>
    );

    const component = screen.getByTestId('test-ai-suggestions');
    expect(component).toHaveClass('custom-class');
  });

  test('handles personality selection correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AISuggestions {...mockProps} />
      </TestWrapper>
    );

    const personalitySelect = screen.getByLabelText('Select brand personality traits');
    await user.click(personalitySelect);

    // Select multiple personality traits
    await user.click(screen.getByText('Professional'));
    await user.click(screen.getByText('Innovative'));

    // Check that chips are displayed
    expect(screen.getByText('Professional')).toBeInTheDocument();
    expect(screen.getByText('Innovative')).toBeInTheDocument();
  });
});
