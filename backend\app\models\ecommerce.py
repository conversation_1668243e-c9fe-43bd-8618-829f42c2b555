"""
E-commerce models for store connections, products, and synchronization.
@since 2024-1-1 to 2025-25-7
"""

from typing import Optional, Dict, Any, List, Union
from datetime import datetime, timezone
from decimal import Decimal
from enum import Enum
from pydantic import BaseModel, Field, ConfigDict, validator
from bson import ObjectId

from app.models.user import PyObjectId, utc_now
from app.models.common import (
    StatusEnum, 
    SyncStatusEnum, 
    EcommercePlatformEnum,
    TimestampMixin,
    MetadataMixin
)


class ProductStatusEnum(str, Enum):
    """Product status enumeration."""
    ACTIVE = "active"
    DRAFT = "draft"
    ARCHIVED = "archived"
    OUT_OF_STOCK = "out_of_stock"


class SyncOperationEnum(str, Enum):
    """Sync operation types."""
    FULL_SYNC = "full_sync"
    INCREMENTAL_SYNC = "incremental_sync"
    PRODUCT_UPDATE = "product_update"
    PRODUCT_DELETE = "product_delete"
    WEBHOOK_SYNC = "webhook_sync"


class ProductVariant(BaseModel):
    """Product variant model."""
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )

    variant_id: str = Field(..., description="External variant ID")
    title: str = Field(..., description="Variant title")
    price: Decimal = Field(..., description="Variant price")
    compare_at_price: Optional[Decimal] = Field(None, description="Compare at price")
    sku: Optional[str] = Field(None, description="Stock keeping unit")
    inventory_quantity: int = Field(default=0, description="Available inventory")
    weight: Optional[float] = Field(None, description="Weight in grams")
    option1: Optional[str] = Field(None, description="First option value")
    option2: Optional[str] = Field(None, description="Second option value")
    option3: Optional[str] = Field(None, description="Third option value")
    image_url: Optional[str] = Field(None, description="Variant image URL")
    available: bool = Field(default=True, description="Whether variant is available")


class ProductImage(BaseModel):
    """Product image model."""
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )

    image_id: str = Field(..., description="External image ID")
    url: str = Field(..., description="Image URL")
    alt_text: Optional[str] = Field(None, description="Alt text for accessibility")
    width: Optional[int] = Field(None, description="Image width")
    height: Optional[int] = Field(None, description="Image height")
    position: int = Field(default=1, description="Display position")


class EcommerceStore(BaseModel):
    """E-commerce store connection model."""
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str},
        json_schema_extra={
            "example": {
                "_id": "60d5ec9af682dbd12a0a9fb9",
                "user_id": "60d5ec9af682dbd12a0a9fb8",
                "platform": "shopify",
                "store_name": "My Awesome Store",
                "store_url": "https://my-awesome-store.myshopify.com",
                "status": "connected",
                "last_sync_at": "2023-12-01T12:00:00Z",
                "total_products": 150,
                "sync_enabled": True
            }
        }
    )

    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    user_id: PyObjectId = Field(..., description="User who owns this store")
    platform: EcommercePlatformEnum = Field(..., description="E-commerce platform")
    
    # Store information
    store_name: str = Field(..., max_length=200, description="Store name")
    store_url: str = Field(..., description="Store URL")
    store_id: str = Field(..., description="External store ID")
    
    # Authentication credentials (encrypted)
    access_token: str = Field(..., description="Encrypted access token")
    refresh_token: Optional[str] = Field(None, description="Encrypted refresh token")
    api_key: Optional[str] = Field(None, description="Encrypted API key")
    api_secret: Optional[str] = Field(None, description="Encrypted API secret")
    webhook_secret: Optional[str] = Field(None, description="Webhook verification secret")
    
    # Connection status
    status: SyncStatusEnum = Field(default=SyncStatusEnum.CONNECTED, description="Connection status")
    last_sync_at: Optional[datetime] = Field(None, description="Last successful sync")
    last_error: Optional[str] = Field(None, description="Last error message")
    
    # Sync configuration
    sync_enabled: bool = Field(default=True, description="Whether sync is enabled")
    auto_sync_interval: int = Field(default=3600, description="Auto sync interval in seconds")
    webhook_url: Optional[str] = Field(None, description="Webhook endpoint URL")
    
    # Statistics
    total_products: int = Field(default=0, description="Total products in store")
    synced_products: int = Field(default=0, description="Number of synced products")
    
    # Timestamps
    created_at: datetime = Field(default_factory=utc_now)
    updated_at: datetime = Field(default_factory=utc_now)
    connected_at: datetime = Field(default_factory=utc_now)
    
    # Metadata
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional store metadata")


class SyncedProduct(BaseModel):
    """Synced product model."""
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str},
        json_schema_extra={
            "example": {
                "_id": "60d5ec9af682dbd12a0a9fb9",
                "user_id": "60d5ec9af682dbd12a0a9fb8",
                "store_id": "60d5ec9af682dbd12a0a9fb7",
                "external_product_id": "12345678",
                "title": "Premium Wireless Headphones",
                "description": "High-quality wireless headphones with noise cancellation",
                "price": "199.99",
                "status": "active",
                "tags": ["electronics", "audio", "wireless"],
                "category": "Electronics > Audio > Headphones"
            }
        }
    )

    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    user_id: PyObjectId = Field(..., description="User who owns this product")
    store_id: PyObjectId = Field(..., description="Associated store ID")
    
    # External product information
    external_product_id: str = Field(..., description="External product ID")
    platform: EcommercePlatformEnum = Field(..., description="Source platform")
    
    # Product details
    title: str = Field(..., max_length=500, description="Product title")
    description: Optional[str] = Field(None, description="Product description")
    vendor: Optional[str] = Field(None, description="Product vendor/brand")
    product_type: Optional[str] = Field(None, description="Product type")
    
    # Pricing
    price: Decimal = Field(..., description="Product price")
    compare_at_price: Optional[Decimal] = Field(None, description="Compare at price")
    currency: str = Field(default="USD", description="Currency code")
    
    # Inventory
    sku: Optional[str] = Field(None, description="Stock keeping unit")
    inventory_quantity: int = Field(default=0, description="Available inventory")
    track_inventory: bool = Field(default=True, description="Whether to track inventory")
    
    # Organization
    tags: List[str] = Field(default_factory=list, description="Product tags")
    category: Optional[str] = Field(None, description="Product category")
    collections: List[str] = Field(default_factory=list, description="Product collections")
    
    # Media
    images: List[ProductImage] = Field(default_factory=list, description="Product images")
    featured_image: Optional[str] = Field(None, description="Featured image URL")
    
    # Variants
    variants: List[ProductVariant] = Field(default_factory=list, description="Product variants")
    has_variants: bool = Field(default=False, description="Whether product has variants")
    
    # Status and visibility
    status: ProductStatusEnum = Field(default=ProductStatusEnum.ACTIVE, description="Product status")
    published: bool = Field(default=True, description="Whether product is published")
    
    # SEO
    seo_title: Optional[str] = Field(None, description="SEO title")
    seo_description: Optional[str] = Field(None, description="SEO description")
    handle: Optional[str] = Field(None, description="URL handle/slug")
    
    # Sync information
    last_synced_at: datetime = Field(default_factory=utc_now, description="Last sync timestamp")
    sync_version: int = Field(default=1, description="Sync version for conflict resolution")
    external_updated_at: Optional[datetime] = Field(None, description="Last updated in external system")
    
    # Timestamps
    created_at: datetime = Field(default_factory=utc_now)
    updated_at: datetime = Field(default_factory=utc_now)
    
    # Content generation metadata
    content_generated: bool = Field(default=False, description="Whether content has been generated")
    last_content_generation: Optional[datetime] = Field(None, description="Last content generation")
    content_generation_count: int = Field(default=0, description="Number of times content generated")
    
    # Metadata
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional product metadata")

    @validator('price', 'compare_at_price', pre=True)
    def validate_price(cls, v):
        """Validate and convert price to Decimal."""
        if v is None:
            return v
        if isinstance(v, str):
            return Decimal(v)
        return Decimal(str(v))


class SyncLog(BaseModel):
    """Sync operation log model."""
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )

    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    user_id: PyObjectId = Field(..., description="User ID")
    store_id: PyObjectId = Field(..., description="Store ID")
    
    # Operation details
    operation_type: SyncOperationEnum = Field(..., description="Type of sync operation")
    status: StatusEnum = Field(..., description="Operation status")
    
    # Results
    total_items: int = Field(default=0, description="Total items processed")
    successful_items: int = Field(default=0, description="Successfully processed items")
    failed_items: int = Field(default=0, description="Failed items")
    
    # Timing
    started_at: datetime = Field(default_factory=utc_now)
    completed_at: Optional[datetime] = Field(None, description="Completion timestamp")
    duration_seconds: Optional[float] = Field(None, description="Operation duration")
    
    # Error handling
    error_message: Optional[str] = Field(None, description="Error message if failed")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Detailed error information")
    
    # Metadata
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional sync metadata")


class ProductContentGeneration(BaseModel):
    """Product-based content generation tracking."""
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )

    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    user_id: PyObjectId = Field(..., description="User ID")
    product_id: PyObjectId = Field(..., description="Product ID")
    content_id: Optional[PyObjectId] = Field(None, description="Generated content ID")

    # Generation parameters
    platform: str = Field(..., description="Target platform")
    content_type: str = Field(default="social_post", description="Type of content generated")
    prompt_template: Optional[str] = Field(None, description="Template used for generation")

    # Product context used
    product_title: str = Field(..., description="Product title used")
    product_description: Optional[str] = Field(None, description="Product description used")
    product_price: Decimal = Field(..., description="Product price used")
    product_tags: List[str] = Field(default_factory=list, description="Product tags used")
    product_images: List[str] = Field(default_factory=list, description="Product image URLs used")

    # Generated content
    generated_text: Optional[str] = Field(None, description="Generated text content")
    generated_images: List[str] = Field(default_factory=list, description="Generated image URLs")

    # Performance tracking
    engagement_score: Optional[float] = Field(None, description="Content engagement score")
    conversion_rate: Optional[float] = Field(None, description="Conversion rate if tracked")

    # Timestamps
    created_at: datetime = Field(default_factory=utc_now)
    updated_at: datetime = Field(default_factory=utc_now)

    # Metadata
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional generation metadata")


class EcommerceWebhook(BaseModel):
    """Webhook event tracking for e-commerce platforms."""
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )

    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    store_id: PyObjectId = Field(..., description="Store ID")

    # Webhook details
    event_type: str = Field(..., description="Webhook event type")
    platform: EcommercePlatformEnum = Field(..., description="Source platform")
    external_id: Optional[str] = Field(None, description="External event/resource ID")

    # Processing status
    status: StatusEnum = Field(default=StatusEnum.PENDING, description="Processing status")
    processed_at: Optional[datetime] = Field(None, description="Processing timestamp")

    # Payload
    payload: Dict[str, Any] = Field(..., description="Webhook payload")
    headers: Optional[Dict[str, str]] = Field(None, description="Request headers")

    # Error handling
    error_message: Optional[str] = Field(None, description="Error message if processing failed")
    retry_count: int = Field(default=0, description="Number of retry attempts")
    max_retries: int = Field(default=3, description="Maximum retry attempts")

    # Timestamps
    received_at: datetime = Field(default_factory=utc_now)
    created_at: datetime = Field(default_factory=utc_now)


class InventoryChangeType(str, Enum):
    """Types of inventory changes."""
    SYNC = "sync"
    MANUAL = "manual"
    SALE = "sale"
    RESTOCK = "restock"
    ADJUSTMENT = "adjustment"
    RETURN = "return"
    DAMAGE = "damage"
    TRANSFER = "transfer"


class InventoryAlert(BaseModel):
    """Inventory alert configuration and tracking."""
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )

    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    user_id: PyObjectId = Field(..., description="User ID")
    store_id: PyObjectId = Field(..., description="Store ID")
    product_id: PyObjectId = Field(..., description="Product ID")
    variant_id: Optional[str] = Field(None, description="Product variant ID")

    # Alert configuration
    alert_type: str = Field(..., description="Alert type (low_stock, out_of_stock, high_demand)")
    threshold: int = Field(..., description="Inventory threshold for alert")
    is_active: bool = Field(default=True, description="Whether alert is active")

    # Alert status
    last_triggered: Optional[datetime] = Field(None, description="Last time alert was triggered")
    trigger_count: int = Field(default=0, description="Number of times alert has been triggered")
    is_resolved: bool = Field(default=False, description="Whether alert condition is resolved")

    # Notification settings
    email_enabled: bool = Field(default=True, description="Send email notifications")
    webhook_enabled: bool = Field(default=False, description="Send webhook notifications")
    webhook_url: Optional[str] = Field(None, description="Webhook URL for notifications")

    # Metadata
    created_at: datetime = Field(default_factory=utc_now)
    updated_at: datetime = Field(default_factory=utc_now)


class InventoryHistory(BaseModel):
    """Inventory change history tracking."""
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )

    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    user_id: PyObjectId = Field(..., description="User ID")
    store_id: PyObjectId = Field(..., description="Store ID")
    product_id: PyObjectId = Field(..., description="Product ID")
    variant_id: Optional[str] = Field(None, description="Product variant ID")

    # Change details
    change_type: InventoryChangeType = Field(..., description="Type of inventory change")
    previous_quantity: int = Field(..., description="Previous inventory quantity")
    new_quantity: int = Field(..., description="New inventory quantity")
    change_amount: int = Field(..., description="Amount of change (can be negative)")

    # Change context
    reason: Optional[str] = Field(None, description="Reason for change")
    reference_id: Optional[str] = Field(None, description="External reference (order ID, etc.)")
    changed_by: Optional[str] = Field(None, description="User or system that made the change")

    # Platform sync info
    external_sync: bool = Field(default=False, description="Whether change came from external platform")
    sync_timestamp: Optional[datetime] = Field(None, description="When change was synced from platform")

    # Metadata
    created_at: datetime = Field(default_factory=utc_now)


class InventorySnapshot(BaseModel):
    """Daily inventory snapshot for analytics."""
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )

    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    user_id: PyObjectId = Field(..., description="User ID")
    store_id: PyObjectId = Field(..., description="Store ID")
    snapshot_date: datetime = Field(..., description="Date of snapshot")

    # Aggregate metrics
    total_products: int = Field(default=0, description="Total products tracked")
    total_inventory_value: Decimal = Field(default=Decimal('0'), description="Total inventory value")
    low_stock_products: int = Field(default=0, description="Products with low stock")
    out_of_stock_products: int = Field(default=0, description="Out of stock products")

    # Category breakdown
    category_metrics: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="Metrics by category")

    # Top products by various metrics
    top_products_by_value: List[Dict[str, Any]] = Field(default_factory=list, description="Top products by inventory value")
    top_products_by_quantity: List[Dict[str, Any]] = Field(default_factory=list, description="Top products by quantity")

    # Change summary
    total_changes: int = Field(default=0, description="Total inventory changes in period")
    net_change: int = Field(default=0, description="Net inventory change")

    # Metadata
    created_at: datetime = Field(default_factory=utc_now)


class EcommerceAnalytics(BaseModel):
    """E-commerce analytics and insights."""
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )

    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    user_id: PyObjectId = Field(..., description="User ID")
    store_id: PyObjectId = Field(..., description="Store ID")

    # Time period
    period_start: datetime = Field(..., description="Analytics period start")
    period_end: datetime = Field(..., description="Analytics period end")

    # Product metrics
    total_products: int = Field(default=0, description="Total products")
    active_products: int = Field(default=0, description="Active products")
    out_of_stock_products: int = Field(default=0, description="Out of stock products")

    # Content generation metrics
    content_generated: int = Field(default=0, description="Content pieces generated")
    campaigns_created: int = Field(default=0, description="Campaigns created")
    social_posts: int = Field(default=0, description="Social posts created")

    # Performance metrics
    avg_engagement_rate: Optional[float] = Field(None, description="Average engagement rate")
    top_performing_products: List[str] = Field(default_factory=list, description="Top performing product IDs")

    # Category insights
    category_performance: Optional[Dict[str, Any]] = Field(None, description="Performance by category")
    price_range_analysis: Optional[Dict[str, Any]] = Field(None, description="Analysis by price range")

    # Inventory insights
    inventory_turnover: Optional[float] = Field(None, description="Inventory turnover rate")
    avg_days_in_stock: Optional[float] = Field(None, description="Average days products stay in stock")
    inventory_alerts_triggered: int = Field(default=0, description="Number of inventory alerts triggered")

    # Timestamps
    created_at: datetime = Field(default_factory=utc_now)
    updated_at: datetime = Field(default_factory=utc_now)

    # Metadata
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional analytics metadata")
