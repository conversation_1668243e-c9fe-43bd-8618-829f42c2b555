// @since 2024-1-1 to 2025-25-7
import axios from 'axios';
import { API_BASE_URL } from '../config';

/**
 * Email Provider Service
 * Handles all API communications for email provider configuration management
 */
class EmailProviderService {
  constructor() {
    this.baseURL = `${API_BASE_URL}/admin/email-providers`;
    this.setupInterceptors();
  }

  /**
   * Setup axios interceptors for authentication and error handling
   */
  setupInterceptors() {
    // Request interceptor to add auth token
    axios.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('adminToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    axios.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Handle unauthorized access
          localStorage.removeItem('adminToken');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Get all email providers with optional filtering
   */
  async getProviders(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.skip !== undefined) params.append('skip', filters.skip);
      if (filters.limit !== undefined) params.append('limit', filters.limit);
      if (filters.provider_type) params.append('provider_type', filters.provider_type);
      if (filters.email_type) params.append('email_type', filters.email_type);
      if (filters.status) params.append('status', filters.status);
      if (filters.is_active !== undefined) params.append('is_active', filters.is_active);
      if (filters.search) params.append('search', filters.search);

      const response = await axios.get(`${this.baseURL}?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching email providers:', error);
      throw error;
    }
  }

  /**
   * Get a specific email provider by ID
   */
  async getProvider(providerId) {
    try {
      const response = await axios.get(`${this.baseURL}/${providerId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching email provider ${providerId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new email provider
   */
  async createProvider(providerData) {
    try {
      const response = await axios.post(this.baseURL, providerData);
      return response.data;
    } catch (error) {
      console.error('Error creating email provider:', error);
      throw error;
    }
  }

  /**
   * Update an existing email provider
   */
  async updateProvider(providerId, providerData) {
    try {
      const response = await axios.put(`${this.baseURL}/${providerId}`, providerData);
      return response.data;
    } catch (error) {
      console.error(`Error updating email provider ${providerId}:`, error);
      throw error;
    }
  }

  /**
   * Delete an email provider
   */
  async deleteProvider(providerId) {
    try {
      const response = await axios.delete(`${this.baseURL}/${providerId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting email provider ${providerId}:`, error);
      throw error;
    }
  }

  /**
   * Test an email provider configuration
   */
  async testProvider(providerId, testData) {
    try {
      const response = await axios.post(`${this.baseURL}/${providerId}/test`, testData);
      return response.data;
    } catch (error) {
      console.error(`Error testing email provider ${providerId}:`, error);
      throw error;
    }
  }

  /**
   * Get email provider dashboard overview
   */
  async getDashboard() {
    try {
      const response = await axios.get(`${this.baseURL}/dashboard/overview`);
      return response.data;
    } catch (error) {
      console.error('Error fetching email provider dashboard:', error);
      throw error;
    }
  }

  /**
   * Get health status for a specific provider
   */
  async getProviderHealth(providerId) {
    try {
      const response = await axios.get(`${this.baseURL}/${providerId}/health`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching provider health ${providerId}:`, error);
      throw error;
    }
  }

  /**
   * Get provider statistics
   */
  async getProviderStats(providerId, filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.start_date) params.append('start_date', filters.start_date);
      if (filters.end_date) params.append('end_date', filters.end_date);
      if (filters.email_type) params.append('email_type', filters.email_type);

      const response = await axios.get(`${this.baseURL}/${providerId}/stats?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching provider stats ${providerId}:`, error);
      throw error;
    }
  }

  /**
   * Get delivery logs for a provider
   */
  async getDeliveryLogs(providerId, filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.skip !== undefined) params.append('skip', filters.skip);
      if (filters.limit !== undefined) params.append('limit', filters.limit);
      if (filters.start_date) params.append('start_date', filters.start_date);
      if (filters.end_date) params.append('end_date', filters.end_date);
      if (filters.status) params.append('status', filters.status);

      const response = await axios.get(`${this.baseURL}/${providerId}/logs?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching delivery logs ${providerId}:`, error);
      throw error;
    }
  }

  /**
   * Export provider configurations
   */
  async exportProviders(providerIds = []) {
    try {
      const params = new URLSearchParams();
      providerIds.forEach(id => params.append('provider_ids', id));

      const response = await axios.get(`${this.baseURL}/export?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error exporting email providers:', error);
      throw error;
    }
  }

  /**
   * Import provider configurations
   */
  async importProviders(importData) {
    try {
      const response = await axios.post(`${this.baseURL}/import`, importData);
      return response.data;
    } catch (error) {
      console.error('Error importing email providers:', error);
      throw error;
    }
  }

  /**
   * Validate provider configuration
   */
  async validateProvider(providerData) {
    try {
      const response = await axios.post(`${this.baseURL}/validate`, providerData);
      return response.data;
    } catch (error) {
      console.error('Error validating email provider:', error);
      throw error;
    }
  }

  /**
   * Get failover configuration for an email type
   */
  async getFailoverConfig(emailType) {
    try {
      const response = await axios.get(`${this.baseURL}/failover/${emailType}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching failover config for ${emailType}:`, error);
      throw error;
    }
  }

  /**
   * Update failover configuration for an email type
   */
  async updateFailoverConfig(emailType, failoverData) {
    try {
      const response = await axios.put(`${this.baseURL}/failover/${emailType}`, failoverData);
      return response.data;
    } catch (error) {
      console.error(`Error updating failover config for ${emailType}:`, error);
      throw error;
    }
  }

  /**
   * Get provider types and their capabilities
   */
  getProviderTypes() {
    return [
      {
        type: 'smtp',
        name: 'SMTP',
        description: 'Standard SMTP email server',
        capabilities: ['transactional', 'marketing', 'system'],
        settings: ['host', 'port', 'username', 'password', 'use_tls', 'use_ssl']
      },
      {
        type: 'sendgrid',
        name: 'SendGrid',
        description: 'SendGrid email delivery service',
        capabilities: ['transactional', 'marketing'],
        settings: ['api_key']
      },
      {
        type: 'mailgun',
        name: 'Mailgun',
        description: 'Mailgun email delivery service',
        capabilities: ['transactional', 'marketing'],
        settings: ['api_key', 'domain', 'region']
      },
      {
        type: 'aws_ses',
        name: 'AWS SES',
        description: 'Amazon Simple Email Service',
        capabilities: ['transactional', 'marketing'],
        settings: ['access_key_id', 'secret_access_key', 'region']
      }
    ];
  }

  /**
   * Get email types
   */
  getEmailTypes() {
    return [
      {
        type: 'transactional',
        name: 'Transactional',
        description: 'User-triggered emails (welcome, password reset, etc.)'
      },
      {
        type: 'marketing',
        name: 'Marketing',
        description: 'Promotional and campaign emails'
      },
      {
        type: 'system',
        name: 'System',
        description: 'Administrative and monitoring notifications'
      }
    ];
  }
}

// Create and export a singleton instance
export const emailProviderService = new EmailProviderService();
export default emailProviderService;
