"""
Lemon Squeezy integration service for ACEO add-on system.
Handles product variant creation, checkout sessions, and webhook processing.
@since 2024-1-1 to 2025-25-7
"""
import logging
import json
import hmac
import hashlib
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone

import httpx
from app.db.mongodb import get_database
from app.core.config import settings
# Models are now handled as dictionaries for MongoDB
from app.services.addon_usage_tracking import usage_tracker
from app.services.notification import notification_service
from app.models.notification import NotificationType
from app.core.monitoring import record_addon_purchase_metrics
from bson import ObjectId

logger = logging.getLogger(__name__)

async def send_addon_notification(user_id: str, title: str, message: str, notification_type: str = "info"):
    """Send addon-related notification using the notification service."""
    try:
        # Convert notification_type string to NotificationType enum
        type_mapping = {
            "info": NotificationType.SYSTEM,
            "success": NotificationType.BILLING,
            "error": NotificationType.ERROR,
            "warning": NotificationType.SYSTEM
        }

        notification_type_enum = type_mapping.get(notification_type, NotificationType.SYSTEM)

        await notification_service.create_notification(
            user_id=ObjectId(user_id),
            title=title,
            message=message,
            notification_type=notification_type_enum,
            entity_type="addon",
            send_push=True,
            send_email=False  # Don't send email for addon notifications
        )
        return True
    except Exception as e:
        logger.warning(f"Failed to send addon notification: {str(e)}")
        return False


class LemonSqueezyAddonService:
    """Service for managing Lemon Squeezy add-on products and purchases."""
    
    def __init__(self):
        self.api_key = settings.LEMON_SQUEEZY_API_KEY
        self.store_id = getattr(settings, 'LEMON_SQUEEZY_ADDON_STORE_ID', settings.LEMON_SQUEEZY_STORE_ID)
        self.webhook_secret = getattr(settings, 'LEMON_SQUEEZY_ADDON_WEBHOOK_SECRET', settings.LEMON_SQUEEZY_WEBHOOK_SECRET)
        self.base_url = "https://api.lemonsqueezy.com/v1"
        
        # Add-on product configurations for Lemon Squeezy
        self.addon_products = {
            "regeneration_booster": {
                "name": "Extra Regeneration Credits",
                "description": "Boost your content optimization with additional regeneration credits",
                "variants": {
                    "basic": {
                        "name": "Basic Pack - 100 Credits",
                        "price": 999,  # $9.99 in cents
                        "credits": 100,
                        "description": "100 additional regeneration credits"
                    },
                    "premium": {
                        "name": "Premium Pack - 250 Credits", 
                        "price": 1999,  # $19.99 in cents
                        "credits": 250,
                        "description": "250 additional regeneration credits"
                    },
                    "enterprise": {
                        "name": "Enterprise Pack - 600 Credits",
                        "price": 3999,  # $39.99 in cents
                        "credits": 600,
                        "description": "600 additional regeneration credits"
                    }
                }
            },
            "image_pack_premium": {
                "name": "Premium Image Generation Pack",
                "description": "Enhanced image generation capabilities with DALL-E",
                "variants": {
                    "basic": {
                        "name": "Basic Pack - 50 Images",
                        "price": 1599,  # $15.99 in cents
                        "credits": 50,
                        "description": "50 premium AI-generated images"
                    },
                    "premium": {
                        "name": "Premium Pack - 120 Images",
                        "price": 2999,  # $29.99 in cents
                        "credits": 120,
                        "description": "120 premium AI-generated images"
                    },
                    "enterprise": {
                        "name": "Enterprise Pack - 300 Images",
                        "price": 5999,  # $59.99 in cents
                        "credits": 300,
                        "description": "300 premium AI-generated images"
                    }
                }
            },
            "sentiment_analysis_pro": {
                "name": "Advanced Sentiment Analysis",
                "description": "Deep sentiment analysis with emotion detection",
                "variants": {
                    "basic": {
                        "name": "Basic Pack - 1000 Analyses",
                        "price": 1999,  # $19.99 in cents
                        "credits": 1000,
                        "description": "1000 advanced sentiment analyses"
                    },
                    "premium": {
                        "name": "Premium Pack - 2500 Analyses",
                        "price": 3999,  # $39.99 in cents
                        "credits": 2500,
                        "description": "2500 advanced sentiment analyses"
                    }
                }
            },
            "additional_user_seats": {
                "name": "Additional Team Seats",
                "description": "Expand your team with additional user accounts",
                "variants": {
                    "monthly": {
                        "name": "Additional Seat - Monthly",
                        "price": 1500,  # $15.00 in cents
                        "credits": 1,
                        "description": "1 additional team member per month",
                        "billing_cycle": "monthly"
                    }
                }
            },
            "priority_support": {
                "name": "Priority Support",
                "description": "24/7 priority support with guaranteed response times",
                "variants": {
                    "monthly": {
                        "name": "Priority Support - Monthly",
                        "price": 4999,  # $49.99 in cents
                        "credits": 1,
                        "description": "Priority support access",
                        "billing_cycle": "monthly"
                    }
                }
            },
            "white_label_platform": {
                "name": "White Label Platform",
                "description": "Rebrand the platform with your logo and colors",
                "variants": {
                    "monthly": {
                        "name": "White Label - Monthly",
                        "price": 9999,  # $99.99 in cents
                        "credits": 1,
                        "description": "White label platform access",
                        "billing_cycle": "monthly"
                    }
                }
            }
        }
    
    async def create_checkout_session(self, user_id: int, addon_id: str, 
                                    variant: str = "basic") -> Dict[str, Any]:
        """Create a Lemon Squeezy checkout session for an add-on purchase."""
        try:
            # Get product configuration
            product_config = self.addon_products.get(addon_id)
            if not product_config:
                raise ValueError(f"Unknown add-on: {addon_id}")
            
            variant_config = product_config["variants"].get(variant)
            if not variant_config:
                raise ValueError(f"Unknown variant: {variant} for add-on: {addon_id}")
            
            # Create checkout session
            checkout_data = {
                "data": {
                    "type": "checkouts",
                    "attributes": {
                        "checkout_data": {
                            "custom": {
                                "user_id": str(user_id),
                                "addon_id": addon_id,
                                "variant": variant
                            }
                        },
                        "product_options": {
                            "name": variant_config["name"],
                            "description": variant_config["description"],
                            "media": [],
                            "redirect_url": f"{settings.FRONTEND_URL}/addons?purchase=success",
                            "receipt_button_text": "Go to Add-ons",
                            "receipt_link_url": f"{settings.FRONTEND_URL}/addons"
                        },
                        "checkout_options": {
                            "embed": False,
                            "media": True,
                            "logo": True
                        },
                        "expires_at": None,
                        "preview": False,
                        "test_mode": settings.ENVIRONMENT != "production"
                    },
                    "relationships": {
                        "store": {
                            "data": {
                                "type": "stores",
                                "id": str(self.store_id)
                            }
                        },
                        "variant": {
                            "data": {
                                "type": "variants",
                                "id": await self._get_or_create_variant_id(addon_id, variant)
                            }
                        }
                    }
                }
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/checkouts",
                    headers={
                        "Authorization": f"Bearer {self.api_key}",
                        "Accept": "application/vnd.api+json",
                        "Content-Type": "application/vnd.api+json"
                    },
                    json=checkout_data
                )
                
                if response.status_code == 201:
                    checkout = response.json()
                    return {
                        "success": True,
                        "checkout_url": checkout["data"]["attributes"]["url"],
                        "checkout_id": checkout["data"]["id"]
                    }
                else:
                    logger.error(f"Lemon Squeezy checkout creation failed: {response.text}")
                    return {
                        "success": False,
                        "error": "Failed to create checkout session"
                    }
                    
        except Exception as e:
            logger.error(f"Error creating checkout session: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _get_or_create_variant_id(self, addon_id: str, variant: str) -> str:
        """Get or create a Lemon Squeezy variant ID for the add-on."""
        # In production, this would check if the variant exists and create it if not
        # For now, return a placeholder that would be configured in Lemon Squeezy dashboard
        variant_mapping = {
            "regeneration_booster_basic": "123456",
            "regeneration_booster_premium": "123457", 
            "regeneration_booster_enterprise": "123458",
            "image_pack_premium_basic": "123459",
            "image_pack_premium_premium": "123460",
            "image_pack_premium_enterprise": "123461",
            "sentiment_analysis_pro_basic": "123462",
            "sentiment_analysis_pro_premium": "123463",
            "additional_user_seats_monthly": "123464",
            "priority_support_monthly": "123465",
            "white_label_platform_monthly": "123466"
        }
        
        variant_key = f"{addon_id}_{variant}"
        return variant_mapping.get(variant_key, "123456")  # Default variant ID
    
    async def process_webhook(self, payload: bytes, signature: str) -> Dict[str, Any]:
        """Process Lemon Squeezy webhook events."""
        try:
            # Verify webhook signature
            if not self._verify_webhook_signature(payload, signature):
                return {"success": False, "error": "Invalid webhook signature"}
            
            # Parse webhook data
            webhook_data = json.loads(payload.decode('utf-8'))
            event_name = webhook_data.get("meta", {}).get("event_name")
            
            if event_name == "order_created":
                return await self._handle_order_created(webhook_data)
            elif event_name == "order_refunded":
                return await self._handle_order_refunded(webhook_data)
            elif event_name == "subscription_created":
                return await self._handle_subscription_created(webhook_data)
            elif event_name == "subscription_cancelled":
                return await self._handle_subscription_cancelled(webhook_data)
            else:
                logger.info(f"Unhandled webhook event: {event_name}")
                return {"success": True, "message": "Event ignored"}
                
        except Exception as e:
            logger.error(f"Error processing webhook: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _verify_webhook_signature(self, payload: bytes, signature: str) -> bool:
        """Verify the webhook signature from Lemon Squeezy."""
        try:
            expected_signature = hmac.new(
                self.webhook_secret.encode('utf-8'),
                payload,
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(f"sha256={expected_signature}", signature)
        except Exception as e:
            logger.error(f"Error verifying webhook signature: {str(e)}")
            return False
    
    async def _handle_order_created(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle successful add-on purchase."""
        try:
            db = await get_database()

            order_data = webhook_data["data"]
            custom_data = order_data["attributes"]["first_order_item"]["product_options"]["custom"]

            user_id = int(custom_data["user_id"])
            addon_id = custom_data["addon_id"]
            variant = custom_data["variant"]

            # Get product configuration
            product_config = self.addon_products[addon_id]
            variant_config = product_config["variants"][variant]

            # Create user add-on record
            user_addon_data = {
                "user_id": user_id,
                "addon_id": addon_id,
                "variant": variant,
                "total_credits": variant_config["credits"],
                "credits_remaining": variant_config["credits"],
                "credits_used": 0,
                "purchase_price": variant_config["price"] / 100,  # Convert from cents
                "purchased_at": datetime.now(timezone.utc),
                "expires_at": self._calculate_expiry_date(variant_config),
                "is_active": True,
                "lemon_squeezy_order_id": order_data["id"],
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }

            # Insert user addon
            user_addons_collection = db["user_addons"]
            addon_result = await user_addons_collection.insert_one(user_addon_data)

            # Create purchase record
            purchase_data = {
                "user_id": user_id,
                "user_addon_id": addon_result.inserted_id,
                "addon_id": addon_id,
                "variant": variant,
                "amount": variant_config["price"] / 100,
                "currency": "USD",
                "lemon_squeezy_order_id": order_data["id"],
                "status": "completed",
                "purchased_at": datetime.now(timezone.utc),
                "completed_at": datetime.now(timezone.utc),
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }

            # Insert purchase record
            purchases_collection = db["addon_purchases"]
            await purchases_collection.insert_one(purchase_data)
            
            # Send notification (if notification service is available)
            try:
                await send_addon_notification(
                    str(user_id),
                    "Add-on Purchase Successful",
                    f"Your {product_config['name']} ({variant_config['name']}) has been activated!",
                    "success"
                )
            except Exception as e:
                logger.warning(f"Failed to send purchase notification: {str(e)}")
            
            # Record metrics
            record_addon_purchase_metrics(
                addon_id, 
                variant, 
                "unknown",  # Would get from user data
                variant_config["price"] / 100
            )
            
            logger.info(f"Add-on purchase processed: {addon_id} for user {user_id}")
            
            return {"success": True, "message": "Purchase processed successfully"}
            
        except Exception as e:
            logger.error(f"Error handling order created: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _handle_order_refunded(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle add-on refund."""
        try:
            db = await get_database()

            order_data = webhook_data["data"]
            order_id = order_data["id"]

            # Find the user add-on
            user_addons_collection = db["user_addons"]
            user_addon = await user_addons_collection.find_one({
                "lemon_squeezy_order_id": order_id
            })

            if user_addon:
                # Mark as refunded
                await user_addons_collection.update_one(
                    {"_id": user_addon["_id"]},
                    {
                        "$set": {
                            "is_refunded": True,
                            "is_active": False,
                            "refunded_at": datetime.now(timezone.utc),
                            "updated_at": datetime.now(timezone.utc)
                        }
                    }
                )

                # Update purchase record
                purchases_collection = db["addon_purchases"]
                await purchases_collection.update_one(
                    {"lemon_squeezy_order_id": order_id},
                    {
                        "$set": {
                            "status": "refunded",
                            "refunded_at": datetime.now(timezone.utc),
                            "updated_at": datetime.now(timezone.utc)
                        }
                    }
                )

                # Send notification (if notification service is available)
                try:
                    await send_addon_notification(
                        user_addon["user_id"],
                        "Add-on Refund Processed",
                        f"Your refund for {user_addon['addon_id']} has been processed.",
                        "info"
                    )
                except Exception as e:
                    logger.warning(f"Failed to send refund notification: {str(e)}")

                logger.info(f"Add-on refund processed: {user_addon['addon_id']} for user {user_addon['user_id']}")

            return {"success": True, "message": "Refund processed successfully"}
            
        except Exception as e:
            logger.error(f"Error handling order refunded: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _handle_subscription_created(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle recurring add-on subscription creation."""
        # Similar to order_created but for recurring add-ons
        return await self._handle_order_created(webhook_data)
    
    async def _handle_subscription_cancelled(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle recurring add-on subscription cancellation."""
        try:
            db = await get_database()

            subscription_data = webhook_data["data"]
            subscription_id = subscription_data["id"]

            # Find and deactivate recurring add-ons
            user_addons_collection = db["user_addons"]
            user_addons_cursor = user_addons_collection.find({
                "lemon_squeezy_subscription_id": subscription_id
            })

            async for user_addon in user_addons_cursor:
                # Update addon to inactive
                await user_addons_collection.update_one(
                    {"_id": user_addon["_id"]},
                    {
                        "$set": {
                            "is_active": False,
                            "cancelled_at": datetime.now(timezone.utc),
                            "updated_at": datetime.now(timezone.utc)
                        }
                    }
                )

                # Send notification (if notification service is available)
                try:
                    await send_addon_notification(
                        user_addon["user_id"],
                        "Add-on Subscription Cancelled",
                        f"Your {user_addon['addon_id']} subscription has been cancelled.",
                        "warning"
                    )
                except Exception as e:
                    logger.warning(f"Failed to send cancellation notification: {str(e)}")

            return {"success": True, "message": "Subscription cancellation processed"}
            
        except Exception as e:
            logger.error(f"Error handling subscription cancelled: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _calculate_expiry_date(self, variant_config: Dict[str, Any]) -> Optional[datetime]:
        """Calculate expiry date for add-on based on billing cycle."""
        billing_cycle = variant_config.get("billing_cycle")

        if billing_cycle == "monthly":
            # Add 30 days for monthly billing
            from datetime import timedelta
            return datetime.now(timezone.utc) + timedelta(days=30)
        elif billing_cycle == "yearly":
            # Add 365 days for yearly billing
            from datetime import timedelta
            return datetime.now(timezone.utc) + timedelta(days=365)
        else:
            # One-time purchase, no expiry
            return None


# Global service instance
lemon_squeezy_addon_service = LemonSqueezyAddonService()


async def create_addon_checkout(user_id: int, addon_id: str, variant: str = "basic") -> Dict[str, Any]:
    """Create a checkout session for an add-on purchase."""
    return await lemon_squeezy_addon_service.create_checkout_session(user_id, addon_id, variant)


async def process_addon_webhook(payload: bytes, signature: str) -> Dict[str, Any]:
    """Process Lemon Squeezy webhook for add-on purchases."""
    return await lemon_squeezy_addon_service.process_webhook(payload, signature)
