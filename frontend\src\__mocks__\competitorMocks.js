/**
 * Mock data and utilities for competitor testing
 @since 2024-1-1 to 2025-25-7
*/

/**
 * Create a mock competitor object
 */
export const createMockCompetitor = (overrides = {}) => ({
  id: 'comp-123',
  _id: 'comp-123',
  name: 'Test Competitor',
  industry: 'Technology',
  description: 'A test competitor for unit testing',
  website: 'https://example.com',
  social_profiles: {
    linkedin: 'https://linkedin.com/company/test',
    twitter: 'https://twitter.com/test',
    facebook: 'https://facebook.com/test',
    instagram: 'https://instagram.com/test'
  },
  metrics: {
    linkedin: {
      followers_count: 10000,
      engagement_rate: 3.5,
      posts_count: 150,
      posting_frequency: 1.2,
      content_types: {
        text: 40,
        image: 35,
        video: 20,
        carousel: 5
      }
    },
    twitter: {
      followers_count: 25000,
      engagement_rate: 2.8,
      posts_count: 300,
      posting_frequency: 2.1,
      content_types: {
        text: 60,
        image: 25,
        video: 15
      }
    },
    facebook: {
      followers_count: 15000,
      engagement_rate: 4.2,
      posts_count: 120,
      posting_frequency: 0.8,
      content_types: {
        text: 30,
        image: 40,
        video: 25,
        link: 5
      }
    },
    instagram: {
      followers_count: 50000,
      engagement_rate: 5.1,
      posts_count: 200,
      posting_frequency: 1.5,
      content_types: {
        image: 70,
        video: 25,
        carousel: 5
      }
    }
  },
  overall_score: 75.5,
  trend: 'growing',
  last_updated: new Date().toISOString(),
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides
});

/**
 * Create mock comparison data
 */
export const createMockComparisonData = (competitors = []) => ({
  competitors: competitors.map(competitor => ({
    id: competitor.id,
    name: competitor.name,
    overall_score: competitor.overall_score || 75,
    metrics: competitor.metrics || {},
    trend: competitor.trend || 'stable'
  })),
  industry_benchmarks: {
    linkedin: {
      avg_followers: 8500,
      avg_engagement_rate: 3.2,
      avg_posting_frequency: 1.1
    },
    twitter: {
      avg_followers: 22000,
      avg_engagement_rate: 2.5,
      avg_posting_frequency: 2.0
    },
    facebook: {
      avg_followers: 12000,
      avg_engagement_rate: 3.8,
      avg_posting_frequency: 0.7
    },
    instagram: {
      avg_followers: 45000,
      avg_engagement_rate: 4.8,
      avg_posting_frequency: 1.3
    }
  },
  analysis_summary: {
    top_performer: competitors[0]?.name || 'Test Competitor',
    key_insights: [
      'Instagram shows highest engagement rates',
      'Twitter has most frequent posting',
      'LinkedIn shows professional content focus'
    ],
    recommendations: [
      'Increase video content on Instagram',
      'Optimize posting times for better engagement',
      'Focus on industry-specific content themes'
    ]
  },
  generated_at: new Date().toISOString()
});

/**
 * Create mock recommendations data
 */
export const createMockRecommendations = () => ({
  recommendations: [
    {
      area: 'content_strategy',
      priority: 'high',
      recommendation: 'Increase video content production to match top performers',
      reasoning: 'Video content shows 40% higher engagement rates across all platforms',
      impact_score: 8.5,
      effort_required: 'medium'
    },
    {
      area: 'posting_frequency',
      priority: 'medium',
      recommendation: 'Optimize posting schedule based on audience activity',
      reasoning: 'Competitors posting during peak hours see 25% better engagement',
      impact_score: 6.2,
      effort_required: 'low'
    },
    {
      area: 'platform_focus',
      priority: 'medium',
      recommendation: 'Increase Instagram presence to capture younger demographics',
      reasoning: 'Instagram shows highest growth potential in your industry',
      impact_score: 7.1,
      effort_required: 'high'
    }
  ],
  generated_at: new Date().toISOString(),
  confidence_score: 0.85
});

/**
 * Create mock user comparison data
 */
export const createMockUserComparison = (competitors = []) => ({
  user_metrics: {
    avg_likes: 150,
    avg_comments: 25,
    avg_shares: 12,
    engagement_rate: 3.2
  },
  competitors: competitors.map(competitor => ({
    id: competitor.id,
    name: competitor.name,
    metrics: {
      avg_likes: Math.floor(Math.random() * 300) + 100,
      avg_comments: Math.floor(Math.random() * 50) + 10,
      avg_shares: Math.floor(Math.random() * 30) + 5,
      engagement_rate: Math.random() * 3 + 2
    }
  })),
  performance_gap: {
    likes: -15.5,
    comments: 8.2,
    shares: -22.1,
    engagement_rate: -0.8
  },
  content_strategy_comparison: {
    top_performing_content_types: ['video', 'carousel', 'image'],
    recommended_content_mix: {
      video: 0.35,
      image: 0.40,
      carousel: 0.15,
      text: 0.10
    }
  },
  posting_routine_comparison: {
    recommended_days: [
      ['Monday', 15],
      ['Wednesday', 18],
      ['Friday', 22]
    ],
    recommended_times: [
      ['9:00 AM', 12],
      ['1:00 PM', 16],
      ['6:00 PM', 20]
    ]
  },
  generated_at: new Date().toISOString()
});

/**
 * Create mock industry benchmarks
 */
export const createMockIndustryBenchmarks = (platform = 'linkedin') => ({
  platform,
  industry: 'Technology',
  benchmarks: {
    followers_count: {
      percentile_25: 5000,
      percentile_50: 12000,
      percentile_75: 25000,
      percentile_90: 50000
    },
    engagement_rate: {
      percentile_25: 2.1,
      percentile_50: 3.2,
      percentile_75: 4.8,
      percentile_90: 6.5
    },
    posting_frequency: {
      percentile_25: 0.5,
      percentile_50: 1.2,
      percentile_75: 2.1,
      percentile_90: 3.5
    }
  },
  sample_size: 1250,
  last_updated: new Date().toISOString()
});

/**
 * Mock API responses
 */
export const mockApiResponses = {
  compareCompetitors: (competitorIds, platforms) => 
    Promise.resolve(createMockComparisonData(
      competitorIds.map(id => createMockCompetitor({ id }))
    )),
  
  getIndustryBenchmarks: (platform) => 
    Promise.resolve(createMockIndustryBenchmarks(platform)),
  
  refreshCompetitorAnalytics: () => 
    Promise.resolve({ status: 'success', message: 'Refresh started' }),
  
  exportCompetitorData: () => 
    Promise.resolve(new Blob(['mock data'], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })),
  
  getRecommendations: () => 
    Promise.resolve(createMockRecommendations()),
  
  compareWithUser: (competitorIds) => 
    Promise.resolve(createMockUserComparison(
      competitorIds.map(id => createMockCompetitor({ id }))
    ))
};

/**
 * Mock error responses
 */
export const mockErrorResponses = {
  networkError: () => Promise.reject(new Error('Network error')),
  validationError: () => Promise.reject(new Error('Invalid competitor IDs')),
  permissionError: () => Promise.reject(new Error('Insufficient permissions')),
  rateLimitError: () => Promise.reject(new Error('Rate limit exceeded')),
  serverError: () => Promise.reject(new Error('Internal server error'))
};

/**
 * Test utilities
 */
export const testUtils = {
  /**
   * Create a competitor list for testing
   */
  createCompetitorList: (count = 3) => 
    Array.from({ length: count }, (_, i) => 
      createMockCompetitor({ 
        id: `comp-${i + 1}`, 
        name: `Competitor ${String.fromCharCode(65 + i)}` 
      })
    ),

  /**
   * Create mock context value
   */
  createMockContext: (overrides = {}) => ({
    competitors: testUtils.createCompetitorList(),
    loading: false,
    error: null,
    fetchCompetitors: vi.fn(),
    compareCompetitors: vi.fn().mockResolvedValue(createMockComparisonData()),
    getRecommendations: vi.fn().mockResolvedValue(createMockRecommendations()),
    ...overrides
  }),

  /**
   * Wait for async operations
   */
  waitForAsync: () => new Promise(resolve => setTimeout(resolve, 0))
};
