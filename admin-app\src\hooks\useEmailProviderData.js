// @since 2024-1-1 to 2025-25-7
import { useState, useCallback } from 'react';
import { emailProviderService } from '../services/emailProviderService';

/**
 * Custom hook for managing email provider data and operations
 * Provides state management and API integration for email provider configuration
 */
export const useEmailProviderData = () => {
  const [providers, setProviders] = useState([]);
  const [dashboard, setDashboard] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Network status monitoring
  const handleOnline = useCallback(() => setIsOnline(true), []);
  const handleOffline = useCallback(() => setIsOnline(false), []);

  // Add event listeners for network status
  if (typeof window !== 'undefined') {
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
  }

  /**
   * Fetch all email providers with optional filtering
   */
  const fetchProviders = useCallback(async (filters = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailProviderService.getProviders(filters);
      setProviders(response.data.providers || []);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to fetch email providers';
      setError(errorMessage);
      console.error('Error fetching email providers:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Fetch dashboard overview data
   */
  const fetchDashboard = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailProviderService.getDashboard();
      setDashboard(response.data);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to fetch dashboard data';
      setError(errorMessage);
      console.error('Error fetching dashboard data:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Get a specific email provider by ID
   */
  const getProvider = useCallback(async (providerId) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailProviderService.getProvider(providerId);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to fetch email provider';
      setError(errorMessage);
      console.error('Error fetching email provider:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Create a new email provider
   */
  const createProvider = useCallback(async (providerData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailProviderService.createProvider(providerData);
      
      // Update local state
      setProviders(prev => [...prev, response.data]);
      
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to create email provider';
      setError(errorMessage);
      console.error('Error creating email provider:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Update an existing email provider
   */
  const updateProvider = useCallback(async (providerId, providerData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailProviderService.updateProvider(providerId, providerData);
      
      // Update local state
      setProviders(prev => 
        prev.map(provider => 
          provider.id === providerId ? response.data : provider
        )
      );
      
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to update email provider';
      setError(errorMessage);
      console.error('Error updating email provider:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Delete an email provider
   */
  const deleteProvider = useCallback(async (providerId) => {
    setLoading(true);
    setError(null);
    
    try {
      await emailProviderService.deleteProvider(providerId);
      
      // Update local state
      setProviders(prev => prev.filter(provider => provider.id !== providerId));
      
      return true;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to delete email provider';
      setError(errorMessage);
      console.error('Error deleting email provider:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Test an email provider configuration
   */
  const testProvider = useCallback(async (providerId, testData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailProviderService.testProvider(providerId, testData);
      
      // Update provider status in local state if test result affects it
      if (response.data.success !== undefined) {
        setProviders(prev => 
          prev.map(provider => {
            if (provider.id === providerId) {
              return {
                ...provider,
                status: response.data.success ? 'active' : 'failed',
                last_test_date: new Date().toISOString(),
                last_test_result: response.data
              };
            }
            return provider;
          })
        );
      }
      
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to test email provider';
      setError(errorMessage);
      console.error('Error testing email provider:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Get health status for a specific provider
   */
  const getProviderHealth = useCallback(async (providerId) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailProviderService.getProviderHealth(providerId);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to fetch provider health';
      setError(errorMessage);
      console.error('Error fetching provider health:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Get provider statistics
   */
  const getProviderStats = useCallback(async (providerId, filters = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await emailProviderService.getProviderStats(providerId, filters);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to fetch provider statistics';
      setError(errorMessage);
      console.error('Error fetching provider statistics:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Refresh all data
   */
  const refreshData = useCallback(async () => {
    try {
      await Promise.all([
        fetchProviders(),
        fetchDashboard()
      ]);
    } catch (err) {
      console.error('Error refreshing data:', err);
      throw err;
    }
  }, [fetchProviders, fetchDashboard]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Reset all state
   */
  const reset = useCallback(() => {
    setProviders([]);
    setDashboard(null);
    setLoading(false);
    setError(null);
  }, []);

  return {
    // State
    providers,
    dashboard,
    loading,
    error,
    isOnline,
    
    // Actions
    fetchProviders,
    fetchDashboard,
    getProvider,
    createProvider,
    updateProvider,
    deleteProvider,
    testProvider,
    getProviderHealth,
    getProviderStats,
    refreshData,
    clearError,
    reset,
    
    // Computed values
    totalProviders: providers.length,
    activeProviders: providers.filter(p => p.is_active && p.status === 'active').length,
    failedProviders: providers.filter(p => p.status === 'failed').length,
    hasProviders: providers.length > 0,
    hasError: !!error
  };
};
