/**
 * Higher-order component for wrapping campaign components with error boundary
 * Separated from main component file to support Fast Refresh
 @since 2024-1-1 to 2025-25-7
*/

import CampaignErrorBoundary from './CampaignErrorBoundary';

/**
 * Higher-order component for wrapping campaign components with error boundary
 * 
 * @param {React.Component} Component - The component to wrap
 * @param {React.Component} fallback - Optional custom fallback component
 * @returns {React.Component} - Wrapped component with error boundary
 */
export const withCampaignErrorBoundary = (Component, fallback = null) => {
  const WrappedComponent = function(props) {
    return (
      <CampaignErrorBoundary fallback={fallback}>
        <Component {...props} />
      </CampaignErrorBoundary>
    );
  };
  
  // Set display name for debugging
  WrappedComponent.displayName = `withCampaignErrorBoundary(${Component.displayName || Component.name || 'Component'})`;
  
  return WrappedComponent;
};

export default withCampaignErrorBoundary;
