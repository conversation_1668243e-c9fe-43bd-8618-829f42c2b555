/**
 * Test setup file for Vitest
 @since 2024-1-1 to 2025-25-7
*/

// <PERSON>ck React for testing environment
import { vi } from 'vitest';

// Mock React.createElement for platform service tests
global.React = {
  createElement: vi.fn((component, props, ...children) => ({
    type: component,
    props: { ...props, children },
  })),
};

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  warn: vi.fn(),
  error: vi.fn(),
  debug: vi.fn(),
};

// Setup DOM environment
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});
