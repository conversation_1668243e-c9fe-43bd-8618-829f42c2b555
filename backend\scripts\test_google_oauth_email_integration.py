#!/usr/bin/env python3
"""
Google OAuth Email Integration Test

This script tests the integration between Google OAuth and email services
to ensure welcome emails are properly sent when users register via Google OAuth.

Usage:
    python scripts/test_google_oauth_email_integration.py

@since 2024-1-1 to 2025-25-7
"""
import asyncio
import sys
import os
from datetime import datetime
from unittest.mock import Mock, patch

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.services.email_service import send_welcome_email
from app.services.social_media.google import GoogleIntegration


class GoogleOAuthEmailIntegrationTester:
    """Test Google OAuth email integration."""
    
    def __init__(self):
        self.results = []
    
    def log_result(self, test_name: str, status: str, message: str):
        """Log test result."""
        result = {
            "test": test_name,
            "status": status,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        self.results.append(result)
        
        color = {
            "PASS": "\033[92m",    # Green
            "FAIL": "\033[91m",    # Red
            "WARNING": "\033[93m", # Yellow
        }.get(status, "\033[0m")
        
        reset_color = "\033[0m"
        print(f"[{color}{status}{reset_color}] {test_name}: {message}")
    
    async def test_welcome_email_function(self):
        """Test welcome email function directly."""
        try:
            result = await send_welcome_email("<EMAIL>", "Test User")
            if result:
                self.log_result(
                    "Welcome Email Function",
                    "PASS",
                    "Welcome email function executed successfully"
                )
            else:
                self.log_result(
                    "Welcome Email Function",
                    "FAIL",
                    "Welcome email function returned False"
                )
        except Exception as e:
            self.log_result(
                "Welcome Email Function",
                "FAIL",
                f"Welcome email function failed: {str(e)}"
            )
    
    def test_google_oauth_service_import(self):
        """Test Google OAuth service can import email functions."""
        try:
            # Test that Google OAuth routes can import email functions
            from app.api.routes.auth import send_welcome_email as oauth_welcome_email
            
            self.log_result(
                "Google OAuth Import",
                "PASS",
                "Google OAuth routes successfully import email functions"
            )
        except ImportError as e:
            self.log_result(
                "Google OAuth Import",
                "FAIL",
                f"Failed to import email functions in OAuth routes: {str(e)}"
            )
    
    async def test_oauth_user_creation_flow(self):
        """Test the OAuth user creation flow with email sending."""
        try:
            # Mock the user creation process
            with patch('app.services.user.create_oauth_user') as mock_create_user, \
                 patch('app.services.email_service.send_welcome_email') as mock_send_email:
                
                # Configure mocks
                mock_user = Mock()
                mock_user.id = "test_user_id"
                mock_user.email = "<EMAIL>"
                mock_user.full_name = "Test User"
                mock_create_user.return_value = mock_user
                mock_send_email.return_value = True
                
                # Simulate OAuth user creation
                user_info = {
                    "email": "<EMAIL>",
                    "name": "Test User",
                    "id": "google_user_123",
                    "given_name": "Test",
                    "family_name": "User",
                    "picture": "https://example.com/avatar.jpg",
                    "verified_email": True
                }
                
                # Test user creation
                user = await mock_create_user(
                    email=user_info["email"],
                    full_name=user_info.get("name", ""),
                    oauth_provider="google",
                    oauth_id=user_info["id"],
                    first_name=user_info.get("given_name"),
                    last_name=user_info.get("family_name"),
                    avatar=user_info.get("picture"),
                    is_verified=user_info.get("verified_email", False)
                )
                
                # Test welcome email sending
                email_result = await mock_send_email(user.email, user.full_name)
                
                if email_result:
                    self.log_result(
                        "OAuth User Creation Flow",
                        "PASS",
                        "OAuth user creation and welcome email flow works correctly"
                    )
                else:
                    self.log_result(
                        "OAuth User Creation Flow",
                        "FAIL",
                        "Welcome email sending failed in OAuth flow"
                    )
                    
        except Exception as e:
            self.log_result(
                "OAuth User Creation Flow",
                "FAIL",
                f"OAuth user creation flow failed: {str(e)}"
            )
    
    def test_email_template_availability(self):
        """Test that required email templates are available."""
        template_path = os.path.join(os.path.dirname(__file__), '..', 'app', 'templates', 'emails')
        required_templates = [
            'welcome.html',
            'password_reset.html',
            'email_verification.html',
            'trial_started.html',
            'team_invitation.html'
        ]
        
        missing_templates = []
        for template in required_templates:
            if not os.path.exists(os.path.join(template_path, template)):
                missing_templates.append(template)
        
        if missing_templates:
            self.log_result(
                "Email Templates",
                "WARNING",
                f"Missing templates: {', '.join(missing_templates)}"
            )
        else:
            self.log_result(
                "Email Templates",
                "PASS",
                f"All {len(required_templates)} required email templates found"
            )
    
    def test_error_handling(self):
        """Test email service error handling."""
        try:
            # Test with invalid email service configuration
            from app.services.email_service import EmailService
            
            # Create email service instance
            email_service = EmailService()
            
            # Test that it handles missing configuration gracefully
            if hasattr(email_service, 'smtp_host'):
                self.log_result(
                    "Error Handling",
                    "PASS",
                    "Email service handles configuration properly"
                )
            else:
                self.log_result(
                    "Error Handling",
                    "FAIL",
                    "Email service missing required configuration"
                )
                
        except Exception as e:
            self.log_result(
                "Error Handling",
                "WARNING",
                f"Email service error handling test failed: {str(e)}"
            )
    
    def generate_report(self):
        """Generate test report."""
        total_tests = len(self.results)
        passed_tests = len([r for r in self.results if r["status"] == "PASS"])
        failed_tests = len([r for r in self.results if r["status"] == "FAIL"])
        warning_tests = len([r for r in self.results if r["status"] == "WARNING"])
        
        print("\n" + "="*60)
        print("GOOGLE OAUTH EMAIL INTEGRATION TEST REPORT")
        print("="*60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Warnings: {warning_tests}")
        print()
        
        if failed_tests == 0:
            print("✅ GOOGLE OAUTH EMAIL INTEGRATION: WORKING")
            print("Email services are properly integrated with Google OAuth flow.")
        else:
            print("❌ GOOGLE OAUTH EMAIL INTEGRATION: ISSUES FOUND")
            print("Please fix the failed tests before deploying to production.")
        
        return failed_tests == 0


async def main():
    """Main test function."""
    print("Starting Google OAuth Email Integration Tests...")
    print(f"Timestamp: {datetime.now().isoformat()}")
    print()
    
    tester = GoogleOAuthEmailIntegrationTester()
    
    # Run all tests
    print("=== Email Function Tests ===")
    await tester.test_welcome_email_function()
    
    print("\n=== Import Tests ===")
    tester.test_google_oauth_service_import()
    
    print("\n=== OAuth Flow Tests ===")
    await tester.test_oauth_user_creation_flow()
    
    print("\n=== Template Tests ===")
    tester.test_email_template_availability()
    
    print("\n=== Error Handling Tests ===")
    tester.test_error_handling()
    
    # Generate final report
    success = tester.generate_report()
    
    return 0 if success else 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error during testing: {str(e)}")
        sys.exit(1)
