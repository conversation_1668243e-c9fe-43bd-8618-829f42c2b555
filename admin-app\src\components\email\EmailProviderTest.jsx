// @since 2024-1-1 to 2025-25-7
import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Chip,
  Grid,
  Divider
} from '@mui/material';
import {
  Send as SendIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Info as InfoIcon
} from '@mui/icons-material';

const EmailProviderTest = ({ 
  open, 
  provider = null, 
  onClose, 
  onSubmit 
}) => {
  const [testData, setTestData] = useState({
    test_email: '',
    test_subject: 'ACE Social Email Configuration Test',
    test_message: 'This is a test email to verify your email provider configuration. If you receive this email, your provider is working correctly.'
  });
  
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [testResult, setTestResult] = useState(null);

  const handleInputChange = (field, value) => {
    setTestData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!testData.test_email.trim()) {
      newErrors.test_email = 'Test email address is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(testData.test_email)) {
      newErrors.test_email = 'Invalid email format';
    }

    if (!testData.test_subject.trim()) {
      newErrors.test_subject = 'Subject is required';
    }

    if (!testData.test_message.trim()) {
      newErrors.test_message = 'Message is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleTest = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setTestResult(null);
    
    try {
      const result = await onSubmit(testData);
      setTestResult(result);
    } catch (error) {
      console.error('Error testing provider:', error);
      setTestResult({
        success: false,
        message: 'Test failed due to an unexpected error',
        test_details: { error: error.message }
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setTestData({
      test_email: '',
      test_subject: 'ACE Social Email Configuration Test',
      test_message: 'This is a test email to verify your email provider configuration. If you receive this email, your provider is working correctly.'
    });
    setErrors({});
    setTestResult(null);
    setLoading(false);
    onClose();
  };

  const getProviderTypeColor = (type) => {
    switch (type) {
      case 'smtp':
        return 'primary';
      case 'sendgrid':
        return 'secondary';
      case 'mailgun':
        return 'info';
      case 'aws_ses':
        return 'warning';
      default:
        return 'default';
    }
  };

  const formatTestDetails = (details) => {
    if (!details || typeof details !== 'object') {
      return null;
    }

    return Object.entries(details).map(([key, value]) => (
      <Box key={key} sx={{ mb: 1 }}>
        <Typography variant="caption" color="text.secondary">
          {key.replace(/_/g, ' ').toUpperCase()}:
        </Typography>
        <Typography variant="body2" sx={{ ml: 1 }}>
          {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
        </Typography>
      </Box>
    ));
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose} 
      maxWidth="sm" 
      fullWidth
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <SendIcon color="primary" />
          <Box>
            <Typography variant="h6">
              Test Email Provider
            </Typography>
            {provider && (
              <Typography variant="body2" color="text.secondary">
                {provider.name} ({provider.provider_type})
              </Typography>
            )}
          </Box>
        </Box>
      </DialogTitle>
      
      <DialogContent dividers>
        {provider && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Provider Information
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Box>
                  <Typography variant="caption" color="text.secondary">
                    Name:
                  </Typography>
                  <Typography variant="body2">
                    {provider.name}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Box>
                  <Typography variant="caption" color="text.secondary">
                    Type:
                  </Typography>
                  <Box sx={{ mt: 0.5 }}>
                    <Chip
                      label={provider.provider_type.toUpperCase()}
                      color={getProviderTypeColor(provider.provider_type)}
                      size="small"
                    />
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Box>
                  <Typography variant="caption" color="text.secondary">
                    From Email:
                  </Typography>
                  <Typography variant="body2">
                    {provider.from_email}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Box>
                  <Typography variant="caption" color="text.secondary">
                    Priority:
                  </Typography>
                  <Typography variant="body2">
                    {provider.priority}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
            <Divider sx={{ my: 2 }} />
          </Box>
        )}

        <Typography variant="subtitle2" gutterBottom>
          Test Configuration
        </Typography>
        
        <Box sx={{ mb: 2 }}>
          <TextField
            fullWidth
            label="Test Email Address"
            value={testData.test_email}
            onChange={(e) => handleInputChange('test_email', e.target.value)}
            error={!!errors.test_email}
            helperText={errors.test_email || 'Enter the email address where you want to receive the test email'}
            placeholder="<EMAIL>"
            sx={{ mb: 2 }}
          />
          
          <TextField
            fullWidth
            label="Subject"
            value={testData.test_subject}
            onChange={(e) => handleInputChange('test_subject', e.target.value)}
            error={!!errors.test_subject}
            helperText={errors.test_subject}
            sx={{ mb: 2 }}
          />
          
          <TextField
            fullWidth
            label="Message"
            multiline
            rows={4}
            value={testData.test_message}
            onChange={(e) => handleInputChange('test_message', e.target.value)}
            error={!!errors.test_message}
            helperText={errors.test_message}
          />
        </Box>

        {/* Test Result */}
        {testResult && (
          <Box sx={{ mt: 3 }}>
            <Divider sx={{ mb: 2 }} />
            <Typography variant="subtitle2" gutterBottom>
              Test Result
            </Typography>
            
            <Alert 
              severity={testResult.success ? 'success' : 'error'}
              icon={testResult.success ? <SuccessIcon /> : <ErrorIcon />}
              sx={{ mb: 2 }}
            >
              <Typography variant="body2">
                {testResult.message}
              </Typography>
            </Alert>

            {testResult.response_time && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Response Time:
                </Typography>
                <Typography variant="body2">
                  {testResult.response_time.toFixed(0)}ms
                </Typography>
              </Box>
            )}

            {testResult.tested_at && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Tested At:
                </Typography>
                <Typography variant="body2">
                  {new Date(testResult.tested_at).toLocaleString()}
                </Typography>
              </Box>
            )}

            {testResult.test_details && Object.keys(testResult.test_details).length > 0 && (
              <Box>
                <Typography variant="caption" color="text.secondary" gutterBottom>
                  Test Details:
                </Typography>
                <Box sx={{ 
                  bgcolor: 'grey.50', 
                  p: 2, 
                  borderRadius: 1, 
                  maxHeight: 200, 
                  overflow: 'auto',
                  fontFamily: 'monospace',
                  fontSize: '0.875rem'
                }}>
                  {formatTestDetails(testResult.test_details)}
                </Box>
              </Box>
            )}
          </Box>
        )}

        {/* Loading State */}
        {loading && (
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center', 
            py: 3 
          }}>
            <CircularProgress size={24} sx={{ mr: 2 }} />
            <Typography variant="body2" color="text.secondary">
              Sending test email...
            </Typography>
          </Box>
        )}

        {/* Information */}
        <Alert severity="info" sx={{ mt: 2 }}>
          <Typography variant="body2">
            This test will send a real email using the configured provider. 
            Make sure to use a valid email address that you have access to.
          </Typography>
        </Alert>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={handleClose}>
          {testResult ? 'Close' : 'Cancel'}
        </Button>
        {!testResult && (
          <Button 
            onClick={handleTest} 
            variant="contained"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={16} /> : <SendIcon />}
          >
            {loading ? 'Testing...' : 'Send Test Email'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default EmailProviderTest;
