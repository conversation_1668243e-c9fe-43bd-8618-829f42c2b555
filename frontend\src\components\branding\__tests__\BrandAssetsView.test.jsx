/**
 * Tests for BrandAssetsView component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import BrandAssetsView from '../BrandAssetsView';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the API
vi.mock('../../../api', () => ({
  default: {
    post: vi.fn(),
    delete: vi.fn()
  }
}));

// Mock hooks
vi.mock('../../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showSuccessNotification: vi.fn(),
    showErrorNotification: vi.fn()
  }))
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn().mockResolvedValue(undefined),
  },
});

describe('BrandAssetsView', () => {
  const mockAssets = [
    {
      asset_id: 'asset-1',
      name: 'Logo Primary',
      asset_type: 'logo',
      url: 'https://example.com/logo.png',
      description: 'Primary brand logo',
      tags: 'logo, primary',
      created_at: '2024-01-01T00:00:00Z'
    },
    {
      asset_id: 'asset-2',
      name: 'Brand Colors',
      asset_type: 'color_palette',
      url: 'https://example.com/colors.png',
      description: 'Brand color palette',
      tags: 'colors, palette',
      created_at: '2024-01-02T00:00:00Z'
    }
  ];

  const mockProps = {
    assets: mockAssets,
    profileId: 'profile-123',
    onAssetUploaded: vi.fn(),
    onAssetDeleted: vi.fn(),
    onError: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders brand assets view correctly', () => {
    render(
      <TestWrapper>
        <BrandAssetsView {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Upload Asset')).toBeInTheDocument();
    expect(screen.getByText('Filter by Type')).toBeInTheDocument();
    expect(screen.getByText('Logo Primary')).toBeInTheDocument();
    expect(screen.getByText('Brand Colors')).toBeInTheDocument();
  });

  test('filters assets by type', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandAssetsView {...mockProps} />
      </TestWrapper>
    );

    // Open filter dropdown
    const filterSelect = screen.getByLabelText('Filter by Type');
    await user.click(filterSelect);

    // Select logo filter
    await user.click(screen.getByText('Logo'));

    // Should only show logo assets
    expect(screen.getByText('Logo Primary')).toBeInTheDocument();
    expect(screen.queryByText('Brand Colors')).not.toBeInTheDocument();
  });

  test('opens upload dialog when upload button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandAssetsView {...mockProps} />
      </TestWrapper>
    );

    const uploadButton = screen.getByLabelText('Upload new brand asset');
    await user.click(uploadButton);

    expect(screen.getByText('Upload Brand Asset')).toBeInTheDocument();
    expect(screen.getByText('Asset Name')).toBeInTheDocument();
    expect(screen.getByText('Asset Type')).toBeInTheDocument();
  });

  test('uploads asset successfully', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.post.mockResolvedValue({
      data: { asset_id: 'new-asset', asset_name: 'New Logo' }
    });

    const file = new File(['test'], 'test.png', { type: 'image/png' });

    render(
      <TestWrapper>
        <BrandAssetsView {...mockProps} />
      </TestWrapper>
    );

    // Open upload dialog
    const uploadButton = screen.getByLabelText('Upload new brand asset');
    await user.click(uploadButton);

    // Fill in form
    const nameInput = screen.getByLabelText('Asset Name');
    await user.type(nameInput, 'New Logo');

    const typeSelect = screen.getByLabelText('Asset Type');
    await user.click(typeSelect);
    await user.click(screen.getByText('Logo'));

    // Upload file
    const fileInput = screen.getByLabelText('Select file to upload');
    await user.upload(fileInput, file);

    // Submit
    const submitButton = screen.getByLabelText('Upload asset');
    await user.click(submitButton);

    await waitFor(() => {
      expect(api.default.post).toHaveBeenCalledWith(
        '/api/brand-profiles/profile-123/assets',
        expect.any(FormData),
        expect.objectContaining({
          headers: { 'Content-Type': 'multipart/form-data' }
        })
      );
    });

    expect(mockProps.onAssetUploaded).toHaveBeenCalled();
  });

  test('handles upload errors gracefully', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.post.mockRejectedValue(new Error('Upload failed'));

    const file = new File(['test'], 'test.png', { type: 'image/png' });

    render(
      <TestWrapper>
        <BrandAssetsView {...mockProps} />
      </TestWrapper>
    );

    // Open upload dialog and fill form
    const uploadButton = screen.getByLabelText('Upload new brand asset');
    await user.click(uploadButton);

    const nameInput = screen.getByLabelText('Asset Name');
    await user.type(nameInput, 'New Logo');

    const fileInput = screen.getByLabelText('Select file to upload');
    await user.upload(fileInput, file);

    const submitButton = screen.getByLabelText('Upload asset');
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockProps.onError).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  test('deletes asset with confirmation', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.delete.mockResolvedValue({});
    global.window.confirm = vi.fn(() => true);

    render(
      <TestWrapper>
        <BrandAssetsView {...mockProps} />
      </TestWrapper>
    );

    const deleteButton = screen.getAllByLabelText(/Delete/)[0];
    await user.click(deleteButton);

    await waitFor(() => {
      expect(api.default.delete).toHaveBeenCalledWith(
        '/api/brand-profiles/profile-123/assets/asset-1'
      );
    });

    expect(mockProps.onAssetDeleted).toHaveBeenCalledWith('asset-1');
  });

  test('cancels delete when user declines confirmation', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    global.window.confirm = vi.fn(() => false);

    render(
      <TestWrapper>
        <BrandAssetsView {...mockProps} />
      </TestWrapper>
    );

    const deleteButton = screen.getAllByLabelText(/Delete/)[0];
    await user.click(deleteButton);

    expect(api.default.delete).not.toHaveBeenCalled();
    expect(mockProps.onAssetDeleted).not.toHaveBeenCalled();
  });

  test('copies asset URL to clipboard', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandAssetsView {...mockProps} />
      </TestWrapper>
    );

    const copyButton = screen.getAllByLabelText('Copy URL')[0];
    await user.click(copyButton);

    expect(navigator.clipboard.writeText).toHaveBeenCalledWith('https://example.com/logo.png');
  });

  test('opens preview dialog', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandAssetsView {...mockProps} />
      </TestWrapper>
    );

    const previewButton = screen.getAllByLabelText('Preview')[0];
    await user.click(previewButton);

    expect(screen.getByText('Logo Primary')).toBeInTheDocument();
    expect(screen.getByAltText('Logo Primary')).toBeInTheDocument();
  });

  test('downloads asset', async () => {
    const user = userEvent.setup();
    
    // Mock document.createElement and related methods
    const mockLink = {
      href: '',
      download: '',
      click: vi.fn(),
    };
    const createElementSpy = vi.spyOn(document, 'createElement').mockReturnValue(mockLink);
    const appendChildSpy = vi.spyOn(document.body, 'appendChild').mockImplementation(() => {});
    const removeChildSpy = vi.spyOn(document.body, 'removeChild').mockImplementation(() => {});

    render(
      <TestWrapper>
        <BrandAssetsView {...mockProps} />
      </TestWrapper>
    );

    const downloadButton = screen.getAllByLabelText('Download')[0];
    await user.click(downloadButton);

    expect(createElementSpy).toHaveBeenCalledWith('a');
    expect(mockLink.href).toBe('https://example.com/logo.png');
    expect(mockLink.download).toBe('Logo Primary');
    expect(mockLink.click).toHaveBeenCalled();
    expect(appendChildSpy).toHaveBeenCalledWith(mockLink);
    expect(removeChildSpy).toHaveBeenCalledWith(mockLink);

    createElementSpy.mockRestore();
    appendChildSpy.mockRestore();
    removeChildSpy.mockRestore();
  });

  test('hides upload and delete buttons when readOnly is true', () => {
    render(
      <TestWrapper>
        <BrandAssetsView {...mockProps} readOnly={true} />
      </TestWrapper>
    );

    expect(screen.queryByLabelText('Upload new brand asset')).not.toBeInTheDocument();
    expect(screen.queryByLabelText(/Delete/)).not.toBeInTheDocument();
  });

  test('disables controls when disabled prop is true', () => {
    render(
      <TestWrapper>
        <BrandAssetsView {...mockProps} disabled={true} />
      </TestWrapper>
    );

    const uploadButton = screen.getByLabelText('Upload new brand asset');
    const deleteButtons = screen.getAllByLabelText(/Delete/);

    expect(uploadButton).toBeDisabled();
    deleteButtons.forEach(button => {
      expect(button).toBeDisabled();
    });
  });

  test('shows empty state when no assets', () => {
    render(
      <TestWrapper>
        <BrandAssetsView {...mockProps} assets={[]} />
      </TestWrapper>
    );

    expect(screen.getByText('No assets yet')).toBeInTheDocument();
  });

  test('groups assets by type correctly', () => {
    const assetsWithMultipleTypes = [
      ...mockAssets,
      {
        asset_id: 'asset-3',
        name: 'Secondary Logo',
        asset_type: 'logo',
        url: 'https://example.com/logo2.png',
        description: 'Secondary logo',
        tags: 'logo, secondary',
        created_at: '2024-01-03T00:00:00Z'
      }
    ];

    render(
      <TestWrapper>
        <BrandAssetsView {...mockProps} assets={assetsWithMultipleTypes} />
      </TestWrapper>
    );

    // Should show both logos under logo section
    expect(screen.getByText('Logo Primary')).toBeInTheDocument();
    expect(screen.getByText('Secondary Logo')).toBeInTheDocument();
    expect(screen.getByText('Brand Colors')).toBeInTheDocument();
  });

  test('applies correct accessibility attributes', () => {
    render(
      <TestWrapper>
        <BrandAssetsView {...mockProps} />
      </TestWrapper>
    );

    const uploadButton = screen.getByLabelText('Upload new brand asset');
    const deleteButtons = screen.getAllByLabelText(/Delete/);
    const previewButtons = screen.getAllByLabelText('Preview');
    const copyButtons = screen.getAllByLabelText('Copy URL');
    const downloadButtons = screen.getAllByLabelText('Download');

    expect(uploadButton).toHaveAttribute('aria-label', 'Upload new brand asset');
    expect(deleteButtons[0]).toHaveAttribute('aria-label', 'Delete Logo Primary');
    expect(previewButtons[0]).toBeInTheDocument();
    expect(copyButtons[0]).toBeInTheDocument();
    expect(downloadButtons[0]).toBeInTheDocument();
  });

  test('passes through additional props', () => {
    render(
      <TestWrapper>
        <BrandAssetsView 
          {...mockProps} 
          data-testid="test-brand-assets"
          className="custom-class"
        />
      </TestWrapper>
    );

    const component = screen.getByTestId('test-brand-assets');
    expect(component).toHaveClass('custom-class');
  });

  test('validates required fields in upload form', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandAssetsView {...mockProps} />
      </TestWrapper>
    );

    // Open upload dialog
    const uploadButton = screen.getByLabelText('Upload new brand asset');
    await user.click(uploadButton);

    // Try to submit without required fields
    const submitButton = screen.getByLabelText('Upload asset');
    expect(submitButton).toBeDisabled();

    // Add asset name
    const nameInput = screen.getByLabelText('Asset Name');
    await user.type(nameInput, 'Test Asset');

    // Should still be disabled without file
    expect(submitButton).toBeDisabled();

    // Add file
    const file = new File(['test'], 'test.png', { type: 'image/png' });
    const fileInput = screen.getByLabelText('Select file to upload');
    await user.upload(fileInput, file);

    // Should now be enabled
    expect(submitButton).not.toBeDisabled();
  });
});
