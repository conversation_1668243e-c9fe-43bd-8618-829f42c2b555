// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import Typography from '../Typography';

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('Typography', () => {
  const mockTypography = {
    fonts: ['Inter', 'Roboto'],
    style: 'professional'
  };

  const mockProps = {
    typography: mockTypography,
    onChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders typography settings correctly', () => {
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Typography Settings')).toBeInTheDocument();
    expect(screen.getByText(/Define your brand's typography to maintain consistency/)).toBeInTheDocument();
  });

  test('displays font selection section', () => {
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Font Selection')).toBeInTheDocument();
    expect(screen.getByLabelText('Add Custom Font')).toBeInTheDocument();
    expect(screen.getByText('Popular Fonts')).toBeInTheDocument();
  });

  test('displays text style section', () => {
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Text Style')).toBeInTheDocument();
    expect(screen.getByLabelText('Overall Text Style')).toBeInTheDocument();
  });

  test('shows selected fonts', () => {
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Selected Fonts')).toBeInTheDocument();
    expect(screen.getByText('Inter')).toBeInTheDocument();
    expect(screen.getByText('Roboto')).toBeInTheDocument();
  });

  test('shows font preview section', () => {
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Font Preview')).toBeInTheDocument();
    expect(screen.getByText('Inter - Heading Example')).toBeInTheDocument();
    expect(screen.getByText('Roboto - Heading Example')).toBeInTheDocument();
  });

  test('shows typography style preview', () => {
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Typography Style Preview')).toBeInTheDocument();
    expect(screen.getByText('Main Headline')).toBeInTheDocument();
    expect(screen.getByText('Subheading that provides additional context')).toBeInTheDocument();
    expect(screen.getByText('Call to Action')).toBeInTheDocument();
  });

  test('handles custom font input', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    const fontInput = screen.getByLabelText('Add Custom Font');
    await user.type(fontInput, 'Helvetica');

    expect(fontInput).toHaveValue('Helvetica');
  });

  test('handles adding custom font', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    const fontInput = screen.getByLabelText('Add Custom Font');
    const addButton = screen.getAllByText('Add Font')[0];

    await user.type(fontInput, 'Helvetica');
    await user.click(addButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockTypography,
        fonts: [...mockTypography.fonts, 'Helvetica']
      });
    });
  });

  test('disables add button when font input is empty', () => {
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    const addButton = screen.getAllByText('Add Font')[0];
    expect(addButton).toBeDisabled();
  });

  test('enables add button when font input has value', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    const fontInput = screen.getByLabelText('Add Custom Font');
    const addButton = screen.getAllByText('Add Font')[0];

    await user.type(fontInput, 'Helvetica');
    expect(addButton).not.toBeDisabled();
  });

  test('handles popular font selection', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    const fontSelect = screen.getByLabelText('Select Font');
    await user.click(fontSelect);
    
    const montserratOption = screen.getByText('Montserrat');
    await user.click(montserratOption);

    expect(fontSelect).toHaveTextContent('Montserrat');
  });

  test('handles adding popular font', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    const fontSelect = screen.getByLabelText('Select Font');
    const addButton = screen.getAllByText('Add Font')[1];

    await user.click(fontSelect);
    const montserratOption = screen.getByText('Montserrat');
    await user.click(montserratOption);
    
    await user.click(addButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockTypography,
        fonts: [...mockTypography.fonts, 'Montserrat']
      });
    });
  });

  test('handles font removal', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    // Find the delete button for Inter font
    const interChip = screen.getByText('Inter').closest('.MuiChip-root');
    const deleteButton = interChip.querySelector('[data-testid="CancelIcon"]');
    
    if (deleteButton) {
      await user.click(deleteButton);

      await waitFor(() => {
        expect(mockProps.onChange).toHaveBeenCalledWith({
          ...mockTypography,
          fonts: ['Roboto']
        });
      });
    }
  });

  test('handles text style change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    const styleSelect = screen.getByLabelText('Overall Text Style');
    await user.click(styleSelect);
    
    const casualOption = screen.getByText('Casual');
    await user.click(casualOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockTypography,
        style: 'casual'
      });
    });
  });

  test('shows style description for professional style', () => {
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText(/Clean, straightforward typography that conveys expertise/)).toBeInTheDocument();
  });

  test('shows style description for casual style', () => {
    const casualTypography = {
      ...mockTypography,
      style: 'casual'
    };

    render(
      <TestWrapper>
        <Typography typography={casualTypography} onChange={mockProps.onChange} />
      </TestWrapper>
    );

    expect(screen.getByText(/Friendly, approachable typography that feels conversational/)).toBeInTheDocument();
  });

  test('shows style description for elegant style', () => {
    const elegantTypography = {
      ...mockTypography,
      style: 'elegant'
    };

    render(
      <TestWrapper>
        <Typography typography={elegantTypography} onChange={mockProps.onChange} />
      </TestWrapper>
    );

    expect(screen.getByText(/Sophisticated typography with refined details/)).toBeInTheDocument();
  });

  test('shows empty state when no fonts selected', () => {
    const emptyTypography = {
      fonts: [],
      style: 'professional'
    };

    render(
      <TestWrapper>
        <Typography typography={emptyTypography} onChange={mockProps.onChange} />
      </TestWrapper>
    );

    expect(screen.getByText('No fonts selected yet')).toBeInTheDocument();
    expect(screen.getByText('Add fonts to see preview')).toBeInTheDocument();
    expect(screen.getByText('Add fonts to see a complete typography preview')).toBeInTheDocument();
  });

  test('prevents adding duplicate fonts', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    const fontInput = screen.getByLabelText('Add Custom Font');
    const addButton = screen.getAllByText('Add Font')[0];

    // Try to add a font that already exists
    await user.type(fontInput, 'Inter');
    await user.click(addButton);

    // Should not call onChange since font already exists
    expect(mockProps.onChange).not.toHaveBeenCalled();
  });

  test('prevents adding duplicate popular fonts', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    const fontSelect = screen.getByLabelText('Select Font');
    const addButton = screen.getAllByText('Add Font')[1];

    // Try to add a font that already exists
    await user.click(fontSelect);
    const robotoOption = screen.getByText('Roboto');
    await user.click(robotoOption);
    
    await user.click(addButton);

    // Should not call onChange since font already exists
    expect(mockProps.onChange).not.toHaveBeenCalled();
  });

  test('renders with default props when no typography provided', () => {
    render(
      <TestWrapper>
        <Typography onChange={vi.fn()} />
      </TestWrapper>
    );

    expect(screen.getByText('Typography Settings')).toBeInTheDocument();
    expect(screen.getByText('No fonts selected yet')).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    // Check for proper labels and roles
    expect(screen.getByRole('textbox', { name: /add custom font/i })).toBeInTheDocument();
    expect(screen.getByRole('combobox', { name: /select font/i })).toBeInTheDocument();
    expect(screen.getByRole('combobox', { name: /overall text style/i })).toBeInTheDocument();
  });

  test('displays font families correctly in preview', () => {
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    // Check if fonts are applied to preview elements
    const interHeading = screen.getByText('Inter - Heading Example');
    const robotoHeading = screen.getByText('Roboto - Heading Example');
    
    expect(interHeading).toBeInTheDocument();
    expect(robotoHeading).toBeInTheDocument();
  });

  test('shows correct selected style', () => {
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Selected style:')).toBeInTheDocument();
    expect(screen.getByText('Professional')).toBeInTheDocument();
  });

  test('clears font input after adding font', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Typography {...mockProps} />
      </TestWrapper>
    );

    const fontInput = screen.getByLabelText('Add Custom Font');
    const addButton = screen.getAllByText('Add Font')[0];

    await user.type(fontInput, 'Helvetica');
    await user.click(addButton);

    await waitFor(() => {
      expect(fontInput).toHaveValue('');
    });
  });
});
