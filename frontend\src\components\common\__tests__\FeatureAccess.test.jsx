/**
 * FeatureAccess Component Test Suite
 * Comprehensive testing for enterprise-grade feature access control
 @since 2024-1-1 to 2025-25-7
*/

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import FeatureAccess from '../FeatureAccess';

// Test theme
const theme = createTheme();

// Test wrapper component
const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

// Mock contexts
const mockAuth = {
  user: {
    id: 'test-user',
    subscription: {
      plan_id: 'creator',
      is_appsumo_lifetime: false
    }
  },
  hasFeature: jest.fn(),
  getFeatureDescription: jest.fn(),
  getUserRole: jest.fn(),
  getUserPermissions: jest.fn()
};

const mockTrial = {
  startTrial: jest.fn(),
  isTrial: false,
  trialDaysLeft: 0
};

jest.mock('../../contexts/AuthContext', () => ({
  useAuth: () => mockAuth
}));

jest.mock('../../contexts/TrialContext', () => ({
  useTrial: () => mockTrial
}));

// Mock react-router-dom hooks
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => ({ pathname: '/test' })
}));

describe('FeatureAccess', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockAuth.hasFeature.mockReturnValue(false);
    mockAuth.getFeatureDescription.mockReturnValue('Test feature description');
    mockAuth.getUserRole.mockReturnValue('viewer');
    mockAuth.getUserPermissions.mockReturnValue([]);
  });

  describe('Basic Functionality', () => {
    test('renders children when user has access', () => {
      mockAuth.hasFeature.mockReturnValue(true);

      render(
        <TestWrapper>
          <FeatureAccess featureKey="test_feature">
            <div>Protected Content</div>
          </FeatureAccess>
        </TestWrapper>
      );

      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    test('shows access denied message when user lacks access', () => {
      render(
        <TestWrapper>
          <FeatureAccess featureKey="test_feature" title="Test Feature">
            <div>Protected Content</div>
          </FeatureAccess>
        </TestWrapper>
      );

      expect(screen.getByText('Test Feature')).toBeInTheDocument();
      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });

    test('uses feature key as title when no title provided', () => {
      render(
        <TestWrapper>
          <FeatureAccess featureKey="advanced_analytics">
            <div>Protected Content</div>
          </FeatureAccess>
        </TestWrapper>
      );

      expect(screen.getByText('Advanced Analytics')).toBeInTheDocument();
    });
  });

  describe('Permission Checking', () => {
    test('checks role hierarchy when enabled', () => {
      mockAuth.getUserRole.mockReturnValue('editor');

      render(
        <TestWrapper>
          <FeatureAccess 
            featureKey="test_feature" 
            requiredRole="admin"
            enableRoleHierarchy
          >
            <div>Protected Content</div>
          </FeatureAccess>
        </TestWrapper>
      );

      // Should deny access since editor < admin
      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });

    test('checks specific permissions when provided', () => {
      mockAuth.getUserPermissions.mockReturnValue(['read', 'write']);

      render(
        <TestWrapper>
          <FeatureAccess 
            featureKey="test_feature" 
            requiredPermissions={['read', 'write', 'delete']}
          >
            <div>Protected Content</div>
          </FeatureAccess>
        </TestWrapper>
      );

      // Should deny access since user lacks 'delete' permission
      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });

    test('checks plan hierarchy', () => {
      mockAuth.user.subscription.plan_id = 'accelerator';

      render(
        <TestWrapper>
          <FeatureAccess
            featureKey="test_feature"
            requiredPlan="dominator"
          >
            <div>Protected Content</div>
          </FeatureAccess>
        </TestWrapper>
      );

      // Should deny access since accelerator < dominator
      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });
  });

  describe('Variants', () => {
    test('renders tooltip variant correctly', () => {
      render(
        <TestWrapper>
          <FeatureAccess featureKey="test_feature" variant="tooltip">
            <div>Protected Content</div>
          </FeatureAccess>
        </TestWrapper>
      );

      // Tooltip variant should return null when access denied
      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });

    test('renders compact variant correctly', () => {
      render(
        <TestWrapper>
          <FeatureAccess featureKey="test_feature" variant="compact">
            <div>Protected Content</div>
          </FeatureAccess>
        </TestWrapper>
      );

      expect(screen.getByText(/requires.*plan/)).toBeInTheDocument();
      expect(screen.getByText('Upgrade')).toBeInTheDocument();
    });

    test('renders minimal variant correctly', () => {
      render(
        <TestWrapper>
          <FeatureAccess featureKey="test_feature" variant="minimal">
            <div>Protected Content</div>
          </FeatureAccess>
        </TestWrapper>
      );

      expect(screen.getByRole('alert')).toBeInTheDocument();
    });

    test('renders inline variant correctly', () => {
      render(
        <TestWrapper>
          <FeatureAccess featureKey="test_feature" variant="inline">
            <div>Protected Content</div>
          </FeatureAccess>
        </TestWrapper>
      );

      expect(screen.getByText('Premium')).toBeInTheDocument();
    });
  });

  describe('Disable Only Mode', () => {
    test('renders disabled children when disableOnly is true', () => {
      render(
        <TestWrapper>
          <FeatureAccess featureKey="test_feature" disableOnly>
            <button>Test Button</button>
          </FeatureAccess>
        </TestWrapper>
      );

      const button = screen.getByText('Test Button');
      expect(button).toBeInTheDocument();
      expect(button.closest('div')).toHaveStyle({ opacity: '0.6' });
    });

    test('shows upgrade info in disableOnly mode when enabled', () => {
      render(
        <TestWrapper>
          <FeatureAccess 
            featureKey="test_feature" 
            disableOnly 
            showUpgradeInfo
          >
            <button>Test Button</button>
          </FeatureAccess>
        </TestWrapper>
      );

      expect(screen.getByText(/Requires.*plan/)).toBeInTheDocument();
    });
  });

  describe('Analytics Integration', () => {
    test('tracks analytics events when enabled', () => {
      const onAnalytics = jest.fn();

      render(
        <TestWrapper>
          <FeatureAccess 
            featureKey="test_feature" 
            enableAnalytics 
            onAnalytics={onAnalytics}
          >
            <div>Protected Content</div>
          </FeatureAccess>
        </TestWrapper>
      );

      expect(onAnalytics).toHaveBeenCalledWith(
        expect.objectContaining({
          component: 'FeatureAccess',
          action: 'access_checked',
          featureKey: 'test_feature'
        })
      );
    });

    test('tracks upgrade button clicks', async () => {
      const user = userEvent.setup();
      const onAnalytics = jest.fn();

      render(
        <TestWrapper>
          <FeatureAccess 
            featureKey="test_feature" 
            variant="compact"
            enableAnalytics 
            onAnalytics={onAnalytics}
          >
            <div>Protected Content</div>
          </FeatureAccess>
        </TestWrapper>
      );

      const upgradeButton = screen.getByText('Upgrade');
      await user.click(upgradeButton);

      expect(onAnalytics).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'upgrade_clicked'
        })
      );
    });
  });

  describe('Access Logging', () => {
    test('logs access attempts when enabled', () => {
      const onAccessLog = jest.fn();

      render(
        <TestWrapper>
          <FeatureAccess 
            featureKey="test_feature" 
            enableAccessLogging 
            onAccessLog={onAccessLog}
          >
            <div>Protected Content</div>
          </FeatureAccess>
        </TestWrapper>
      );

      expect(onAccessLog).toHaveBeenCalledWith(
        expect.objectContaining({
          featureKey: 'test_feature',
          accessResult: false,
          userId: 'test-user'
        })
      );
    });
  });

  describe('Trial Integration', () => {
    test('shows trial button for free users', () => {
      render(
        <TestWrapper>
          <FeatureAccess featureKey="test_feature" upgradeStrategy="trial">
            <div>Protected Content</div>
          </FeatureAccess>
        </TestWrapper>
      );

      expect(screen.getByText('Start 14-Day Free Trial')).toBeInTheDocument();
    });

    test('handles trial start', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <FeatureAccess featureKey="test_feature" upgradeStrategy="trial">
            <div>Protected Content</div>
          </FeatureAccess>
        </TestWrapper>
      );

      const trialButton = screen.getByText('Start 14-Day Free Trial');
      await user.click(trialButton);

      expect(mockTrial.startTrial).toHaveBeenCalled();
    });
  });

  describe('AppSumo Integration', () => {
    test('shows AppSumo upgrade for AppSumo users', () => {
      mockAuth.user.subscription.is_appsumo_lifetime = true;
      mockAuth.user.subscription.plan_name = 'AppSumo Plus';

      render(
        <TestWrapper>
          <FeatureAccess featureKey="test_feature">
            <div>Protected Content</div>
          </FeatureAccess>
        </TestWrapper>
      );

      expect(screen.getByText('Upgrade AppSumo Tier')).toBeInTheDocument();
    });
  });

  describe('Permission Details', () => {
    test('shows permission details when enabled', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <FeatureAccess 
            featureKey="test_feature" 
            showPermissionDetails
          >
            <div>Protected Content</div>
          </FeatureAccess>
        </TestWrapper>
      );

      const detailsButton = screen.getByText('Permission Details');
      await user.click(detailsButton);

      expect(screen.getByText('Feature Access')).toBeInTheDocument();
      expect(screen.getByText('Role Permission')).toBeInTheDocument();
      expect(screen.getByText('Plan Access')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA labels', () => {
      render(
        <TestWrapper>
          <FeatureAccess 
            featureKey="test_feature" 
            ariaLabel="Custom accessibility label"
          >
            <div>Protected Content</div>
          </FeatureAccess>
        </TestWrapper>
      );

      expect(screen.getByLabelText('Custom accessibility label')).toBeInTheDocument();
    });

    test('supports keyboard navigation', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <FeatureAccess featureKey="test_feature" variant="compact">
            <div>Protected Content</div>
          </FeatureAccess>
        </TestWrapper>
      );

      const upgradeButton = screen.getByText('Upgrade');
      upgradeButton.focus();
      
      expect(document.activeElement).toBe(upgradeButton);
      
      await user.keyboard('{Enter}');
      // Should trigger upgrade action
    });
  });

  describe('Error Handling', () => {
    test('handles missing auth context gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <TestWrapper>
          <FeatureAccess featureKey="test_feature">
            <div>Protected Content</div>
          </FeatureAccess>
        </TestWrapper>
      );

      // Should not crash
      expect(screen.getByText(/Test Feature/)).toBeInTheDocument();

      consoleSpy.mockRestore();
    });
  });
});
