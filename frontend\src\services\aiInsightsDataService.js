/**
 * Enhanced AI Insights Data Service - Enterprise-grade data aggregation for OpenAI integration
 * 
 * Features:
 * - Comprehensive metrics aggregation from multiple sources
 * - Subscription-based data filtering and enhancement
 * - Performance optimization with caching and batching
 * - Real-time data updates and WebSocket integration
 * - Error handling and fallback mechanisms
 * - Analytics tracking and monitoring
 @since 2024-1-1 to 2025-25-7
*/

import { dashboardApiService } from './dashboardApiService';
import { logger } from '../utils/logger';

// ACE Social subscription plans configuration
const SUBSCRIPTION_PLANS = {
  creator: {
    id: 'creator',
    tier: 1,
    maxDataPoints: 30,
    includeCompetitive: false,
    includePredictive: false,
    aiComplexity: 'basic'
  },
  accelerator: {
    id: 'accelerator', 
    tier: 2,
    maxDataPoints: 90,
    includeCompetitive: true,
    includePredictive: true,
    aiComplexity: 'advanced'
  },
  dominator: {
    id: 'dominator',
    tier: 3,
    maxDataPoints: -1, // unlimited
    includeCompetitive: true,
    includePredictive: true,
    aiComplexity: 'premium'
  }
};

/**
 * AI Insights Data Service Class
 */
class AIInsightsDataService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    this.batchQueue = [];
    this.batchTimeout = null;
    this.batchDelay = 1000; // 1 second
    this.isProcessing = false;
    
    // Performance tracking
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageProcessingTime: 0,
      cacheHits: 0,
      cacheMisses: 0
    };
  }

  /**
   * Aggregate comprehensive metrics data for AI insights
   * @param {Object} subscription - User subscription information
   * @param {Object} options - Configuration options
   * @returns {Promise<Object>} Aggregated metrics data
   */
  async aggregateMetricsData(subscription, options = {}) {
    const startTime = Date.now();
    const planConfig = SUBSCRIPTION_PLANS[subscription?.plan_id] || SUBSCRIPTION_PLANS.creator;
    
    try {
      this.metrics.totalRequests++;
      
      // Check cache first
      const cacheKey = this._generateCacheKey(subscription, options);
      const cachedData = this._getFromCache(cacheKey);
      
      if (cachedData) {
        this.metrics.cacheHits++;
        logger.debug('AI Insights data served from cache', { cacheKey });
        return cachedData;
      }
      
      this.metrics.cacheMisses++;
      
      // Define data sources based on subscription plan
      const dataSources = this._getDataSources(planConfig, options);
      
      // Fetch data in parallel with error handling
      const dataResults = await this._fetchDataInParallel(dataSources);
      
      // Aggregate and structure data for AI processing
      const aggregatedData = this._aggregateData(dataResults, planConfig);
      
      // Apply subscription-based filtering and enhancement
      const enhancedData = this._enhanceDataForPlan(aggregatedData, planConfig);
      
      // Cache the result
      this._setCache(cacheKey, enhancedData);
      
      const processingTime = Date.now() - startTime;
      this._updateMetrics(processingTime, true);
      
      logger.info('AI Insights data aggregated successfully', {
        planId: planConfig.id,
        dataPoints: Object.keys(enhancedData).length,
        processingTime
      });
      
      return enhancedData;
      
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this._updateMetrics(processingTime, false);
      
      logger.error('Failed to aggregate AI insights data', error);
      throw new Error(`Data aggregation failed: ${error.message}`);
    }
  }

  /**
   * Get data sources configuration based on subscription plan
   * @private
   */
  _getDataSources(planConfig, options) {
    const baseSources = [
      { key: 'overview', endpoint: '/api/analytics/overview', priority: 'high' },
      { key: 'content', endpoint: '/api/analytics/content', priority: 'high' },
      { key: 'audience', endpoint: '/api/analytics/audience', priority: 'medium' },
      { key: 'sentiment', endpoint: '/api/analytics/sentiment', priority: 'medium' }
    ];

    // Add competitive data for accelerator+ plans
    if (planConfig.includeCompetitive) {
      baseSources.push(
        { key: 'competitors', endpoint: '/api/analytics/competitors', priority: 'low' },
        { key: 'benchmarks', endpoint: '/api/analytics/benchmarks', priority: 'low' }
      );
    }

    // Add predictive analytics for accelerator+ plans
    if (planConfig.includePredictive) {
      baseSources.push(
        { key: 'predictions', endpoint: '/api/analytics/predictions', priority: 'low' },
        { key: 'trends', endpoint: '/api/analytics/trends', priority: 'medium' }
      );
    }

    return baseSources;
  }

  /**
   * Fetch data from multiple sources in parallel
   * @private
   */
  async _fetchDataInParallel(dataSources) {
    const fetchPromises = dataSources.map(async (source) => {
      try {
        const result = await dashboardApiService.makeApiCall(source.endpoint, {
          useCache: true,
          cacheTTL: this.cacheTimeout
        });
        
        return { key: source.key, data: result, success: true };
      } catch (error) {
        logger.warn(`Failed to fetch ${source.key} data`, error);
        return { key: source.key, data: null, success: false, error: error.message };
      }
    });

    const results = await Promise.allSettled(fetchPromises);
    
    return results.reduce((acc, result) => {
      if (result.status === 'fulfilled') {
        acc[result.value.key] = result.value;
      }
      return acc;
    }, {});
  }

  /**
   * Aggregate data from multiple sources into structured format
   * @private
   */
  _aggregateData(dataResults, planConfig) {
    const aggregated = {
      performance: this._aggregatePerformanceMetrics(dataResults),
      content: this._aggregateContentAnalytics(dataResults),
      audience: this._aggregateAudienceData(dataResults),
      sentiment: this._aggregateSentimentData(dataResults),
      temporal: this._aggregateTemporalData(dataResults),
      metadata: {
        planId: planConfig.id,
        dataPoints: Object.keys(dataResults).length,
        timestamp: new Date().toISOString(),
        sources: Object.keys(dataResults)
      }
    };

    // Add competitive data if available
    if (planConfig.includeCompetitive && dataResults.competitors?.success) {
      aggregated.competitive = this._aggregateCompetitiveData(dataResults);
    }

    // Add predictive data if available
    if (planConfig.includePredictive && dataResults.predictions?.success) {
      aggregated.predictive = this._aggregatePredictiveData(dataResults);
    }

    return aggregated;
  }

  /**
   * Aggregate performance metrics
   * @private
   */
  _aggregatePerformanceMetrics(dataResults) {
    const overview = dataResults.overview?.data || {};
    const content = dataResults.content?.data || {};
    
    return {
      engagement: {
        rate: overview.engagement_rate || 0,
        total: overview.total_engagements || 0,
        trend: overview.engagement_trend || 'stable',
        previousPeriod: overview.previous_period?.engagement_rate || 0
      },
      reach: {
        total: overview.total_impressions || 0,
        unique: overview.unique_reach || 0,
        trend: overview.reach_trend || 'stable',
        previousPeriod: overview.previous_period?.total_impressions || 0
      },
      growth: {
        followers: overview.total_followers || 0,
        rate: overview.growth_rate || 0,
        trend: overview.growth_trend || 'stable',
        previousPeriod: overview.previous_period?.total_followers || 0
      },
      contentPerformance: {
        topPosts: content.top_performing || [],
        averageEngagement: content.average_engagement || 0,
        bestPerformingType: content.best_content_type || 'unknown'
      }
    };
  }

  /**
   * Aggregate content analytics
   * @private
   */
  _aggregateContentAnalytics(dataResults) {
    const content = dataResults.content?.data || {};

    return {
      distribution: content.content_type_distribution || {},
      performance: content.content_performance || [],
      optimalTimes: content.optimal_posting_times || {},
      hashtags: content.top_hashtags || [],
      topics: content.trending_topics || [],
      frequency: content.posting_frequency || {}
    };
  }

  /**
   * Aggregate audience demographics and behavior data
   * @private
   */
  _aggregateAudienceData(dataResults) {
    const audience = dataResults.audience?.data || {};

    return {
      demographics: {
        age: audience.age_distribution || {},
        gender: audience.gender_distribution || {},
        location: audience.location_distribution || {},
        interests: audience.interests || []
      },
      behavior: {
        activeHours: audience.active_hours || {},
        engagementPatterns: audience.engagement_patterns || {},
        deviceUsage: audience.device_usage || {},
        platformPreferences: audience.platform_preferences || {}
      },
      growth: {
        newFollowers: audience.new_followers || 0,
        unfollowers: audience.unfollowers || 0,
        retentionRate: audience.retention_rate || 0,
        churnRate: audience.churn_rate || 0
      }
    };
  }

  /**
   * Aggregate sentiment analysis data
   * @private
   */
  _aggregateSentimentData(dataResults) {
    const sentiment = dataResults.sentiment?.data || {};

    return {
      overall: {
        score: sentiment.overall_sentiment || 0,
        distribution: sentiment.sentiment_distribution || {},
        trend: sentiment.sentiment_trend || 'neutral',
        confidence: sentiment.confidence_score || 0
      },
      byContent: sentiment.content_sentiment || [],
      byPlatform: sentiment.platform_sentiment || {},
      keywords: {
        positive: sentiment.positive_keywords || [],
        negative: sentiment.negative_keywords || [],
        neutral: sentiment.neutral_keywords || []
      },
      emotions: sentiment.emotion_analysis || {}
    };
  }

  /**
   * Aggregate temporal data and trends
   * @private
   */
  _aggregateTemporalData(dataResults) {
    const overview = dataResults.overview?.data || {};
    const trends = dataResults.trends?.data || {};

    return {
      timeSeriesData: {
        engagement: overview.historical_engagement_rate || [],
        reach: overview.historical_impressions || [],
        followers: overview.historical_followers || []
      },
      patterns: {
        dailyActivity: trends.daily_patterns || {},
        weeklyTrends: trends.weekly_trends || {},
        monthlyGrowth: trends.monthly_growth || {},
        seasonality: trends.seasonal_patterns || {}
      },
      optimalTiming: {
        bestDays: trends.optimal_days || [],
        bestHours: trends.optimal_hours || [],
        worstTimes: trends.poor_performance_times || []
      }
    };
  }

  /**
   * Aggregate competitive intelligence data
   * @private
   */
  _aggregateCompetitiveData(dataResults) {
    const competitors = dataResults.competitors?.data || {};
    const benchmarks = dataResults.benchmarks?.data || {};

    return {
      benchmarks: {
        engagement: benchmarks.engagement_benchmarks || {},
        reach: benchmarks.reach_benchmarks || {},
        growth: benchmarks.growth_benchmarks || {}
      },
      competitors: competitors.competitor_list || [],
      comparison: {
        performance: competitors.performance_comparison || {},
        contentStrategy: competitors.content_strategy_analysis || {},
        audienceOverlap: competitors.audience_overlap || {}
      },
      opportunities: competitors.competitive_opportunities || [],
      threats: competitors.competitive_threats || []
    };
  }

  /**
   * Aggregate predictive analytics data
   * @private
   */
  _aggregatePredictiveData(dataResults) {
    const predictions = dataResults.predictions?.data || {};

    return {
      performance: {
        nextWeek: predictions.weekly_forecast || {},
        nextMonth: predictions.monthly_forecast || {},
        confidence: predictions.forecast_confidence || 0
      },
      recommendations: predictions.ai_recommendations || [],
      trends: {
        emerging: predictions.emerging_trends || [],
        declining: predictions.declining_trends || [],
        stable: predictions.stable_trends || []
      },
      optimization: {
        contentSuggestions: predictions.content_optimization || [],
        timingSuggestions: predictions.timing_optimization || [],
        audienceTargeting: predictions.audience_optimization || []
      }
    };
  }

  /**
   * Enhance data based on subscription plan capabilities
   * @private
   */
  _enhanceDataForPlan(aggregatedData, planConfig) {
    const enhanced = { ...aggregatedData };

    // Apply data point limits for non-unlimited plans
    if (planConfig.maxDataPoints > 0) {
      enhanced.performance.contentPerformance.topPosts =
        enhanced.performance.contentPerformance.topPosts.slice(0, planConfig.maxDataPoints);

      enhanced.content.performance =
        enhanced.content.performance.slice(0, planConfig.maxDataPoints);
    }

    // Add plan-specific enhancements
    enhanced.planCapabilities = {
      aiComplexity: planConfig.aiComplexity,
      includeCompetitive: planConfig.includeCompetitive,
      includePredictive: planConfig.includePredictive,
      maxDataPoints: planConfig.maxDataPoints
    };

    return enhanced;
  }

  /**
   * Generate cache key for data aggregation
   * @private
   */
  _generateCacheKey(subscription, options) {
    const planId = subscription?.plan_id || 'creator';
    const optionsHash = JSON.stringify(options);
    return `ai_insights_${planId}_${btoa(optionsHash)}`;
  }

  /**
   * Get data from cache
   * @private
   */
  _getFromCache(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  /**
   * Set data in cache
   * @private
   */
  _setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Update performance metrics
   * @private
   */
  _updateMetrics(processingTime, success) {
    if (success) {
      this.metrics.successfulRequests++;
    } else {
      this.metrics.failedRequests++;
    }
    
    // Update average processing time
    const totalRequests = this.metrics.successfulRequests + this.metrics.failedRequests;
    this.metrics.averageProcessingTime = 
      (this.metrics.averageProcessingTime * (totalRequests - 1) + processingTime) / totalRequests;
  }

  /**
   * Get service metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
    logger.info('AI Insights data cache cleared');
  }
}

// Export singleton instance
export const aiInsightsDataService = new AIInsightsDataService();
export default aiInsightsDataService;
