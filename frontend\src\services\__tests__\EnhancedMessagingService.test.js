/**
 * Enhanced Messaging Service Test Suite
 * 
 * Comprehensive tests for the enhanced messaging service including
 * Prometheus metrics, encryption, and deduplication features.
 @since 2024-1-1 to 2025-25-7
*/

import { jest } from '@jest/globals';
import enhancedMessagingService from '../EnhancedMessagingService';
import { PrometheusMetricsCollector } from '../../utils/PrometheusMetricsCollector';
import { MessageEncryptionService } from '../../utils/MessageEncryptionService';
import { MessageDeduplicationService } from '../../utils/MessageDeduplicationService';

// Mock dependencies
jest.mock('../../utils/PrometheusMetricsCollector');
jest.mock('../../utils/MessageEncryptionService');
jest.mock('../../utils/MessageDeduplicationService');
jest.mock('../NetworkMonitor');
jest.mock('../AuthService');

describe('EnhancedMessagingService', () => {
  let mockMetricsCollector;
  let mockEncryptionService;
  let mockDeduplicationService;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Setup mock instances
    mockMetricsCollector = {
      recordMessageSend: jest.fn(),
      recordWebSocketConnection: jest.fn(),
      recordCircuitBreakerState: jest.fn(),
      recordQueueProcessing: jest.fn(),
      recordError: jest.fn(),
      setUserContext: jest.fn(),
      getStats: jest.fn(() => ({ hits: 100, misses: 10 })),
      flush: jest.fn()
    };
    
    mockEncryptionService = {
      encryptMessage: jest.fn(async (msg) => ({
        ...msg,
        content: 'encrypted_content',
        _encryption: {
          encrypted: true,
          algorithm: 'AES-256-GCM',
          encryption_level: 'high'
        }
      }))
    };
    
    mockDeduplicationService = {
      isDuplicate: jest.fn(async () => false),
      markAsSent: jest.fn(async () => true),
      getStats: jest.fn(() => ({ hits: 50, misses: 200 })),
      cleanup: jest.fn()
    };
    
    // Mock constructors
    PrometheusMetricsCollector.mockImplementation(() => mockMetricsCollector);
    MessageEncryptionService.mockImplementation(() => mockEncryptionService);
    MessageDeduplicationService.mockImplementation(() => mockDeduplicationService);
  });

  describe('Service Initialization', () => {
    test('should initialize enhancement services correctly', () => {
      expect(PrometheusMetricsCollector).toHaveBeenCalledWith({
        enabled: true,
        endpoint: '/api/metrics/messaging',
        batchSize: 50,
        flushInterval: 30000
      });
      
      expect(MessageEncryptionService).toHaveBeenCalledWith({
        enabled: expect.any(Boolean),
        algorithm: 'AES-256-GCM',
        keyRotationInterval: 86400000
      });
      
      expect(MessageDeduplicationService).toHaveBeenCalledWith({
        enabled: true,
        windowMs: 30000,
        maxCacheSize: 1000,
        useRedis: true
      });
    });

    test('should generate correlation ID', () => {
      const correlationId = enhancedMessagingService.generateCorrelationId();
      expect(correlationId).toMatch(/^msg_\d+_[a-z0-9]+$/);
    });

    test('should set user context correctly', () => {
      const user = {
        id: 'user_123',
        email: '<EMAIL>',
        subscription: { plan: 'accelerator' }
      };
      
      enhancedMessagingService.setUserContext(user);
      
      expect(mockMetricsCollector.setUserContext).toHaveBeenCalledWith(user);
      expect(enhancedMessagingService.subscriptionTier).toBe('accelerator');
    });
  });

  describe('Message Sending with Enhancements', () => {
    const mockMessageData = {
      content: 'Test message content',
      platform: 'linkedin',
      conversation_id: 'conv_123'
    };

    test('should handle message sending with all enhancements', async () => {
      // Mock successful send
      const mockSentMessage = { id: 'msg_456', ...mockMessageData };
      
      // Mock WebSocket send
      enhancedMessagingService.isConnected = true;
      enhancedMessagingService.useWebSocket = true;
      enhancedMessagingService.socket = {
        send: jest.fn()
      };
      
      const result = await enhancedMessagingService.sendMessage(mockMessageData);
      
      // Verify deduplication check
      expect(mockDeduplicationService.isDuplicate).toHaveBeenCalledWith(
        expect.objectContaining({
          content: mockMessageData.content,
          platform: mockMessageData.platform
        })
      );
      
      // Verify encryption
      expect(mockEncryptionService.encryptMessage).toHaveBeenCalled();
      
      // Verify metrics recording
      expect(mockMetricsCollector.recordMessageSend).toHaveBeenCalledWith(
        'linkedin',
        'creator',
        'attempt',
        0,
        expect.objectContaining({
          content_length: mockMessageData.content.length
        })
      );
      
      // Verify deduplication marking
      expect(mockDeduplicationService.markAsSent).toHaveBeenCalled();
    });

    test('should handle duplicate message detection', async () => {
      // Mock duplicate detection
      mockDeduplicationService.isDuplicate.mockResolvedValueOnce(true);
      
      const result = await enhancedMessagingService.sendMessage(mockMessageData);
      
      expect(result.status).toBe('duplicate');
      expect(result.duplicate_detected).toBe(true);
      
      // Verify metrics recorded duplicate block
      expect(mockMetricsCollector.recordMessageSend).toHaveBeenCalledWith(
        'linkedin',
        'creator',
        'duplicate_blocked',
        expect.any(Number),
        expect.objectContaining({
          reason: 'deduplication'
        })
      );
    });

    test('should handle encryption failure gracefully', async () => {
      // Mock encryption failure
      const encryptionError = new Error('Encryption failed');
      mockEncryptionService.encryptMessage.mockRejectedValueOnce(encryptionError);
      
      const result = await enhancedMessagingService.sendMessage(mockMessageData);
      
      // Should continue with unencrypted message
      expect(result.content).toBe(mockMessageData.content);
      
      // Verify error recorded
      expect(mockMetricsCollector.recordError).toHaveBeenCalledWith(
        'encryption_failed',
        'Encryption failed',
        expect.any(Object)
      );
    });

    test('should handle offline message queuing', async () => {
      // Mock offline state
      enhancedMessagingService.networkMonitor = { isOnline: false };
      
      const result = await enhancedMessagingService.sendMessage(mockMessageData);
      
      expect(result.status).toBe('sending');
      
      // Verify metrics recorded offline queue
      expect(mockMetricsCollector.recordMessageSend).toHaveBeenCalledWith(
        'linkedin',
        'creator',
        'queued_offline',
        expect.any(Number),
        expect.any(Object)
      );
    });
  });

  describe('Circuit Breaker Integration', () => {
    test('should record circuit breaker opening', () => {
      enhancedMessagingService.openCircuitBreaker('websocket');
      
      expect(mockMetricsCollector.recordCircuitBreakerState).toHaveBeenCalledWith(
        'websocket',
        'opened',
        'creator',
        expect.objectContaining({
          error_count: expect.any(Number)
        })
      );
    });

    test('should record circuit breaker reset', () => {
      enhancedMessagingService.resetCircuitBreaker('websocket');
      
      expect(mockMetricsCollector.recordCircuitBreakerState).toHaveBeenCalledWith(
        'websocket',
        'reset',
        'creator',
        expect.any(Object)
      );
    });
  });

  describe('Queue Processing with Metrics', () => {
    test('should record queue processing metrics', async () => {
      // Setup queue with test messages
      enhancedMessagingService.messageQueue = [
        { id: 'msg1', nextRetryTime: Date.now() - 1000 },
        { id: 'msg2', nextRetryTime: Date.now() - 1000 }
      ];
      
      // Mock online state
      enhancedMessagingService.networkMonitor = { isOnline: true };
      
      await enhancedMessagingService.processMessageQueue();
      
      // Verify queue processing start recorded
      expect(mockMetricsCollector.recordQueueProcessing).toHaveBeenCalledWith(
        'start',
        'creator',
        expect.objectContaining({
          queue_size: 2
        })
      );
      
      // Verify queue processing completion recorded
      expect(mockMetricsCollector.recordQueueProcessing).toHaveBeenCalledWith(
        'complete',
        'creator',
        expect.objectContaining({
          initial_queue_size: 2,
          processing_duration: expect.any(Number)
        })
      );
    });
  });

  describe('Service Statistics', () => {
    test('should return comprehensive service statistics', () => {
      const stats = enhancedMessagingService.getServiceStats();
      
      expect(stats).toHaveProperty('connection');
      expect(stats).toHaveProperty('circuitBreakers');
      expect(stats).toHaveProperty('queue');
      expect(stats).toHaveProperty('enhancements');
      expect(stats).toHaveProperty('userContext');
      expect(stats).toHaveProperty('network');
      
      // Verify enhancement services status
      expect(stats.enhancements.metricsEnabled).toBe(true);
      expect(stats.enhancements.encryptionEnabled).toBe(true);
      expect(stats.enhancements.deduplicationEnabled).toBe(true);
      
      // Verify metrics included
      expect(stats.metrics).toEqual({ hits: 100, misses: 10 });
      expect(stats.deduplication).toEqual({ hits: 50, misses: 200 });
    });
  });

  describe('Service Cleanup', () => {
    test('should cleanup all enhancement services', () => {
      enhancedMessagingService.cleanup();
      
      expect(mockMetricsCollector.flush).toHaveBeenCalled();
      expect(mockDeduplicationService.cleanup).toHaveBeenCalled();
      
      // Verify internal state cleared
      expect(enhancedMessagingService.messageQueue).toHaveLength(0);
      expect(enhancedMessagingService.pendingMessages.size).toBe(0);
    });
  });

  describe('Error Handling', () => {
    test('should handle enhancement service initialization errors', () => {
      // Mock constructor error
      PrometheusMetricsCollector.mockImplementationOnce(() => {
        throw new Error('Metrics initialization failed');
      });
      
      // Should not throw, should handle gracefully
      expect(() => {
        enhancedMessagingService.initializeEnhancementServices();
      }).not.toThrow();
    });

    test('should handle deduplication service errors gracefully', async () => {
      // Mock deduplication error
      mockDeduplicationService.isDuplicate.mockRejectedValueOnce(new Error('Deduplication failed'));
      
      const result = await enhancedMessagingService.sendMessage({
        content: 'Test message',
        platform: 'twitter'
      });
      
      // Should continue processing despite deduplication error
      expect(result).toBeDefined();
    });
  });
});
