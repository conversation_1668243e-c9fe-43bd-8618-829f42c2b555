// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Typography,
  Box,
  Chip,
  Alert,
  Tabs,
  Tab,
  Paper,
  Divider
} from '@mui/material';
import {
  Save as SaveIcon,
  Preview as PreviewIcon,
  Code as CodeIcon,
  Visibility as VisibilityIcon,
  Upload as UploadIcon,
  GetApp as ImportIcon
} from '@mui/icons-material';

const EmailTemplateEditor = ({ 
  open, 
  mode = 'create', 
  template = null, 
  onClose, 
  onSubmit 
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: 'transactional',
    subject: '',
    html_content: '',
    text_content: '',
    variables: [],
    tags: [],
    language: 'en',
    allow_unsubscribe: true,
    track_opens: true,
    track_clicks: true
  });

  const [currentTab, setCurrentTab] = useState(0);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);

  useEffect(() => {
    if (mode === 'edit' && template) {
      setFormData({
        name: template.name || '',
        description: template.description || '',
        category: template.category || 'transactional',
        subject: template.subject || '',
        html_content: template.html_content || '',
        text_content: template.text_content || '',
        variables: template.variables || [],
        tags: template.tags || [],
        language: template.language || 'en',
        allow_unsubscribe: template.allow_unsubscribe !== false,
        track_opens: template.track_opens !== false,
        track_clicks: template.track_clicks !== false
      });
    } else if (mode === 'duplicate' && template) {
      setFormData({
        name: `Copy of ${template.name}`,
        description: template.description || '',
        category: template.category || 'transactional',
        subject: template.subject || '',
        html_content: template.html_content || '',
        text_content: template.text_content || '',
        variables: template.variables || [],
        tags: template.tags || [],
        language: template.language || 'en',
        allow_unsubscribe: template.allow_unsubscribe !== false,
        track_opens: template.track_opens !== false,
        track_clicks: template.track_clicks !== false
      });
    } else {
      // Reset for create mode
      setFormData({
        name: '',
        description: '',
        category: 'transactional',
        subject: '',
        html_content: '',
        text_content: '',
        variables: [],
        tags: [],
        language: 'en',
        allow_unsubscribe: true,
        track_opens: true,
        track_clicks: true
      });
    }
  }, [mode, template, open]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Template name is required';
    }

    if (!formData.subject.trim()) {
      newErrors.subject = 'Subject is required';
    }

    if (!formData.html_content.trim()) {
      newErrors.html_content = 'HTML content is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting template:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (file && file.type === 'text/html') {
      const reader = new FileReader();
      reader.onload = (e) => {
        const htmlContent = e.target.result;
        handleInputChange('html_content', htmlContent);
        setUploadDialogOpen(false);

        // Auto-extract title as template name if empty
        const titleMatch = htmlContent.match(/<title>(.*?)<\/title>/i);
        if (titleMatch && !formData.name) {
          handleInputChange('name', titleMatch[1]);
        }
      };
      reader.readAsText(file);
    } else {
      alert('Please select a valid HTML file');
    }
  };

  const handleImportFromUrl = async (url) => {
    try {
      setLoading(true);
      const response = await fetch(url);
      const htmlContent = await response.text();
      handleInputChange('html_content', htmlContent);
      setUploadDialogOpen(false);
    } catch (error) {
      console.error('Error importing from URL:', error);
      alert('Failed to import HTML from URL');
    } finally {
      setLoading(false);
    }
  };

  const getDialogTitle = () => {
    switch (mode) {
      case 'create':
        return 'Create Email Template';
      case 'edit':
        return 'Edit Email Template';
      case 'duplicate':
        return 'Duplicate Email Template';
      default:
        return 'Email Template';
    }
  };

  const tabLabels = [
    { label: 'Basic Info', icon: null },
    { label: 'Content', icon: <CodeIcon /> },
    { label: 'Preview', icon: <VisibilityIcon /> },
    { label: 'Settings', icon: null }
  ];

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="lg" 
      fullWidth
      PaperProps={{
        sx: { minHeight: '80vh' }
      }}
    >
      <DialogTitle>
        {getDialogTitle()}
      </DialogTitle>
      
      <DialogContent dividers>
        {/* Tabs */}
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}
        >
          {tabLabels.map((tab, index) => (
            <Tab
              key={index}
              icon={tab.icon}
              label={tab.label}
              iconPosition="start"
            />
          ))}
        </Tabs>

        {/* Tab Content */}
        {currentTab === 0 && (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Template Name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                error={!!errors.name}
                helperText={errors.name}
                placeholder="Welcome Email Template"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  value={formData.category}
                  label="Category"
                  onChange={(e) => handleInputChange('category', e.target.value)}
                >
                  <MenuItem value="transactional">Transactional</MenuItem>
                  <MenuItem value="marketing">Marketing</MenuItem>
                  <MenuItem value="system">System</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Brief description of this email template"
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Subject Line"
                value={formData.subject}
                onChange={(e) => handleInputChange('subject', e.target.value)}
                error={!!errors.subject}
                helperText={errors.subject}
                placeholder="Welcome to {{company_name}}!"
              />
            </Grid>
          </Grid>
        )}

        {currentTab === 1 && (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  HTML Content
                </Typography>
                <Button
                  variant="outlined"
                  startIcon={<UploadIcon />}
                  onClick={() => setUploadDialogOpen(true)}
                  size="small"
                >
                  Import HTML
                </Button>
              </Box>
              <TextField
                fullWidth
                multiline
                rows={15}
                value={formData.html_content}
                onChange={(e) => handleInputChange('html_content', e.target.value)}
                error={!!errors.html_content}
                helperText={errors.html_content || "You can paste HTML directly or use the Import button to upload an HTML file"}
                placeholder="Enter your HTML email content here..."
                sx={{ fontFamily: 'monospace' }}
              />
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Plain Text Content (Optional)
              </Typography>
              <TextField
                fullWidth
                multiline
                rows={8}
                value={formData.text_content}
                onChange={(e) => handleInputChange('text_content', e.target.value)}
                placeholder="Plain text version of your email..."
              />
            </Grid>
          </Grid>
        )}

        {currentTab === 2 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Email Preview
            </Typography>
            <Paper 
              variant="outlined" 
              sx={{ 
                p: 2, 
                minHeight: 400, 
                bgcolor: 'grey.50',
                border: '1px solid',
                borderColor: 'grey.300'
              }}
            >
              <Typography variant="subtitle2" gutterBottom>
                Subject: {formData.subject || 'No subject'}
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              {formData.html_content ? (
                <Box
                  dangerouslySetInnerHTML={{ __html: formData.html_content }}
                  sx={{ 
                    '& *': { 
                      maxWidth: '100% !important',
                      wordBreak: 'break-word'
                    }
                  }}
                />
              ) : (
                <Typography color="text.secondary" style={{ fontStyle: 'italic' }}>
                  No content to preview. Add HTML content in the Content tab.
                </Typography>
              )}
            </Paper>
          </Box>
        )}

        {currentTab === 3 && (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Template Settings
              </Typography>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Language</InputLabel>
                <Select
                  value={formData.language}
                  label="Language"
                  onChange={(e) => handleInputChange('language', e.target.value)}
                >
                  <MenuItem value="en">English</MenuItem>
                  <MenuItem value="es">Spanish</MenuItem>
                  <MenuItem value="fr">French</MenuItem>
                  <MenuItem value="de">German</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Chip
                  label="Allow Unsubscribe"
                  color={formData.allow_unsubscribe ? 'success' : 'default'}
                  clickable
                  onClick={() => handleInputChange('allow_unsubscribe', !formData.allow_unsubscribe)}
                />
                <Chip
                  label="Track Opens"
                  color={formData.track_opens ? 'success' : 'default'}
                  clickable
                  onClick={() => handleInputChange('track_opens', !formData.track_opens)}
                />
                <Chip
                  label="Track Clicks"
                  color={formData.track_clicks ? 'success' : 'default'}
                  clickable
                  onClick={() => handleInputChange('track_clicks', !formData.track_clicks)}
                />
              </Box>
            </Grid>
          </Grid>
        )}

        {Object.keys(errors).length > 0 && (
          <Alert severity="error" sx={{ mt: 2 }}>
            Please fix the errors above before submitting.
          </Alert>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained"
          disabled={loading}
          startIcon={<SaveIcon />}
        >
          {loading ? 'Saving...' : (mode === 'create' ? 'Create Template' : 'Update Template')}
        </Button>
      </DialogActions>

      {/* HTML Import Dialog */}
      <Dialog
        open={uploadDialogOpen}
        onClose={() => setUploadDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Import HTML Template</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            You can import HTML content from a file or URL. The content will be loaded into the template editor.
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Upload HTML File
              </Typography>
              <input
                type="file"
                accept=".html,.htm"
                onChange={handleFileUpload}
                style={{ width: '100%', padding: '8px', border: '1px solid #ccc', borderRadius: '4px' }}
              />
            </Grid>

            <Grid item xs={12}>
              <Divider sx={{ my: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  OR
                </Typography>
              </Divider>
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Import from URL
              </Typography>
              <TextField
                fullWidth
                placeholder="https://example.com/template.html"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleImportFromUrl(e.target.value);
                  }
                }}
                helperText="Press Enter to import from URL"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUploadDialogOpen(false)}>
            Cancel
          </Button>
        </DialogActions>
      </Dialog>
    </Dialog>
  );
};

export default EmailTemplateEditor;
