/**
 * Google OAuth Callback Handler
 * 
 * Handles the OAuth callback from Google and processes the authentication
 * result. This page is displayed when users return from Google OAuth flow.
 * 
 * Features:
 * - Secure state parameter validation
 * - Error handling for OAuth failures
 * - Loading states with ACE Social branding
 * - Automatic redirection after successful authentication
 * - Token management and storage
 * 
 * @since 2024-1-1 to 2025-25-7
 */
import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Box,
  CircularProgress,
  Typography,
  Alert,
  Card,
  CardContent,
  useTheme,
  alpha,
  Fade
} from '@mui/material';
import { CheckCircle, Error as ErrorIcon } from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';
import Logo from '../../components/common/Logo';
import api from '../../api';

const GoogleCallback = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { setUser, setToken } = useAuth();
  const { showSuccess, showError } = useAdvancedToast();
  const theme = useTheme();

  const [status, setStatus] = useState('processing'); // 'processing', 'success', 'error'
  const [message, setMessage] = useState('Processing Google authentication...');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    const handleGoogleCallback = async () => {
      try {
        // Get parameters from URL
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');

        // Check for OAuth errors
        if (error) {
          throw new Error(`Google OAuth error: ${error}`);
        }

        if (!code || !state) {
          throw new Error('Missing authorization code or state parameter');
        }

        // Validate state parameter (CSRF protection)
        const storedState = localStorage.getItem('google_oauth_state');
        if (!storedState || storedState !== state) {
          throw new Error('Invalid state parameter - possible CSRF attack');
        }

        // Clean up stored state
        localStorage.removeItem('google_oauth_state');
        localStorage.removeItem('google_oauth_redirect_uri');

        setMessage('Exchanging authorization code for tokens...');

        // Exchange code for tokens
        const response = await api.get('/api/auth/google/callback', {
          params: { code, state }
        });

        if (!response.data?.access_token) {
          throw new Error('No access token received from server');
        }

        // Store tokens and user data
        const { access_token, refresh_token, user } = response.data;
        
        // Update auth context
        setToken(access_token);
        setUser(user);

        // Store tokens in localStorage
        localStorage.setItem('token', access_token);
        if (refresh_token) {
          localStorage.setItem('refresh_token', refresh_token);
        }

        setStatus('success');
        setMessage('Google authentication successful!');
        
        showSuccess('Successfully signed in with Google!');

        // Redirect to dashboard after a short delay
        setTimeout(() => {
          navigate('/dashboard', { replace: true });
        }, 2000);

      } catch (error) {
        console.error('Google OAuth callback error:', error);
        
        setStatus('error');
        setMessage(error.message || 'Google authentication failed');
        
        showError(error.message || 'Google authentication failed');

        // Redirect to login page after a delay
        setTimeout(() => {
          navigate('/login', { replace: true });
        }, 3000);
      }
    };

    // Only process if we have search params
    if (searchParams.toString()) {
      handleGoogleCallback();
    } else {
      // No params, redirect to login
      navigate('/login', { replace: true });
    }
  }, [searchParams, navigate, setUser, setToken, showSuccess, showError]);

  const getStatusIcon = () => {
    switch (status) {
      case 'processing':
        return <CircularProgress size={48} color="primary" />;
      case 'success':
        return <CheckCircle sx={{ fontSize: 48, color: 'success.main' }} />;
      case 'error':
        return <ErrorIcon sx={{ fontSize: 48, color: 'error.main' }} />;
      default:
        return <CircularProgress size={48} color="primary" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      default:
        return 'info';
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: `linear-gradient(135deg, ${alpha('#15110E', 0.9)} 0%, ${alpha('#4E40C5', 0.9)} 100%)`,
        padding: 2,
      }}
    >
      <Fade in={mounted} timeout={800}>
        <Card
          sx={{
            maxWidth: 500,
            width: '100%',
            background: theme.palette.background.paper,
            borderRadius: 3,
            boxShadow: `0 20px 60px ${alpha(theme.palette.common.black, 0.3)}`,
            overflow: 'visible',
            position: 'relative',
          }}
        >
          {/* Decorative background element */}
          <Box
            sx={{
              position: 'absolute',
              top: -20,
              left: '50%',
              transform: 'translateX(-50%)',
              width: 120,
              height: 120,
              background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
              borderRadius: '50%',
              filter: 'blur(40px)',
            }}
          />

          <CardContent sx={{ p: 4, textAlign: 'center', position: 'relative' }}>
            {/* Logo */}
            <Box sx={{ mb: 3 }}>
              <Logo size="large" />
            </Box>

            {/* Status Icon */}
            <Box sx={{ mb: 3 }}>
              {getStatusIcon()}
            </Box>

            {/* Status Message */}
            <Typography
              variant="h5"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: theme.palette.text.primary,
                mb: 2,
              }}
            >
              Google Authentication
            </Typography>

            <Alert
              severity={getStatusColor()}
              sx={{
                mb: 2,
                '& .MuiAlert-message': {
                  fontSize: '1rem',
                },
              }}
            >
              {message}
            </Alert>

            {status === 'processing' && (
              <Typography variant="body2" color="text.secondary">
                Please wait while we complete your authentication...
              </Typography>
            )}

            {status === 'success' && (
              <Typography variant="body2" color="text.secondary">
                Redirecting you to the dashboard...
              </Typography>
            )}

            {status === 'error' && (
              <Typography variant="body2" color="text.secondary">
                Redirecting you back to the login page...
              </Typography>
            )}
          </CardContent>
        </Card>
      </Fade>
    </Box>
  );
};

export default GoogleCallback;
