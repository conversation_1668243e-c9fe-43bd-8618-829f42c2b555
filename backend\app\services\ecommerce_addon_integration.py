"""
E-commerce add-on integration service for ACE Social billing and usage tracking.
@since 2024-1-1 to 2025-25-7
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from bson import ObjectId

from app.models.user import User, PyObjectId
from app.models.addon import UserAddon, AddonUsage, AddonCatalog
from app.services.addon_usage_tracking import track_addon_usage, usage_tracker
from app.services.feature_access import has_feature_access, check_feature_access
from app.services.billing import purchase_addon
from app.core.monitoring import ADDON_PURCHASES, ADDON_USAGE, ADDON_REVENUE
from app.db.mongodb import get_database

logger = logging.getLogger(__name__)

# E-commerce specific add-on definitions
ECOMMERCE_ADDONS = {
    "ecommerce_store_connections": {
        "id": "ecommerce_store_connections",
        "name": "E-commerce Store Connections",
        "description": "Connect multiple e-commerce stores (Shopify, WooCommerce)",
        "category": "ecommerce",
        "base_price": 19.99,
        "currency": "USD",
        "features": ["store_connections", "product_sync", "webhook_support"],
        "default_credits": 5,  # Number of stores
        "is_popular": True,
        "package_options": [
            {"quantity": 3, "price": 19.99, "name": "Starter Pack"},
            {"quantity": 10, "price": 49.99, "name": "Business Pack"},
            {"quantity": 25, "price": 99.99, "name": "Enterprise Pack"}
        ]
    },
    "product_content_generation": {
        "id": "product_content_generation",
        "name": "Product Content Generation Credits",
        "description": "AI-powered content generation for e-commerce products",
        "category": "ecommerce",
        "base_price": 29.99,
        "currency": "USD",
        "features": ["product_content_generation", "product_image_generation"],
        "default_credits": 100,
        "is_popular": True,
        "package_options": [
            {"quantity": 50, "price": 19.99, "name": "Starter Pack"},
            {"quantity": 100, "price": 29.99, "name": "Standard Pack"},
            {"quantity": 250, "price": 59.99, "name": "Business Pack"},
            {"quantity": 500, "price": 99.99, "name": "Enterprise Pack"}
        ]
    },
    "ecommerce_icp_generation": {
        "id": "ecommerce_icp_generation",
        "name": "E-commerce ICP Generation",
        "description": "Generate customer personas based on product data",
        "category": "ecommerce",
        "base_price": 39.99,
        "currency": "USD",
        "features": ["ecommerce_icp_generation", "product_analysis"],
        "default_credits": 50,
        "package_options": [
            {"quantity": 25, "price": 24.99, "name": "Starter Pack"},
            {"quantity": 50, "price": 39.99, "name": "Standard Pack"},
            {"quantity": 100, "price": 69.99, "name": "Business Pack"}
        ]
    },
    "ecommerce_campaigns": {
        "id": "ecommerce_campaigns",
        "name": "E-commerce Campaign Management",
        "description": "Advanced campaign management for product promotion",
        "category": "ecommerce",
        "base_price": 49.99,
        "currency": "USD",
        "features": ["ecommerce_campaigns", "product_targeting", "campaign_analytics"],
        "default_credits": 25,
        "package_options": [
            {"quantity": 10, "price": 29.99, "name": "Starter Pack"},
            {"quantity": 25, "price": 49.99, "name": "Standard Pack"},
            {"quantity": 50, "price": 89.99, "name": "Business Pack"}
        ]
    }
}

# E-commerce feature to add-on mapping
ECOMMERCE_FEATURE_MAP = {
    "ecommerce_integration": ["ecommerce_store_connections"],
    "store_connections": ["ecommerce_store_connections"],
    "product_sync": ["ecommerce_store_connections"],
    "product_content_generation": ["product_content_generation"],
    "product_image_generation": ["product_content_generation"],
    "ecommerce_icp_generation": ["ecommerce_icp_generation"],
    "product_analysis": ["ecommerce_icp_generation"],
    "ecommerce_campaigns": ["ecommerce_campaigns"],
    "product_targeting": ["ecommerce_campaigns"],
    "campaign_analytics": ["ecommerce_campaigns"]
}

# Usage type mappings for e-commerce features
ECOMMERCE_USAGE_TYPES = {
    "store_connection": "ecommerce_store_connections",
    "product_content_generation": "product_content_generation",
    "product_image_generation": "product_content_generation",
    "ecommerce_icp_generation": "ecommerce_icp_generation",
    "ecommerce_campaign_creation": "ecommerce_campaigns"
}


class EcommerceAddonIntegration:
    """
    Service for integrating e-commerce features with ACEO add-on system.
    """
    
    async def check_ecommerce_feature_access(
        self,
        user: User,
        feature: str,
        usage_amount: int = 1
    ) -> Dict[str, Any]:
        """
        Check if user has access to e-commerce feature through subscription or add-ons.
        
        Args:
            user: User to check
            feature: E-commerce feature name
            usage_amount: Amount of usage to check
            
        Returns:
            Access check result
        """
        try:
            # First check base subscription access
            has_base_access = await has_feature_access(user, feature)
            
            # Check add-on access
            has_addon_access = await self._check_addon_access(user, feature, usage_amount)
            
            # Combine results
            result = {
                "has_access": has_base_access or has_addon_access["has_access"],
                "source": "subscription" if has_base_access else ("addon" if has_addon_access["has_access"] else "none"),
                "credits_remaining": has_addon_access.get("credits_remaining", 0),
                "addon_id": has_addon_access.get("addon_id"),
                "requires_upgrade": not (has_base_access or has_addon_access["has_access"]),
                "recommended_addons": []
            }
            
            # Add recommendations if no access
            if result["requires_upgrade"]:
                result["recommended_addons"] = await self._get_feature_recommendations(feature)
            
            return result
            
        except Exception as e:
            logger.error(f"Error checking e-commerce feature access: {str(e)}")
            return {
                "has_access": False,
                "source": "error",
                "error": str(e)
            }
    
    async def track_ecommerce_usage(
        self,
        user: User,
        usage_type: str,
        amount: int = 1,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Track usage of e-commerce features and deduct from appropriate add-ons.
        
        Args:
            user: User performing the action
            usage_type: Type of e-commerce usage
            amount: Amount to track
            metadata: Additional usage metadata
            
        Returns:
            Usage tracking result
        """
        try:
            # Map usage type to add-on
            addon_type = ECOMMERCE_USAGE_TYPES.get(usage_type)
            if not addon_type:
                logger.warning(f"Unknown e-commerce usage type: {usage_type}")
                return {"success": False, "error": "Unknown usage type"}
            
            # Track usage through existing system
            result = await track_addon_usage(
                str(user.id),
                usage_type,
                amount,
                metadata
            )
            
            # Record e-commerce specific metrics
            if ADDON_USAGE:
                ADDON_USAGE.labels(
                    addon_id=addon_type,
                    usage_type=usage_type,
                    subscription_tier=user.subscription.plan_id if user.subscription else "free"
                ).inc(amount)
            
            # Check if user needs upsell prompt
            if result.get("credits_remaining", 0) <= 5:  # Low credits threshold
                await self._trigger_upsell_prompt(user, addon_type, usage_type)
            
            return result
            
        except Exception as e:
            logger.error(f"Error tracking e-commerce usage: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def get_ecommerce_usage_summary(self, user: User) -> Dict[str, Any]:
        """
        Get comprehensive usage summary for e-commerce features.
        
        Args:
            user: User to get summary for
            
        Returns:
            Usage summary
        """
        try:
            summary = {
                "store_connections": {
                    "used": 0,
                    "remaining": 0,
                    "total": 0
                },
                "content_generation": {
                    "used": 0,
                    "remaining": 0,
                    "total": 0
                },
                "icp_generation": {
                    "used": 0,
                    "remaining": 0,
                    "total": 0
                },
                "campaigns": {
                    "used": 0,
                    "remaining": 0,
                    "total": 0
                }
            }
            
            # Get user's e-commerce add-ons
            db = await get_database()
            addons = await db["user_addons"].find({
                "user_id": ObjectId(str(user.id)),
                "addon_id": {"$in": list(ECOMMERCE_ADDONS.keys())},
                "is_active": True
            }).to_list(length=None)
            
            # Calculate usage for each add-on type
            for addon in addons:
                addon_id = addon["addon_id"]
                
                if addon_id == "ecommerce_store_connections":
                    summary["store_connections"]["total"] += addon.get("total_credits", 0)
                    summary["store_connections"]["remaining"] += addon.get("credits_remaining", 0)
                    summary["store_connections"]["used"] += addon.get("credits_used", 0)
                
                elif addon_id == "product_content_generation":
                    summary["content_generation"]["total"] += addon.get("total_credits", 0)
                    summary["content_generation"]["remaining"] += addon.get("credits_remaining", 0)
                    summary["content_generation"]["used"] += addon.get("credits_used", 0)
                
                elif addon_id == "ecommerce_icp_generation":
                    summary["icp_generation"]["total"] += addon.get("total_credits", 0)
                    summary["icp_generation"]["remaining"] += addon.get("credits_remaining", 0)
                    summary["icp_generation"]["used"] += addon.get("credits_used", 0)
                
                elif addon_id == "ecommerce_campaigns":
                    summary["campaigns"]["total"] += addon.get("total_credits", 0)
                    summary["campaigns"]["remaining"] += addon.get("credits_remaining", 0)
                    summary["campaigns"]["used"] += addon.get("credits_used", 0)
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting e-commerce usage summary: {str(e)}")
            return {}
    
    async def purchase_ecommerce_addon(
        self,
        user: User,
        addon_id: str,
        package_quantity: int = 1
    ) -> Dict[str, Any]:
        """
        Purchase an e-commerce add-on for the user.
        
        Args:
            user: User making the purchase
            addon_id: E-commerce add-on ID
            package_quantity: Package quantity to purchase
            
        Returns:
            Purchase result
        """
        try:
            # Validate add-on exists
            if addon_id not in ECOMMERCE_ADDONS:
                raise ValueError(f"Unknown e-commerce add-on: {addon_id}")
            
            addon_config = ECOMMERCE_ADDONS[addon_id]
            
            # Use existing purchase system
            result = await purchase_addon(user.id, addon_id, package_quantity)
            
            # Record e-commerce specific metrics
            if ADDON_PURCHASES:
                package_option = next(
                    (opt for opt in addon_config["package_options"] if opt["quantity"] == package_quantity),
                    {"price": addon_config["base_price"]}
                )

                ADDON_PURCHASES.labels(
                    addon_id=addon_id,
                    variant=f"quantity_{package_quantity}",
                    subscription_tier=user.subscription.plan_id if user.subscription else "free"
                ).inc()

                if ADDON_REVENUE:
                    ADDON_REVENUE.labels(
                        addon_id=addon_id,
                        variant=f"quantity_{package_quantity}"
                    ).inc(float(package_option["price"]))
            
            return {
                "success": True,
                "addon_id": addon_id,
                "quantity": package_quantity,
                "subscription": result
            }
            
        except Exception as e:
            logger.error(f"Error purchasing e-commerce add-on: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _check_addon_access(
        self,
        user: User,
        feature: str,
        usage_amount: int
    ) -> Dict[str, Any]:
        """Check add-on access for a specific feature."""
        try:
            # Get required add-ons for this feature
            required_addons = ECOMMERCE_FEATURE_MAP.get(feature, [])
            if not required_addons:
                return {"has_access": False}
            
            # Check user's add-ons
            db = await get_database()
            user_addons = await db["user_addons"].find({
                "user_id": ObjectId(str(user.id)),
                "addon_id": {"$in": required_addons},
                "is_active": True
            }).to_list(length=None)
            
            # Find add-on with sufficient credits
            for addon in user_addons:
                credits_remaining = addon.get("credits_remaining", 0)
                if credits_remaining >= usage_amount:
                    return {
                        "has_access": True,
                        "addon_id": addon["addon_id"],
                        "credits_remaining": credits_remaining
                    }
            
            return {"has_access": False}
            
        except Exception as e:
            logger.error(f"Error checking add-on access: {str(e)}")
            return {"has_access": False, "error": str(e)}
    
    async def _get_feature_recommendations(self, feature: str) -> List[Dict[str, Any]]:
        """Get add-on recommendations for a feature."""
        required_addons = ECOMMERCE_FEATURE_MAP.get(feature, [])
        recommendations = []
        
        for addon_id in required_addons:
            if addon_id in ECOMMERCE_ADDONS:
                addon_config = ECOMMERCE_ADDONS[addon_id]
                recommendations.append({
                    "addon_id": addon_id,
                    "name": addon_config["name"],
                    "description": addon_config["description"],
                    "base_price": addon_config["base_price"],
                    "package_options": addon_config["package_options"]
                })
        
        return recommendations
    
    async def _trigger_upsell_prompt(
        self,
        user: User,
        addon_type: str,
        usage_type: str
    ) -> None:
        """Trigger upsell prompt for low credits."""
        try:
            # This would integrate with existing notification system
            logger.info(f"Triggering upsell prompt for user {user.id}, addon {addon_type}, usage {usage_type}")

            # Could send email, in-app notification, etc.
            # For now, just log the event

        except Exception as e:
            logger.error(f"Error triggering upsell prompt: {str(e)}")


# Create singleton instance
ecommerce_addon_integration = EcommerceAddonIntegration()
