/**
 * Tests for BrandProfileDetail component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import BrandProfileDetail from '../BrandProfileDetail';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock child components
vi.mock('../BrandColorPaletteView', () => ({
  default: ({ colorPalette, onError, disabled, showPreview }) => (
    <div data-testid="brand-color-palette-view">
      Color Palette View
      {disabled && <span data-testid="disabled">Disabled</span>}
      {!showPreview && <span data-testid="no-preview">No Preview</span>}
    </div>
  )
}));

vi.mock('../BrandTypographyView', () => ({
  default: ({ typography, onError, disabled, readOnly }) => (
    <div data-testid="brand-typography-view">
      Typography View
      {disabled && <span data-testid="disabled">Disabled</span>}
      {readOnly && <span data-testid="readonly">Read Only</span>}
    </div>
  )
}));

vi.mock('../BrandAssetsView', () => ({
  default: ({ assets, profileId, onError, disabled, readOnly }) => (
    <div data-testid="brand-assets-view">
      Assets View - Profile: {profileId}
      {disabled && <span data-testid="disabled">Disabled</span>}
      {readOnly && <span data-testid="readonly">Read Only</span>}
    </div>
  )
}));

vi.mock('../BrandVisualStyleView', () => ({
  default: ({ visualStyle, onError, disabled, readOnly }) => (
    <div data-testid="brand-visual-style-view">
      Visual Style View
      {disabled && <span data-testid="disabled">Disabled</span>}
      {readOnly && <span data-testid="readonly">Read Only</span>}
    </div>
  )
}));

vi.mock('../BrandVoiceView', () => ({
  default: ({ brandVoice, onError, disabled, readOnly }) => (
    <div data-testid="brand-voice-view">
      Brand Voice View
      {disabled && <span data-testid="disabled">Disabled</span>}
      {readOnly && <span data-testid="readonly">Read Only</span>}
    </div>
  )
}));

vi.mock('../BrandCampaignsView', () => ({
  default: ({ campaignIds, profileId, onCampaignAssociated, onError, disabled, readOnly }) => (
    <div data-testid="brand-campaigns-view">
      Campaigns View - Profile: {profileId}
      {disabled && <span data-testid="disabled">Disabled</span>}
      {readOnly && <span data-testid="readonly">Read Only</span>
    </div>
  )
}));

describe('BrandProfileDetail', () => {
  const mockProfile = {
    id: 'profile-123',
    name: 'Test Brand Profile',
    description: 'A test brand profile',
    is_default: false,
    color_palette: {
      primary: '#4E40C5',
      secondary: '#00E4BC',
      accent: '#FF5733'
    },
    typography: {
      primary_font: 'Roboto',
      secondary_font: 'Open Sans'
    },
    assets: [
      { id: 'asset-1', name: 'Logo', type: 'logo', url: 'logo.png' }
    ],
    visual_style: {
      photography_style: 'lifestyle',
      lighting: 'bright'
    },
    brand_voice: {
      tone: 'friendly',
      personality: 'professional'
    },
    associated_campaign_ids: ['campaign-1', 'campaign-2']
  };

  const mockProps = {
    profile: mockProfile,
    onEdit: vi.fn(),
    onDelete: vi.fn(),
    onSetDefault: vi.fn(),
    onDuplicate: vi.fn(),
    onError: vi.fn(),
    onCampaignAssociated: vi.fn(),
    activeTab: 0,
    onTabChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders profile detail correctly', () => {
    render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Test Brand Profile')).toBeInTheDocument();
    expect(screen.getByText('A test brand profile')).toBeInTheDocument();
    expect(screen.getByText('T')).toBeInTheDocument(); // Profile initial
  });

  test('shows default badge when profile is default', () => {
    const defaultProfile = { ...mockProfile, is_default: true };
    
    render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} profile={defaultProfile} />
      </TestWrapper>
    );

    expect(screen.getByText('Default')).toBeInTheDocument();
  });

  test('hides set default button when profile is already default', () => {
    const defaultProfile = { ...mockProfile, is_default: true };
    
    render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} profile={defaultProfile} />
      </TestWrapper>
    );

    expect(screen.queryByLabelText('Set as default profile')).not.toBeInTheDocument();
  });

  test('shows all action buttons when showActions is true', () => {
    render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} showActions={true} />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Set as default profile')).toBeInTheDocument();
    expect(screen.getByLabelText('Duplicate profile')).toBeInTheDocument();
    expect(screen.getByLabelText('Edit profile')).toBeInTheDocument();
    expect(screen.getByLabelText('Delete profile')).toBeInTheDocument();
  });

  test('hides action buttons when showActions is false', () => {
    render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} showActions={false} />
      </TestWrapper>
    );

    expect(screen.queryByLabelText('Set as default profile')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('Duplicate profile')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('Edit profile')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('Delete profile')).not.toBeInTheDocument();
  });

  test('disables action buttons when disabled prop is true', () => {
    render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} disabled={true} />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Set as default profile')).toBeDisabled();
    expect(screen.getByLabelText('Duplicate profile')).toBeDisabled();
    expect(screen.getByLabelText('Edit profile')).toBeDisabled();
    expect(screen.getByLabelText('Delete profile')).toBeDisabled();
  });

  test('disables edit and delete buttons when readOnly prop is true', () => {
    render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} readOnly={true} />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Set as default profile')).toBeDisabled();
    expect(screen.getByLabelText('Duplicate profile')).not.toBeDisabled();
    expect(screen.getByLabelText('Edit profile')).toBeDisabled();
    expect(screen.getByLabelText('Delete profile')).toBeDisabled();
  });

  test('calls onEdit when edit button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} />
      </TestWrapper>
    );

    const editButton = screen.getByLabelText('Edit profile');
    await user.click(editButton);

    expect(mockProps.onEdit).toHaveBeenCalledWith(mockProfile);
  });

  test('calls onDelete when delete button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} />
      </TestWrapper>
    );

    const deleteButton = screen.getByLabelText('Delete profile');
    await user.click(deleteButton);

    expect(mockProps.onDelete).toHaveBeenCalledWith(mockProfile);
  });

  test('calls onSetDefault when set default button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} />
      </TestWrapper>
    );

    const setDefaultButton = screen.getByLabelText('Set as default profile');
    await user.click(setDefaultButton);

    expect(mockProps.onSetDefault).toHaveBeenCalledWith(mockProfile);
  });

  test('calls onDuplicate when duplicate button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} />
      </TestWrapper>
    );

    const duplicateButton = screen.getByLabelText('Duplicate profile');
    await user.click(duplicateButton);

    expect(mockProps.onDuplicate).toHaveBeenCalledWith(mockProfile);
  });

  test('displays all tabs correctly', () => {
    render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Colors')).toBeInTheDocument();
    expect(screen.getByText('Typography')).toBeInTheDocument();
    expect(screen.getByText('Assets')).toBeInTheDocument();
    expect(screen.getByText('Visual Style')).toBeInTheDocument();
    expect(screen.getByText('Brand Voice')).toBeInTheDocument();
    expect(screen.getByText('Campaigns')).toBeInTheDocument();
  });

  test('shows correct tab content based on activeTab', () => {
    render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} activeTab={0} />
      </TestWrapper>
    );

    expect(screen.getByTestId('brand-color-palette-view')).toBeInTheDocument();
    expect(screen.queryByTestId('brand-typography-view')).not.toBeInTheDocument();
  });

  test('switches tab content when activeTab changes', () => {
    const { rerender } = render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} activeTab={0} />
      </TestWrapper>
    );

    expect(screen.getByTestId('brand-color-palette-view')).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} activeTab={1} />
      </TestWrapper>
    );

    expect(screen.getByTestId('brand-typography-view')).toBeInTheDocument();
    expect(screen.queryByTestId('brand-color-palette-view')).not.toBeInTheDocument();
  });

  test('calls onTabChange when tab is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} />
      </TestWrapper>
    );

    const typographyTab = screen.getByText('Typography');
    await user.click(typographyTab);

    expect(mockProps.onTabChange).toHaveBeenCalled();
  });

  test('passes disabled prop to child components', () => {
    render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} activeTab={0} disabled={true} />
      </TestWrapper>
    );

    expect(screen.getByTestId('disabled')).toBeInTheDocument();
  });

  test('passes readOnly prop to child components', () => {
    render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} activeTab={1} readOnly={true} />
      </TestWrapper>
    );

    expect(screen.getByTestId('readonly')).toBeInTheDocument();
  });

  test('passes profileId to relevant child components', () => {
    render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} activeTab={2} />
      </TestWrapper>
    );

    expect(screen.getByText('Assets View - Profile: profile-123')).toBeInTheDocument();
  });

  test('handles error in action handlers gracefully', async () => {
    const user = userEvent.setup();
    const onEditMock = vi.fn().mockImplementation(() => {
      throw new Error('Edit error');
    });
    
    render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} onEdit={onEditMock} />
      </TestWrapper>
    );

    const editButton = screen.getByLabelText('Edit profile');
    await user.click(editButton);

    expect(mockProps.onError).toHaveBeenCalledWith(expect.any(Error));
  });

  test('returns null when no profile is provided', () => {
    const { container } = render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} profile={null} />
      </TestWrapper>
    );

    expect(container.firstChild).toBeNull();
  });

  test('passes through additional props', () => {
    render(
      <TestWrapper>
        <BrandProfileDetail 
          {...mockProps} 
          data-testid="test-profile-detail"
          className="custom-class"
        />
      </TestWrapper>
    );

    const component = screen.getByTestId('test-profile-detail');
    expect(component).toHaveClass('custom-class');
  });

  test('shows all tab content correctly', () => {
    const tabs = [
      { index: 0, testId: 'brand-color-palette-view' },
      { index: 1, testId: 'brand-typography-view' },
      { index: 2, testId: 'brand-assets-view' },
      { index: 3, testId: 'brand-visual-style-view' },
      { index: 4, testId: 'brand-voice-view' },
      { index: 5, testId: 'brand-campaigns-view' }
    ];

    tabs.forEach(({ index, testId }) => {
      const { unmount } = render(
        <TestWrapper>
          <BrandProfileDetail {...mockProps} activeTab={index} />
        </TestWrapper>
      );

      expect(screen.getByTestId(testId)).toBeInTheDocument();
      unmount();
    });
  });

  test('handles missing profile properties gracefully', () => {
    const incompleteProfile = {
      id: 'profile-123',
      name: 'Incomplete Profile',
      is_default: false
    };

    render(
      <TestWrapper>
        <BrandProfileDetail {...mockProps} profile={incompleteProfile} />
      </TestWrapper>
    );

    expect(screen.getByText('Incomplete Profile')).toBeInTheDocument();
    expect(screen.getByText('I')).toBeInTheDocument(); // Profile initial
  });
});
