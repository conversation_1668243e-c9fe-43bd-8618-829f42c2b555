/**
 * CompetitorComparison Component Test Suite
 * Comprehensive testing for enterprise-grade competitor analysis component
 @since 2024-1-1 to 2025-25-7
*/

import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { BrowserRouter } from 'react-router-dom';

// Component under test
import CompetitorComparison from '../CompetitorComparison';

// Mock contexts and hooks
import { CompetitorProvider } from '../../../contexts/CompetitorContext';
import { NotificationProvider } from '../../../contexts/NotificationContext';

// Test utilities
import { createMockCompetitor, createMockComparisonData } from '../../../__mocks__/competitorMocks';
import { axe, toHaveNoViolations } from 'jest-axe';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock external dependencies
vi.mock('../../../api/competitor-analytics', () => ({
  compareCompetitors: vi.fn(),
  getIndustryBenchmarks: vi.fn(),
  refreshCompetitorAnalytics: vi.fn(),
  exportCompetitorData: vi.fn()
}));

vi.mock('../../../hooks/useAnalytics', () => ({
  useAnalytics: () => ({
    trackEvent: vi.fn(),
    trackError: vi.fn(),
    trackPerformance: vi.fn()
  })
}));

vi.mock('../../../hooks/useLocalStorage', () => ({
  useLocalStorage: (key, defaultValue) => [defaultValue, vi.fn()]
}));

vi.mock('../../../hooks/useDebounce', () => ({
  useDebounce: (fn) => fn
}));

// Test theme
const theme = createTheme({
  palette: {
    primary: { main: '#4E40C5' },
    secondary: { main: '#EBAE1B' }
  }
});

// Test wrapper component
const TestWrapper = ({ children, competitorContextValue = {} }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      <NotificationProvider>
        <CompetitorProvider value={competitorContextValue}>
          {children}
        </CompetitorProvider>
      </NotificationProvider>
    </ThemeProvider>
  </BrowserRouter>
);

// Mock data
const mockCompetitors = [
  createMockCompetitor({ id: '1', name: 'Competitor A' }),
  createMockCompetitor({ id: '2', name: 'Competitor B' }),
  createMockCompetitor({ id: '3', name: 'Competitor C' })
];

const mockComparisonData = createMockComparisonData(mockCompetitors);

const defaultContextValue = {
  competitors: mockCompetitors,
  loading: false,
  error: null,
  fetchCompetitors: vi.fn(),
  compareCompetitors: vi.fn().mockResolvedValue(mockComparisonData),
  getRecommendations: vi.fn()
};

describe('CompetitorComparison Component', () => {
  let user;

  beforeEach(() => {
    user = userEvent.setup();
    vi.clearAllMocks();
  });

  describe('Rendering and Basic Functionality', () => {
    test('renders without crashing', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorComparison />
        </TestWrapper>
      );

      expect(screen.getByText('Competitor Analysis Dashboard')).toBeInTheDocument();
    });

    test('displays loading state correctly', () => {
      const loadingContextValue = {
        ...defaultContextValue,
        loading: true
      };

      render(
        <TestWrapper competitorContextValue={loadingContextValue}>
          <CompetitorComparison />
        </TestWrapper>
      );

      expect(screen.getByTestId('loading-skeleton')).toBeInTheDocument();
    });

    test('displays error state correctly', () => {
      const errorContextValue = {
        ...defaultContextValue,
        error: 'Failed to load competitors'
      };

      render(
        <TestWrapper competitorContextValue={errorContextValue}>
          <CompetitorComparison />
        </TestWrapper>
      );

      expect(screen.getByText('Failed to load competitors')).toBeInTheDocument();
      expect(screen.getByText('Retry')).toBeInTheDocument();
    });

    test('shows empty state when insufficient competitors selected', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorComparison competitorIds={['1']} />
        </TestWrapper>
      );

      expect(screen.getByText('Select Competitors to Compare')).toBeInTheDocument();
      expect(screen.getByText('Back to Competitors')).toBeInTheDocument();
    });
  });

  describe('Platform Selection', () => {
    test('renders platform selection chips', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorComparison competitorIds={['1', '2']} />
        </TestWrapper>
      );

      expect(screen.getByText('LinkedIn')).toBeInTheDocument();
      expect(screen.getByText('Twitter')).toBeInTheDocument();
      expect(screen.getByText('Facebook')).toBeInTheDocument();
      expect(screen.getByText('Instagram')).toBeInTheDocument();
    });

    test('handles platform selection changes', async () => {
      const onPlatformChange = vi.fn();

      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorComparison 
            competitorIds={['1', '2']} 
            onPlatformChange={onPlatformChange}
          />
        </TestWrapper>
      );

      const linkedinChip = screen.getByText('LinkedIn');
      await user.click(linkedinChip);

      expect(onPlatformChange).toHaveBeenCalled();
    });
  });

  describe('Competitor Selection', () => {
    test('renders competitor selection dropdown', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorComparison />
        </TestWrapper>
      );

      expect(screen.getByLabelText('Select Competitors to Compare')).toBeInTheDocument();
    });

    test('handles competitor selection changes', async () => {
      const onCompetitorSelect = vi.fn();

      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorComparison onCompetitorSelect={onCompetitorSelect} />
        </TestWrapper>
      );

      const selectInput = screen.getByLabelText('Select Competitors to Compare');
      await user.click(selectInput);

      const competitorOption = screen.getByText('Competitor A');
      await user.click(competitorOption);

      expect(onCompetitorSelect).toHaveBeenCalled();
    });
  });

  describe('Tab Navigation', () => {
    test('renders all tabs correctly', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorComparison competitorIds={['1', '2']} />
        </TestWrapper>
      );

      expect(screen.getByText('Performance Metrics')).toBeInTheDocument();
      expect(screen.getByText('AI Insights')).toBeInTheDocument();
      expect(screen.getByText('Trends Analysis')).toBeInTheDocument();
      expect(screen.getByText('Benchmarking')).toBeInTheDocument();
    });

    test('handles tab changes correctly', async () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorComparison competitorIds={['1', '2']} />
        </TestWrapper>
      );

      const insightsTab = screen.getByText('AI Insights');
      await user.click(insightsTab);

      expect(screen.getByText('Generate AI Insights')).toBeInTheDocument();
    });
  });

  describe('Data Export', () => {
    test('export button is disabled when no data', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorComparison competitorIds={['1', '2']} />
        </TestWrapper>
      );

      const exportButton = screen.getByLabelText('Export comparison data');
      expect(exportButton).toBeDisabled();
    });

    test('handles export functionality', async () => {
      const mockExport = vi.fn();
      
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorComparison 
            competitorIds={['1', '2']} 
            onExport={mockExport}
          />
        </TestWrapper>
      );

      // Simulate having comparison data
      const exportButton = screen.getByLabelText('Export comparison data');
      await user.click(exportButton);

      // Export should be called when data is available
      await waitFor(() => {
        expect(mockExport).toHaveBeenCalled();
      });
    });
  });

  describe('Accessibility', () => {
    test('has no accessibility violations', async () => {
      const { container } = render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorComparison competitorIds={['1', '2']} />
        </TestWrapper>
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('supports keyboard navigation', async () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorComparison competitorIds={['1', '2']} />
        </TestWrapper>
      );

      // Test tab navigation
      await user.tab();
      expect(document.activeElement).toHaveAttribute('aria-label', 'Refresh competitor data');

      await user.tab();
      expect(document.activeElement).toHaveAttribute('aria-label', 'Export comparison data');
    });

    test('has proper ARIA labels and roles', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorComparison competitorIds={['1', '2']} />
        </TestWrapper>
      );

      expect(screen.getByRole('main')).toHaveAttribute('aria-label', 'Competitor comparison analysis');
      expect(screen.getByRole('region')).toHaveAttribute('aria-labelledby', 'comparison-header');
    });
  });

  describe('Performance and Optimization', () => {
    test('memoizes expensive calculations', () => {
      const { rerender } = render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorComparison competitorIds={['1', '2']} />
        </TestWrapper>
      );

      // Re-render with same props should not cause unnecessary recalculations
      rerender(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorComparison competitorIds={['1', '2']} />
        </TestWrapper>
      );

      // Component should render without issues
      expect(screen.getByText('Competitor Analysis Dashboard')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    test('handles API errors gracefully', async () => {
      const errorContextValue = {
        ...defaultContextValue,
        compareCompetitors: vi.fn().mockRejectedValue(new Error('API Error'))
      };

      render(
        <TestWrapper competitorContextValue={errorContextValue}>
          <CompetitorComparison competitorIds={['1', '2']} />
        </TestWrapper>
      );

      // Should show error state
      await waitFor(() => {
        expect(screen.getByText(/API Error/)).toBeInTheDocument();
      });
    });

    test('provides retry functionality', async () => {
      const retryFn = vi.fn();
      
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorComparison competitorIds={['1', '2']} />
        </TestWrapper>
      );

      // Simulate error state and retry
      const retryButton = screen.queryByText('Retry');
      if (retryButton) {
        await user.click(retryButton);
        expect(retryFn).toHaveBeenCalled();
      }
    });
  });

  describe('Plan Integration', () => {
    test('respects plan limits for competitor selection', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorComparison 
            competitorIds={['1', '2', '3', '4']} 
            userPlan="creator" 
          />
        </TestWrapper>
      );

      // Creator plan should show limit message
      expect(screen.getByText(/allows up to 3 competitors/)).toBeInTheDocument();
    });

    test('shows feature gates for premium features', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorComparison 
            competitorIds={['1', '2']} 
            userPlan="creator" 
          />
        </TestWrapper>
      );

      // Should show feature gate for export
      const exportButton = screen.getByLabelText('Export comparison data');
      expect(exportButton).toBeInTheDocument();
    });
  });
});
