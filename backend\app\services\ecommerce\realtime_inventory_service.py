"""
Real-time Inventory Tracking Service with WebSocket Support.
Provides live inventory updates, automated alerts, and comprehensive tracking.
@since 2024-1-1 to 2025-25-7
"""

import logging
import asyncio
import json
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from bson import ObjectId
from dataclasses import dataclass, asdict

from app.models.ecommerce import SyncedProduct
from app.models.user import User
from app.db.mongodb import get_database
from app.core.redis import get_redis_client
from app.core.monitoring import monitor_performance, log_audit_event, OperationType
from app.core.websocket_manager import websocket_manager
from app.services.notifications.notification_service import notification_service

logger = logging.getLogger(__name__)

# Collection names
PRODUCTS_COLLECTION = "synced_products"
INVENTORY_LOGS_COLLECTION = "inventory_logs"
INVENTORY_ALERTS_COLLECTION = "inventory_alerts"
LOW_STOCK_RULES_COLLECTION = "low_stock_rules"

# Redis keys
INVENTORY_CACHE_KEY = "inventory:{product_id}"
LOW_STOCK_CACHE_KEY = "low_stock:{store_id}"
INVENTORY_ALERTS_KEY = "inventory_alerts:{user_id}"

# WebSocket channels
INVENTORY_CHANNEL = "inventory_updates"
ALERTS_CHANNEL = "inventory_alerts"

# Alert thresholds
DEFAULT_LOW_STOCK_THRESHOLD = 10
DEFAULT_OUT_OF_STOCK_THRESHOLD = 0


@dataclass
class InventoryUpdate:
    """Inventory update data structure."""
    product_id: str
    store_id: str
    user_id: str
    old_quantity: int
    new_quantity: int
    change_amount: int
    reason: str
    changed_by: str
    timestamp: datetime
    location: Optional[str] = None
    batch_id: Optional[str] = None


@dataclass
class InventoryAlert:
    """Inventory alert data structure."""
    alert_id: str
    product_id: str
    store_id: str
    user_id: str
    alert_type: str  # low_stock, out_of_stock, negative_stock
    current_quantity: int
    threshold: int
    product_title: str
    product_sku: str
    timestamp: datetime
    acknowledged: bool = False


class RealTimeInventoryService:
    """
    Comprehensive real-time inventory tracking service.
    """
    
    def __init__(self):
        self.redis_client = None
        self.db = None
        self.active_subscriptions: Set[str] = set()
        
    async def _get_redis_client(self):
        """Get Redis client for caching."""
        if not self.redis_client:
            self.redis_client = await get_redis_client()
        return self.redis_client
    
    async def _get_database(self):
        """Get MongoDB database."""
        if not self.db:
            self.db = await get_database()
        return self.db
    
    @monitor_performance("update_inventory_realtime")
    async def update_inventory_realtime(
        self,
        product_id: str,
        store_id: str,
        user_id: str,
        new_quantity: int,
        reason: str,
        changed_by: str,
        location: Optional[str] = None,
        batch_id: Optional[str] = None,
        notify_subscribers: bool = True
    ) -> Dict[str, Any]:
        """
        Update inventory with real-time notifications.
        
        Args:
            product_id: Product ID
            store_id: Store ID
            user_id: User ID
            new_quantity: New inventory quantity
            reason: Reason for change
            changed_by: User who made the change
            location: Optional location/warehouse
            batch_id: Optional batch ID for bulk operations
            notify_subscribers: Whether to send real-time notifications
            
        Returns:
            Update result with change details
        """
        try:
            db = await self._get_database()
            
            # Get current product data
            product = await db[PRODUCTS_COLLECTION].find_one({
                "_id": ObjectId(product_id),
                "user_id": ObjectId(user_id),
                "store_id": ObjectId(store_id)
            })
            
            if not product:
                return {
                    "success": False,
                    "error": "Product not found or access denied"
                }
            
            old_quantity = product.get("inventory_quantity", 0)
            change_amount = new_quantity - old_quantity
            
            # Update product inventory
            await db[PRODUCTS_COLLECTION].update_one(
                {"_id": ObjectId(product_id)},
                {
                    "$set": {
                        "inventory_quantity": new_quantity,
                        "last_inventory_update": datetime.now(timezone.utc),
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            
            # Create inventory update record
            inventory_update = InventoryUpdate(
                product_id=product_id,
                store_id=store_id,
                user_id=user_id,
                old_quantity=old_quantity,
                new_quantity=new_quantity,
                change_amount=change_amount,
                reason=reason,
                changed_by=changed_by,
                timestamp=datetime.now(timezone.utc),
                location=location,
                batch_id=batch_id
            )
            
            # Log inventory change
            await self._log_inventory_change(inventory_update)
            
            # Update cache
            await self._update_inventory_cache(product_id, new_quantity)
            
            # Check for alerts
            alerts = await self._check_inventory_alerts(
                product_id, store_id, user_id, new_quantity, product
            )
            
            # Send real-time notifications
            if notify_subscribers:
                await self._broadcast_inventory_update(inventory_update, product)
                
                # Send alerts if any
                for alert in alerts:
                    await self._broadcast_inventory_alert(alert)
            
            # Log audit event
            log_audit_event(
                operation_type=OperationType.UPDATE,
                resource_type="inventory",
                resource_id=product_id,
                user_id=user_id,
                details={
                    "old_quantity": old_quantity,
                    "new_quantity": new_quantity,
                    "change_amount": change_amount,
                    "reason": reason
                }
            )
            
            return {
                "success": True,
                "product_id": product_id,
                "old_quantity": old_quantity,
                "new_quantity": new_quantity,
                "change_amount": change_amount,
                "alerts_triggered": len(alerts),
                "timestamp": inventory_update.timestamp.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error updating inventory for product {product_id}: {str(e)}")
            return {
                "success": False,
                "error": f"Failed to update inventory: {str(e)}",
                "product_id": product_id
            }
    
    async def _log_inventory_change(self, update: InventoryUpdate):
        """Log inventory change to database."""
        try:
            db = await self._get_database()
            
            log_doc = {
                **asdict(update),
                "_id": ObjectId(),
                "product_id": ObjectId(update.product_id),
                "store_id": ObjectId(update.store_id),
                "user_id": ObjectId(update.user_id)
            }
            
            await db[INVENTORY_LOGS_COLLECTION].insert_one(log_doc)
            
        except Exception as e:
            logger.error(f"Error logging inventory change: {str(e)}")
    
    async def _update_inventory_cache(self, product_id: str, quantity: int):
        """Update inventory cache in Redis."""
        try:
            redis = await self._get_redis_client()
            if redis:
                cache_key = INVENTORY_CACHE_KEY.format(product_id=product_id)
                cache_data = {
                    "quantity": quantity,
                    "last_updated": datetime.now(timezone.utc).isoformat()
                }
                await redis.setex(cache_key, 3600, json.dumps(cache_data))
                
        except Exception as e:
            logger.error(f"Error updating inventory cache: {str(e)}")
    
    async def _check_inventory_alerts(
        self,
        product_id: str,
        store_id: str,
        user_id: str,
        quantity: int,
        product: Dict[str, Any]
    ) -> List[InventoryAlert]:
        """Check for inventory alerts and create them if needed."""
        alerts = []
        
        try:
            # Get alert rules for this store/product
            alert_rules = await self._get_alert_rules(store_id, product_id)
            
            low_stock_threshold = alert_rules.get("low_stock_threshold", DEFAULT_LOW_STOCK_THRESHOLD)
            out_of_stock_threshold = alert_rules.get("out_of_stock_threshold", DEFAULT_OUT_OF_STOCK_THRESHOLD)
            
            # Check for different alert types
            alert_type = None
            threshold = 0
            
            if quantity < 0:
                alert_type = "negative_stock"
                threshold = 0
            elif quantity <= out_of_stock_threshold:
                alert_type = "out_of_stock"
                threshold = out_of_stock_threshold
            elif quantity <= low_stock_threshold:
                alert_type = "low_stock"
                threshold = low_stock_threshold
            
            if alert_type:
                # Check if we already have an active alert for this product
                db = await self._get_database()
                existing_alert = await db[INVENTORY_ALERTS_COLLECTION].find_one({
                    "product_id": ObjectId(product_id),
                    "alert_type": alert_type,
                    "acknowledged": False
                })
                
                if not existing_alert:
                    # Create new alert
                    alert = InventoryAlert(
                        alert_id=str(ObjectId()),
                        product_id=product_id,
                        store_id=store_id,
                        user_id=user_id,
                        alert_type=alert_type,
                        current_quantity=quantity,
                        threshold=threshold,
                        product_title=product.get("title", "Unknown Product"),
                        product_sku=product.get("sku", ""),
                        timestamp=datetime.now(timezone.utc)
                    )
                    
                    # Save alert to database
                    await self._save_inventory_alert(alert)
                    alerts.append(alert)
            
            return alerts
            
        except Exception as e:
            logger.error(f"Error checking inventory alerts: {str(e)}")
            return []
    
    async def _get_alert_rules(self, store_id: str, product_id: str) -> Dict[str, Any]:
        """Get alert rules for store/product."""
        try:
            db = await self._get_database()
            
            # Check for product-specific rules first
            product_rules = await db[LOW_STOCK_RULES_COLLECTION].find_one({
                "product_id": ObjectId(product_id)
            })
            
            if product_rules:
                return product_rules
            
            # Check for store-wide rules
            store_rules = await db[LOW_STOCK_RULES_COLLECTION].find_one({
                "store_id": ObjectId(store_id),
                "product_id": None
            })
            
            if store_rules:
                return store_rules
            
            # Return default rules
            return {
                "low_stock_threshold": DEFAULT_LOW_STOCK_THRESHOLD,
                "out_of_stock_threshold": DEFAULT_OUT_OF_STOCK_THRESHOLD
            }
            
        except Exception as e:
            logger.error(f"Error getting alert rules: {str(e)}")
            return {
                "low_stock_threshold": DEFAULT_LOW_STOCK_THRESHOLD,
                "out_of_stock_threshold": DEFAULT_OUT_OF_STOCK_THRESHOLD
            }
    
    async def _save_inventory_alert(self, alert: InventoryAlert):
        """Save inventory alert to database."""
        try:
            db = await self._get_database()
            
            alert_doc = {
                **asdict(alert),
                "_id": ObjectId(alert.alert_id),
                "product_id": ObjectId(alert.product_id),
                "store_id": ObjectId(alert.store_id),
                "user_id": ObjectId(alert.user_id)
            }
            
            await db[INVENTORY_ALERTS_COLLECTION].insert_one(alert_doc)
            
        except Exception as e:
            logger.error(f"Error saving inventory alert: {str(e)}")
    
    async def _broadcast_inventory_update(
        self,
        update: InventoryUpdate,
        product: Dict[str, Any]
    ):
        """Broadcast inventory update via WebSocket."""
        try:
            message = {
                "type": "inventory_update",
                "data": {
                    "product_id": update.product_id,
                    "store_id": update.store_id,
                    "product_title": product.get("title", ""),
                    "product_sku": product.get("sku", ""),
                    "old_quantity": update.old_quantity,
                    "new_quantity": update.new_quantity,
                    "change_amount": update.change_amount,
                    "reason": update.reason,
                    "timestamp": update.timestamp.isoformat(),
                    "location": update.location
                }
            }
            
            # Send to user's inventory channel
            channel = f"{INVENTORY_CHANNEL}:{update.user_id}"
            await websocket_manager.broadcast_to_channel(channel, message)
            
        except Exception as e:
            logger.error(f"Error broadcasting inventory update: {str(e)}")
    
    async def _broadcast_inventory_alert(self, alert: InventoryAlert):
        """Broadcast inventory alert via WebSocket."""
        try:
            message = {
                "type": "inventory_alert",
                "data": {
                    "alert_id": alert.alert_id,
                    "product_id": alert.product_id,
                    "product_title": alert.product_title,
                    "product_sku": alert.product_sku,
                    "alert_type": alert.alert_type,
                    "current_quantity": alert.current_quantity,
                    "threshold": alert.threshold,
                    "timestamp": alert.timestamp.isoformat()
                }
            }
            
            # Send to user's alerts channel
            channel = f"{ALERTS_CHANNEL}:{alert.user_id}"
            await websocket_manager.broadcast_to_channel(channel, message)
            
            # Also send push notification
            await notification_service.send_inventory_alert(alert)
            
        except Exception as e:
            logger.error(f"Error broadcasting inventory alert: {str(e)}")


    @monitor_performance("get_inventory_status")
    async def get_inventory_status(
        self,
        user_id: str,
        store_ids: Optional[List[str]] = None,
        include_alerts: bool = True
    ) -> Dict[str, Any]:
        """
        Get comprehensive inventory status for user's stores.

        Args:
            user_id: User ID
            store_ids: Optional specific store IDs
            include_alerts: Whether to include active alerts

        Returns:
            Comprehensive inventory status
        """
        try:
            db = await self._get_database()

            # Build query
            query: Dict[str, Any] = {"user_id": ObjectId(user_id)}
            if store_ids:
                query["store_id"] = {"$in": [ObjectId(sid) for sid in store_ids]}

            # Get inventory summary
            pipeline = [
                {"$match": query},
                {
                    "$group": {
                        "_id": "$store_id",
                        "total_products": {"$sum": 1},
                        "total_inventory": {"$sum": "$inventory_quantity"},
                        "out_of_stock": {
                            "$sum": {"$cond": [{"$lte": ["$inventory_quantity", 0]}, 1, 0]}
                        },
                        "low_stock": {
                            "$sum": {"$cond": [
                                {"$and": [
                                    {"$gt": ["$inventory_quantity", 0]},
                                    {"$lte": ["$inventory_quantity", DEFAULT_LOW_STOCK_THRESHOLD]}
                                ]}, 1, 0
                            ]}
                        },
                        "in_stock": {
                            "$sum": {"$cond": [{"$gt": ["$inventory_quantity", DEFAULT_LOW_STOCK_THRESHOLD]}, 1, 0]}
                        }
                    }
                },
                {
                    "$lookup": {
                        "from": "stores",
                        "localField": "_id",
                        "foreignField": "_id",
                        "as": "store_info"
                    }
                }
            ]

            inventory_summary = await db[PRODUCTS_COLLECTION].aggregate(pipeline).to_list(None)

            # Get active alerts if requested
            active_alerts = []
            if include_alerts:
                alerts_query = {
                    "user_id": ObjectId(user_id),
                    "acknowledged": False
                }
                if store_ids:
                    alerts_query["store_id"] = {"$in": [ObjectId(sid) for sid in store_ids]}

                active_alerts = await db[INVENTORY_ALERTS_COLLECTION].find(
                    alerts_query
                ).sort("timestamp", -1).to_list(None)

            # Format response
            stores_status = []
            for store_data in inventory_summary:
                store_info = store_data.get("store_info", [{}])[0]
                stores_status.append({
                    "store_id": str(store_data["_id"]),
                    "store_name": store_info.get("name", "Unknown Store"),
                    "total_products": store_data["total_products"],
                    "total_inventory": store_data["total_inventory"],
                    "in_stock": store_data["in_stock"],
                    "low_stock": store_data["low_stock"],
                    "out_of_stock": store_data["out_of_stock"],
                    "stock_health": self._calculate_stock_health(store_data)
                })

            # Format alerts
            formatted_alerts = []
            for alert in active_alerts:
                formatted_alerts.append({
                    "alert_id": str(alert["_id"]),
                    "product_id": str(alert["product_id"]),
                    "store_id": str(alert["store_id"]),
                    "alert_type": alert["alert_type"],
                    "product_title": alert["product_title"],
                    "product_sku": alert["product_sku"],
                    "current_quantity": alert["current_quantity"],
                    "threshold": alert["threshold"],
                    "timestamp": alert["timestamp"].isoformat()
                })

            return {
                "success": True,
                "user_id": user_id,
                "stores": stores_status,
                "active_alerts": formatted_alerts,
                "summary": {
                    "total_stores": len(stores_status),
                    "total_products": sum(s["total_products"] for s in stores_status),
                    "total_inventory": sum(s["total_inventory"] for s in stores_status),
                    "total_alerts": len(formatted_alerts)
                },
                "last_updated": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting inventory status: {str(e)}")
            return {
                "success": False,
                "error": f"Failed to get inventory status: {str(e)}",
                "user_id": user_id
            }

    def _calculate_stock_health(self, store_data: Dict[str, Any]) -> str:
        """Calculate overall stock health for a store."""
        total = store_data["total_products"]
        if total == 0:
            return "unknown"

        out_of_stock_pct = (store_data["out_of_stock"] / total) * 100
        low_stock_pct = (store_data["low_stock"] / total) * 100

        if out_of_stock_pct > 20:
            return "critical"
        elif out_of_stock_pct > 10 or low_stock_pct > 30:
            return "warning"
        elif low_stock_pct > 15:
            return "attention"
        else:
            return "healthy"

    @monitor_performance("subscribe_to_inventory_updates")
    async def subscribe_to_inventory_updates(
        self,
        user_id: str,
        connection_id: str,
        store_ids: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Subscribe to real-time inventory updates.

        Args:
            user_id: User ID
            connection_id: WebSocket connection ID
            store_ids: Optional specific store IDs to subscribe to

        Returns:
            Subscription result
        """
        try:
            # Subscribe to inventory updates channel
            inventory_channel = f"{INVENTORY_CHANNEL}:{user_id}"
            await websocket_manager.subscribe_to_channel(connection_id, inventory_channel)

            # Subscribe to alerts channel
            alerts_channel = f"{ALERTS_CHANNEL}:{user_id}"
            await websocket_manager.subscribe_to_channel(connection_id, alerts_channel)

            # Track active subscription
            subscription_key = f"{user_id}:{connection_id}"
            self.active_subscriptions.add(subscription_key)

            # Send initial inventory status
            initial_status = await self.get_inventory_status(user_id, store_ids)

            await websocket_manager.send_to_connection(connection_id, {
                "type": "subscription_confirmed",
                "data": {
                    "channels": [inventory_channel, alerts_channel],
                    "initial_status": initial_status
                }
            })

            return {
                "success": True,
                "user_id": user_id,
                "connection_id": connection_id,
                "channels": [inventory_channel, alerts_channel]
            }

        except Exception as e:
            logger.error(f"Error subscribing to inventory updates: {str(e)}")
            return {
                "success": False,
                "error": f"Failed to subscribe: {str(e)}",
                "user_id": user_id
            }

    async def unsubscribe_from_inventory_updates(
        self,
        user_id: str,
        connection_id: str
    ) -> Dict[str, Any]:
        """
        Unsubscribe from real-time inventory updates.

        Args:
            user_id: User ID
            connection_id: WebSocket connection ID

        Returns:
            Unsubscription result
        """
        try:
            # Unsubscribe from channels
            inventory_channel = f"{INVENTORY_CHANNEL}:{user_id}"
            alerts_channel = f"{ALERTS_CHANNEL}:{user_id}"

            await websocket_manager.unsubscribe_from_channel(connection_id, inventory_channel)
            await websocket_manager.unsubscribe_from_channel(connection_id, alerts_channel)

            # Remove from active subscriptions
            subscription_key = f"{user_id}:{connection_id}"
            self.active_subscriptions.discard(subscription_key)

            return {
                "success": True,
                "user_id": user_id,
                "connection_id": connection_id
            }

        except Exception as e:
            logger.error(f"Error unsubscribing from inventory updates: {str(e)}")
            return {
                "success": False,
                "error": f"Failed to unsubscribe: {str(e)}",
                "user_id": user_id
            }

    @monitor_performance("acknowledge_inventory_alert")
    async def acknowledge_inventory_alert(
        self,
        user_id: str,
        alert_id: str
    ) -> Dict[str, Any]:
        """
        Acknowledge an inventory alert.

        Args:
            user_id: User ID
            alert_id: Alert ID to acknowledge

        Returns:
            Acknowledgment result
        """
        try:
            db = await self._get_database()

            # Update alert as acknowledged
            result = await db[INVENTORY_ALERTS_COLLECTION].update_one(
                {
                    "_id": ObjectId(alert_id),
                    "user_id": ObjectId(user_id)
                },
                {
                    "$set": {
                        "acknowledged": True,
                        "acknowledged_at": datetime.now(timezone.utc)
                    }
                }
            )

            if result.matched_count == 0:
                return {
                    "success": False,
                    "error": "Alert not found or access denied"
                }

            # Broadcast acknowledgment
            message = {
                "type": "alert_acknowledged",
                "data": {
                    "alert_id": alert_id,
                    "acknowledged_at": datetime.now(timezone.utc).isoformat()
                }
            }

            channel = f"{ALERTS_CHANNEL}:{user_id}"
            await websocket_manager.broadcast_to_channel(channel, message)

            return {
                "success": True,
                "alert_id": alert_id,
                "acknowledged_at": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(f"Error acknowledging inventory alert: {str(e)}")
            return {
                "success": False,
                "error": f"Failed to acknowledge alert: {str(e)}",
                "alert_id": alert_id
            }


# Create singleton instance
realtime_inventory_service = RealTimeInventoryService()
