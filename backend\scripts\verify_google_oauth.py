#!/usr/bin/env python3
"""
Google OAuth 2.0 Production Readiness Verification Script

This script verifies that the Google OAuth implementation is production-ready
by checking all security measures, configurations, and integrations.

Usage:
    python scripts/verify_google_oauth.py

@since 2024-1-1 to 2025-25-7
"""
import asyncio
import sys
import os
from typing import List, Dict, Any
from datetime import datetime

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.core.config import settings
from app.services.social_media.google import GoogleIntegration
from app.services.social_media.factory import SocialMediaIntegrationFactory
from app.api.dependencies.rate_limiter import google_oauth_limiter
from app.services.audit_logging import AuditLogger


class GoogleOAuthVerifier:
    """Comprehensive verification of Google OAuth implementation."""
    
    def __init__(self):
        self.results: List[Dict[str, Any]] = []
        self.google_integration = GoogleIntegration()
        self.audit_logger = AuditLogger()
    
    def log_result(self, check_name: str, status: str, message: str, details: Dict | None = None):
        """Log verification result."""
        result = {
            "check": check_name,
            "status": status,  # "PASS", "FAIL", "WARNING"
            "message": message,
            "details": details or {},
            "timestamp": datetime.now().isoformat()
        }
        self.results.append(result)
        
        # Color coding for console output
        color = {
            "PASS": "\033[92m",    # Green
            "FAIL": "\033[91m",    # Red
            "WARNING": "\033[93m", # Yellow
        }.get(status, "")
        reset = "\033[0m"
        
        print(f"{color}[{status}]{reset} {check_name}: {message}")
        if details:
            for key, value in details.items():
                print(f"  {key}: {value}")
    
    def check_environment_configuration(self):
        """Verify environment configuration."""
        print("\n=== Environment Configuration ===")
        
        # Check Google OAuth credentials
        if settings.GOOGLE_CLIENT_ID:
            self.log_result(
                "Google Client ID",
                "PASS",
                "Google Client ID is configured",
                {"client_id_length": len(settings.GOOGLE_CLIENT_ID)}
            )
        else:
            self.log_result(
                "Google Client ID",
                "FAIL",
                "Google Client ID is not configured. Set GOOGLE_CLIENT_ID environment variable."
            )
        
        if settings.GOOGLE_CLIENT_SECRET:
            self.log_result(
                "Google Client Secret",
                "PASS",
                "Google Client Secret is configured",
                {"secret_length": len(settings.GOOGLE_CLIENT_SECRET)}
            )
        else:
            self.log_result(
                "Google Client Secret",
                "FAIL",
                "Google Client Secret is not configured. Set GOOGLE_CLIENT_SECRET environment variable."
            )
        
        # Check database configuration
        if settings.MONGODB_URL:
            self.log_result(
                "Database Configuration",
                "PASS",
                "MongoDB URL is configured"
            )
        else:
            self.log_result(
                "Database Configuration",
                "FAIL",
                "MongoDB URL is not configured"
            )
    
    async def check_google_integration(self):
        """Verify Google integration service."""
        print("\n=== Google Integration Service ===")
        
        try:
            # Test authorization URL generation
            if settings.GOOGLE_CLIENT_ID:
                auth_url, state = await self.google_integration.get_authorization_url(
                    "http://localhost:3000/auth/google/callback"
                )
                
                # Verify URL structure
                required_params = [
                    "client_id", "redirect_uri", "state", "scope",
                    "response_type", "code_challenge", "code_challenge_method"
                ]
                
                missing_params = [param for param in required_params if param not in auth_url]
                
                if not missing_params:
                    self.log_result(
                        "Authorization URL Generation",
                        "PASS",
                        "Authorization URL contains all required parameters",
                        {"url_length": len(auth_url), "state_length": len(state)}
                    )
                else:
                    self.log_result(
                        "Authorization URL Generation",
                        "FAIL",
                        f"Missing required parameters: {missing_params}"
                    )
                
                # Verify PKCE implementation
                if "code_challenge_method=S256" in auth_url:
                    self.log_result(
                        "PKCE Implementation",
                        "PASS",
                        "PKCE with S256 method is properly implemented"
                    )
                else:
                    self.log_result(
                        "PKCE Implementation",
                        "FAIL",
                        "PKCE implementation is missing or incorrect"
                    )
                
                # Verify state parameter security
                if ":" in state and len(state.split(":")[0]) >= 32:
                    self.log_result(
                        "State Parameter Security",
                        "PASS",
                        "State parameter is cryptographically secure"
                    )
                else:
                    self.log_result(
                        "State Parameter Security",
                        "FAIL",
                        "State parameter is not secure enough"
                    )
            else:
                self.log_result(
                    "Google Integration",
                    "FAIL",
                    "Cannot test integration without Google credentials"
                )
                
        except Exception as e:
            self.log_result(
                "Google Integration",
                "FAIL",
                f"Integration test failed: {str(e)}"
            )
    
    def check_factory_integration(self):
        """Verify factory integration."""
        print("\n=== Factory Integration ===")
        
        try:
            # Test factory can create Google integration
            integration = SocialMediaIntegrationFactory.get_integration("google")
            
            if isinstance(integration, GoogleIntegration):
                self.log_result(
                    "Factory Integration",
                    "PASS",
                    "Google integration is properly registered in factory"
                )
            else:
                self.log_result(
                    "Factory Integration",
                    "FAIL",
                    f"Factory returned wrong type: {type(integration)}"
                )
                
        except Exception as e:
            self.log_result(
                "Factory Integration",
                "FAIL",
                f"Factory integration failed: {str(e)}"
            )
    
    def check_rate_limiting(self):
        """Verify rate limiting configuration."""
        print("\n=== Rate Limiting ===")
        
        # Check rate limiter configuration
        if hasattr(google_oauth_limiter, 'max_requests'):
            self.log_result(
                "Rate Limiter Configuration",
                "PASS",
                "Google OAuth rate limiter is configured",
                {
                    "max_requests": google_oauth_limiter.max_requests,
                    "time_window": google_oauth_limiter.time_window
                }
            )
        else:
            self.log_result(
                "Rate Limiter Configuration",
                "FAIL",
                "Google OAuth rate limiter is not properly configured"
            )
    
    def check_audit_logging(self):
        """Verify audit logging configuration."""
        print("\n=== Audit Logging ===")
        
        try:
            # Check audit logger initialization
            if self.audit_logger.collection_name:
                self.log_result(
                    "Audit Logger Configuration",
                    "PASS",
                    "Audit logger is properly configured",
                    {"collection": self.audit_logger.collection_name}
                )
            else:
                self.log_result(
                    "Audit Logger Configuration",
                    "FAIL",
                    "Audit logger is not properly configured"
                )
                
        except Exception as e:
            self.log_result(
                "Audit Logging",
                "FAIL",
                f"Audit logging check failed: {str(e)}"
            )
    
    def check_security_measures(self):
        """Verify security measures."""
        print("\n=== Security Measures ===")
        
        # Check HTTPS enforcement (in production)
        if settings.ENVIRONMENT == "production":
            # In production, should enforce HTTPS
            self.log_result(
                "HTTPS Enforcement",
                "WARNING",
                "Ensure HTTPS is enforced in production deployment"
            )
        else:
            self.log_result(
                "HTTPS Enforcement",
                "PASS",
                "Development environment - HTTPS enforcement not required"
            )
        
        # Check secret key configuration
        if hasattr(settings, 'SECRET_KEY') and len(settings.SECRET_KEY) >= 32:
            self.log_result(
                "Secret Key Security",
                "PASS",
                "Secret key is properly configured"
            )
        else:
            self.log_result(
                "Secret Key Security",
                "WARNING",
                "Ensure secret key is properly configured and secure"
            )
    
    def generate_report(self):
        """Generate final verification report."""
        print("\n" + "="*60)
        print("GOOGLE OAUTH PRODUCTION READINESS REPORT")
        print("="*60)
        
        total_checks = len(self.results)
        passed = len([r for r in self.results if r["status"] == "PASS"])
        failed = len([r for r in self.results if r["status"] == "FAIL"])
        warnings = len([r for r in self.results if r["status"] == "WARNING"])
        
        print(f"Total Checks: {total_checks}")
        print(f"Passed: {passed}")
        print(f"Failed: {failed}")
        print(f"Warnings: {warnings}")
        
        if failed > 0:
            print("\n❌ PRODUCTION READINESS: FAILED")
            print("Please fix the failed checks before deploying to production.")
            return False
        elif warnings > 0:
            print("\n⚠️  PRODUCTION READINESS: PASSED WITH WARNINGS")
            print("Please review the warnings before deploying to production.")
            return True
        else:
            print("\n✅ PRODUCTION READINESS: PASSED")
            print("Google OAuth implementation is ready for production deployment.")
            return True
    
    async def run_all_checks(self):
        """Run all verification checks."""
        print("Starting Google OAuth Production Readiness Verification...")
        print(f"Timestamp: {datetime.now().isoformat()}")
        
        self.check_environment_configuration()
        await self.check_google_integration()
        self.check_factory_integration()
        self.check_rate_limiting()
        self.check_audit_logging()
        self.check_security_measures()
        
        return self.generate_report()


async def main():
    """Main verification function."""
    verifier = GoogleOAuthVerifier()
    success = await verifier.run_all_checks()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())
