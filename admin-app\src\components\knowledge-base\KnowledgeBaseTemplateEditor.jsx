// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Typography,
  Box,
  Alert,
  IconButton,
  Chip
} from '@mui/material';
import {
  Save as SaveIcon,
  Close as CloseIcon,
  Add as AddIcon
} from '@mui/icons-material';

const KnowledgeBaseTemplateEditor = ({ 
  open, 
  mode = 'create', 
  template = null, 
  onClose, 
  onSubmit 
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    article_type: 'help_article',
    category: 'general',
    template_content: '',
    variables: []
  });

  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [newVariable, setNewVariable] = useState('');

  useEffect(() => {
    if (mode === 'edit' && template) {
      setFormData({
        name: template.name || '',
        description: template.description || '',
        article_type: template.article_type || 'help_article',
        category: template.category || 'general',
        template_content: template.template_content || '',
        variables: template.variables || []
      });
    } else {
      // Reset for create mode
      setFormData({
        name: '',
        description: '',
        article_type: 'help_article',
        category: 'general',
        template_content: '',
        variables: []
      });
    }
  }, [mode, template, open]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const handleAddVariable = () => {
    if (newVariable.trim() && !formData.variables.includes(newVariable.trim())) {
      setFormData(prev => ({
        ...prev,
        variables: [...prev.variables, newVariable.trim()]
      }));
      setNewVariable('');
    }
  };

  const handleRemoveVariable = (variableToRemove) => {
    setFormData(prev => ({
      ...prev,
      variables: prev.variables.filter(variable => variable !== variableToRemove)
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Template name is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Template description is required';
    }

    if (!formData.template_content.trim()) {
      newErrors.template_content = 'Template content is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting template:', error);
    } finally {
      setLoading(false);
    }
  };

  const getDialogTitle = () => {
    switch (mode) {
      case 'create':
        return 'Create Article Template';
      case 'edit':
        return 'Edit Article Template';
      default:
        return 'Article Template';
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: { minHeight: '70vh' }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          {getDialogTitle()}
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      
      <DialogContent dividers>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <TextField
              fullWidth
              label="Template Name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              error={!!errors.name}
              helperText={errors.name}
              placeholder="FAQ Template"
            />
          </Grid>
          
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>Article Type</InputLabel>
              <Select
                value={formData.article_type}
                label="Article Type"
                onChange={(e) => handleInputChange('article_type', e.target.value)}
              >
                <MenuItem value="help_article">Help Article</MenuItem>
                <MenuItem value="blog_post">Blog Post</MenuItem>
                <MenuItem value="announcement">Announcement</MenuItem>
                <MenuItem value="technical_doc">Technical Documentation</MenuItem>
                <MenuItem value="faq">FAQ</MenuItem>
                <MenuItem value="tutorial">Tutorial</MenuItem>
                <MenuItem value="troubleshooting">Troubleshooting</MenuItem>
                <MenuItem value="user_guide">User Guide</MenuItem>
                <MenuItem value="api_documentation">API Documentation</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Description"
              multiline
              rows={3}
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              error={!!errors.description}
              helperText={errors.description}
              placeholder="Brief description of this template"
            />
          </Grid>
          
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Category</InputLabel>
              <Select
                value={formData.category}
                label="Category"
                onChange={(e) => handleInputChange('category', e.target.value)}
              >
                <MenuItem value="faq">FAQ</MenuItem>
                <MenuItem value="tutorials">Tutorials</MenuItem>
                <MenuItem value="api_documentation">API Documentation</MenuItem>
                <MenuItem value="user_guides">User Guides</MenuItem>
                <MenuItem value="troubleshooting">Troubleshooting</MenuItem>
                <MenuItem value="announcements">Announcements</MenuItem>
                <MenuItem value="technical">Technical</MenuItem>
                <MenuItem value="billing">Billing</MenuItem>
                <MenuItem value="account">Account</MenuItem>
                <MenuItem value="integrations">Integrations</MenuItem>
                <MenuItem value="features">Features</MenuItem>
                <MenuItem value="general">General</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              Template Content
            </Typography>
            <TextField
              fullWidth
              multiline
              rows={12}
              value={formData.template_content}
              onChange={(e) => handleInputChange('template_content', e.target.value)}
              error={!!errors.template_content}
              helperText={errors.template_content || "Use {{variable_name}} for template variables"}
              placeholder="# {{article_title}}

## Overview
{{overview_content}}

## Steps
1. {{step_1}}
2. {{step_2}}
3. {{step_3}}

## Conclusion
{{conclusion_content}}"
              sx={{ fontFamily: 'monospace' }}
            />
          </Grid>
          
          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom>
              Template Variables
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Define variables that can be replaced when creating articles from this template
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
              {formData.variables.map((variable) => (
                <Chip
                  key={variable}
                  label={`{{${variable}}}`}
                  onDelete={() => handleRemoveVariable(variable)}
                  size="small"
                />
              ))}
            </Box>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <TextField
                size="small"
                placeholder="Variable name (e.g., article_title)"
                value={newVariable}
                onChange={(e) => setNewVariable(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddVariable();
                  }
                }}
              />
              <Button
                variant="outlined"
                size="small"
                onClick={handleAddVariable}
                startIcon={<AddIcon />}
              >
                Add Variable
              </Button>
            </Box>
          </Grid>
        </Grid>

        {Object.keys(errors).length > 0 && (
          <Alert severity="error" sx={{ mt: 2 }}>
            Please fix the errors above before submitting.
          </Alert>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained"
          disabled={loading}
          startIcon={<SaveIcon />}
        >
          {loading ? 'Saving...' : (mode === 'create' ? 'Create Template' : 'Update Template')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default KnowledgeBaseTemplateEditor;
