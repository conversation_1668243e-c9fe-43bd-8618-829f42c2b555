// @since 2024-1-1 to 2025-25-7
import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  LinearProgress,
  Chip,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  CircularProgress,
  Avatar,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  Article as ArticleIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Visibility as ViewIcon,
  ThumbUp as HelpfulIcon,
  Refresh as RefreshIcon,
  CheckCircle as PublishedIcon,
  Draft as DraftIcon,
  Archive as ArchivedIcon,
  Analytics as AnalyticsIcon,
  Schedule as ScheduleIcon,
  Star as FeaturedIcon,
  Category as CategoryIcon,
  Search as SearchIcon,
  People as UsersIcon
} from '@mui/icons-material';

const KnowledgeBaseDashboard = ({ 
  dashboard, 
  loading = false, 
  onRefresh 
}) => {
  const formatNumber = (value) => {
    return value?.toLocaleString() || '0';
  };

  const formatPercentage = (value) => {
    return `${(value || 0).toFixed(1)}%`;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'published':
        return 'success';
      case 'draft':
        return 'default';
      case 'archived':
        return 'warning';
      case 'scheduled':
        return 'info';
      case 'under_review':
        return 'primary';
      default:
        return 'default';
    }
  };

  const getCategoryColor = (category) => {
    const colors = {
      'faq': 'primary',
      'tutorials': 'secondary',
      'api_documentation': 'info',
      'user_guides': 'success',
      'troubleshooting': 'warning',
      'announcements': 'error'
    };
    return colors[category] || 'default';
  };

  if (loading && !dashboard) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!dashboard) {
    return (
      <Alert severity="info">
        No dashboard data available. Please check your knowledge base configuration.
      </Alert>
    );
  }

  return (
    <Box>
      {/* Overview Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <ArticleIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" component="div">
                    Total Articles
                  </Typography>
                  <Typography variant="h4" component="div" color="primary">
                    {dashboard.total_articles || 0}
                  </Typography>
                </Box>
              </Box>
              <Typography variant="body2" color="text.secondary">
                {dashboard.published_articles || 0} published, {dashboard.draft_articles || 0} drafts
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                  <ViewIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" component="div">
                    Total Views
                  </Typography>
                  <Typography variant="h4" component="div" color="success.main">
                    {formatNumber(dashboard.total_views || 0)}
                  </Typography>
                </Box>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Across all published articles
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                  <HelpfulIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" component="div">
                    Helpful Votes
                  </Typography>
                  <Typography variant="h4" component="div" color="secondary.main">
                    {formatNumber(dashboard.total_helpful_votes || 0)}
                  </Typography>
                </Box>
              </Box>
              <Typography variant="body2" color="text.secondary">
                User feedback on articles
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                  <AnalyticsIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" component="div">
                    Avg. Helpfulness
                  </Typography>
                  <Typography variant="h4" component="div" color="info.main">
                    {formatPercentage(dashboard.average_helpfulness_score || 0)}
                  </Typography>
                </Box>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Overall content quality score
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Content Performance and Categories */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6" component="div">
                  Popular Categories
                </Typography>
                <Tooltip title="Refresh Data">
                  <IconButton onClick={onRefresh} size="small">
                    <RefreshIcon />
                  </IconButton>
                </Tooltip>
              </Box>
              
              {dashboard.popular_categories && dashboard.popular_categories.length > 0 ? (
                <List>
                  {dashboard.popular_categories.slice(0, 6).map((category, index) => (
                    <ListItem key={category.category} divider={index < 5}>
                      <ListItemIcon>
                        <Avatar sx={{ bgcolor: getCategoryColor(category.category) + '.main', width: 32, height: 32 }}>
                          <CategoryIcon fontSize="small" />
                        </Avatar>
                      </ListItemIcon>
                      <ListItemText
                        primary={category.category.replace('_', ' ').toUpperCase()}
                        secondary={`${category.count} articles`}
                      />
                      <Box sx={{ minWidth: 60 }}>
                        <LinearProgress
                          variant="determinate"
                          value={(category.count / dashboard.total_articles) * 100}
                          sx={{ height: 6, borderRadius: 3 }}
                        />
                      </Box>
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography color="text.secondary" sx={{ textAlign: 'center', py: 3 }}>
                  No category data available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" component="div" gutterBottom>
                Content Status Overview
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <PublishedIcon color="success" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h4" color="success.main">
                      {dashboard.published_articles || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Published
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <DraftIcon color="action" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h4" color="text.primary">
                      {dashboard.draft_articles || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Drafts
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <ArchivedIcon color="warning" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h4" color="warning.main">
                      {dashboard.archived_articles || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Archived
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <ScheduleIcon color="info" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h4" color="info.main">
                      0
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Scheduled
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Top Performing Articles */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" component="div" gutterBottom>
                Most Viewed Articles
              </Typography>
              
              {dashboard.most_viewed_articles && dashboard.most_viewed_articles.length > 0 ? (
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Article</TableCell>
                        <TableCell align="right">Views</TableCell>
                        <TableCell align="right">Score</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {dashboard.most_viewed_articles.slice(0, 5).map((article) => (
                        <TableRow key={article.id} hover>
                          <TableCell>
                            <Box>
                              <Typography variant="subtitle2" fontWeight="medium">
                                {article.title}
                              </Typography>
                              <Chip
                                label={article.category}
                                size="small"
                                color={getCategoryColor(article.category)}
                                variant="outlined"
                              />
                            </Box>
                          </TableCell>
                          
                          <TableCell align="right">
                            <Typography variant="body2" fontWeight="medium">
                              {formatNumber(article.view_count)}
                            </Typography>
                          </TableCell>
                          
                          <TableCell align="right">
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: 1 }}>
                              <LinearProgress
                                variant="determinate"
                                value={(article.helpfulness_score || 0) * 100}
                                sx={{ width: 40, height: 4 }}
                              />
                              <Typography variant="caption">
                                {formatPercentage((article.helpfulness_score || 0) * 100)}
                              </Typography>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography color="text.secondary" sx={{ textAlign: 'center', py: 3 }}>
                  No article data available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" component="div" gutterBottom>
                Most Helpful Articles
              </Typography>
              
              {dashboard.most_helpful_articles && dashboard.most_helpful_articles.length > 0 ? (
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Article</TableCell>
                        <TableCell align="right">Votes</TableCell>
                        <TableCell align="right">Score</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {dashboard.most_helpful_articles.slice(0, 5).map((article) => (
                        <TableRow key={article.id} hover>
                          <TableCell>
                            <Box>
                              <Typography variant="subtitle2" fontWeight="medium">
                                {article.title}
                              </Typography>
                              <Chip
                                label={article.category}
                                size="small"
                                color={getCategoryColor(article.category)}
                                variant="outlined"
                              />
                            </Box>
                          </TableCell>
                          
                          <TableCell align="right">
                            <Typography variant="body2" fontWeight="medium">
                              {formatNumber(article.helpful_votes)}
                            </Typography>
                          </TableCell>
                          
                          <TableCell align="right">
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: 1 }}>
                              <HelpfulIcon color="success" fontSize="small" />
                              <Typography variant="body2" color="success.main" fontWeight="medium">
                                {formatPercentage((article.helpfulness_score || 0) * 100)}
                              </Typography>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography color="text.secondary" sx={{ textAlign: 'center', py: 3 }}>
                  No helpful article data available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Articles */}
      <Card>
        <CardContent>
          <Typography variant="h6" component="div" gutterBottom>
            Recent Articles
          </Typography>
          
          {dashboard.recent_articles && dashboard.recent_articles.length > 0 ? (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Article</TableCell>
                    <TableCell>Category</TableCell>
                    <TableCell>Views</TableCell>
                    <TableCell>Helpfulness</TableCell>
                    <TableCell>Created</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {dashboard.recent_articles.slice(0, 10).map((article) => (
                    <TableRow key={article.id} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar sx={{ bgcolor: getCategoryColor(article.category) + '.main', width: 32, height: 32 }}>
                            <ArticleIcon fontSize="small" />
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle2" fontWeight="medium">
                              {article.title}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      
                      <TableCell>
                        <Chip
                          label={article.category.replace('_', ' ')}
                          color={getCategoryColor(article.category)}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <ViewIcon fontSize="small" color="action" />
                          <Typography variant="body2">
                            {formatNumber(article.view_count)}
                          </Typography>
                        </Box>
                      </TableCell>
                      
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <LinearProgress
                            variant="determinate"
                            value={(article.helpfulness_score || 0) * 100}
                            sx={{ width: 60, height: 4 }}
                          />
                          <Typography variant="caption">
                            {formatPercentage((article.helpfulness_score || 0) * 100)}
                          </Typography>
                        </Box>
                      </TableCell>
                      
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(article.created_at).toLocaleDateString()}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Typography color="text.secondary" sx={{ textAlign: 'center', py: 3 }}>
              No recent articles available
            </Typography>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default KnowledgeBaseDashboard;
