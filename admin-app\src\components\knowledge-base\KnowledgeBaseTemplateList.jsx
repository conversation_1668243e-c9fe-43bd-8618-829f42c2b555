// @since 2024-1-1 to 2025-25-7
import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Avatar,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Paper,
  Divider
} from '@mui/material';
import {
  MoreVert as MoreIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Template as TemplateIcon,
  Visibility as ViewIcon,
  FileCopy as CopyIcon
} from '@mui/icons-material';

const KnowledgeBaseTemplateList = ({ 
  templates = [], 
  loading = false, 
  onEdit, 
  onDelete, 
  onRefresh 
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);

  const handleMenuOpen = (event, template) => {
    setAnchorEl(event.currentTarget);
    setSelectedTemplate(template);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedTemplate(null);
  };

  const handleEdit = () => {
    if (selectedTemplate && onEdit) {
      onEdit(selectedTemplate);
    }
    handleMenuClose();
  };

  const handleDelete = () => {
    if (selectedTemplate && onDelete) {
      onDelete(selectedTemplate.id);
    }
    handleMenuClose();
  };

  const handlePreview = () => {
    setPreviewDialogOpen(true);
    handleMenuClose();
  };

  const getCategoryColor = (category) => {
    const colors = {
      'faq': 'primary',
      'tutorials': 'secondary',
      'api_documentation': 'info',
      'user_guides': 'success',
      'troubleshooting': 'warning',
      'announcements': 'error'
    };
    return colors[category] || 'default';
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString();
  };

  if (templates.length === 0 && !loading) {
    return (
      <Card>
        <CardContent>
          <Alert severity="info">
            No article templates found. Click "Create Template" to create your first template.
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Box>
      <Grid container spacing={3}>
        {templates.map((template) => (
          <Grid item xs={12} sm={6} md={4} key={template.id}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Avatar sx={{ bgcolor: getCategoryColor(template.category) + '.main' }}>
                    <TemplateIcon />
                  </Avatar>
                  <IconButton
                    size="small"
                    onClick={(e) => handleMenuOpen(e, template)}
                  >
                    <MoreIcon />
                  </IconButton>
                </Box>
                
                <Typography variant="h6" component="h3" gutterBottom>
                  {template.name}
                </Typography>
                
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {template.description}
                </Typography>
                
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                  <Chip
                    label={template.category.replace('_', ' ')}
                    color={getCategoryColor(template.category)}
                    size="small"
                    variant="outlined"
                  />
                  <Chip
                    label={template.article_type.replace('_', ' ')}
                    size="small"
                    variant="outlined"
                  />
                  {!template.is_active && (
                    <Chip
                      label="Inactive"
                      color="error"
                      size="small"
                      variant="outlined"
                    />
                  )}
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 'auto' }}>
                  <Typography variant="caption" color="text.secondary">
                    Used {template.usage_count || 0} times
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {formatDate(template.updated_at)}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleEdit}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit Template</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={handlePreview}>
          <ListItemIcon>
            <ViewIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Preview Template</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Delete Template</ListItemText>
        </MenuItem>
      </Menu>

      {/* Template Preview Dialog */}
      <Dialog
        open={previewDialogOpen}
        onClose={() => setPreviewDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Template Preview: {selectedTemplate?.name}
        </DialogTitle>
        <DialogContent dividers>
          {selectedTemplate && (
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Description:
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {selectedTemplate.description}
              </Typography>

              <Typography variant="subtitle2" gutterBottom>
                Category: {selectedTemplate.category.replace('_', ' ')}
              </Typography>
              <Typography variant="subtitle2" gutterBottom>
                Type: {selectedTemplate.article_type.replace('_', ' ')}
              </Typography>

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle2" gutterBottom>
                Template Content:
              </Typography>
              <Paper
                variant="outlined"
                sx={{
                  p: 2,
                  bgcolor: 'grey.50',
                  fontFamily: 'monospace',
                  whiteSpace: 'pre-wrap',
                  maxHeight: 400,
                  overflow: 'auto'
                }}
              >
                {selectedTemplate.template_content}
              </Paper>

              {selectedTemplate.variables && selectedTemplate.variables.length > 0 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Available Variables:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {selectedTemplate.variables.map((variable) => (
                      <Chip
                        key={variable}
                        label={`{{${variable}}}`}
                        size="small"
                        variant="outlined"
                      />
                    ))}
                  </Box>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialogOpen(false)}>
            Close
          </Button>
          {selectedTemplate && onEdit && (
            <Button
              variant="contained"
              onClick={() => {
                setPreviewDialogOpen(false);
                onEdit(selectedTemplate);
              }}
            >
              Edit Template
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default KnowledgeBaseTemplateList;
