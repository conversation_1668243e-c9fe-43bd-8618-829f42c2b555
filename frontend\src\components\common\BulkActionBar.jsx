/**
 * @fileoverview BulkActionBar - Enterprise-grade bulk action component
 * @version 1.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import React, { memo, forwardRef, useImperativeHandle, useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Toolbar,
  Typography,
  IconButton,
  Button,
  Chip,
  Tooltip,
  Fade,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Close as CloseIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Share as ShareIcon,
  Download as DownloadIcon,
  Archive as ArchiveIcon,
  Visibility as ViewIcon,
  MoreVert as MoreIcon
} from '@mui/icons-material';

/**
 * BulkActionBar component for handling bulk operations on selected items
 */
const BulkActionBar = memo(forwardRef(({
  selectedItems = [],
  onClearSelection,
  onDelete,
  onEdit,
  onShare,
  onDownload,
  onArchive,
  onView,
  actions = [],
  position = 'fixed',
  elevation = 4,
  color = 'primary',
  showItemCount = true,
  maxWidth = 'lg',
  className = '',
  ...props
}, ref) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [isVisible, setIsVisible] = useState(selectedItems.length > 0);

  // Update visibility when selectedItems change
  React.useEffect(() => {
    setIsVisible(selectedItems.length > 0);
  }, [selectedItems.length]);

  /**
   * Handle clear selection
   */
  const handleClearSelection = useCallback(() => {
    if (onClearSelection) {
      onClearSelection();
    }
    setIsVisible(false);
  }, [onClearSelection]);

  /**
   * Handle bulk action
   */
  const handleBulkAction = useCallback((action, handler) => {
    if (handler && selectedItems.length > 0) {
      handler(selectedItems);
    }
  }, [selectedItems]);

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    clearSelection: handleClearSelection,
    getSelectedItems: () => selectedItems,
    isVisible: () => isVisible
  }), [handleClearSelection, selectedItems, isVisible]);

  // Default actions
  const defaultActions = [
    {
      id: 'view',
      label: 'View',
      icon: ViewIcon,
      handler: onView,
      color: 'primary'
    },
    {
      id: 'edit',
      label: 'Edit',
      icon: EditIcon,
      handler: onEdit,
      color: 'primary'
    },
    {
      id: 'share',
      label: 'Share',
      icon: ShareIcon,
      handler: onShare,
      color: 'primary'
    },
    {
      id: 'download',
      label: 'Download',
      icon: DownloadIcon,
      handler: onDownload,
      color: 'primary'
    },
    {
      id: 'archive',
      label: 'Archive',
      icon: ArchiveIcon,
      handler: onArchive,
      color: 'warning'
    },
    {
      id: 'delete',
      label: 'Delete',
      icon: DeleteIcon,
      handler: onDelete,
      color: 'error'
    }
  ];

  // Combine default and custom actions
  const allActions = [...defaultActions, ...actions].filter(action => action.handler);

  if (!isVisible || selectedItems.length === 0) {
    return null;
  }

  return (
    <Fade in={isVisible} timeout={300}>
      <Box
        sx={{
          position: position,
          bottom: position === 'fixed' ? 16 : 'auto',
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: theme.zIndex.snackbar,
          maxWidth: theme.breakpoints.values[maxWidth],
          width: '100%',
          px: 2
        }}
        className={className}
        {...props}
      >
        <Toolbar
          sx={{
            backgroundColor: theme.palette.background.paper,
            borderRadius: 2,
            boxShadow: theme.shadows[elevation],
            border: `1px solid ${theme.palette.divider}`,
            minHeight: { xs: 56, sm: 64 },
            px: { xs: 1, sm: 2 }
          }}
        >
          {/* Selection info */}
          <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
            {showItemCount && (
              <Chip
                label={`${selectedItems.length} selected`}
                color={color}
                size="small"
                sx={{ mr: 2 }}
              />
            )}
            
            {!isMobile && (
              <Typography variant="body2" color="text.secondary">
                {selectedItems.length === 1 
                  ? '1 item selected' 
                  : `${selectedItems.length} items selected`
                }
              </Typography>
            )}
          </Box>

          {/* Actions */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {allActions.slice(0, isMobile ? 2 : 4).map((action) => (
              <Tooltip key={action.id} title={action.label}>
                <IconButton
                  size="small"
                  color={action.color || 'primary'}
                  onClick={() => handleBulkAction(action.id, action.handler)}
                  aria-label={`${action.label} ${selectedItems.length} items`}
                >
                  <action.icon fontSize="small" />
                </IconButton>
              </Tooltip>
            ))}

            {/* More actions for mobile */}
            {isMobile && allActions.length > 2 && (
              <Tooltip title="More actions">
                <IconButton size="small">
                  <MoreIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}

            {/* Clear selection */}
            <Tooltip title="Clear selection">
              <IconButton
                size="small"
                onClick={handleClearSelection}
                aria-label="Clear selection"
                sx={{ ml: 1 }}
              >
                <CloseIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        </Toolbar>
      </Box>
    </Fade>
  );
}));

BulkActionBar.displayName = 'BulkActionBar';

BulkActionBar.propTypes = {
  /** Array of selected items */
  selectedItems: PropTypes.array,
  /** Callback when selection is cleared */
  onClearSelection: PropTypes.func,
  /** Callback for delete action */
  onDelete: PropTypes.func,
  /** Callback for edit action */
  onEdit: PropTypes.func,
  /** Callback for share action */
  onShare: PropTypes.func,
  /** Callback for download action */
  onDownload: PropTypes.func,
  /** Callback for archive action */
  onArchive: PropTypes.func,
  /** Callback for view action */
  onView: PropTypes.func,
  /** Custom actions array */
  actions: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string.isRequired,
    label: PropTypes.string.isRequired,
    icon: PropTypes.elementType.isRequired,
    handler: PropTypes.func.isRequired,
    color: PropTypes.string
  })),
  /** Position of the action bar */
  position: PropTypes.oneOf(['fixed', 'relative', 'absolute']),
  /** Material-UI elevation */
  elevation: PropTypes.number,
  /** Color theme */
  color: PropTypes.string,
  /** Show item count chip */
  showItemCount: PropTypes.bool,
  /** Maximum width constraint */
  maxWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),
  /** Additional CSS class */
  className: PropTypes.string
};

export default BulkActionBar;
