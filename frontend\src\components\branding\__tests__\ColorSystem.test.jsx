// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import ColorSystem from '../ColorSystem';

// Mock the notification hook
const mockShowSuccessNotification = vi.fn();
const mockShowErrorNotification = vi.fn();

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showSuccessNotification: mockShowSuccessNotification,
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Mock the accessibility validation utility
vi.mock('../../../utils/accessibilityValidation', () => ({
  validateColorContrast: vi.fn((textColor, backgroundColor) => ({
    ratio: 4.5,
    level: 'AA',
    passesAA: true,
    passesAAA: false
  }))
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn().mockResolvedValue(undefined),
  },
});

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#1976d2',
      },
      secondary: {
        main: '#dc004e',
      },
      error: {
        main: '#d32f2f',
      },
      warning: {
        main: '#ed6c02',
      },
      success: {
        main: '#2e7d32',
      },
      grey: {
        300: '#e0e0e0',
        400: '#bdbdbd',
        500: '#9e9e9e',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('ColorSystem', () => {
  const mockColorSystem = {
    primary: '#4E40C5',
    secondary: '#00E4BC',
    accent: '#FF5733',
    background: '#F8F9FA',
    text: '#333333',
    text_secondary: '#666666',
    surface: '#FFFFFF',
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#F44336',
    info: '#2196F3',
    relationship: 'complementary',
    gradients: [
      {
        id: '1',
        name: 'Primary Gradient',
        colors: ['#4E40C5', '#00E4BC'],
        angle: 45,
        created: '2023-01-01T00:00:00Z'
      }
    ]
  };

  const mockProps = {
    colorSystem: mockColorSystem,
    onChange: vi.fn(),
    onError: vi.fn(),
    onExport: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders color system correctly', () => {
    render(
      <TestWrapper>
        <ColorSystem {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Advanced Color System')).toBeInTheDocument();
    expect(screen.getByText('Define your brand\'s comprehensive color system with semantic tokens, gradients, and accessibility validation')).toBeInTheDocument();
  });

  test('shows loading skeleton when no color system provided', () => {
    render(
      <TestWrapper>
        <ColorSystem colorSystem={null} onChange={vi.fn()} />
      </TestWrapper>
    );

    // Should show skeleton loaders
    const skeletons = screen.getAllByTestId(/skeleton/i);
    expect(skeletons.length).toBeGreaterThan(0);
  });

  test('displays accessibility and consistency scores', () => {
    render(
      <TestWrapper>
        <ColorSystem {...mockProps} showAccessibility={true} />
      </TestWrapper>
    );

    expect(screen.getByText('Consistency Score')).toBeInTheDocument();
    expect(screen.getByText('Accessibility Score')).toBeInTheDocument();
    expect(screen.getByText('Tokens Defined')).toBeInTheDocument();
    expect(screen.getByText('Gradients')).toBeInTheDocument();
  });

  test('handles semantic token color change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorSystem {...mockProps} />
      </TestWrapper>
    );

    // Switch to color tokens tab (should be default)
    const primaryColorInput = screen.getByDisplayValue('#4E40C5');
    await user.clear(primaryColorInput);
    await user.type(primaryColorInput, '#FF0000');

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith(
        expect.objectContaining({
          primary: '#FF0000'
        })
      );
    });
  });

  test('handles color relationship change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorSystem {...mockProps} />
      </TestWrapper>
    );

    // Find and click the relationship select
    const relationshipSelect = screen.getByRole('combobox', { name: /color relationship/i });
    await user.click(relationshipSelect);
    
    const analogousOption = screen.getByText('Analogous');
    await user.click(analogousOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith(
        expect.objectContaining({
          relationship: 'analogous'
        })
      );
    });
  });

  test('handles gradient creation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorSystem {...mockProps} />
      </TestWrapper>
    );

    // Switch to gradients tab
    const gradientsTab = screen.getByText('Gradients');
    await user.click(gradientsTab);

    // Fill gradient form
    const nameInput = screen.getByLabelText(/gradient name/i);
    await user.type(nameInput, 'Test Gradient');

    const angleInput = screen.getByLabelText(/angle/i);
    await user.clear(angleInput);
    await user.type(angleInput, '90');

    // Add gradient
    const addButton = screen.getByRole('button', { name: /add gradient/i });
    await user.click(addButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith(
        expect.objectContaining({
          gradients: expect.arrayContaining([
            expect.objectContaining({
              name: 'Test Gradient',
              angle: 90
            })
          ])
        })
      );
    });
  });

  test('handles gradient removal', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorSystem {...mockProps} />
      </TestWrapper>
    );

    // Switch to gradients tab
    const gradientsTab = screen.getByText('Gradients');
    await user.click(gradientsTab);

    // Find and click delete button for existing gradient
    const deleteButtons = screen.getAllByTestId('DeleteIcon');
    if (deleteButtons.length > 0) {
      await user.click(deleteButtons[0]);

      await waitFor(() => {
        expect(mockProps.onChange).toHaveBeenCalledWith(
          expect.objectContaining({
            gradients: []
          })
        );
      });
    }
  });

  test('handles color picker interaction', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorSystem {...mockProps} />
      </TestWrapper>
    );

    // Switch to color picker tab
    const colorPickerTab = screen.getByText('Color Picker');
    await user.click(colorPickerTab);

    expect(screen.getByText('Interactive Color Picker')).toBeInTheDocument();
    expect(screen.getByText('Quick Color Selection')).toBeInTheDocument();
  });

  test('handles copy color functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorSystem {...mockProps} />
      </TestWrapper>
    );

    // Find and click a copy button
    const copyButtons = screen.getAllByLabelText(/copy color/i);
    if (copyButtons.length > 0) {
      await user.click(copyButtons[0]);

      await waitFor(() => {
        expect(navigator.clipboard.writeText).toHaveBeenCalled();
        expect(mockShowSuccessNotification).toHaveBeenCalledWith(
          expect.stringContaining('Copied')
        );
      });
    }
  });

  test('displays preview correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorSystem {...mockProps} />
      </TestWrapper>
    );

    // Switch to preview tab
    const previewTab = screen.getByText('Preview');
    await user.click(previewTab);

    expect(screen.getByText('Color System Preview')).toBeInTheDocument();
    expect(screen.getByText('Sample Heading')).toBeInTheDocument();
    expect(screen.getByText('Primary Button')).toBeInTheDocument();
  });

  test('handles template selection', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorSystem {...mockProps} showTemplates={true} />
      </TestWrapper>
    );

    // Click template button
    const templateButton = screen.getByLabelText('Open design system templates');
    await user.click(templateButton);

    // Should open template dialog
    await waitFor(() => {
      expect(screen.getByText('Design System Templates')).toBeInTheDocument();
    });
  });

  test('handles export functionality', async () => {
    // Mock URL.createObjectURL and related APIs
    global.URL.createObjectURL = vi.fn(() => 'mock-url');
    global.URL.revokeObjectURL = vi.fn();
    
    const mockLink = {
      href: '',
      download: '',
      click: vi.fn(),
    };
    
    vi.spyOn(document, 'createElement').mockReturnValue(mockLink);
    vi.spyOn(document.body, 'appendChild').mockImplementation(() => {});
    vi.spyOn(document.body, 'removeChild').mockImplementation(() => {});

    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorSystem {...mockProps} />
      </TestWrapper>
    );

    // Click export button
    const exportButton = screen.getByLabelText('Export color system');
    await user.click(exportButton);

    // Should open export dialog
    await waitFor(() => {
      expect(screen.getByText('Export Color System')).toBeInTheDocument();
    });
  });

  test('handles disabled state correctly', () => {
    render(
      <TestWrapper>
        <ColorSystem {...mockProps} disabled />
      </TestWrapper>
    );

    const exportButton = screen.getByLabelText('Export color system');
    expect(exportButton).toBeDisabled();
  });

  test('handles read-only state correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorSystem {...mockProps} readOnly />
      </TestWrapper>
    );

    // Switch to gradients tab
    const gradientsTab = screen.getByText('Gradients');
    await user.click(gradientsTab);

    // Should not show add controls in read-only mode
    const addButton = screen.queryByRole('button', { name: /add gradient/i });
    expect(addButton).toBeDisabled();
  });

  test('validates color format correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ColorSystem {...mockProps} />
      </TestWrapper>
    );

    // Try to enter invalid color
    const primaryColorInput = screen.getByDisplayValue('#4E40C5');
    await user.clear(primaryColorInput);
    await user.type(primaryColorInput, 'invalid-color');

    await waitFor(() => {
      expect(mockShowErrorNotification).toHaveBeenCalledWith(
        expect.stringContaining('Color validation')
      );
    });
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <ColorSystem {...mockProps} />
      </TestWrapper>
    );

    // Check for proper ARIA labels
    expect(screen.getByLabelText('Export color system')).toBeInTheDocument();
    expect(screen.getByLabelText('Open design system templates')).toBeInTheDocument();

    // Check for proper tab navigation
    const tabs = screen.getAllByRole('tab');
    expect(tabs.length).toBeGreaterThan(0);
  });
});
