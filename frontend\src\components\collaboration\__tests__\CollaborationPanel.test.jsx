// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import CollaborationPanel from '../CollaborationPanel';

// Mock the collaboration hook
const mockCollaboration = {
  isConnected: true,
  isConnecting: false,
  activeUsers: [
    {
      id: 'user1',
      name: '<PERSON>',
      status: 'active',
      activity: 'editing',
      lastActive: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
      avatar: null
    },
    {
      id: 'user2',
      name: '<PERSON>',
      status: 'away',
      activity: 'viewing',
      lastActive: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
      avatar: 'https://example.com/avatar.jpg'
    }
  ],
  comments: [
    {
      id: 'comment1',
      userId: 'user1',
      comment: { text: 'This is a test comment' },
      timestamp: new Date(Date.now() - 1000 * 60 * 10), // 10 minutes ago
      replies: [
        {
          id: 'reply1',
          userId: 'user2',
          reply: { text: 'This is a reply' },
          timestamp: new Date(Date.now() - 1000 * 60 * 5) // 5 minutes ago
        }
      ]
    }
  ],
  lockedSections: {
    'section1': {
      userId: 'user1',
      expiresAt: new Date(Date.now() + 1000 * 60 * 30) // 30 minutes from now
    }
  },
  lastSyncTimestamp: new Date(Date.now() - 1000 * 60 * 2), // 2 minutes ago
  connect: vi.fn(),
  addComment: vi.fn(),
  addCommentReply: vi.fn(),
  unlockSection: vi.fn(),
  requestSync: vi.fn()
};

vi.mock('../../../hooks/useCollaboration', () => ({
  default: () => mockCollaboration,
}));

// Mock the auth context
const mockAuth = {
  user: {
    id: 'user1',
    name: 'John Doe'
  }
};

vi.mock('../../../contexts/AuthContext', () => ({
  useAuth: () => mockAuth,
}));

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
      success: {
        main: '#4CAF50',
      },
      warning: {
        main: '#FF9800',
      },
      error: {
        main: '#F44336',
      },
      grey: {
        400: '#BDBDBD',
        500: '#9E9E9E',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('CollaborationPanel', () => {
  const mockProps = {
    contentId: 'test-content-id'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders collaboration panel correctly', () => {
    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Users')).toBeInTheDocument();
    expect(screen.getByText('Comments')).toBeInTheDocument();
    expect(screen.getByText('Locks')).toBeInTheDocument();
    expect(screen.getByText('History')).toBeInTheDocument();
  });

  test('displays active users correctly', () => {
    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    // Should show user count
    expect(screen.getByText('Active Users (2)')).toBeInTheDocument();
    
    // Should show user names
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    
    // Should show "You" chip for current user
    expect(screen.getByText('You')).toBeInTheDocument();
  });

  test('displays user status and activity correctly', () => {
    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    // Should show user activities
    expect(screen.getByText('Editing')).toBeInTheDocument();
    expect(screen.getByText('Viewing')).toBeInTheDocument();
    
    // Should show relative timestamps
    expect(screen.getByText(/ago/)).toBeInTheDocument();
  });

  test('handles tab switching', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    // Switch to Comments tab
    const commentsTab = screen.getByRole('tab', { name: /comments/i });
    await user.click(commentsTab);

    // Should show comments content
    expect(screen.getByText('This is a test comment')).toBeInTheDocument();
  });

  test('displays comments correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    // Switch to Comments tab
    const commentsTab = screen.getByRole('tab', { name: /comments/i });
    await user.click(commentsTab);

    // Should show comment content
    expect(screen.getByText('This is a test comment')).toBeInTheDocument();
    expect(screen.getByText('This is a reply')).toBeInTheDocument();
  });

  test('handles comment submission', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    // Switch to Comments tab
    const commentsTab = screen.getByRole('tab', { name: /comments/i });
    await user.click(commentsTab);

    // Type in comment field
    const commentInput = screen.getByPlaceholderText('Add a comment...');
    await user.type(commentInput, 'New test comment');

    // Submit comment
    const submitButton = screen.getByRole('button', { name: /comment/i });
    await user.click(submitButton);

    expect(mockCollaboration.addComment).toHaveBeenCalledWith({
      text: 'New test comment',
      position: 'general',
      contentId: 'test-content-id'
    });
  });

  test('handles reply submission', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    // Switch to Comments tab
    const commentsTab = screen.getByRole('tab', { name: /comments/i });
    await user.click(commentsTab);

    // Click reply button
    const replyButton = screen.getByRole('button', { name: /reply/i });
    await user.click(replyButton);

    // Type in reply field
    const replyInput = screen.getByPlaceholderText('Write a reply...');
    await user.type(replyInput, 'New test reply');

    // Submit reply
    const sendButton = screen.getByRole('button', { name: /send/i });
    await user.click(sendButton);

    expect(mockCollaboration.addCommentReply).toHaveBeenCalledWith('comment1', {
      text: 'New test reply',
      contentId: 'test-content-id'
    });
  });

  test('displays locked sections', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    // Switch to Locks tab
    const locksTab = screen.getByRole('tab', { name: /locks/i });
    await user.click(locksTab);

    // Should show locked sections
    expect(screen.getByText('Locked Sections')).toBeInTheDocument();
    expect(screen.getByText('Section section1')).toBeInTheDocument();
    expect(screen.getByText('Locked by you')).toBeInTheDocument();
  });

  test('handles section unlocking', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    // Switch to Locks tab
    const locksTab = screen.getByRole('tab', { name: /locks/i });
    await user.click(locksTab);

    // Click unlock button
    const unlockButton = screen.getByRole('button', { name: /unlock/i });
    await user.click(unlockButton);

    expect(mockCollaboration.unlockSection).toHaveBeenCalledWith('section1');
  });

  test('displays collaboration history', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    // Switch to History tab
    const historyTab = screen.getByRole('tab', { name: /history/i });
    await user.click(historyTab);

    // Should show history content
    expect(screen.getByText('Collaboration History')).toBeInTheDocument();
    expect(screen.getByText(/Last synchronized/)).toBeInTheDocument();
  });

  test('handles sync request', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    // Switch to History tab
    const historyTab = screen.getByRole('tab', { name: /history/i });
    await user.click(historyTab);

    // Click sync button
    const syncButton = screen.getByRole('button', { name: /sync now/i });
    await user.click(syncButton);

    expect(mockCollaboration.requestSync).toHaveBeenCalled();
  });

  test('handles connection refresh', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    // Click refresh button
    const refreshButton = screen.getByLabelText('Refresh');
    await user.click(refreshButton);

    expect(mockCollaboration.connect).toHaveBeenCalled();
  });

  test('shows disconnection warning', () => {
    const disconnectedCollaboration = {
      ...mockCollaboration,
      isConnected: false
    };

    vi.mocked(require('../../../hooks/useCollaboration').default).mockReturnValue(disconnectedCollaboration);

    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Not connected to collaboration server')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /reconnect/i })).toBeInTheDocument();
  });

  test('shows empty states correctly', async () => {
    const user = userEvent.setup();
    
    const emptyCollaboration = {
      ...mockCollaboration,
      activeUsers: [],
      comments: [],
      lockedSections: {}
    };

    vi.mocked(require('../../../hooks/useCollaboration').default).mockReturnValue(emptyCollaboration);

    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    // Users tab empty state
    expect(screen.getByText('No active users')).toBeInTheDocument();

    // Comments tab empty state
    const commentsTab = screen.getByRole('tab', { name: /comments/i });
    await user.click(commentsTab);
    expect(screen.getByText('No comments yet')).toBeInTheDocument();

    // Locks tab empty state
    const locksTab = screen.getByRole('tab', { name: /locks/i });
    await user.click(locksTab);
    expect(screen.getByText('No locked sections')).toBeInTheDocument();
  });

  test('displays comment count badge', () => {
    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    // Should show comment count in badge
    expect(screen.getByText('1')).toBeInTheDocument(); // Badge content
  });

  test('handles reply form toggle', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    // Switch to Comments tab
    const commentsTab = screen.getByRole('tab', { name: /comments/i });
    await user.click(commentsTab);

    // Click reply button to show form
    const replyButton = screen.getByRole('button', { name: /reply/i });
    await user.click(replyButton);

    // Should show reply form
    expect(screen.getByPlaceholderText('Write a reply...')).toBeInTheDocument();

    // Click cancel to hide form
    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    await user.click(cancelButton);

    // Should hide reply form
    expect(screen.queryByPlaceholderText('Write a reply...')).not.toBeInTheDocument();
  });

  test('disables comment submission when disconnected', async () => {
    const disconnectedCollaboration = {
      ...mockCollaboration,
      isConnected: false
    };

    vi.mocked(require('../../../hooks/useCollaboration').default).mockReturnValue(disconnectedCollaboration);

    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    // Switch to Comments tab
    const commentsTab = screen.getByRole('tab', { name: /comments/i });
    await user.click(commentsTab);

    // Comment input should be disabled
    const commentInput = screen.getByPlaceholderText('Add a comment...');
    expect(commentInput).toBeDisabled();

    // Submit button should be disabled
    const submitButton = screen.getByRole('button', { name: /comment/i });
    expect(submitButton).toBeDisabled();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    // Check for proper ARIA labels and roles
    expect(screen.getByRole('tablist', { name: /collaboration tabs/i })).toBeInTheDocument();
    expect(screen.getAllByRole('tab')).toHaveLength(4);
    expect(screen.getByRole('list')).toBeInTheDocument();
    
    // Check for proper button labels
    expect(screen.getByLabelText('Refresh')).toBeInTheDocument();
  });

  test('displays user avatars correctly', () => {
    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    // Should show user avatars
    const avatars = screen.getAllByRole('img');
    expect(avatars.length).toBeGreaterThan(0);
    
    // Should show user initials for users without avatars
    expect(screen.getByText('J')).toBeInTheDocument(); // John Doe initial
  });

  test('formats timestamps correctly', () => {
    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    // Should show relative timestamps
    expect(screen.getByText(/ago/)).toBeInTheDocument();
  });

  test('handles user status colors correctly', () => {
    render(
      <TestWrapper>
        <CollaborationPanel {...mockProps} />
      </TestWrapper>
    );

    // Should render user status indicators
    const statusIndicators = screen.getAllByRole('listitem');
    expect(statusIndicators.length).toBeGreaterThan(0);
  });
});
