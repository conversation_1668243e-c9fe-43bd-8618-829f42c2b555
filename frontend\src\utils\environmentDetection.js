/**
 * Environment Detection Utilities
 * Detects deployment environment and platform capabilities
 @since 2024-1-1 to 2025-25-7
*/

/**
 * Check if the current environment supports WebSockets
 * @returns {boolean} True if WebSockets are supported
 */
export const isWebSocketSupported = () => {
  try {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      return false;
    }

    // Check if WebSocket is available in the browser
    if (typeof WebSocket === 'undefined') {
      return false;
    }

    // Safely get hostname with fallback
    const hostname = window.location?.hostname || '';
  const serverlessPlatforms = [
    'vercel.app',
    'vercel.com',
    'netlify.app',
    'netlify.com',
    'railway.app',
    'render.com',
    'surge.sh',
    'github.io',
    'gitlab.io',
    'firebase.app',
    'firebaseapp.com'
  ];

  const isServerless = serverlessPlatforms.some(platform => 
    hostname.includes(platform)
  );

    if (isServerless) {
      console.log(`[EnvironmentDetection] Detected serverless platform: ${hostname}`);
      return false;
    }

    return true;
  } catch (error) {
    console.warn('[EnvironmentDetection] Error checking WebSocket support:', error);
    // Default to false (fallback mode) if there's an error
    return false;
  }
};

/**
 * Get the deployment environment
 * @returns {string} The deployment environment
 */
export const getDeploymentEnvironment = () => {
  try {
    if (typeof window === 'undefined') {
      return 'development';
    }

    const hostname = window.location?.hostname || 'localhost';
  
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return 'development';
  }
  
  if (hostname.includes('staging') || hostname.includes('dev')) {
    return 'staging';
  }
  
    if (hostname.includes('vercel.app') || hostname.includes('netlify.app')) {
      return 'preview';
    }

    return 'production';
  } catch (error) {
    console.warn('[EnvironmentDetection] Error getting deployment environment:', error);
    return 'development';
  }
};

/**
 * Check if we should use fallback mode for real-time features
 * @returns {boolean} True if fallback mode should be used
 */
export const shouldUseFallbackMode = () => {
  return !isWebSocketSupported();
};

/**
 * Get platform-specific configuration
 * @returns {Object} Configuration object
 */
export const getPlatformConfig = () => {
  const environment = getDeploymentEnvironment();
  const supportsWebSocket = isWebSocketSupported();
  
  return {
    environment,
    supportsWebSocket,
    useFallbackMode: !supportsWebSocket,
    pollInterval: supportsWebSocket ? 30000 : 5000, // More frequent polling if no WebSocket
    enableRealTimeUpdates: supportsWebSocket,
    enableMetrics: environment === 'production',
    enableDebugLogging: environment === 'development'
  };
};

/**
 * Log environment information
 */
export const logEnvironmentInfo = () => {
  try {
    if (typeof window === 'undefined') {
      console.log('[EnvironmentDetection] Running in non-browser environment');
      return;
    }

    const config = getPlatformConfig();
    console.log('[EnvironmentDetection] Platform configuration:', {
      hostname: window.location?.hostname || 'unknown',
      environment: config.environment,
      webSocketSupport: config.supportsWebSocket,
      fallbackMode: config.useFallbackMode,
      pollInterval: config.pollInterval
    });
  } catch (error) {
    console.warn('[EnvironmentDetection] Error logging environment info:', error);
  }
};
