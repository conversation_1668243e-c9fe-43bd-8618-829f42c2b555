// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { Avatar } from '@mui/material';
import '@testing-library/jest-dom';
import CustomCardHeader from '../CustomCardHeader';

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
        contrastText: '#FFFFFF',
      },
      error: {
        main: '#F44336',
      },
      success: {
        main: '#4CAF50',
      },
      warning: {
        main: '#FF9800',
      },
      info: {
        main: '#2196F3',
      },
      text: {
        secondary: '#666666',
      },
      grey: {
        500: '#9E9E9E',
      },
    },
    spacing: (factor) => `${8 * factor}px`,
    shadows: [
      'none',
      '0px 2px 4px rgba(0,0,0,0.1)',
      '0px 4px 8px rgba(0,0,0,0.15)',
    ],
    transitions: {
      create: () => 'all 0.3s ease',
      duration: {
        short: 250,
      },
    },
    breakpoints: {
      down: (key) => `@media (max-width:${key === 'sm' ? 600 : 960}px)`,
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('CustomCardHeader', () => {
  const mockProps = {
    title: 'Test Header',
    subheader: 'Test Subheader'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders header with title and subheader correctly', () => {
    render(
      <TestWrapper>
        <CustomCardHeader {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Test Header')).toBeInTheDocument();
    expect(screen.getByText('Test Subheader')).toBeInTheDocument();
    expect(screen.getByRole('banner')).toBeInTheDocument();
  });

  test('applies different variants correctly', () => {
    const { rerender } = render(
      <TestWrapper>
        <CustomCardHeader {...mockProps} variant="minimal" />
      </TestWrapper>
    );

    let header = screen.getByRole('banner');
    expect(header).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <CustomCardHeader {...mockProps} variant="prominent" />
      </TestWrapper>
    );

    header = screen.getByRole('banner');
    expect(header).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <CustomCardHeader {...mockProps} variant="compact" />
      </TestWrapper>
    );

    header = screen.getByRole('banner');
    expect(header).toBeInTheDocument();
  });

  test('shows divider when enabled', () => {
    render(
      <TestWrapper>
        <CustomCardHeader {...mockProps} divider={true} />
      </TestWrapper>
    );

    const divider = document.querySelector('hr');
    expect(divider).toBeInTheDocument();
  });

  test('hides divider when disabled', () => {
    render(
      <TestWrapper>
        <CustomCardHeader {...mockProps} divider={false} />
      </TestWrapper>
    );

    const divider = document.querySelector('hr');
    expect(divider).not.toBeInTheDocument();
  });

  test('renders avatar correctly', () => {
    const avatar = <Avatar>JD</Avatar>;
    
    render(
      <TestWrapper>
        <CustomCardHeader {...mockProps} avatar={avatar} />
      </TestWrapper>
    );

    expect(screen.getByText('JD')).toBeInTheDocument();
  });

  test('handles avatar click', async () => {
    const user = userEvent.setup();
    const onAvatarClick = vi.fn();
    const avatar = <Avatar>JD</Avatar>;
    
    render(
      <TestWrapper>
        <CustomCardHeader 
          {...mockProps} 
          avatar={avatar}
          onAvatarClick={onAvatarClick}
        />
      </TestWrapper>
    );

    const avatarElement = screen.getByText('JD');
    await user.click(avatarElement);

    expect(onAvatarClick).toHaveBeenCalled();
  });

  test('handles title click', async () => {
    const user = userEvent.setup();
    const onTitleClick = vi.fn();
    
    render(
      <TestWrapper>
        <CustomCardHeader 
          {...mockProps} 
          onTitleClick={onTitleClick}
        />
      </TestWrapper>
    );

    const titleElement = screen.getByText('Test Header');
    await user.click(titleElement);

    expect(onTitleClick).toHaveBeenCalled();
  });

  test('handles header click when interactive', async () => {
    const user = userEvent.setup();
    const onHeaderClick = vi.fn();
    
    render(
      <TestWrapper>
        <CustomCardHeader 
          {...mockProps} 
          interactive={true}
          onHeaderClick={onHeaderClick}
        />
      </TestWrapper>
    );

    const headerElement = screen.getByRole('banner');
    await user.click(headerElement);

    expect(onHeaderClick).toHaveBeenCalled();
  });

  test('shows loading state correctly', () => {
    render(
      <TestWrapper>
        <CustomCardHeader loading={true} />
      </TestWrapper>
    );

    // Should show skeleton loading
    expect(screen.getAllByTestId('skeleton')).toHaveLength(3);
  });

  test('shows error state correctly', () => {
    const error = 'Test error message';
    
    render(
      <TestWrapper>
        <CustomCardHeader error={error} />
      </TestWrapper>
    );

    expect(screen.getByText('Header Error')).toBeInTheDocument();
    expect(screen.getByText(error)).toBeInTheDocument();
  });

  test('renders breadcrumbs correctly', () => {
    const breadcrumbs = [
      { label: 'Home', href: '/' },
      { label: 'Category', href: '/category' },
      { label: 'Current Page' }
    ];

    render(
      <TestWrapper>
        <CustomCardHeader {...mockProps} breadcrumbs={breadcrumbs} />
      </TestWrapper>
    );

    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Category')).toBeInTheDocument();
    expect(screen.getByText('Current Page')).toBeInTheDocument();
  });

  test('renders metadata chips correctly', () => {
    const metadata = [
      { label: 'Published', color: 'success' },
      { label: 'Featured', color: 'primary' }
    ];

    render(
      <TestWrapper>
        <CustomCardHeader {...mockProps} metadata={metadata} />
      </TestWrapper>
    );

    expect(screen.getByText('Published')).toBeInTheDocument();
    expect(screen.getByText('Featured')).toBeInTheDocument();
  });

  test('renders badges correctly', () => {
    const badges = [
      { content: 5, color: 'primary', tooltip: 'Notifications' },
      { content: 'NEW', color: 'error', tooltip: 'New items' }
    ];

    render(
      <TestWrapper>
        <CustomCardHeader {...mockProps} badges={badges} />
      </TestWrapper>
    );

    expect(screen.getByText('5')).toBeInTheDocument();
    expect(screen.getByText('NEW')).toBeInTheDocument();
  });

  test('shows status indicator and text', () => {
    render(
      <TestWrapper>
        <CustomCardHeader 
          {...mockProps} 
          status="success"
          statusText="Active"
        />
      </TestWrapper>
    );

    expect(screen.getByText('Active')).toBeInTheDocument();
  });

  test('handles overflow menu', async () => {
    const user = userEvent.setup();
    const overflowActions = [
      { label: 'Edit', onClick: vi.fn() },
      { label: 'Delete', onClick: vi.fn() }
    ];

    render(
      <TestWrapper>
        <CustomCardHeader 
          {...mockProps} 
          showOverflowMenu={true}
          overflowActions={overflowActions}
        />
      </TestWrapper>
    );

    const overflowButton = screen.getByLabelText('More options');
    await user.click(overflowButton);

    expect(screen.getByText('Edit')).toBeInTheDocument();
    expect(screen.getByText('Delete')).toBeInTheDocument();
  });

  test('handles overflow action click', async () => {
    const user = userEvent.setup();
    const editAction = vi.fn();
    const overflowActions = [
      { label: 'Edit', onClick: editAction }
    ];

    render(
      <TestWrapper>
        <CustomCardHeader 
          {...mockProps} 
          showOverflowMenu={true}
          overflowActions={overflowActions}
        />
      </TestWrapper>
    );

    const overflowButton = screen.getByLabelText('More options');
    await user.click(overflowButton);

    const editMenuItem = screen.getByText('Edit');
    await user.click(editMenuItem);

    expect(editAction).toHaveBeenCalled();
  });

  test('truncates long title text', () => {
    const longTitle = 'This is a very long title that should be truncated';
    
    render(
      <TestWrapper>
        <CustomCardHeader 
          title={longTitle}
          maxTitleLength={20}
        />
      </TestWrapper>
    );

    expect(screen.getByText('This is a very long...')).toBeInTheDocument();
  });

  test('truncates long subheader text', () => {
    const longSubheader = 'This is a very long subheader that should be truncated';
    
    render(
      <TestWrapper>
        <CustomCardHeader 
          {...mockProps}
          subheader={longSubheader}
          maxSubheaderLength={20}
        />
      </TestWrapper>
    );

    expect(screen.getByText('This is a very long...')).toBeInTheDocument();
  });

  test('handles analytics tracking', async () => {
    const user = userEvent.setup();
    const onAnalytics = vi.fn();
    
    render(
      <TestWrapper>
        <CustomCardHeader 
          {...mockProps} 
          interactive={true}
          onHeaderClick={vi.fn()}
          enableAnalytics={true}
          onAnalytics={onAnalytics}
        />
      </TestWrapper>
    );

    const headerElement = screen.getByRole('banner');
    await user.click(headerElement);

    expect(onAnalytics).toHaveBeenCalledWith(
      expect.objectContaining({
        component: 'CustomCardHeader',
        action: 'header_click'
      })
    );
  });

  test('renders custom action elements', () => {
    const action = <button>Custom Action</button>;
    
    render(
      <TestWrapper>
        <CustomCardHeader {...mockProps} action={action} />
      </TestWrapper>
    );

    expect(screen.getByText('Custom Action')).toBeInTheDocument();
  });

  test('applies elevated styling', () => {
    render(
      <TestWrapper>
        <CustomCardHeader {...mockProps} elevated={true} />
      </TestWrapper>
    );

    const header = screen.getByRole('banner');
    expect(header).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <CustomCardHeader 
          {...mockProps} 
          ariaLabel="Custom header"
          role="heading"
        />
      </TestWrapper>
    );

    const header = screen.getByRole('heading');
    expect(header).toHaveAttribute('aria-label', 'Custom header');
  });

  test('applies custom styles', () => {
    const customSx = { backgroundColor: 'red' };
    
    render(
      <TestWrapper>
        <CustomCardHeader {...mockProps} sx={customSx} />
      </TestWrapper>
    );

    const header = screen.getByRole('banner');
    expect(header).toBeInTheDocument();
  });

  test('handles avatar configuration', () => {
    const avatarConfig = {
      src: 'https://example.com/avatar.jpg',
      alt: 'User avatar',
      fallback: 'U'
    };

    render(
      <TestWrapper>
        <CustomCardHeader {...mockProps} avatarConfig={avatarConfig} />
      </TestWrapper>
    );

    const avatar = screen.getByRole('img');
    expect(avatar).toHaveAttribute('src', 'https://example.com/avatar.jpg');
    expect(avatar).toHaveAttribute('alt', 'User avatar');
  });

  test('handles avatar error with fallback', () => {
    const avatarConfig = {
      src: 'invalid-url',
      fallback: 'FB'
    };

    render(
      <TestWrapper>
        <CustomCardHeader {...mockProps} avatarConfig={avatarConfig} />
      </TestWrapper>
    );

    // Trigger error
    const avatar = screen.getByRole('img');
    fireEvent.error(avatar);

    expect(screen.getByText('FB')).toBeInTheDocument();
  });
});
