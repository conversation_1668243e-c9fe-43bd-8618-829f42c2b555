/**
 * Tests for ICPComparisonChart component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import ICPComparisonChart from '../ICPComparisonChart';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the hooks and API
vi.mock('../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showErrorNotification: vi.fn(),
    showSuccessNotification: vi.fn()
  }))
}));

vi.mock('../../api/icp-performance', () => ({
  compareICPs: vi.fn()
}));

// Mock D3
vi.mock('d3', () => ({
  select: vi.fn(() => ({
    selectAll: vi.fn(() => ({
      remove: vi.fn()
    })),
    append: vi.fn(() => ({
      attr: vi.fn(() => ({
        attr: vi.fn(() => ({
          append: vi.fn(() => ({
            attr: vi.fn(() => ({}))
          }))
        }))
      }))
    })),
    call: vi.fn(() => ({})),
    datum: vi.fn(() => ({
      attr: vi.fn(() => ({}))
    })),
    data: vi.fn(() => ({
      enter: vi.fn(() => ({
        append: vi.fn(() => ({
          attr: vi.fn(() => ({
            attr: vi.fn(() => ({
              attr: vi.fn(() => ({
                attr: vi.fn(() => ({
                  attr: vi.fn(() => ({}))
                }))
              }))
            }))
          })),
          text: vi.fn(() => ({})),
          style: vi.fn(() => ({}))
        }))
      }))
    }))
  })),
  scaleBand: vi.fn(() => ({
    domain: vi.fn(() => ({
      range: vi.fn(() => ({
        padding: vi.fn(() => ({
          bandwidth: vi.fn(() => 50)
        }))
      }))
    }))
  })),
  scaleLinear: vi.fn(() => ({
    domain: vi.fn(() => ({
      range: vi.fn(() => ({}))
    }))
  })),
  max: vi.fn(() => 100),
  axisBottom: vi.fn(() => ({})),
  axisLeft: vi.fn(() => ({}))
}));

describe('ICPComparisonChart', () => {
  const { compareICPs } = require('../../api/icp-performance');
  const mockNotification = {
    showErrorNotification: vi.fn(),
    showSuccessNotification: vi.fn()
  };

  const mockData = {
    best_performing_icp_id: 'icp1',
    best_performing_icp_name: 'Tech Startups',
    icps: [
      {
        icp_id: 'icp1',
        icp_name: 'Tech Startups',
        avg_engagement_rate: 7.2,
        avg_views: 15000,
        avg_likes: 850,
        avg_comments: 120,
        avg_shares: 45
      },
      {
        icp_id: 'icp2',
        icp_name: 'E-commerce Brands',
        avg_engagement_rate: 5.8,
        avg_views: 12000,
        avg_likes: 680,
        avg_comments: 95,
        avg_shares: 32
      },
      {
        icp_id: 'icp3',
        icp_name: 'Healthcare Providers',
        avg_engagement_rate: 4.5,
        avg_views: 8500,
        avg_likes: 420,
        avg_comments: 65,
        avg_shares: 18
      }
    ],
    performance_gap: {
      avg_engagement_rate: {
        average_gap: 1.8,
        max_gap: 2.7,
        min_gap: 1.3
      },
      avg_views: {
        average_gap: 3500,
        max_gap: 6500,
        min_gap: 1500
      }
    },
    common_patterns: {
      platforms: {
        'LinkedIn': {
          average_engagement: 6.5,
          best_icp: { icp_name: 'Tech Startups' },
          worst_icp: { icp_name: 'Healthcare Providers' }
        },
        'Twitter': {
          average_engagement: 4.8,
          best_icp: { icp_name: 'Tech Startups' },
          worst_icp: { icp_name: 'Healthcare Providers' }
        }
      }
    },
    significance_analysis: {
      'icp1': {
        is_significant: true,
        difference_percent: 2.4,
        t_statistic: 3.2
      },
      'icp2': {
        is_significant: false,
        difference_percent: 0.8,
        t_statistic: 1.1
      },
      'icp3': {
        is_significant: true,
        difference_percent: -1.7,
        t_statistic: -2.8
      }
    },
    recommendations: [
      'Focus more resources on Tech Startups ICP as it shows the highest engagement',
      'Analyze successful content patterns from Tech Startups and apply to other ICPs',
      'Consider adjusting content strategy for Healthcare Providers to improve performance'
    ]
  };

  beforeEach(() => {
    vi.clearAllMocks();
    compareICPs.mockResolvedValue(mockData);

    const { useNotification } = require('../../hooks/useNotification');
    useNotification.mockReturnValue(mockNotification);

    // Mock clientWidth for chart containers
    Object.defineProperty(HTMLElement.prototype, 'clientWidth', {
      configurable: true,
      value: 800
    });
  });

  test('renders ICP comparison chart dashboard', async () => {
    render(
      <TestWrapper>
        <ICPComparisonChart icpIds={['icp1', 'icp2', 'icp3']} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('ICP Performance Comparison')).toBeInTheDocument();
      expect(screen.getByText('Tech Startups is the best performing ICP with an average engagement rate of 7.20%')).toBeInTheDocument();
    });
  });

  test('displays comparison data correctly', async () => {
    render(
      <TestWrapper>
        <ICPComparisonChart icpIds={['icp1', 'icp2', 'icp3']} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Engagement Rate Comparison')).toBeInTheDocument();
      expect(screen.getByText('Performance Gap Analysis')).toBeInTheDocument();
      expect(screen.getByText('Platform Performance Across ICPs')).toBeInTheDocument();
      expect(screen.getByText('Statistical Significance Analysis')).toBeInTheDocument();
      expect(screen.getByText('AI-Powered Recommendations')).toBeInTheDocument();
    });
  });

  test('shows loading state initially', () => {
    render(
      <TestWrapper>
        <ICPComparisonChart icpIds={['icp1', 'icp2']} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('shows error when less than 2 ICPs provided', async () => {
    render(
      <TestWrapper>
        <ICPComparisonChart icpIds={['icp1']} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Please select at least two ICPs to compare')).toBeInTheDocument();
    });
  });

  test('handles API error gracefully', async () => {
    compareICPs.mockRejectedValue(new Error('API Error'));

    render(
      <TestWrapper>
        <ICPComparisonChart icpIds={['icp1', 'icp2']} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Failed to load comparison data. Please try again.')).toBeInTheDocument();
      expect(screen.getByText('Retry')).toBeInTheDocument();
    });
  });

  test('handles refresh functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ICPComparisonChart icpIds={['icp1', 'icp2', 'icp3']} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(compareICPs).toHaveBeenCalledWith(['icp1', 'icp2', 'icp3']);
    });

    // Clear previous calls
    vi.clearAllMocks();

    // Click refresh button
    const refreshButton = screen.getByText('Refresh');
    await user.click(refreshButton);

    await waitFor(() => {
      expect(compareICPs).toHaveBeenCalledWith(['icp1', 'icp2', 'icp3'], true);
      expect(mockNotification.showSuccessNotification).toHaveBeenCalledWith('Comparison data refreshed successfully');
    });
  });

  test('handles export functionality', async () => {
    const user = userEvent.setup();
    
    // Mock document.createElement and related methods
    const mockLink = {
      setAttribute: vi.fn(),
      click: vi.fn(),
      style: {}
    };
    const mockCreateElement = vi.spyOn(document, 'createElement').mockReturnValue(mockLink);
    const mockAppendChild = vi.spyOn(document.body, 'appendChild').mockImplementation(() => {});
    const mockRemoveChild = vi.spyOn(document.body, 'removeChild').mockImplementation(() => {});
    
    render(
      <TestWrapper>
        <ICPComparisonChart icpIds={['icp1', 'icp2', 'icp3']} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('ICP Performance Comparison')).toBeInTheDocument();
    });

    // Click export button
    const exportButton = screen.getByText('Export');
    await user.click(exportButton);

    expect(mockCreateElement).toHaveBeenCalledWith('a');
    expect(mockLink.click).toHaveBeenCalled();
    expect(mockNotification.showSuccessNotification).toHaveBeenCalledWith(
      'Generating ICP comparison report...'
    );

    // Cleanup mocks
    mockCreateElement.mockRestore();
    mockAppendChild.mockRestore();
    mockRemoveChild.mockRestore();
  });

  test('handles metric selection', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ICPComparisonChart icpIds={['icp1', 'icp2', 'icp3']} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('ICP Performance Comparison')).toBeInTheDocument();
    });

    // Change metric
    const metricSelect = screen.getByLabelText('Metric');
    await user.click(metricSelect);
    await user.click(screen.getByText('Views'));

    expect(screen.getByText('Views Comparison')).toBeInTheDocument();
  });

  test('displays performance gap analysis', async () => {
    render(
      <TestWrapper>
        <ICPComparisonChart icpIds={['icp1', 'icp2', 'icp3']} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Performance Gap Analysis')).toBeInTheDocument();
      expect(screen.getByText('Average Gap: 1.80%')).toBeInTheDocument();
      expect(screen.getByText('Max Gap: 2.70%')).toBeInTheDocument();
      expect(screen.getByText('Min Gap: 1.30%')).toBeInTheDocument();
    });
  });

  test('displays statistical significance analysis', async () => {
    render(
      <TestWrapper>
        <ICPComparisonChart icpIds={['icp1', 'icp2', 'icp3']} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Statistical Significance Analysis')).toBeInTheDocument();
      expect(screen.getByText('Tech Startups')).toBeInTheDocument();
      expect(screen.getByText('E-commerce Brands')).toBeInTheDocument();
      expect(screen.getByText('Healthcare Providers')).toBeInTheDocument();
    });
  });

  test('displays recommendations section', async () => {
    render(
      <TestWrapper>
        <ICPComparisonChart icpIds={['icp1', 'icp2', 'icp3']} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('AI-Powered Recommendations')).toBeInTheDocument();
      expect(screen.getByText('Focus more resources on Tech Startups ICP as it shows the highest engagement')).toBeInTheDocument();
      expect(screen.getByText('Analyze successful content patterns from Tech Startups and apply to other ICPs')).toBeInTheDocument();
    });
  });

  test('shows no recommendations message when none available', async () => {
    const dataWithoutRecommendations = {
      ...mockData,
      recommendations: []
    };
    compareICPs.mockResolvedValue(dataWithoutRecommendations);

    render(
      <TestWrapper>
        <ICPComparisonChart icpIds={['icp1', 'icp2']} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('No recommendations available.')).toBeInTheDocument();
    });
  });

  test('calls onDataLoaded prop when data is loaded', async () => {
    const mockOnDataLoaded = vi.fn();
    
    render(
      <TestWrapper>
        <ICPComparisonChart icpIds={['icp1', 'icp2']} onDataLoaded={mockOnDataLoaded} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockOnDataLoaded).toHaveBeenCalledWith(mockData);
    });
  });

  test('shows no data message when comparison data is null', async () => {
    compareICPs.mockResolvedValue(null);

    render(
      <TestWrapper>
        <ICPComparisonChart icpIds={['icp1', 'icp2']} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('No comparison data available. Please select ICPs to compare.')).toBeInTheDocument();
    });
  });

  test('handles retry functionality on error', async () => {
    const user = userEvent.setup();
    compareICPs.mockRejectedValueOnce(new Error('API Error'));

    render(
      <TestWrapper>
        <ICPComparisonChart icpIds={['icp1', 'icp2']} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Retry')).toBeInTheDocument();
    });

    // Clear the mock and make it succeed on retry
    vi.clearAllMocks();
    compareICPs.mockResolvedValue(mockData);

    // Click retry button
    const retryButton = screen.getByText('Retry');
    await user.click(retryButton);

    await waitFor(() => {
      expect(compareICPs).toHaveBeenCalledWith(['icp1', 'icp2'], true);
    });
  });

  test('disables buttons during loading', async () => {
    compareICPs.mockImplementation(() => new Promise(() => {})); // Never resolves

    render(
      <TestWrapper>
        <ICPComparisonChart icpIds={['icp1', 'icp2']} />
      </TestWrapper>
    );

    await waitFor(() => {
      const refreshButton = screen.getByText('Refreshing...');
      expect(refreshButton).toBeDisabled();
    });
  });

  test('disables export when no data available', () => {
    render(
      <TestWrapper>
        <ICPComparisonChart icpIds={['icp1']} />
      </TestWrapper>
    );

    const exportButton = screen.getByText('Export');
    expect(exportButton).toBeDisabled();
  });

  test('handles missing performance gap data gracefully', async () => {
    const incompleteData = {
      ...mockData,
      performance_gap: null
    };
    compareICPs.mockResolvedValue(incompleteData);

    render(
      <TestWrapper>
        <ICPComparisonChart icpIds={['icp1', 'icp2']} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('No performance gap data available.')).toBeInTheDocument();
    });
  });

  test('handles missing platform data gracefully', async () => {
    const incompleteData = {
      ...mockData,
      common_patterns: { platforms: {} }
    };
    compareICPs.mockResolvedValue(incompleteData);

    render(
      <TestWrapper>
        <ICPComparisonChart icpIds={['icp1', 'icp2']} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('ICP Performance Comparison')).toBeInTheDocument();
    });
  });

  test('handles missing significance analysis gracefully', async () => {
    const incompleteData = {
      ...mockData,
      significance_analysis: null
    };
    compareICPs.mockResolvedValue(incompleteData);

    render(
      <TestWrapper>
        <ICPComparisonChart icpIds={['icp1', 'icp2']} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('ICP Performance Comparison')).toBeInTheDocument();
      expect(screen.queryByText('Statistical Significance Analysis')).not.toBeInTheDocument();
    });
  });
});
