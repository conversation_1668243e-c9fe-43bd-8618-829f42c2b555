/**
 * Tests for SocialMediaMetrics component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import SocialMediaMetrics from '../SocialMediaMetrics';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock D3
vi.mock('d3', () => ({
  select: vi.fn(() => ({
    selectAll: vi.fn(() => ({
      remove: vi.fn()
    })),
    append: vi.fn(() => ({
      attr: vi.fn(() => ({
        attr: vi.fn(() => ({
          append: vi.fn(() => ({
            attr: vi.fn(() => ({}))
          }))
        }))
      }))
    })),
    call: vi.fn(() => ({})),
    datum: vi.fn(() => ({
      attr: vi.fn(() => ({}))
    })),
    data: vi.fn(() => ({
      enter: vi.fn(() => ({
        append: vi.fn(() => ({
          attr: vi.fn(() => ({
            attr: vi.fn(() => ({
              attr: vi.fn(() => ({
                attr: vi.fn(() => ({
                  attr: vi.fn(() => ({}))
                }))
              }))
            }))
          })),
          text: vi.fn(() => ({})),
          style: vi.fn(() => ({})),
          on: vi.fn(() => ({}))
        }))
      }))
    }))
  })),
  scaleLinear: vi.fn(() => ({
    domain: vi.fn(() => ({
      range: vi.fn(() => ({}))
    }))
  })),
  scaleTime: vi.fn(() => ({
    domain: vi.fn(() => ({
      range: vi.fn(() => ({}))
    }))
  })),
  scaleBand: vi.fn(() => ({
    domain: vi.fn(() => ({
      range: vi.fn(() => ({
        padding: vi.fn(() => ({
          bandwidth: vi.fn(() => 50)
        }))
      }))
    }))
  })),
  pie: vi.fn(() => ({
    value: vi.fn(() => ({}))
  })),
  arc: vi.fn(() => ({
    innerRadius: vi.fn(() => ({
      outerRadius: vi.fn(() => ({}))
    }))
  })),
  line: vi.fn(() => ({
    x: vi.fn(() => ({
      y: vi.fn(() => ({}))
    }))
  })),
  max: vi.fn(() => 100),
  extent: vi.fn(() => [new Date(), new Date()]),
  axisBottom: vi.fn(() => ({})),
  axisLeft: vi.fn(() => ({})),
  timeParse: vi.fn(() => ({})),
  timeFormat: vi.fn(() => ({})),
  schemeCategory10: ['#1f77b4', '#ff7f0e', '#2ca02c']
}));

describe('SocialMediaMetrics', () => {
  const mockData = {
    total_impressions: 150000,
    total_engagements: 12500,
    engagement_rate: 8.33,
    total_followers: 8500,
    time_series_data: [
      {
        date: '2023-01-01',
        impressions: 25000,
        engagements: 2000
      },
      {
        date: '2023-01-02',
        impressions: 28000,
        engagements: 2300
      },
      {
        date: '2023-01-03',
        impressions: 30000,
        engagements: 2500
      }
    ],
    platform_comparison: [
      {
        platform: 'LinkedIn',
        engagement_rate: 9.2
      },
      {
        platform: 'Twitter',
        engagement_rate: 7.8
      },
      {
        platform: 'Instagram',
        engagement_rate: 8.1
      }
    ],
    engagement_breakdown: [
      {
        type: 'Likes',
        value: 8500,
        percentage: 68
      },
      {
        type: 'Comments',
        value: 2000,
        percentage: 16
      },
      {
        type: 'Shares',
        value: 2000,
        percentage: 16
      }
    ]
  };

  const mockProps = {
    data: mockData,
    loading: false,
    onRefresh: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock document.createElement for export functionality
    global.document.createElement = vi.fn(() => ({
      setAttribute: vi.fn(),
      click: vi.fn(),
      style: {}
    }));

    global.document.body.appendChild = vi.fn();
    global.document.body.removeChild = vi.fn();

    // Mock clientWidth for D3 charts
    Object.defineProperty(HTMLElement.prototype, 'clientWidth', {
      configurable: true,
      value: 800
    });
  });

  test('renders social media metrics component', () => {
    render(
      <TestWrapper>
        <SocialMediaMetrics {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Social Media Metrics')).toBeInTheDocument();
    expect(screen.getByText('Overview')).toBeInTheDocument();
    expect(screen.getByText('Time Series')).toBeInTheDocument();
    expect(screen.getByText('Platform Comparison')).toBeInTheDocument();
    expect(screen.getByText('Engagement Breakdown')).toBeInTheDocument();
  });

  test('shows loading state correctly', () => {
    render(
      <TestWrapper>
        <SocialMediaMetrics {...mockProps} loading={true} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(screen.getByLabelText('Refresh analytics data')).toBeDisabled();
  });

  test('displays metrics cards when data is available', () => {
    render(
      <TestWrapper>
        <SocialMediaMetrics {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('150,000')).toBeInTheDocument(); // Total impressions
    expect(screen.getByText('12,500')).toBeInTheDocument(); // Total engagements
    expect(screen.getByText('8.33%')).toBeInTheDocument(); // Engagement rate
    expect(screen.getByText('8,500')).toBeInTheDocument(); // Total followers
  });

  test('handles tab navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SocialMediaMetrics {...mockProps} />
      </TestWrapper>
    );

    // Click on Time Series tab
    const timeSeriesTab = screen.getByText('Time Series');
    await user.click(timeSeriesTab);

    // Should show time series chart container
    expect(screen.getByText('Performance Over Time')).toBeInTheDocument();

    // Click on Platform Comparison tab
    const platformTab = screen.getByText('Platform Comparison');
    await user.click(platformTab);

    // Should show platform comparison chart
    expect(screen.getByText('Platform Performance')).toBeInTheDocument();
  });

  test('handles refresh functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SocialMediaMetrics {...mockProps} />
      </TestWrapper>
    );

    const refreshButton = screen.getByLabelText('Refresh analytics data');
    await user.click(refreshButton);

    expect(mockProps.onRefresh).toHaveBeenCalled();
  });

  test('handles export functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SocialMediaMetrics {...mockProps} />
      </TestWrapper>
    );

    const exportButton = screen.getByLabelText('Export analytics data');
    await user.click(exportButton);

    // Should create download link
    expect(global.document.createElement).toHaveBeenCalledWith('a');
  });

  test('disables export when no data available', () => {
    render(
      <TestWrapper>
        <SocialMediaMetrics {...mockProps} data={null} />
      </TestWrapper>
    );

    const exportButton = screen.getByLabelText('Export analytics data');
    expect(exportButton).toBeDisabled();
  });

  test('hides export button when showExport is false', () => {
    render(
      <TestWrapper>
        <SocialMediaMetrics {...mockProps} showExport={false} />
      </TestWrapper>
    );

    expect(screen.queryByLabelText('Export analytics data')).not.toBeInTheDocument();
  });

  test('shows error state when error prop is provided', () => {
    const errorMessage = 'Failed to load analytics data';
    
    render(
      <TestWrapper>
        <SocialMediaMetrics 
          {...mockProps} 
          error={errorMessage}
          loading={false}
        />
      </TestWrapper>
    );

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  test('shows no data state when data is null', () => {
    render(
      <TestWrapper>
        <SocialMediaMetrics 
          {...mockProps} 
          data={null}
          loading={false}
        />
      </TestWrapper>
    );

    expect(screen.getByText('No analytics data available. Please check your data source or try refreshing.')).toBeInTheDocument();
  });

  test('renders D3 charts when data is available', async () => {
    render(
      <TestWrapper>
        <SocialMediaMetrics {...mockProps} />
      </TestWrapper>
    );

    // Wait for charts to render
    await waitFor(() => {
      // D3 select should be called for chart rendering
      expect(require('d3').select).toHaveBeenCalled();
    });
  });

  test('handles missing time series data gracefully', () => {
    const dataWithoutTimeSeries = {
      ...mockData,
      time_series_data: null
    };

    render(
      <TestWrapper>
        <SocialMediaMetrics {...mockProps} data={dataWithoutTimeSeries} />
      </TestWrapper>
    );

    expect(screen.getByText('Social Media Metrics')).toBeInTheDocument();
    // Should not crash
  });

  test('handles missing platform comparison data gracefully', () => {
    const dataWithoutPlatforms = {
      ...mockData,
      platform_comparison: null
    };

    render(
      <TestWrapper>
        <SocialMediaMetrics {...mockProps} data={dataWithoutPlatforms} />
      </TestWrapper>
    );

    expect(screen.getByText('Social Media Metrics')).toBeInTheDocument();
    // Should not crash
  });

  test('handles missing engagement breakdown data gracefully', () => {
    const dataWithoutEngagement = {
      ...mockData,
      engagement_breakdown: null
    };

    render(
      <TestWrapper>
        <SocialMediaMetrics {...mockProps} data={dataWithoutEngagement} />
      </TestWrapper>
    );

    expect(screen.getByText('Social Media Metrics')).toBeInTheDocument();
    // Should not crash
  });

  test('applies correct accessibility attributes', () => {
    render(
      <TestWrapper>
        <SocialMediaMetrics {...mockProps} />
      </TestWrapper>
    );

    // Check ARIA labels
    expect(screen.getByLabelText('Export analytics data')).toBeInTheDocument();
    expect(screen.getByLabelText('Refresh analytics data')).toBeInTheDocument();

    // Check tooltips
    expect(screen.getByTitle('Export Analytics Data')).toBeInTheDocument();
    expect(screen.getByTitle('Refresh Data')).toBeInTheDocument();
  });

  test('shows loading spinner during export', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SocialMediaMetrics {...mockProps} />
      </TestWrapper>
    );

    const exportButton = screen.getByLabelText('Export analytics data');
    await user.click(exportButton);

    // Should briefly show loading spinner (this test might be flaky due to timing)
    // In a real scenario, you might want to mock the async behavior
  });

  test('formats numbers correctly in metrics cards', () => {
    render(
      <TestWrapper>
        <SocialMediaMetrics {...mockProps} />
      </TestWrapper>
    );

    // Should format large numbers with commas
    expect(screen.getByText('150,000')).toBeInTheDocument();
    expect(screen.getByText('12,500')).toBeInTheDocument();
    expect(screen.getByText('8,500')).toBeInTheDocument();
  });

  test('handles tab switching correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SocialMediaMetrics {...mockProps} />
      </TestWrapper>
    );

    // Start on Overview tab
    expect(screen.getByText('Total Impressions')).toBeInTheDocument();

    // Switch to Time Series tab
    await user.click(screen.getByText('Time Series'));
    expect(screen.getByText('Performance Over Time')).toBeInTheDocument();

    // Switch to Platform Comparison tab
    await user.click(screen.getByText('Platform Comparison'));
    expect(screen.getByText('Platform Performance')).toBeInTheDocument();

    // Switch to Engagement Breakdown tab
    await user.click(screen.getByText('Engagement Breakdown'));
    expect(screen.getByText('Engagement Distribution')).toBeInTheDocument();
  });
});
