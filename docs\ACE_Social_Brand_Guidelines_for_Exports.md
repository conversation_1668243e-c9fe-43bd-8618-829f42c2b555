# ACE Social Brand Guidelines for Exports

## Overview

This document establishes comprehensive brand guidelines for all exported reports and documents from the ACE Social platform. These guidelines ensure consistent brand representation across all export formats including PDF, CSV, Excel, and JSON reports.

## Brand Identity

### Primary Brand Colors

The ACE Social color palette must be consistently applied across all export formats:

```css
/* Primary Brand Colors */
--ace-purple: #4E40C5;      /* Primary brand color */
--ace-dark: #15110E;        /* Dark backgrounds and text */
--ace-yellow: #EBAE1B;      /* Accent and highlights */
--ace-white: #FFFFFF;       /* Pure white for contrast */

/* Extended Purple Palette */
--ace-purple-light: #6C4BFA;
--ace-purple-lighter: #8A72FF;
--ace-purple-lightest: #B19FFF;

/* Semantic Colors */
--success-color: #4CAF50;   /* Positive metrics, growth */
--warning-color: #FF9800;   /* Alerts, attention needed */
--error-color: #F44336;     /* Errors, negative metrics */
--info-color: #2196F3;      /* Information, neutral data */

/* Text Hierarchy */
--text-primary: #1A1A2E;    /* Main headings, important text */
--text-secondary: #4A4A68;  /* Body text, descriptions */
--text-muted: #AAAAAA;      /* Captions, metadata */

/* Background Colors */
--background-light: #F0F4FF; /* Light backgrounds */
--background-card: rgba(255, 255, 255, 0.85); /* Card backgrounds */
```

### Color Usage Guidelines

#### Primary Applications
- **ACE Purple (#4E40C5)**: Main headers, primary buttons, key metrics, brand elements
- **ACE Dark (#15110E)**: Primary text, data labels, important information
- **ACE Yellow (#EBAE1B)**: Accent elements, highlights, call-to-action secondary elements
- **ACE White (#FFFFFF)**: Backgrounds, contrast elements, clean spacing

#### Semantic Applications
- **Success Green (#4CAF50)**: Positive growth indicators, successful metrics, achievements
- **Warning Orange (#FF9800)**: Attention items, moderate alerts, review needed
- **Error Red (#F44336)**: Negative trends, critical alerts, failures
- **Info Blue (#2196F3)**: Neutral information, tips, general data

### Typography Standards

#### Font Family
All exports must use the Inter font family with appropriate fallbacks:

```css
font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
```

#### Typography Hierarchy

```css
/* Report Titles */
.report-title {
    font-size: 28px;
    font-weight: 700;
    letter-spacing: -0.5px;
    color: var(--ace-purple);
    line-height: 1.2;
}

/* Section Titles */
.section-title {
    font-size: 24px;
    font-weight: 600;
    letter-spacing: -0.5px;
    color: var(--text-primary);
    line-height: 1.3;
}

/* Subsection Titles */
.subsection-title {
    font-size: 20px;
    font-weight: 600;
    letter-spacing: -0.3px;
    color: var(--text-primary);
    line-height: 1.4;
}

/* Body Text */
.body-text {
    font-size: 16px;
    font-weight: 400;
    line-height: 1.7;
    color: var(--text-secondary);
}

/* Caption Text */
.caption-text {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-muted);
    line-height: 1.5;
}

/* Data Values */
.data-value {
    font-size: 32px;
    font-weight: 700;
    color: var(--ace-purple);
    line-height: 1.2;
}

/* Small Data Values */
.data-value-small {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.2;
}
```

### Logo Usage

#### Logo Specifications
- **Primary Logo**: Use the full ACE Social logo with text
- **Icon Only**: Use only when space is extremely limited
- **Minimum Size**: 120px width for digital, 1 inch for print
- **Clear Space**: Maintain clear space equal to the height of the "A" in ACE around the logo

#### Logo Variations
```css
/* Standard Logo */
.logo {
    max-width: 180px;
    height: auto;
    margin-bottom: 20px;
}

/* Small Logo (mobile/compact) */
.logo-small {
    max-width: 140px;
    height: auto;
}

/* Logo on Dark Backgrounds */
.logo-dark-bg {
    filter: brightness(0) invert(1);
}

/* Logo with Drop Shadow */
.logo-elevated {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}
```

## Export Format Guidelines

### PDF Reports

#### Page Layout
- **Margins**: 20mm top/bottom, 15mm left/right
- **Orientation**: Portrait (default), Landscape for wide tables
- **Font Size**: Minimum 9pt for body text, 11pt preferred
- **Line Height**: 1.4-1.6 for optimal readability

#### Header Structure
```html
<div class="pdf-header">
    <img src="logo.svg" alt="ACE Social" class="logo">
    <h1 class="report-title">{{report_title}}</h1>
    <p class="report-meta">Generated {{generated_at}} | {{user_name}}</p>
</div>
```

#### Footer Structure
```html
<div class="pdf-footer">
    <div class="footer-left">© ACE Social {{current_year}}</div>
    <div class="footer-center">{{report_title}}</div>
    <div class="footer-right">Page {{page_number}} of {{total_pages}}</div>
</div>
```

### Excel/XLSX Reports

#### Workbook Structure
- **Sheet 1**: Summary/Overview
- **Sheet 2**: Detailed Data
- **Sheet 3**: Charts (if applicable)
- **Sheet Names**: Clear, descriptive names (max 31 characters)

#### Cell Styling
```javascript
// Header Row Styling
const headerStyle = {
    font: { bold: true, color: { rgb: '4E40C5' }, size: 12 },
    fill: { fgColor: { rgb: 'F0F4FF' } },
    border: {
        top: { style: 'thin', color: { rgb: '4E40C5' } },
        bottom: { style: 'thin', color: { rgb: '4E40C5' } }
    },
    alignment: { horizontal: 'center', vertical: 'center' }
};

// Data Cell Styling
const dataStyle = {
    font: { color: { rgb: '4A4A68' }, size: 11 },
    alignment: { horizontal: 'left', vertical: 'center' },
    border: {
        bottom: { style: 'thin', color: { rgb: 'E0E0E0' } }
    }
};

// Number Formatting
const numberFormats = {
    currency: '$#,##0.00',
    percentage: '0.00%',
    integer: '#,##0',
    decimal: '#,##0.00',
    date: 'yyyy-mm-dd',
    datetime: 'yyyy-mm-dd hh:mm:ss'
};
```

### CSV Reports

#### Header Format
```csv
"Report Title","{{report_title}}"
"Generated At","{{generated_at}}"
"Report Period","{{period_start}} to {{period_end}}"
"User","{{user_name}} ({{user_email}})"
"Company","{{company_name}}"
""
"Column 1","Column 2","Column 3"
```

#### Data Formatting Rules
1. **Dates**: ISO 8601 format (YYYY-MM-DD HH:MM:SS UTC)
2. **Numbers**: No thousands separators, decimal point notation
3. **Percentages**: Decimal format (0.15 for 15%) with % suffix for display
4. **Currency**: Numeric values without currency symbols
5. **Text**: Properly escaped with quotes when containing commas or quotes

### JSON Reports

#### Schema Structure
```json
{
    "report_metadata": {
        "report_id": "string",
        "report_type": "string",
        "generated_at": "ISO 8601 datetime",
        "user_id": "string",
        "company_name": "string",
        "period_start": "ISO 8601 datetime",
        "period_end": "ISO 8601 datetime",
        "version": "1.0"
    },
    "summary": {
        "total_records": "integer",
        "key_metrics": {},
        "performance_indicators": []
    },
    "data": [],
    "charts": []
}
```

## Visual Design Elements

### Gradients and Effects

#### Primary Gradient
```css
.primary-gradient {
    background: linear-gradient(135deg, #6C4BFA 0%, #8A72FF 50%, #B19FFF 100%);
}
```

#### Card Shadows
```css
.card-shadow {
    box-shadow: 
        0 4px 24px rgba(0, 0, 0, 0.08),
        0 1px 3px rgba(108, 75, 250, 0.1),
        0 1px 2px rgba(138, 114, 255, 0.1);
}

.elevated-shadow {
    box-shadow: 
        0 6px 20px rgba(108, 75, 250, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.3) inset;
}
```

### Data Visualization

#### Chart Color Palette
```css
/* Primary Chart Colors */
--chart-color-1: #4E40C5;  /* ACE Purple */
--chart-color-2: #EBAE1B;  /* ACE Yellow */
--chart-color-3: #4CAF50;  /* Success Green */
--chart-color-4: #2196F3;  /* Info Blue */
--chart-color-5: #FF9800;  /* Warning Orange */
--chart-color-6: #8A72FF;  /* Purple Light */
--chart-color-7: #66BB6A;  /* Green Light */
--chart-color-8: #42A5F5;  /* Blue Light */
```

#### Performance Indicators
```css
.performance-up {
    color: var(--success-color);
    font-weight: 600;
}

.performance-down {
    color: var(--error-color);
    font-weight: 600;
}

.performance-neutral {
    color: var(--text-muted);
    font-weight: 500;
}
```

## Accessibility Standards

### WCAG 2.1 AA Compliance

#### Color Contrast Requirements
- **Normal Text**: Minimum 4.5:1 contrast ratio
- **Large Text**: Minimum 3:1 contrast ratio
- **UI Components**: Minimum 3:1 contrast ratio

#### Verified Color Combinations
```css
/* High Contrast Combinations (WCAG AA Compliant) */
.high-contrast-text {
    color: #1A1A2E; /* on white background - 12.6:1 ratio */
}

.purple-on-white {
    color: #4E40C5; /* on white background - 7.8:1 ratio */
}

.white-on-purple {
    color: #FFFFFF; /* on #4E40C5 background - 7.8:1 ratio */
}
```

### Responsive Design

#### Breakpoints
```css
/* Mobile First Approach */
@media only screen and (max-width: 640px) {
    .container { margin: 10px; }
    .content { padding: 30px 20px; }
    .logo { max-width: 140px; }
}

@media only screen and (max-width: 400px) {
    .content { padding: 25px 15px; }
}
```

## Implementation Checklist

### Before Export Generation
- [ ] Verify brand colors are correctly applied
- [ ] Confirm typography hierarchy is consistent
- [ ] Check logo placement and sizing
- [ ] Validate color contrast ratios
- [ ] Test responsive behavior (for HTML exports)

### Quality Assurance
- [ ] Review data formatting consistency
- [ ] Verify all text is properly escaped
- [ ] Check for broken images or missing assets
- [ ] Validate export file integrity
- [ ] Test accessibility with screen readers (for HTML)

### Brand Compliance
- [ ] Logo usage follows guidelines
- [ ] Color palette is consistently applied
- [ ] Typography matches specifications
- [ ] Visual hierarchy is clear and logical
- [ ] Overall design reflects ACE Social brand identity

## Contact and Support

For questions about brand guidelines or export formatting:
- **Design Team**: <EMAIL>
- **Technical Support**: <EMAIL>
- **Brand Guidelines**: <EMAIL>

---

*This document is maintained by the ACE Social Design and Development teams. Last updated: {{current_date}}*
