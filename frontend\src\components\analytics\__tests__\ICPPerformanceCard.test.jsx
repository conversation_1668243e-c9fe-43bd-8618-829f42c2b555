/**
 * Tests for ICPPerformanceCard component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import ICPPerformanceCard from '../ICPPerformanceCard';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

describe('ICPPerformanceCard', () => {
  const mockPerformance = {
    icp_id: 'icp-1',
    icp_name: 'Tech Startups',
    avg_engagement_rate: 7.25,
    avg_views: 15000,
    avg_likes: 850,
    avg_comments: 120,
    avg_shares: 45,
    total_content: 25,
    best_performing_platform: 'LinkedIn',
    best_performing_content_type: 'Article',
    best_performing_time: 'Morning',
    engagement_statistics: {
      previous_period: 6.8,
      views_growth: 12.5,
      likes_growth: 8.3,
      comments_growth: -2.1,
      shares_growth: 15.7
    },
    recommendations: [
      'Focus more content on LinkedIn for better engagement',
      'Create more article-type content as it performs best',
      'Post content in the morning hours for optimal reach'
    ]
  };

  const mockCallbacks = {
    onRefresh: vi.fn(),
    onExport: vi.fn(),
    onViewDetails: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders performance card with basic metrics', () => {
    render(
      <TestWrapper>
        <ICPPerformanceCard performance={mockPerformance} />
      </TestWrapper>
    );

    expect(screen.getByText('Tech Startups')).toBeInTheDocument();
    expect(screen.getByText('7.25% Engagement')).toBeInTheDocument();
    expect(screen.getByText('15,000')).toBeInTheDocument(); // Views
    expect(screen.getByText('850')).toBeInTheDocument(); // Likes
    expect(screen.getByText('120')).toBeInTheDocument(); // Comments
    expect(screen.getByText('45')).toBeInTheDocument(); // Shares
  });

  test('shows loading state correctly', () => {
    render(
      <TestWrapper>
        <ICPPerformanceCard loading={true} />
      </TestWrapper>
    );

    // Should show skeleton loaders
    const skeletons = screen.getAllByTestId(/skeleton/i);
    expect(skeletons.length).toBeGreaterThan(0);
  });

  test('shows error state correctly', () => {
    const errorMessage = 'Failed to load performance data';
    render(
      <TestWrapper>
        <ICPPerformanceCard error={errorMessage} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
    expect(screen.getByText('Retry')).toBeInTheDocument();
  });

  test('shows no data state correctly', () => {
    render(
      <TestWrapper>
        <ICPPerformanceCard performance={null} />
      </TestWrapper>
    );

    expect(screen.getByText('No performance data available for this ICP.')).toBeInTheDocument();
  });

  test('displays detailed view when showDetails is true', () => {
    render(
      <TestWrapper>
        <ICPPerformanceCard performance={mockPerformance} showDetails={true} />
      </TestWrapper>
    );

    expect(screen.getByText('Performance Insights')).toBeInTheDocument();
    expect(screen.getByText('Best Platform')).toBeInTheDocument();
    expect(screen.getByText('LinkedIn')).toBeInTheDocument();
    expect(screen.getByText('Best Content Type')).toBeInTheDocument();
    expect(screen.getByText('Article')).toBeInTheDocument();
    expect(screen.getByText('Best Posting Time')).toBeInTheDocument();
    expect(screen.getByText('Morning')).toBeInTheDocument();
    expect(screen.getByText('25 pieces')).toBeInTheDocument();
  });

  test('displays recommendations when available', () => {
    render(
      <TestWrapper>
        <ICPPerformanceCard performance={mockPerformance} showDetails={true} />
      </TestWrapper>
    );

    expect(screen.getByText('AI Recommendations')).toBeInTheDocument();
    expect(screen.getByText('Focus more content on LinkedIn for better engagement')).toBeInTheDocument();
    expect(screen.getByText('Create more article-type content as it performs best')).toBeInTheDocument();
    expect(screen.getByText('Post content in the morning hours for optimal reach')).toBeInTheDocument();
  });

  test('displays performance trends with progress bars', () => {
    render(
      <TestWrapper>
        <ICPPerformanceCard performance={mockPerformance} showDetails={true} />
      </TestWrapper>
    );

    expect(screen.getByText('Performance Trends')).toBeInTheDocument();
    expect(screen.getByText('Engagement Rate')).toBeInTheDocument();
    expect(screen.getByText('Content Volume')).toBeInTheDocument();
    expect(screen.getByText('7.3%')).toBeInTheDocument(); // Engagement rate display
    expect(screen.getByText('25')).toBeInTheDocument(); // Content volume display
  });

  test('shows growth indicators for metrics', () => {
    render(
      <TestWrapper>
        <ICPPerformanceCard performance={mockPerformance} />
      </TestWrapper>
    );

    // Should show growth percentages for metrics
    expect(screen.getByText('+12.5%')).toBeInTheDocument(); // Views growth
    expect(screen.getByText('+8.3%')).toBeInTheDocument(); // Likes growth
    expect(screen.getByText('-2.1%')).toBeInTheDocument(); // Comments decline
    expect(screen.getByText('+15.7%')).toBeInTheDocument(); // Shares growth
  });

  test('shows engagement trend indicator', () => {
    render(
      <TestWrapper>
        <ICPPerformanceCard performance={mockPerformance} />
      </TestWrapper>
    );

    // Should show trending up icon for positive engagement change
    const trendingUpIcons = screen.getAllByTestId('TrendingUpIcon');
    expect(trendingUpIcons.length).toBeGreaterThan(0);
  });

  test('handles refresh action', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ICPPerformanceCard 
          performance={mockPerformance} 
          onRefresh={mockCallbacks.onRefresh}
        />
      </TestWrapper>
    );

    const refreshButton = screen.getByLabelText('Refresh performance data');
    await user.click(refreshButton);

    expect(mockCallbacks.onRefresh).toHaveBeenCalledWith('icp-1');
  });

  test('handles export action', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ICPPerformanceCard 
          performance={mockPerformance} 
          onExport={mockCallbacks.onExport}
        />
      </TestWrapper>
    );

    const exportButton = screen.getByLabelText('Export performance data');
    await user.click(exportButton);

    expect(mockCallbacks.onExport).toHaveBeenCalledWith(mockPerformance);
  });

  test('handles view details action', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ICPPerformanceCard 
          performance={mockPerformance} 
          onViewDetails={mockCallbacks.onViewDetails}
        />
      </TestWrapper>
    );

    const viewDetailsButton = screen.getByLabelText('View detailed performance metrics');
    await user.click(viewDetailsButton);

    expect(mockCallbacks.onViewDetails).toHaveBeenCalledWith('icp-1');
  });

  test('handles retry action in error state', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ICPPerformanceCard 
          error="Failed to load data" 
          onRefresh={mockCallbacks.onRefresh}
          performance={mockPerformance}
        />
      </TestWrapper>
    );

    const retryButton = screen.getByText('Retry');
    await user.click(retryButton);

    expect(mockCallbacks.onRefresh).toHaveBeenCalledWith('icp-1');
  });

  test('renders in compact mode', () => {
    render(
      <TestWrapper>
        <ICPPerformanceCard 
          performance={mockPerformance} 
          compact={true}
          showDetails={true}
        />
      </TestWrapper>
    );

    // In compact mode, details should not be shown even if showDetails is true
    expect(screen.queryByText('Performance Insights')).not.toBeInTheDocument();
    
    // Engagement chip should be small
    const engagementChip = screen.getByText('7.25% Engagement');
    expect(engagementChip).toBeInTheDocument();
  });

  test('hides actions when showActions is false', () => {
    render(
      <TestWrapper>
        <ICPPerformanceCard 
          performance={mockPerformance} 
          showActions={false}
          onRefresh={mockCallbacks.onRefresh}
          onExport={mockCallbacks.onExport}
          onViewDetails={mockCallbacks.onViewDetails}
        />
      </TestWrapper>
    );

    expect(screen.queryByLabelText('Refresh performance data')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('Export performance data')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('View detailed performance metrics')).not.toBeInTheDocument();
  });

  test('handles missing optional performance data gracefully', () => {
    const incompletePerformance = {
      icp_id: 'icp-2',
      icp_name: 'Incomplete ICP',
      avg_engagement_rate: 3.5,
      avg_views: 5000,
      avg_likes: 200,
      avg_comments: 30,
      avg_shares: 10,
      total_content: 5
      // Missing optional fields
    };

    render(
      <TestWrapper>
        <ICPPerformanceCard performance={incompletePerformance} showDetails={true} />
      </TestWrapper>
    );

    expect(screen.getByText('Incomplete ICP')).toBeInTheDocument();
    expect(screen.getByText('3.50% Engagement')).toBeInTheDocument();
    expect(screen.getByText('5 pieces')).toBeInTheDocument();
    
    // Should not show missing optional fields
    expect(screen.queryByText('Best Platform')).not.toBeInTheDocument();
    expect(screen.queryByText('AI Recommendations')).not.toBeInTheDocument();
  });

  test('displays correct color coding for engagement trends', () => {
    const lowEngagementPerformance = {
      ...mockPerformance,
      avg_engagement_rate: 1.5,
      engagement_statistics: {
        previous_period: 2.0,
        views_growth: -5.2,
        likes_growth: -3.1,
        comments_growth: -8.5,
        shares_growth: -12.3
      }
    };

    render(
      <TestWrapper>
        <ICPPerformanceCard performance={lowEngagementPerformance} />
      </TestWrapper>
    );

    // Should show trending down for negative engagement change
    const trendingDownIcons = screen.getAllByTestId('TrendingDownIcon');
    expect(trendingDownIcons.length).toBeGreaterThan(0);
  });

  test('limits recommendations display to 3 items', () => {
    const manyRecommendationsPerformance = {
      ...mockPerformance,
      recommendations: [
        'Recommendation 1',
        'Recommendation 2', 
        'Recommendation 3',
        'Recommendation 4',
        'Recommendation 5'
      ]
    };

    render(
      <TestWrapper>
        <ICPPerformanceCard performance={manyRecommendationsPerformance} showDetails={true} />
      </TestWrapper>
    );

    expect(screen.getByText('Recommendation 1')).toBeInTheDocument();
    expect(screen.getByText('Recommendation 2')).toBeInTheDocument();
    expect(screen.getByText('Recommendation 3')).toBeInTheDocument();
    expect(screen.getByText('+2 more recommendations')).toBeInTheDocument();
    expect(screen.queryByText('Recommendation 4')).not.toBeInTheDocument();
  });

  test('handles zero values gracefully', () => {
    const zeroPerformance = {
      icp_id: 'icp-zero',
      icp_name: 'Zero Performance ICP',
      avg_engagement_rate: 0,
      avg_views: 0,
      avg_likes: 0,
      avg_comments: 0,
      avg_shares: 0,
      total_content: 0
    };

    render(
      <TestWrapper>
        <ICPPerformanceCard performance={zeroPerformance} />
      </TestWrapper>
    );

    expect(screen.getByText('Zero Performance ICP')).toBeInTheDocument();
    expect(screen.getByText('0.00% Engagement')).toBeInTheDocument();
    expect(screen.getAllByText('0')).toHaveLength(4); // Views, Likes, Comments, Shares
  });

  test('applies correct accessibility attributes', () => {
    render(
      <TestWrapper>
        <ICPPerformanceCard 
          performance={mockPerformance} 
          onRefresh={mockCallbacks.onRefresh}
          onExport={mockCallbacks.onExport}
          onViewDetails={mockCallbacks.onViewDetails}
        />
      </TestWrapper>
    );

    // Check ARIA labels
    expect(screen.getByLabelText('Refresh performance data')).toBeInTheDocument();
    expect(screen.getByLabelText('Export performance data')).toBeInTheDocument();
    expect(screen.getByLabelText('View detailed performance metrics')).toBeInTheDocument();

    // Check tooltips
    expect(screen.getByTitle('Average Views per Content')).toBeInTheDocument();
    expect(screen.getByTitle('Average Likes per Content')).toBeInTheDocument();
    expect(screen.getByTitle('Average Comments per Content')).toBeInTheDocument();
    expect(screen.getByTitle('Average Shares per Content')).toBeInTheDocument();
  });
});
