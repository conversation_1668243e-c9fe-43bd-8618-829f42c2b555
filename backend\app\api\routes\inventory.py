"""
Real-time Inventory Tracking API Routes.
Provides endpoints for inventory management, alerts, and real-time updates.
@since 2024-1-1 to 2025-25-7
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
from fastapi import APIRouter, Depends, HTTPException, status, Query, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse

from app.models.user import User
from app.models.ecommerce import InventoryChangeType
from app.middleware.auth import get_current_active_user
from app.api.dependencies.rate_limiter import rate_limit
from app.api.dependencies.feature_access import require_feature_access
from app.services.ecommerce.inventory_service import inventory_service
from app.core.websocket import websocket_manager
from app.schemas.ecommerce import (
    InventoryUpdateRequest, InventoryAlertRequest, InventoryHistoryResponse,
    InventorySnapshotResponse, InventoryStatsResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.websocket("/ws/{user_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    user_id: str,
    connection_type: str = Query("inventory", description="Connection type"),
    room_id: Optional[str] = Query(None, description="Room ID for grouped connections")
):
    """
    WebSocket endpoint for real-time inventory updates.
    
    Args:
        websocket: WebSocket connection
        user_id: User ID for the connection
        connection_type: Type of connection (inventory, sync, general)
        room_id: Optional room ID for store-specific updates
    """
    try:
        await websocket_manager.connect(websocket, user_id, connection_type, room_id)
        
        # Send initial connection confirmation
        await websocket_manager.send_personal_message(websocket, {
            "type": "connection_established",
            "user_id": user_id,
            "connection_type": connection_type,
            "room_id": room_id,
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages from client
                data = await websocket.receive_text()
                message = eval(data) if data else {}
                
                # Handle ping/pong for connection health
                if message.get("type") == "ping":
                    await websocket_manager.send_personal_message(websocket, {
                        "type": "pong",
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    })
                
                # Handle subscription to specific product updates
                elif message.get("type") == "subscribe_product":
                    product_id = message.get("product_id")
                    if product_id:
                        # Add to product-specific room
                        product_room = f"product:{product_id}"
                        await websocket_manager.connect(websocket, user_id, "product", product_room)
                        
                        await websocket_manager.send_personal_message(websocket, {
                            "type": "subscription_confirmed",
                            "product_id": product_id,
                            "timestamp": datetime.now(timezone.utc).isoformat()
                        })
                
            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"Error handling WebSocket message: {str(e)}")
                await websocket_manager.send_personal_message(websocket, {
                    "type": "error",
                    "message": "Error processing message",
                    "timestamp": datetime.now(timezone.utc).isoformat()
                })
                
    except WebSocketDisconnect:
        pass
    except Exception as e:
        logger.error(f"WebSocket connection error for user {user_id}: {str(e)}")
    finally:
        await websocket_manager.disconnect(websocket)


@router.put("/products/{product_id}/inventory")
@rate_limit("inventory_update", limit=100, window=3600)
async def update_product_inventory(
    product_id: str,
    request: InventoryUpdateRequest,
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(require_feature_access("ecommerce_integration"))
):
    """
    Update product inventory with real-time notifications.
    
    Args:
        product_id: Product ID to update
        request: Inventory update request
        current_user: Current authenticated user
        
    Returns:
        Inventory update result
    """
    try:
        result = await inventory_service.update_inventory(
            user_id=str(current_user.id),
            store_id=request.store_id,
            product_id=product_id,
            new_quantity=request.new_quantity,
            change_type=InventoryChangeType(request.reason),
            variant_id=request.variant_id,
            reason=request.reason,
            reference_id=getattr(request, 'reference_id', None),
            changed_by=str(current_user.id)
        )
        
        if not result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "Failed to update inventory")
            )
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=result
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error updating inventory for product {product_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update inventory"
        )


@router.post("/alerts")
@rate_limit("create_alert", limit=50, window=3600)
async def create_inventory_alert(
    request: InventoryAlertRequest,
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(require_feature_access("ecommerce_integration"))
):
    """
    Create a new inventory alert.
    
    Args:
        request: Inventory alert request
        current_user: Current authenticated user
        
    Returns:
        Created alert details
    """
    try:
        result = await inventory_service.create_inventory_alert(
            user_id=str(current_user.id),
            store_id=request.store_id,
            product_id=request.product_id,
            alert_type=request.alert_type,
            threshold=request.threshold,
            variant_id=request.variant_id,
            email_enabled=request.email_enabled,
            webhook_enabled=request.webhook_enabled,
            webhook_url=request.webhook_url
        )
        
        if not result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "Failed to create alert")
            )
        
        return JSONResponse(
            status_code=status.HTTP_201_CREATED,
            content=result
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating inventory alert: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create inventory alert"
        )


@router.get("/history", response_model=InventoryHistoryResponse)
@rate_limit("get_history", limit=200, window=3600)
async def get_inventory_history(
    store_id: str = Query(..., description="Store ID"),
    product_id: Optional[str] = Query(None, description="Product ID filter"),
    variant_id: Optional[str] = Query(None, description="Variant ID filter"),
    change_types: Optional[List[str]] = Query(None, description="Change types filter"),
    start_date: Optional[datetime] = Query(None, description="Start date filter"),
    end_date: Optional[datetime] = Query(None, description="End date filter"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    offset: int = Query(0, ge=0, description="Number of records to skip"),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(require_feature_access("ecommerce_integration"))
):
    """
    Get inventory change history with filtering options.
    
    Args:
        store_id: Store ID
        product_id: Optional product ID filter
        variant_id: Optional variant ID filter
        change_types: Optional change types filter
        start_date: Optional start date filter
        end_date: Optional end date filter
        limit: Number of records to return
        offset: Number of records to skip
        current_user: Current authenticated user
        
    Returns:
        Inventory history records
    """
    try:
        result = await inventory_service.get_inventory_history(
            user_id=str(current_user.id),
            store_id=store_id,
            product_id=product_id,
            variant_id=variant_id,
            change_types=change_types,
            start_date=start_date,
            end_date=end_date,
            limit=limit,
            offset=offset
        )
        
        if not result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "Failed to get inventory history")
            )
        
        return InventoryHistoryResponse(**result)
        
    except Exception as e:
        logger.error(f"Error getting inventory history: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get inventory history"
        )


@router.post("/snapshots")
@rate_limit("create_snapshot", limit=10, window=3600)
async def generate_inventory_snapshot(
    store_id: str = Query(..., description="Store ID"),
    snapshot_date: Optional[datetime] = Query(None, description="Snapshot date"),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(require_feature_access("ecommerce_integration"))
):
    """
    Generate daily inventory snapshot for analytics.
    
    Args:
        store_id: Store ID
        snapshot_date: Date for snapshot (defaults to today)
        current_user: Current authenticated user
        
    Returns:
        Generated snapshot details
    """
    try:
        result = await inventory_service.generate_inventory_snapshot(
            user_id=str(current_user.id),
            store_id=store_id,
            snapshot_date=snapshot_date
        )
        
        if not result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "Failed to generate snapshot")
            )
        
        return JSONResponse(
            status_code=status.HTTP_201_CREATED,
            content=result
        )
        
    except Exception as e:
        logger.error(f"Error generating inventory snapshot: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate inventory snapshot"
        )


@router.get("/stats")
@rate_limit("get_stats", limit=100, window=3600)
async def get_inventory_stats(
    store_id: str = Query(..., description="Store ID"),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(require_feature_access("ecommerce_integration"))
):
    """
    Get real-time inventory statistics.
    
    Args:
        store_id: Store ID
        current_user: Current authenticated user
        
    Returns:
        Inventory statistics
    """
    try:
        # Get WebSocket connection stats
        ws_stats = await websocket_manager.get_connection_stats()
        
        # Return combined stats
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "websocket_stats": ws_stats,
                "store_id": store_id,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting inventory stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get inventory statistics"
        )
