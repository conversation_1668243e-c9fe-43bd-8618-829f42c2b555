// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Typography,
  Box,
  Alert,
  Switch,
  FormControlLabel,
  Chip
} from '@mui/material';
import {
  Save as SaveIcon,
  Campaign as CampaignIcon
} from '@mui/icons-material';

const EmailCampaignEditor = ({ 
  open, 
  mode = 'create', 
  campaign = null, 
  templates = [],
  onClose, 
  onSubmit 
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    template_id: '',
    audience: {
      include_all_users: false,
      user_segments: [],
      subscription_plans: [],
      exclude_unsubscribed: true,
      max_recipients: null
    },
    schedule: {
      send_immediately: true,
      scheduled_at: null,
      timezone: 'UTC'
    },
    ab_test: {
      enabled: false,
      test_percentage: 50.0,
      variant_subject: '',
      variant_content: ''
    }
  });

  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (mode === 'edit' && campaign) {
      setFormData({
        name: campaign.name || '',
        description: campaign.description || '',
        template_id: campaign.template_id || '',
        audience: campaign.audience || formData.audience,
        schedule: campaign.schedule || formData.schedule,
        ab_test: campaign.ab_test || formData.ab_test
      });
    } else {
      // Reset for create mode
      setFormData({
        name: '',
        description: '',
        template_id: '',
        audience: {
          include_all_users: false,
          user_segments: [],
          subscription_plans: [],
          exclude_unsubscribed: true,
          max_recipients: null
        },
        schedule: {
          send_immediately: true,
          scheduled_at: null,
          timezone: 'UTC'
        },
        ab_test: {
          enabled: false,
          test_percentage: 50.0,
          variant_subject: '',
          variant_content: ''
        }
      });
    }
  }, [mode, campaign, open]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const handleNestedChange = (section, field, value) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Campaign name is required';
    }

    if (!formData.template_id) {
      newErrors.template_id = 'Template selection is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting campaign:', error);
    } finally {
      setLoading(false);
    }
  };

  const getDialogTitle = () => {
    switch (mode) {
      case 'create':
        return 'Create Email Campaign';
      case 'edit':
        return 'Edit Email Campaign';
      default:
        return 'Email Campaign';
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: { minHeight: '70vh' }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <CampaignIcon />
          {getDialogTitle()}
        </Box>
      </DialogTitle>
      
      <DialogContent dividers>
        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              Campaign Details
            </Typography>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Campaign Name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              error={!!errors.name}
              helperText={errors.name}
              placeholder="Monthly Newsletter"
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <FormControl fullWidth error={!!errors.template_id}>
              <InputLabel>Email Template</InputLabel>
              <Select
                value={formData.template_id}
                label="Email Template"
                onChange={(e) => handleInputChange('template_id', e.target.value)}
              >
                {templates.map((template) => (
                  <MenuItem key={template.id} value={template.id}>
                    {template.name} ({template.category})
                  </MenuItem>
                ))}
              </Select>
              {errors.template_id && (
                <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                  {errors.template_id}
                </Typography>
              )}
            </FormControl>
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Description"
              multiline
              rows={3}
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Brief description of this campaign"
            />
          </Grid>

          {/* Audience Settings */}
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              Audience Settings
            </Typography>
          </Grid>
          
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.audience.include_all_users}
                  onChange={(e) => handleNestedChange('audience', 'include_all_users', e.target.checked)}
                />
              }
              label="Send to all users"
            />
          </Grid>
          
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.audience.exclude_unsubscribed}
                  onChange={(e) => handleNestedChange('audience', 'exclude_unsubscribed', e.target.checked)}
                />
              }
              label="Exclude unsubscribed users"
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Max Recipients (Optional)"
              type="number"
              value={formData.audience.max_recipients || ''}
              onChange={(e) => handleNestedChange('audience', 'max_recipients', e.target.value ? parseInt(e.target.value) : null)}
              placeholder="Leave empty for no limit"
            />
          </Grid>

          {/* Schedule Settings */}
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              Schedule Settings
            </Typography>
          </Grid>
          
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.schedule.send_immediately}
                  onChange={(e) => handleNestedChange('schedule', 'send_immediately', e.target.checked)}
                />
              }
              label="Send immediately"
            />
          </Grid>
          
          {!formData.schedule.send_immediately && (
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Scheduled Date & Time"
                type="datetime-local"
                value={formData.schedule.scheduled_at || ''}
                onChange={(e) => handleNestedChange('schedule', 'scheduled_at', e.target.value)}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
          )}

          {/* A/B Testing */}
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              A/B Testing (Optional)
            </Typography>
          </Grid>
          
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.ab_test.enabled}
                  onChange={(e) => handleNestedChange('ab_test', 'enabled', e.target.checked)}
                />
              }
              label="Enable A/B testing"
            />
          </Grid>
          
          {formData.ab_test.enabled && (
            <>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Test Percentage"
                  type="number"
                  value={formData.ab_test.test_percentage}
                  onChange={(e) => handleNestedChange('ab_test', 'test_percentage', parseFloat(e.target.value))}
                  inputProps={{ min: 0, max: 100, step: 0.1 }}
                  helperText="Percentage of audience to include in A/B test"
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Variant Subject Line"
                  value={formData.ab_test.variant_subject}
                  onChange={(e) => handleNestedChange('ab_test', 'variant_subject', e.target.value)}
                  placeholder="Alternative subject line for testing"
                />
              </Grid>
            </>
          )}
        </Grid>

        {Object.keys(errors).length > 0 && (
          <Alert severity="error" sx={{ mt: 2 }}>
            Please fix the errors above before submitting.
          </Alert>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained"
          disabled={loading}
          startIcon={<SaveIcon />}
        >
          {loading ? 'Saving...' : (mode === 'create' ? 'Create Campaign' : 'Update Campaign')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EmailCampaignEditor;
