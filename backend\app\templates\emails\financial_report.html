<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Report - ACE Social</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        :root {
            /* ACE Social Brand Colors */
            --ace-purple: #4E40C5;
            --ace-dark: #15110E;
            --ace-yellow: #EBAE1B;
            --ace-white: #FFFFFF;
            --ace-purple-light: #6C4BFA;
            --ace-purple-lighter: #8A72FF;
            --ace-purple-lightest: #B19FFF;
            
            /* Semantic Colors */
            --success-color: #4CAF50;
            --warning-color: #FF9800;
            --error-color: #F44336;
            --info-color: #2196F3;
            
            /* Text Colors */
            --text-primary: #1A1A2E;
            --text-secondary: #4A4A68;
            --text-muted: #AAAAAA;
            --background-light: #F0F4FF;
            --background-card: rgba(255, 255, 255, 0.85);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-secondary);
            background: linear-gradient(135deg, var(--ace-purple-light) 0%, var(--ace-purple-lighter) 50%, var(--ace-purple-lightest) 100%);
            min-height: 100vh;
            padding: 20px 10px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--background-card);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            overflow: hidden;
            box-shadow: 
                0 4px 24px rgba(0, 0, 0, 0.08),
                0 1px 3px rgba(108, 75, 250, 0.1),
                0 1px 2px rgba(138, 114, 255, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, var(--ace-purple) 0%, var(--ace-purple-light) 100%);
            color: var(--ace-white);
            text-align: center;
            padding: 40px 40px 30px;
            position: relative;
        }
        
        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
        }
        
        .logo {
            max-width: 180px;
            height: auto;
            margin-bottom: 20px;
            filter: brightness(0) invert(1);
        }
        
        .report-title {
            font-size: 28px;
            font-weight: 700;
            letter-spacing: -0.5px;
            margin-bottom: 8px;
        }
        
        .report-subtitle {
            font-size: 16px;
            font-weight: 400;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .greeting {
            font-size: 18px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 20px;
        }
        
        .intro-text {
            font-size: 16px;
            color: var(--text-secondary);
            margin-bottom: 30px;
            line-height: 1.7;
        }
        
        .financial-summary {
            background: linear-gradient(135deg, var(--success-color) 0%, #66BB6A 100%);
            color: var(--ace-white);
            border-radius: 16px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }
        
        .financial-summary .amount {
            font-size: 42px;
            font-weight: 700;
            margin: 10px 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .financial-summary .label {
            font-size: 16px;
            font-weight: 500;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .metric-card {
            background: var(--ace-white);
            border-radius: 12px;
            padding: 24px;
            border: 1px solid rgba(108, 75, 250, 0.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transition: transform 0.2s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
        }
        
        .metric-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 8px;
        }
        
        .metric-change {
            font-size: 14px;
            font-weight: 600;
        }
        
        .metric-change.positive {
            color: var(--success-color);
        }
        
        .metric-change.negative {
            color: var(--error-color);
        }
        
        .metric-change.neutral {
            color: var(--text-muted);
        }
        
        .section-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 40px 0 20px;
            letter-spacing: -0.5px;
        }
        
        .divider {
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, rgba(108, 75, 250, 0.2) 50%, transparent 100%);
            margin: 30px 0;
        }
        
        .plan-breakdown {
            background: var(--background-light);
            border-radius: 12px;
            padding: 24px;
            margin: 20px 0;
        }
        
        .plan-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(108, 75, 250, 0.1);
        }
        
        .plan-item:last-child {
            border-bottom: none;
        }
        
        .plan-name {
            font-weight: 600;
            color: var(--text-primary);
            text-transform: capitalize;
        }
        
        .plan-revenue {
            font-weight: 700;
            color: var(--ace-purple);
        }
        
        .plan-count {
            font-size: 14px;
            color: var(--text-muted);
            margin-left: 8px;
        }
        
        .alert-box {
            background: rgba(255, 152, 0, 0.1);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        
        .alert-box.success {
            background: rgba(76, 175, 80, 0.1);
            border-color: var(--success-color);
        }
        
        .alert-box.error {
            background: rgba(244, 67, 54, 0.1);
            border-color: var(--error-color);
        }
        
        .button {
            display: inline-block;
            background: linear-gradient(135deg, var(--ace-purple) 0%, var(--ace-purple-light) 100%);
            color: var(--ace-white);
            padding: 14px 28px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(78, 64, 197, 0.3);
        }
        
        .button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(78, 64, 197, 0.4);
        }
        
        .footer {
            background: var(--background-light);
            text-align: center;
            padding: 30px 40px;
            border-top: 1px solid rgba(108, 75, 250, 0.1);
        }
        
        .footer p {
            color: var(--text-muted);
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .footer-links {
            font-size: 14px;
        }
        
        .footer-links a {
            color: var(--ace-purple);
            text-decoration: none;
            margin: 0 8px;
        }
        
        .footer-links a:hover {
            text-decoration: underline;
        }
        
        /* Responsive Design */
        @media only screen and (max-width: 640px) {
            body {
                padding: 10px 5px;
            }
            
            .container {
                border-radius: 16px;
                margin: 0;
            }
            
            .header {
                padding: 30px 20px 25px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .footer {
                padding: 25px 20px;
            }
            
            .logo {
                max-width: 140px;
            }
            
            .report-title {
                font-size: 24px;
            }
            
            .financial-summary .amount {
                font-size: 36px;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .plan-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
        }
        
        @media only screen and (max-width: 400px) {
            .content {
                padding: 25px 15px;
            }
            
            .financial-summary {
                padding: 24px;
            }
            
            .metric-card {
                padding: 20px;
            }
        }
        
        /* Print Styles */
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .container {
                box-shadow: none;
                border: 1px solid #ddd;
            }
            
            .button {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="{{frontend_url}}/assets/logo.svg" alt="ACE Social" class="logo">
            <h1 class="report-title">{{report_type|title}} Report</h1>
            <p class="report-subtitle">Generated on {{generated_at|date:"F j, Y \a\\t g:i A T"}}</p>
        </div>
        
        <div class="content">
            <h2 class="greeting">Hello {{user_name}},</h2>
            
            <p class="intro-text">
                Here's your {{report_type|lower}} report for the period from 
                <strong>{{period_start|date:"F j, Y"}}</strong> to 
                <strong>{{period_end|date:"F j, Y"}}</strong>. 
                This report provides detailed insights into your financial performance and key business metrics.
            </p>
            
            <!-- Revenue Summary -->
            {% if report_type == 'revenue_summary' %}
            <div class="financial-summary">
                <div class="label">Total Revenue</div>
                <div class="amount">${{total_revenue|floatformat:2}}</div>
                <div>
                    {% if revenue_change > 0 %}
                    <span class="metric-change positive">↑ {{revenue_change|floatformat:1}}%</span>
                    {% elif revenue_change < 0 %}
                    <span class="metric-change negative">↓ {{revenue_change|floatformat:1|cut:"-"}}%</span>
                    {% else %}
                    <span class="metric-change neutral">No change</span>
                    {% endif %}
                    from previous period
                </div>
            </div>
            {% endif %}
            
            <!-- Key Metrics Grid -->
            <div class="metrics-grid">
                {% if mrr %}
                <div class="metric-card">
                    <div class="metric-label">Monthly Recurring Revenue</div>
                    <div class="metric-value">${{mrr|floatformat:2}}</div>
                    {% if mrr_change %}
                    <div class="metric-change {% if mrr_change > 0 %}positive{% elif mrr_change < 0 %}negative{% else %}neutral{% endif %}">
                        {% if mrr_change > 0 %}↑{% elif mrr_change < 0 %}↓{% endif %} {{mrr_change|floatformat:1|cut:"-"}}%
                    </div>
                    {% endif %}
                </div>
                {% endif %}
                
                {% if arr %}
                <div class="metric-card">
                    <div class="metric-label">Annual Recurring Revenue</div>
                    <div class="metric-value">${{arr|floatformat:2}}</div>
                    {% if arr_change %}
                    <div class="metric-change {% if arr_change > 0 %}positive{% elif arr_change < 0 %}negative{% else %}neutral{% endif %}">
                        {% if arr_change > 0 %}↑{% elif arr_change < 0 %}↓{% endif %} {{arr_change|floatformat:1|cut:"-"}}%
                    </div>
                    {% endif %}
                </div>
                {% endif %}
                
                {% if churn_rate %}
                <div class="metric-card">
                    <div class="metric-label">Churn Rate</div>
                    <div class="metric-value">{{churn_rate|floatformat:1}}%</div>
                    {% if churn_change %}
                    <div class="metric-change {% if churn_change < 0 %}positive{% elif churn_change > 0 %}negative{% else %}neutral{% endif %}">
                        {% if churn_change > 0 %}↑{% elif churn_change < 0 %}↓{% endif %} {{churn_change|floatformat:1|cut:"-"}}%
                    </div>
                    {% endif %}
                </div>
                {% endif %}
                
                {% if ltv %}
                <div class="metric-card">
                    <div class="metric-label">Customer Lifetime Value</div>
                    <div class="metric-value">${{ltv|floatformat:2}}</div>
                    {% if ltv_change %}
                    <div class="metric-change {% if ltv_change > 0 %}positive{% elif ltv_change < 0 %}negative{% else %}neutral{% endif %}">
                        {% if ltv_change > 0 %}↑{% elif ltv_change < 0 %}↓{% endif %} {{ltv_change|floatformat:1|cut:"-"}}%
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
            
            <div class="divider"></div>
            
            <!-- Plan Breakdown -->
            {% if plan_breakdown %}
            <h3 class="section-title">Revenue by Subscription Plan</h3>
            <div class="plan-breakdown">
                {% for plan in plan_breakdown %}
                <div class="plan-item">
                    <div>
                        <span class="plan-name">{{plan.name}}</span>
                        <span class="plan-count">({{plan.subscriber_count}} subscribers)</span>
                    </div>
                    <div class="plan-revenue">${{plan.revenue|floatformat:2}}</div>
                </div>
                {% endfor %}
            </div>
            {% endif %}
            
            <!-- Alerts and Insights -->
            {% if payment_failures and payment_failures > 0 %}
            <div class="alert-box error">
                <strong>Payment Failures:</strong> {{payment_failures}} failed payments detected. 
                Consider reaching out to affected customers to update their payment methods.
            </div>
            {% endif %}
            
            {% if revenue_change > 20 %}
            <div class="alert-box success">
                <strong>Excellent Growth:</strong> Revenue increased by {{revenue_change|floatformat:1}}% this period. 
                Your growth strategy is working well!
            </div>
            {% elif revenue_change < -10 %}
            <div class="alert-box error">
                <strong>Revenue Decline:</strong> Revenue decreased by {{revenue_change|floatformat:1|cut:"-"}}% this period. 
                Consider reviewing your retention and acquisition strategies.
            </div>
            {% endif %}
            
            <div class="divider"></div>
            
            <!-- Call to Action -->
            <p>Access your complete financial dashboard for detailed analytics, forecasting, and business intelligence:</p>
            
            <a href="{{frontend_url}}/admin/finance" class="button">View Financial Dashboard</a>
            
            {% if company_name %}
            <p style="margin-top: 30px; font-size: 14px; color: var(--text-muted);">
                This financial report was generated for <strong>{{company_name}}</strong> using ACE Social's advanced financial analytics.
            </p>
            {% endif %}
        </div>
        
        <div class="footer">
            <p>© {% now 'Y' %} ACE Social. All rights reserved.</p>
            <div class="footer-links">
                <a href="{{frontend_url}}/privacy-policy">Privacy Policy</a> •
                <a href="{{frontend_url}}/terms">Terms of Service</a> •
                <a href="{{frontend_url}}/contact">Contact Support</a>
            </div>
        </div>
    </div>
</body>
</html>
