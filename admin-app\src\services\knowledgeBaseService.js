// @since 2024-1-1 to 2025-25-7
import axios from 'axios';
import { API_BASE_URL } from '../config';

/**
 * Knowledge Base Service
 * Handles all API communications for comprehensive knowledge base management
 */
class KnowledgeBaseService {
  constructor() {
    this.baseURL = `${API_BASE_URL}/admin/knowledge-base`;
    this.publicURL = `${API_BASE_URL}/knowledge-base`;
    this.setupInterceptors();
  }

  /**
   * Setup axios interceptors for authentication and error handling
   */
  setupInterceptors() {
    // Request interceptor to add auth token
    axios.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('adminToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    axios.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Handle unauthorized access
          localStorage.removeItem('adminToken');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Article Management Methods

  /**
   * Get all knowledge base articles with optional filtering
   */
  async getArticles(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.skip !== undefined) params.append('skip', filters.skip);
      if (filters.limit !== undefined) params.append('limit', filters.limit);
      if (filters.category) params.append('category', filters.category);
      if (filters.article_type) params.append('article_type', filters.article_type);
      if (filters.status) params.append('status', filters.status);
      if (filters.difficulty_level) params.append('difficulty_level', filters.difficulty_level);
      if (filters.is_featured !== undefined) params.append('is_featured', filters.is_featured);
      if (filters.is_internal !== undefined) params.append('is_internal', filters.is_internal);
      if (filters.author_id) params.append('author_id', filters.author_id);
      if (filters.tags) filters.tags.forEach(tag => params.append('tags', tag));
      if (filters.search) params.append('search', filters.search);
      if (filters.sort_by) params.append('sort_by', filters.sort_by);
      if (filters.sort_order) params.append('sort_order', filters.sort_order);
      if (filters.include_drafts !== undefined) params.append('include_drafts', filters.include_drafts);
      if (filters.include_archived !== undefined) params.append('include_archived', filters.include_archived);

      const response = await axios.get(`${this.baseURL}/articles?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching knowledge base articles:', error);
      throw error;
    }
  }

  /**
   * Get a specific knowledge base article by ID
   */
  async getArticle(articleId) {
    try {
      const response = await axios.get(`${this.baseURL}/articles/${articleId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching knowledge base article ${articleId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new knowledge base article
   */
  async createArticle(articleData) {
    try {
      const response = await axios.post(`${this.baseURL}/articles`, articleData);
      return response.data;
    } catch (error) {
      console.error('Error creating knowledge base article:', error);
      throw error;
    }
  }

  /**
   * Update an existing knowledge base article
   */
  async updateArticle(articleId, articleData) {
    try {
      const response = await axios.put(`${this.baseURL}/articles/${articleId}`, articleData);
      return response.data;
    } catch (error) {
      console.error(`Error updating knowledge base article ${articleId}:`, error);
      throw error;
    }
  }

  /**
   * Delete a knowledge base article
   */
  async deleteArticle(articleId) {
    try {
      const response = await axios.delete(`${this.baseURL}/articles/${articleId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting knowledge base article ${articleId}:`, error);
      throw error;
    }
  }

  /**
   * Duplicate a knowledge base article
   */
  async duplicateArticle(articleId, duplicateData) {
    try {
      const response = await axios.post(`${this.baseURL}/articles/${articleId}/duplicate`, duplicateData);
      return response.data;
    } catch (error) {
      console.error(`Error duplicating knowledge base article ${articleId}:`, error);
      throw error;
    }
  }

  /**
   * Search knowledge base articles
   */
  async searchArticles(query, filters = {}) {
    try {
      const params = new URLSearchParams();
      params.append('query', query);
      
      if (filters.category) params.append('category', filters.category);
      if (filters.article_type) params.append('article_type', filters.article_type);
      if (filters.difficulty_level) params.append('difficulty_level', filters.difficulty_level);
      if (filters.tags) filters.tags.forEach(tag => params.append('tags', tag));
      if (filters.include_internal !== undefined) params.append('include_internal', filters.include_internal);
      if (filters.limit !== undefined) params.append('limit', filters.limit);
      if (filters.include_content !== undefined) params.append('include_content', filters.include_content);

      const response = await axios.get(`${this.baseURL}/search?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error searching knowledge base articles:', error);
      throw error;
    }
  }

  /**
   * Get article analytics
   */
  async getArticleAnalytics(articleId, dateRange = {}) {
    try {
      const params = new URLSearchParams();
      
      if (dateRange.start_date) params.append('start_date', dateRange.start_date);
      if (dateRange.end_date) params.append('end_date', dateRange.end_date);

      const response = await axios.get(`${this.baseURL}/articles/${articleId}/analytics?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching article analytics ${articleId}:`, error);
      throw error;
    }
  }

  // Template Management Methods

  /**
   * Get all article templates
   */
  async getTemplates(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.skip !== undefined) params.append('skip', filters.skip);
      if (filters.limit !== undefined) params.append('limit', filters.limit);
      if (filters.article_type) params.append('article_type', filters.article_type);
      if (filters.category) params.append('category', filters.category);
      if (filters.is_active !== undefined) params.append('is_active', filters.is_active);

      const response = await axios.get(`${this.baseURL}/templates?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching article templates:', error);
      throw error;
    }
  }

  /**
   * Create a new article template
   */
  async createTemplate(templateData) {
    try {
      const response = await axios.post(`${this.baseURL}/templates`, templateData);
      return response.data;
    } catch (error) {
      console.error('Error creating article template:', error);
      throw error;
    }
  }

  /**
   * Update an existing article template
   */
  async updateTemplate(templateId, templateData) {
    try {
      const response = await axios.put(`${this.baseURL}/templates/${templateId}`, templateData);
      return response.data;
    } catch (error) {
      console.error(`Error updating article template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Delete an article template
   */
  async deleteTemplate(templateId) {
    try {
      const response = await axios.delete(`${this.baseURL}/templates/${templateId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting article template ${templateId}:`, error);
      throw error;
    }
  }

  // Dashboard and Analytics Methods

  /**
   * Get knowledge base dashboard overview
   */
  async getDashboard() {
    try {
      const response = await axios.get(`${this.baseURL}/dashboard`);
      return response.data;
    } catch (error) {
      console.error('Error fetching knowledge base dashboard:', error);
      throw error;
    }
  }

  // Bulk Operations

  /**
   * Perform bulk operations on articles
   */
  async bulkOperation(operation, itemIds, parameters = {}) {
    try {
      const response = await axios.post(`${this.baseURL}/articles/bulk`, {
        operation,
        article_ids: itemIds,
        parameters,
        confirm: true
      });
      return response.data;
    } catch (error) {
      console.error('Error performing bulk operation:', error);
      throw error;
    }
  }

  // Public Knowledge Base Methods (for customer portal)

  /**
   * Get published articles for public viewing
   */
  async getPublicArticles(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.skip !== undefined) params.append('skip', filters.skip);
      if (filters.limit !== undefined) params.append('limit', filters.limit);
      if (filters.category) params.append('category', filters.category);
      if (filters.article_type) params.append('article_type', filters.article_type);
      if (filters.difficulty_level) params.append('difficulty_level', filters.difficulty_level);
      if (filters.tags) filters.tags.forEach(tag => params.append('tags', tag));
      if (filters.search) params.append('search', filters.search);
      if (filters.sort_by) params.append('sort_by', filters.sort_by);
      if (filters.sort_order) params.append('sort_order', filters.sort_order);

      const response = await axios.get(`${this.publicURL}/articles?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching public knowledge base articles:', error);
      throw error;
    }
  }

  /**
   * Get a public article by ID
   */
  async getPublicArticle(articleId) {
    try {
      const response = await axios.get(`${this.publicURL}/articles/${articleId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching public knowledge base article ${articleId}:`, error);
      throw error;
    }
  }

  /**
   * Search public articles
   */
  async searchPublicArticles(query, filters = {}) {
    try {
      const params = new URLSearchParams();
      params.append('query', query);
      
      if (filters.category) params.append('category', filters.category);
      if (filters.article_type) params.append('article_type', filters.article_type);
      if (filters.difficulty_level) params.append('difficulty_level', filters.difficulty_level);
      if (filters.tags) filters.tags.forEach(tag => params.append('tags', tag));
      if (filters.limit !== undefined) params.append('limit', filters.limit);

      const response = await axios.get(`${this.publicURL}/search?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error searching public knowledge base articles:', error);
      throw error;
    }
  }

  /**
   * Vote on article helpfulness
   */
  async voteOnArticle(articleId, isHelpful) {
    try {
      const response = await axios.post(`${this.publicURL}/articles/${articleId}/vote`, {
        is_helpful: isHelpful
      });
      return response.data;
    } catch (error) {
      console.error(`Error voting on article ${articleId}:`, error);
      throw error;
    }
  }

  // Utility Methods

  /**
   * Get article categories
   */
  getArticleCategories() {
    return [
      {
        value: 'faq',
        label: 'FAQ',
        description: 'Frequently asked questions'
      },
      {
        value: 'tutorials',
        label: 'Tutorials',
        description: 'Step-by-step guides and tutorials'
      },
      {
        value: 'api_documentation',
        label: 'API Documentation',
        description: 'Technical API documentation'
      },
      {
        value: 'user_guides',
        label: 'User Guides',
        description: 'User manuals and guides'
      },
      {
        value: 'troubleshooting',
        label: 'Troubleshooting',
        description: 'Problem-solving guides'
      },
      {
        value: 'announcements',
        label: 'Announcements',
        description: 'Product updates and announcements'
      },
      {
        value: 'technical',
        label: 'Technical',
        description: 'Technical documentation'
      },
      {
        value: 'billing',
        label: 'Billing',
        description: 'Billing and payment information'
      },
      {
        value: 'account',
        label: 'Account',
        description: 'Account management'
      },
      {
        value: 'integrations',
        label: 'Integrations',
        description: 'Third-party integrations'
      },
      {
        value: 'features',
        label: 'Features',
        description: 'Platform features and capabilities'
      },
      {
        value: 'general',
        label: 'General',
        description: 'General information'
      }
    ];
  }

  /**
   * Get article types
   */
  getArticleTypes() {
    return [
      {
        value: 'help_article',
        label: 'Help Article',
        description: 'General help and support articles'
      },
      {
        value: 'blog_post',
        label: 'Blog Post',
        description: 'Blog posts and news articles'
      },
      {
        value: 'announcement',
        label: 'Announcement',
        description: 'Product announcements and updates'
      },
      {
        value: 'technical_doc',
        label: 'Technical Documentation',
        description: 'Technical documentation and specifications'
      },
      {
        value: 'faq',
        label: 'FAQ',
        description: 'Frequently asked questions'
      },
      {
        value: 'tutorial',
        label: 'Tutorial',
        description: 'Step-by-step tutorials'
      },
      {
        value: 'troubleshooting',
        label: 'Troubleshooting',
        description: 'Troubleshooting guides'
      },
      {
        value: 'user_guide',
        label: 'User Guide',
        description: 'User manuals and guides'
      },
      {
        value: 'api_documentation',
        label: 'API Documentation',
        description: 'API reference documentation'
      }
    ];
  }

  /**
   * Get article statuses
   */
  getArticleStatuses() {
    return [
      { value: 'draft', label: 'Draft', color: 'default' },
      { value: 'published', label: 'Published', color: 'success' },
      { value: 'archived', label: 'Archived', color: 'warning' },
      { value: 'scheduled', label: 'Scheduled', color: 'info' },
      { value: 'under_review', label: 'Under Review', color: 'primary' }
    ];
  }

  /**
   * Get difficulty levels
   */
  getDifficultyLevels() {
    return [
      { value: 'beginner', label: 'Beginner', color: 'success' },
      { value: 'intermediate', label: 'Intermediate', color: 'warning' },
      { value: 'advanced', label: 'Advanced', color: 'error' },
      { value: 'expert', label: 'Expert', color: 'primary' }
    ];
  }
}

// Create and export a singleton instance
export const knowledgeBaseService = new KnowledgeBaseService();
export default knowledgeBaseService;
