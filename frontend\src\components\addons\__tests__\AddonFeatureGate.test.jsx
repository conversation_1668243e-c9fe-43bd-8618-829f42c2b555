/**
 * Tests for AddonFeatureGate component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import AddonFeatureGate from '../AddonFeatureGate';

// Mock the hooks
vi.mock('../../hooks/useAuth', () => ({
  useAuth: vi.fn(() => ({
    user: { id: 'test-user', subscription: { plan_id: 'basic' } }
  }))
}));

vi.mock('../../hooks/useAddons', () => ({
  useAddons: vi.fn(() => ({
    checkFeatureAccess: vi.fn(),
    getAddonEnhancedLimits: vi.fn(),
    getRelevantAddons: vi.fn(),
    trackFeatureAttempt: vi.fn()
  }))
}));

// Mock antd components
vi.mock('antd', () => ({
  Button: ({ children, onClick, ...props }) => (
    <button onClick={onClick} {...props}>{children}</button>
  ),
  Alert: ({ children, message }) => <div role="alert">{message || children}</div>,
  Badge: ({ children, count }) => <span>{children} {count}</span>,
  Progress: ({ percent }) => <div data-testid="progress" data-percent={percent} />,
  Tooltip: ({ children, title }) => <div title={title}>{children}</div>,
  Modal: ({ children, open, onCancel, title }) => 
    open ? (
      <div role="dialog" aria-labelledby="modal-title">
        <h2 id="modal-title">{title}</h2>
        {children}
        <button onClick={onCancel}>Close</button>
      </div>
    ) : null
}));

describe('AddonFeatureGate', () => {
  const mockUseAddons = vi.fn();
  
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mock implementations
    mockUseAddons.mockReturnValue({
      checkFeatureAccess: vi.fn().mockResolvedValue({
        hasAccess: true,
        blockingReason: null,
        suggestedAction: null
      }),
      getAddonEnhancedLimits: vi.fn().mockResolvedValue({
        totalLimit: 100,
        addonBonus: 20,
        baseLimit: 80
      }),
      getRelevantAddons: vi.fn().mockResolvedValue([]),
      trackFeatureAttempt: vi.fn().mockResolvedValue(true)
    });
    
    const { useAddons } = require('../../hooks/useAddons');
    useAddons.mockImplementation(mockUseAddons);
  });

  test('renders children when feature is accessible', async () => {
    render(
      <AddonFeatureGate
        feature="test-feature"
        usageType="test-usage"
        baseLimit={100}
      >
        <div>Feature Content</div>
      </AddonFeatureGate>
    );

    await waitFor(() => {
      expect(screen.getByText('Feature Content')).toBeInTheDocument();
    });
  });

  test('shows blocked state when feature is not accessible', async () => {
    mockUseAddons.mockReturnValue({
      checkFeatureAccess: vi.fn().mockResolvedValue({
        hasAccess: false,
        blockingReason: 'You have reached your limit',
        suggestedAction: 'Upgrade your plan'
      }),
      getAddonEnhancedLimits: vi.fn().mockResolvedValue({
        totalLimit: 100,
        addonBonus: 0,
        baseLimit: 100
      }),
      getRelevantAddons: vi.fn().mockResolvedValue([]),
      trackFeatureAttempt: vi.fn().mockResolvedValue(false)
    });

    const { useAddons } = require('../../hooks/useAddons');
    useAddons.mockImplementation(mockUseAddons);

    render(
      <AddonFeatureGate
        feature="blocked-feature"
        usageType="blocked-usage"
        baseLimit={100}
      >
        <div>Feature Content</div>
      </AddonFeatureGate>
    );

    await waitFor(() => {
      expect(screen.getByText('Feature Limited')).toBeInTheDocument();
      expect(screen.getByText('You have reached your limit')).toBeInTheDocument();
      expect(screen.getByText('Upgrade your plan')).toBeInTheDocument();
    });

    expect(screen.queryByText('Feature Content')).not.toBeInTheDocument();
  });

  test('shows usage indicator when usage is above 50%', async () => {
    render(
      <AddonFeatureGate
        feature="high-usage-feature"
        usageType="high-usage"
        currentUsage={60}
        baseLimit={100}
      >
        <div>Feature Content</div>
      </AddonFeatureGate>
    );

    await waitFor(() => {
      expect(screen.getByText('high-usage-feature Usage')).toBeInTheDocument();
      expect(screen.getByText('60 / 100')).toBeInTheDocument();
    });
  });

  test('shows upgrade modal when upgrade button is clicked', async () => {
    const user = userEvent.setup();
    
    mockUseAddons.mockReturnValue({
      checkFeatureAccess: vi.fn().mockResolvedValue({
        hasAccess: false,
        blockingReason: 'Limit reached',
        suggestedAction: 'Upgrade Now'
      }),
      getAddonEnhancedLimits: vi.fn().mockResolvedValue({
        totalLimit: 100,
        addonBonus: 0,
        baseLimit: 100
      }),
      getRelevantAddons: vi.fn().mockResolvedValue([
        {
          id: 'test-addon',
          name: 'Test Addon',
          description: 'Test addon description',
          features: ['Feature 1', 'Feature 2'],
          pricing: { basic: { price: 9.99, credits: 100 } }
        }
      ]),
      trackFeatureAttempt: vi.fn().mockResolvedValue(false)
    });

    const { useAddons } = require('../../hooks/useAddons');
    useAddons.mockImplementation(mockUseAddons);

    render(
      <AddonFeatureGate
        feature="upgrade-feature"
        usageType="upgrade-usage"
        baseLimit={100}
      >
        <div>Feature Content</div>
      </AddonFeatureGate>
    );

    await waitFor(() => {
      expect(screen.getByText('Upgrade Now')).toBeInTheDocument();
    });

    await user.click(screen.getByText('Upgrade Now'));

    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('Upgrade Your Limits')).toBeInTheDocument();
    });
  });

  test('renders fallback component when provided and blocked', async () => {
    mockUseAddons.mockReturnValue({
      checkFeatureAccess: vi.fn().mockResolvedValue({
        hasAccess: false,
        blockingReason: 'Feature blocked',
        suggestedAction: 'Upgrade'
      }),
      getAddonEnhancedLimits: vi.fn().mockResolvedValue({
        totalLimit: 100,
        addonBonus: 0,
        baseLimit: 100
      }),
      getRelevantAddons: vi.fn().mockResolvedValue([]),
      trackFeatureAttempt: vi.fn().mockResolvedValue(false)
    });

    const { useAddons } = require('../../hooks/useAddons');
    useAddons.mockImplementation(mockUseAddons);

    render(
      <AddonFeatureGate
        feature="fallback-feature"
        usageType="fallback-usage"
        baseLimit={100}
        fallbackComponent={<div>Fallback Content</div>}
      >
        <div>Feature Content</div>
      </AddonFeatureGate>
    );

    await waitFor(() => {
      expect(screen.getByText('Fallback Content')).toBeInTheDocument();
    });
  });

  test('shows loading state initially', () => {
    render(
      <AddonFeatureGate
        feature="loading-feature"
        usageType="loading-usage"
        baseLimit={100}
      >
        <div>Feature Content</div>
      </AddonFeatureGate>
    );

    expect(screen.getByText('Loading feature access...')).toBeInTheDocument();
  });

  test('handles addon bonus display correctly', async () => {
    mockUseAddons.mockReturnValue({
      checkFeatureAccess: vi.fn().mockResolvedValue({
        hasAccess: true,
        blockingReason: null,
        suggestedAction: null
      }),
      getAddonEnhancedLimits: vi.fn().mockResolvedValue({
        totalLimit: 120,
        addonBonus: 20,
        baseLimit: 100
      }),
      getRelevantAddons: vi.fn().mockResolvedValue([]),
      trackFeatureAttempt: vi.fn().mockResolvedValue(true)
    });

    const { useAddons } = require('../../hooks/useAddons');
    useAddons.mockImplementation(mockUseAddons);

    render(
      <AddonFeatureGate
        feature="bonus-feature"
        usageType="bonus-usage"
        currentUsage={80}
        baseLimit={100}
      >
        <div>Feature Content</div>
      </AddonFeatureGate>
    );

    await waitFor(() => {
      expect(screen.getByText('80 / 120')).toBeInTheDocument();
      expect(screen.getByText('+20')).toBeInTheDocument();
    });
  });
});
