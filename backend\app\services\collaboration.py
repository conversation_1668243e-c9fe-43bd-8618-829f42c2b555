# @since 2024-1-1 to 2025-25-7
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from bson import ObjectId

from app.core.config import settings

from app.db.mongodb import get_database
from app.db.repositories.collaboration import (
    create_shared_calendar,
    get_shared_calendar_by_id,
    get_shared_calendars_by_user,
    update_shared_calendar,
    delete_shared_calendar,
    create_collaborator_access,
    get_collaborator_access_by_token,
    create_review_comment,
    get_review_comments_by_content,
    create_or_update_review_status,
    get_review_status_by_content
)
from app.db.repositories.content import get_content_by_id
from app.db.repositories.campaign import get_campaign_by_id
from app.models.collaboration import SharedCalendar, CollaboratorAccess, ReviewComment, ReviewStatus
from app.schemas.collaboration import (
    SharedCalendarUpdate,
    ReviewCommentCreate,
    ReviewStatusCreate,
    ShareCalendarRequest
)
from app.services.email_service import send_calendar_invitation_email
from app.models.notification_rule import NotificationTriggerType

async def share_calendar(request: ShareCalendarRequest, user_id: str) -> Dict[str, Any]:
    """
    Share a calendar with collaborators.
    """
    # Create shared calendar
    calendar_data = {
        "name": request.calendar_name,
        "description": request.description,
        "content_ids": request.content_ids,
        "campaign_ids": request.campaign_ids,
        "requires_approval": request.requires_approval,
        "expiration_date": request.expiration_date
    }

    shared_calendar = await create_shared_calendar(user_id, calendar_data)

    # Create collaborator access for each collaborator
    collaborator_accesses = []
    for collaborator in request.collaborators:
        collaborator_data = {
            "name": collaborator["name"],
            "email": collaborator["email"]
        }

        access = await create_collaborator_access(str(shared_calendar.id), collaborator_data)
        collaborator_accesses.append(access)

        # Send email invitation
        # Get the user's information to use as sender
        db = await get_database()
        user = await db.users.find_one({"_id": ObjectId(user_id)})

        sender_name = user.get("full_name", "Calendar Owner") if user else "Calendar Owner"
        sender_email = user.get("email", "<EMAIL>") if user else "<EMAIL>"

        # Create calendar event details for the invitation
        event_start = datetime.now(timezone.utc)
        event_end = event_start.replace(hour=23, minute=59, second=59)  # End of day

        # Prepare event details dictionary
        event_details = {
            "title": f"Calendar Review: {request.calendar_name}",
            "description": request.description or f"Review shared calendar: {request.calendar_name}",
            "date": event_start.strftime("%B %d, %Y"),
            "time": f"{event_start.strftime('%I:%M %p')} - {event_end.strftime('%I:%M %p')}",
            "calendar_link": f"{settings.FRONTEND_URL}/collaboration/calendar/{access.access_token}"
        }

        await send_calendar_invitation_email(
            email=collaborator["email"],
            name=collaborator["name"],
            event_details=event_details
        )

    return {
        "shared_calendar": shared_calendar,
        "collaborator_accesses": collaborator_accesses
    }

async def get_user_shared_calendars(user_id: str) -> List[SharedCalendar]:
    """
    Get all shared calendars for a user.
    """
    return await get_shared_calendars_by_user(user_id)

async def get_calendar_by_id(calendar_id: str) -> Optional[SharedCalendar]:
    """
    Get a shared calendar by ID.
    """
    return await get_shared_calendar_by_id(calendar_id)

async def update_calendar(calendar_id: str, update_data: SharedCalendarUpdate) -> Optional[SharedCalendar]:
    """
    Update a shared calendar.
    """
    return await update_shared_calendar(calendar_id, update_data.model_dump(exclude_unset=True))

async def delete_calendar(calendar_id: str) -> bool:
    """
    Delete a shared calendar.
    """
    return await delete_shared_calendar(calendar_id)

async def access_shared_calendar(access_token: str, user_data: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
    """
    Access a shared calendar using an access token.
    """
    # Get collaborator access
    collaborator_access = await get_collaborator_access_by_token(access_token)
    if not collaborator_access:
        return {"error": "Invalid access token"}

    # Update collaborator info if provided
    if user_data and user_data.get("name") and user_data.get("email"):
        # In a real implementation, you would update the collaborator info here
        pass

    # Get shared calendar by ID (not token)
    shared_calendar_id = str(collaborator_access.shared_calendar_id)
    shared_calendar = await get_shared_calendar_by_id(shared_calendar_id)
    if not shared_calendar:
        return {"error": "Shared calendar not found"}

    # Check if calendar is expired
    if shared_calendar.expiration_date and shared_calendar.expiration_date < datetime.now(timezone.utc):
        return {
            "error": "This shared calendar has expired."
        }

    # Get contents
    contents = []
    for content_id in shared_calendar.content_ids:
        content = await get_content_by_id(str(content_id))
        if content:
            # Get review status for this content
            review_status = await get_review_status_by_content(str(content_id))

            # Add review status to content
            content_dict = content.model_dump()
            content_dict["review_status"] = review_status.model_dump() if review_status else {"status": "pending"}

            contents.append(content_dict)

    # Get campaigns
    campaigns = []
    for campaign_id in shared_calendar.campaign_ids:
        campaign = await get_campaign_by_id(str(campaign_id))
        if campaign:
            campaigns.append(campaign.model_dump())

    return {
        "calendar": shared_calendar,
        "contents": contents,
        "campaigns": campaigns,
        "collaborator": collaborator_access
    }

async def add_review_comment(comment_data: ReviewCommentCreate) -> ReviewComment:
    """
    Add a review comment to a content and trigger notifications.
    """
    # Create the comment
    comment = await create_review_comment(comment_data.model_dump())

    # Get content details for notification
    db = await get_database()
    content = await db.contents.find_one({"_id": ObjectId(comment_data.content_id)})

    if content:
        # Get content owner for notification
        user_id = content.get("user_id")
        content_title = content.get("title", "Content")

        if user_id:
            # Import here to avoid circular imports
            from app.services.notification_rule import notification_rule_service

            # Prepare event data
            event_data = {
                "content_id": str(comment_data.content_id),
                "content_title": content_title,
                "reviewer_name": comment_data.reviewer_name,
                "reviewer_email": comment_data.reviewer_email,
                "comment": comment_data.comment,
                "entity_id": str(comment_data.content_id),
                "entity_type": "content",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

            # Process event for notification rules
            await notification_rule_service.process_event(
                user_id=str(user_id),
                trigger_type=NotificationTriggerType.CLIENT_COMMENT,
                event_data=event_data
            )

            # Also create a default notification if no rules are set up
            from app.services.notification import notify_client_comment

            # Use the convenience function for client comment notifications
            await notify_client_comment(
                user_id=user_id,
                content_title=content_title,
                reviewer_name=comment_data.reviewer_name,
                reviewer_email=comment_data.reviewer_email,
                comment=comment_data.comment,
                content_id=str(comment_data.content_id)
            )

    return comment

async def get_content_review_comments(content_id: str) -> List[ReviewComment]:
    """
    Get all review comments for a content.
    """
    return await get_review_comments_by_content(content_id)

async def update_content_review_status(status_data: ReviewStatusCreate) -> ReviewStatus:
    """
    Update the review status of a content and trigger notifications.
    """
    # Update the status
    status = await create_or_update_review_status(status_data.model_dump())

    # Get content details for notification
    db = await get_database()
    content = await db.contents.find_one({"_id": ObjectId(status_data.content_id)})

    if content:
        # Get content owner for notification
        user_id = content.get("user_id")
        content_title = content.get("title", "Content")

        if user_id:
            # Import here to avoid circular imports
            from app.services.notification_rule import notification_rule_service

            # Determine trigger type based on status
            trigger_type = NotificationTriggerType.CLIENT_APPROVAL if status_data.status == "approved" else NotificationTriggerType.CLIENT_REJECTION

            # Prepare event data
            event_data = {
                "content_id": str(status_data.content_id),
                "content_title": content_title,
                "reviewer_name": status_data.reviewer_name,
                "reviewer_email": status_data.reviewer_email,
                "status": status_data.status,
                "comments": status_data.comments,
                "entity_id": str(status_data.content_id),
                "entity_type": "content",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

            # Process event for notification rules
            await notification_rule_service.process_event(
                user_id=str(user_id),
                trigger_type=trigger_type,
                event_data=event_data
            )

            # Also create a default notification if no rules are set up
            from app.services.notification import notify_content_review

            # Use the convenience function for content review notifications
            if status_data.reviewer_name:  # Only proceed if reviewer_name is not None
                await notify_content_review(
                    user_id=user_id,
                    content_title=content_title,
                    reviewer_name=status_data.reviewer_name,
                    status=status_data.status,
                    content_id=str(status_data.content_id),
                    comment=status_data.comments[0] if status_data.comments else None
                )

    return status

async def verify_access_token(access_token: str) -> Optional[CollaboratorAccess]:
    """
    Verify a calendar access token and return the collaborator access.
    """
    return await get_collaborator_access_by_token(access_token)

async def get_shared_calendar_by_token(share_token: str) -> Optional[SharedCalendar]:
    """
    Get a shared calendar by share token.
    """
    # Import the repository function with the same name
    from app.db.repositories.collaboration import get_shared_calendar_by_token as repo_get_shared_calendar_by_token

    # Call the repository function
    return await repo_get_shared_calendar_by_token(share_token)

async def check_content_in_calendar(calendar_id: str, content_id: str) -> bool:
    """
    Check if a content is in a shared calendar.
    """
    # Get shared calendar
    shared_calendar = await get_shared_calendar_by_id(calendar_id)
    if not shared_calendar:
        return False

    # Check if content is in calendar
    return any(str(cid) == content_id for cid in shared_calendar.content_ids)
