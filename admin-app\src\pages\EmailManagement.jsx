// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  Tabs,
  Tab,
  Alert,
  Snackbar,
  Fade,
  IconButton,
  Tooltip,
  CircularProgress,
  Chip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Skeleton
} from '@mui/material';
import {
  Add as AddIcon,
  Email as EmailIcon,
  Campaign as CampaignIcon,
  Dashboard as DashboardIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Preview as PreviewIcon,
  FileCopy as DuplicateIcon,
  Send as SendIcon,
  Analytics as AnalyticsIcon,
  Trigger as TriggerIcon,
  CloudOff as OfflineIcon
} from '@mui/icons-material';

import StablePageWrapper from '../components/StablePageWrapper';
import ErrorBoundary from '../components/common/ErrorBoundary';
import EmailTemplateList from '../components/email/EmailTemplateList';
import EmailTemplateEditor from '../components/email/EmailTemplateEditor';
import EmailCampaignList from '../components/email/EmailCampaignList';
import EmailCampaignEditor from '../components/email/EmailCampaignEditor';
import EmailTriggerList from '../components/email/EmailTriggerList';
import EmailManagementDashboard from '../components/email/EmailManagementDashboard';
import { useEmailTemplateEnhancedData } from '../hooks/useEmailTemplateEnhancedData';

const EmailManagement = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [selectedItem, setSelectedItem] = useState(null);
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [editorMode, setEditorMode] = useState('create'); // 'create', 'edit', 'duplicate'
  const [editorType, setEditorType] = useState('template'); // 'template', 'campaign', 'trigger'
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [isRetrying, setIsRetrying] = useState(false);
  const [showOfflineMessage, setShowOfflineMessage] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);

  const {
    templates,
    campaigns,
    triggers,
    dashboard,
    loading,
    error,
    fetchTemplates,
    fetchCampaigns,
    fetchTriggers,
    fetchDashboard,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    duplicateTemplate,
    createCampaign,
    updateCampaign,
    deleteCampaign,
    createTrigger,
    updateTrigger,
    deleteTrigger,
    isOnline
  } = useEmailTemplateEnhancedData();

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    setShowOfflineMessage(!isOnline);
  }, [isOnline]);

  const loadData = async () => {
    try {
      await Promise.all([
        fetchTemplates(),
        fetchCampaigns(),
        fetchTriggers(),
        fetchDashboard()
      ]);
    } catch (error) {
      console.error('Error loading email management data:', error);
      showSnackbar('Failed to load email management data', 'error');
    }
  };

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const handleCreateTemplate = () => {
    setSelectedItem(null);
    setEditorMode('create');
    setEditorType('template');
    setIsEditorOpen(true);
  };

  const handleCreateCampaign = () => {
    setSelectedItem(null);
    setEditorMode('create');
    setEditorType('campaign');
    setIsEditorOpen(true);
  };

  const handleCreateTrigger = () => {
    setSelectedItem(null);
    setEditorMode('create');
    setEditorType('trigger');
    setIsEditorOpen(true);
  };

  const handleEditItem = (item, type) => {
    setSelectedItem(item);
    setEditorMode('edit');
    setEditorType(type);
    setIsEditorOpen(true);
  };

  const handleDuplicateTemplate = (template) => {
    setSelectedItem(template);
    setEditorMode('duplicate');
    setEditorType('template');
    setIsEditorOpen(true);
  };

  const handleDeleteItem = async (itemId, type) => {
    const itemName = type === 'template' ? 'template' : type === 'campaign' ? 'campaign' : 'trigger';
    
    if (window.confirm(`Are you sure you want to delete this ${itemName}? This action cannot be undone.`)) {
      try {
        let success = false;
        
        if (type === 'template') {
          success = await deleteTemplate(itemId);
        } else if (type === 'campaign') {
          success = await deleteCampaign(itemId);
        } else if (type === 'trigger') {
          success = await deleteTrigger(itemId);
        }
        
        if (success) {
          showSnackbar(`${itemName.charAt(0).toUpperCase() + itemName.slice(1)} deleted successfully`, 'success');
          await loadData();
        }
      } catch (error) {
        console.error(`Error deleting ${itemName}:`, error);
        showSnackbar(`Failed to delete ${itemName}`, 'error');
      }
    }
  };

  const handleEditorSubmit = async (itemData) => {
    try {
      let result = null;
      
      if (editorType === 'template') {
        if (editorMode === 'create') {
          result = await createTemplate(itemData);
          showSnackbar('Email template created successfully', 'success');
        } else if (editorMode === 'edit') {
          result = await updateTemplate(selectedItem.id, itemData);
          showSnackbar('Email template updated successfully', 'success');
        } else if (editorMode === 'duplicate') {
          result = await duplicateTemplate(selectedItem.id, itemData);
          showSnackbar('Email template duplicated successfully', 'success');
        }
      } else if (editorType === 'campaign') {
        if (editorMode === 'create') {
          result = await createCampaign(itemData);
          showSnackbar('Email campaign created successfully', 'success');
        } else if (editorMode === 'edit') {
          result = await updateCampaign(selectedItem.id, itemData);
          showSnackbar('Email campaign updated successfully', 'success');
        }
      } else if (editorType === 'trigger') {
        if (editorMode === 'create') {
          result = await createTrigger(itemData);
          showSnackbar('Email trigger created successfully', 'success');
        } else if (editorMode === 'edit') {
          result = await updateTrigger(selectedItem.id, itemData);
          showSnackbar('Email trigger updated successfully', 'success');
        }
      }
      
      setIsEditorOpen(false);
      setSelectedItem(null);
      await loadData();
    } catch (error) {
      console.error('Error saving item:', error);
      showSnackbar(
        `Failed to ${editorMode} ${editorType}`, 
        'error'
      );
    }
  };

  const handleRetry = async () => {
    setIsRetrying(true);
    try {
      await loadData();
      showSnackbar('Data refreshed successfully', 'success');
    } catch (error) {
      showSnackbar('Failed to refresh data', 'error');
    } finally {
      setIsRetrying(false);
    }
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const tabLabels = [
    { label: 'Dashboard', icon: <DashboardIcon /> },
    { label: 'Templates', icon: <EmailIcon /> },
    { label: 'Campaigns', icon: <CampaignIcon /> },
    { label: 'Triggers', icon: <TriggerIcon /> },
    { label: 'Analytics', icon: <AnalyticsIcon /> }
  ];

  const getCreateButton = () => {
    switch (currentTab) {
      case 1: // Templates
        return (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateTemplate}
          >
            Create Template
          </Button>
        );
      case 2: // Campaigns
        return (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateCampaign}
          >
            Create Campaign
          </Button>
        );
      case 3: // Triggers
        return (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateTrigger}
          >
            Create Trigger
          </Button>
        );
      default:
        return null;
    }
  };

  if (loading && !templates.length && !campaigns.length && !dashboard) {
    return (
      <StablePageWrapper>
        <Box sx={{ p: 3 }}>
          <Skeleton variant="text" width={300} height={40} />
          <Skeleton variant="rectangular" width="100%" height={200} sx={{ mt: 2 }} />
          <Grid container spacing={3} sx={{ mt: 2 }}>
            {[1, 2, 3].map((item) => (
              <Grid item xs={12} md={4} key={item}>
                <Skeleton variant="rectangular" height={150} />
              </Grid>
            ))}
          </Grid>
        </Box>
      </StablePageWrapper>
    );
  }

  return (
    <ErrorBoundary>
      <StablePageWrapper>
        <Box sx={{ p: 3 }}>
          {/* Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Box>
              <Typography variant="h4" component="h1" gutterBottom>
                Email Management
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Manage email templates, campaigns, triggers, and monitor performance
              </Typography>
            </Box>
            
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="Refresh Data">
                <IconButton 
                  onClick={handleRetry} 
                  disabled={isRetrying}
                  color="primary"
                >
                  {isRetrying ? <CircularProgress size={24} /> : <RefreshIcon />}
                </IconButton>
              </Tooltip>
              
              {getCreateButton()}
            </Box>
          </Box>

          {/* Offline Message */}
          <Fade in={showOfflineMessage}>
            <Alert 
              severity="warning" 
              icon={<OfflineIcon />}
              sx={{ mb: 2 }}
              action={
                <Button color="inherit" size="small" onClick={handleRetry}>
                  Retry
                </Button>
              }
            >
              You're currently offline. Some features may not be available.
            </Alert>
          </Fade>

          {/* Error Message */}
          {error && (
            <Alert 
              severity="error" 
              sx={{ mb: 2 }}
              action={
                <Button color="inherit" size="small" onClick={handleRetry}>
                  Retry
                </Button>
              }
            >
              {error}
            </Alert>
          )}

          {/* Tabs */}
          <Card sx={{ mb: 3 }}>
            <Tabs
              value={currentTab}
              onChange={handleTabChange}
              variant="fullWidth"
              sx={{ borderBottom: 1, borderColor: 'divider' }}
            >
              {tabLabels.map((tab, index) => (
                <Tab
                  key={index}
                  icon={tab.icon}
                  label={tab.label}
                  iconPosition="start"
                />
              ))}
            </Tabs>
          </Card>

          {/* Tab Content */}
          <Box sx={{ mt: 3 }}>
            {currentTab === 0 && (
              <EmailManagementDashboard
                dashboard={dashboard}
                loading={loading}
                onRefresh={loadData}
              />
            )}
            
            {currentTab === 1 && (
              <EmailTemplateList
                templates={templates}
                loading={loading}
                onEdit={(template) => handleEditItem(template, 'template')}
                onDelete={(templateId) => handleDeleteItem(templateId, 'template')}
                onDuplicate={handleDuplicateTemplate}
                onRefresh={loadData}
              />
            )}
            
            {currentTab === 2 && (
              <EmailCampaignList
                campaigns={campaigns}
                templates={templates}
                loading={loading}
                onEdit={(campaign) => handleEditItem(campaign, 'campaign')}
                onDelete={(campaignId) => handleDeleteItem(campaignId, 'campaign')}
                onRefresh={loadData}
              />
            )}
            
            {currentTab === 3 && (
              <EmailTriggerList
                triggers={triggers}
                templates={templates}
                loading={loading}
                onEdit={(trigger) => handleEditItem(trigger, 'trigger')}
                onDelete={(triggerId) => handleDeleteItem(triggerId, 'trigger')}
                onRefresh={loadData}
              />
            )}
            
            {currentTab === 4 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Email Analytics
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Comprehensive email performance analytics will be available here.
                </Typography>
              </Box>
            )}
          </Box>

          {/* Editor Dialogs */}
          {editorType === 'template' && (
            <EmailTemplateEditor
              open={isEditorOpen}
              mode={editorMode}
              template={selectedItem}
              onClose={() => {
                setIsEditorOpen(false);
                setSelectedItem(null);
              }}
              onSubmit={handleEditorSubmit}
            />
          )}

          {editorType === 'campaign' && (
            <EmailCampaignEditor
              open={isEditorOpen}
              mode={editorMode}
              campaign={selectedItem}
              templates={templates}
              onClose={() => {
                setIsEditorOpen(false);
                setSelectedItem(null);
              }}
              onSubmit={handleEditorSubmit}
            />
          )}

          {/* Snackbar */}
          <Snackbar
            open={snackbar.open}
            autoHideDuration={6000}
            onClose={handleCloseSnackbar}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          >
            <Alert 
              onClose={handleCloseSnackbar} 
              severity={snackbar.severity}
              variant="filled"
            >
              {snackbar.message}
            </Alert>
          </Snackbar>
        </Box>
      </StablePageWrapper>
    </ErrorBoundary>
  );
};

export default EmailManagement;
