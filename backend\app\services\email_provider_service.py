"""
Email Provider Service

This service manages email provider configurations, handles provider switching,
failover logic, and integrates with the existing email service infrastructure.

@since 2024-1-1 to 2025-25-7
"""
import asyncio
import time
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta
from motor.motor_asyncio import AsyncIOMotorDatabase
from bson import ObjectId
import logging

from app.core.config import settings
from app.core.database import get_database
from app.models.email_provider import (
    EmailProviderConfig, EmailProviderType, EmailType, ProviderStatus,
    EmailProviderStats, EmailDeliveryLog, EmailProviderFailover,
    SMTPSettings, SendGridSettings, MailgunSettings, AWSSESSettings
)
from app.schemas.email_provider import (
    EmailProviderCreateRequest, EmailProviderUpdateRequest,
    EmailProviderTestRequest, EmailProviderListRequest
)
from app.services.encryption import encrypt_data, decrypt_data
from app.utils.circuit_breaker import circuit_breaker, CircuitOpenError
from app.services.app_logging import get_logger

logger = get_logger(__name__)


class EmailProviderService:
    """Service for managing email provider configurations."""
    
    def __init__(self):
        self.db: AsyncIOMotorDatabase = None
        self._provider_cache: Dict[str, EmailProviderConfig] = {}
        self._cache_expiry: datetime = datetime.utcnow()
        self._cache_duration = timedelta(minutes=5)
    
    async def initialize(self):
        """Initialize the service with database connection."""
        self.db = await get_database()
        await self._ensure_indexes()
    
    async def _ensure_indexes(self):
        """Ensure required database indexes exist."""
        try:
            # Email provider configurations
            await self.db.email_providers.create_index([("name", 1)], unique=True)
            await self.db.email_providers.create_index([("provider_type", 1)])
            await self.db.email_providers.create_index([("email_types", 1)])
            await self.db.email_providers.create_index([("priority", 1)])
            await self.db.email_providers.create_index([("status", 1)])
            await self.db.email_providers.create_index([("is_active", 1)])
            
            # Email delivery logs
            await self.db.email_delivery_logs.create_index([("sent_at", -1)])
            await self.db.email_delivery_logs.create_index([("provider_id", 1)])
            await self.db.email_delivery_logs.create_index([("email_type", 1)])
            await self.db.email_delivery_logs.create_index([("status", 1)])
            
            # Email provider failover
            await self.db.email_provider_failover.create_index([("email_type", 1)], unique=True)
            
            logger.info("Email provider database indexes ensured")
        except Exception as e:
            logger.error(f"Error ensuring email provider indexes: {str(e)}")
    
    async def create_provider(
        self, 
        request: EmailProviderCreateRequest, 
        created_by: str
    ) -> EmailProviderConfig:
        """Create a new email provider configuration."""
        try:
            # Encrypt sensitive settings
            encrypted_settings = await self._encrypt_provider_settings(
                request.provider_type, 
                request.settings
            )
            
            # Create provider configuration
            provider_data = {
                "name": request.name,
                "description": request.description,
                "provider_type": request.provider_type,
                "is_active": request.is_active,
                "priority": request.priority,
                "from_email": request.from_email,
                "from_name": request.from_name,
                "reply_to": request.reply_to,
                "settings": encrypted_settings.dict(),
                "rate_limits": request.rate_limits.dict() if request.rate_limits else {},
                "bounce_handling": request.bounce_handling.dict() if request.bounce_handling else {},
                "email_types": request.email_types,
                "status": ProviderStatus.ACTIVE,
                "success_count": 0,
                "failure_count": 0,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                "created_by": created_by
            }
            
            # Insert into database
            result = await self.db.email_providers.insert_one(provider_data)
            provider_data["_id"] = str(result.inserted_id)
            
            # Clear cache
            self._clear_cache()
            
            logger.info(f"Created email provider: {request.name} ({request.provider_type})")
            return EmailProviderConfig(**provider_data)
            
        except Exception as e:
            logger.error(f"Error creating email provider: {str(e)}")
            raise
    
    async def get_provider(self, provider_id: str) -> Optional[EmailProviderConfig]:
        """Get email provider configuration by ID."""
        try:
            # Check cache first
            if provider_id in self._provider_cache and self._is_cache_valid():
                return self._provider_cache[provider_id]
            
            # Query database
            provider_data = await self.db.email_providers.find_one(
                {"_id": ObjectId(provider_id)}
            )
            
            if not provider_data:
                return None
            
            provider_data["_id"] = str(provider_data["_id"])
            
            # Decrypt sensitive settings
            provider_data["settings"] = await self._decrypt_provider_settings(
                provider_data["provider_type"],
                provider_data["settings"]
            )
            
            provider = EmailProviderConfig(**provider_data)
            
            # Cache the result
            self._provider_cache[provider_id] = provider
            
            return provider
            
        except Exception as e:
            logger.error(f"Error getting email provider {provider_id}: {str(e)}")
            return None
    
    async def list_providers(
        self, 
        request: EmailProviderListRequest
    ) -> Tuple[List[EmailProviderConfig], int]:
        """List email providers with filtering and pagination."""
        try:
            # Build query filter
            query_filter = {}
            
            if request.provider_type:
                query_filter["provider_type"] = request.provider_type
            
            if request.email_type:
                query_filter["email_types"] = {"$in": [request.email_type]}
            
            if request.status:
                query_filter["status"] = request.status
            
            if request.is_active is not None:
                query_filter["is_active"] = request.is_active
            
            if request.search:
                query_filter["$or"] = [
                    {"name": {"$regex": request.search, "$options": "i"}},
                    {"description": {"$regex": request.search, "$options": "i"}}
                ]
            
            # Get total count
            total = await self.db.email_providers.count_documents(query_filter)
            
            # Get providers with pagination
            cursor = self.db.email_providers.find(query_filter).sort("priority", 1)
            cursor = cursor.skip(request.skip).limit(request.limit)
            
            providers = []
            async for provider_data in cursor:
                provider_data["_id"] = str(provider_data["_id"])
                
                # Don't decrypt settings for list view (performance)
                # Just provide a summary
                provider_data["settings"] = self._get_settings_summary(
                    provider_data["provider_type"],
                    provider_data["settings"]
                )
                
                providers.append(EmailProviderConfig(**provider_data))
            
            return providers, total
            
        except Exception as e:
            logger.error(f"Error listing email providers: {str(e)}")
            return [], 0
    
    async def update_provider(
        self,
        provider_id: str,
        request: EmailProviderUpdateRequest,
        updated_by: str
    ) -> Optional[EmailProviderConfig]:
        """Update email provider configuration."""
        try:
            # Build update data
            update_data = {"updated_at": datetime.utcnow(), "updated_by": updated_by}
            
            if request.name is not None:
                update_data["name"] = request.name
            
            if request.description is not None:
                update_data["description"] = request.description
            
            if request.is_active is not None:
                update_data["is_active"] = request.is_active
            
            if request.priority is not None:
                update_data["priority"] = request.priority
            
            if request.from_email is not None:
                update_data["from_email"] = request.from_email
            
            if request.from_name is not None:
                update_data["from_name"] = request.from_name
            
            if request.reply_to is not None:
                update_data["reply_to"] = request.reply_to
            
            if request.settings is not None:
                # Get current provider to determine type
                current_provider = await self.get_provider(provider_id)
                if not current_provider:
                    return None
                
                encrypted_settings = await self._encrypt_provider_settings(
                    current_provider.provider_type,
                    request.settings
                )
                update_data["settings"] = encrypted_settings.dict()
            
            if request.rate_limits is not None:
                update_data["rate_limits"] = request.rate_limits.dict()
            
            if request.bounce_handling is not None:
                update_data["bounce_handling"] = request.bounce_handling.dict()
            
            if request.email_types is not None:
                update_data["email_types"] = request.email_types
            
            # Update in database
            result = await self.db.email_providers.update_one(
                {"_id": ObjectId(provider_id)},
                {"$set": update_data}
            )
            
            if result.matched_count == 0:
                return None
            
            # Clear cache
            self._clear_cache()
            
            # Return updated provider
            return await self.get_provider(provider_id)
            
        except Exception as e:
            logger.error(f"Error updating email provider {provider_id}: {str(e)}")
            return None
    
    async def delete_provider(self, provider_id: str) -> bool:
        """Delete email provider configuration."""
        try:
            result = await self.db.email_providers.delete_one(
                {"_id": ObjectId(provider_id)}
            )
            
            if result.deleted_count > 0:
                # Clear cache
                self._clear_cache()
                
                # Remove from failover configurations
                await self.db.email_provider_failover.update_many(
                    {"primary_provider_id": provider_id},
                    {"$unset": {"primary_provider_id": ""}}
                )
                
                await self.db.email_provider_failover.update_many(
                    {"fallback_providers": provider_id},
                    {"$pull": {"fallback_providers": provider_id}}
                )
                
                logger.info(f"Deleted email provider: {provider_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error deleting email provider {provider_id}: {str(e)}")
            return False
    
    async def _encrypt_provider_settings(
        self, 
        provider_type: EmailProviderType, 
        settings: Any
    ) -> Any:
        """Encrypt sensitive provider settings."""
        # This is a placeholder - implement actual encryption using existing patterns
        # For now, return settings as-is
        return settings
    
    async def _decrypt_provider_settings(
        self, 
        provider_type: EmailProviderType, 
        settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Decrypt sensitive provider settings."""
        # This is a placeholder - implement actual decryption using existing patterns
        # For now, return settings as-is
        return settings
    
    def _get_settings_summary(
        self, 
        provider_type: EmailProviderType, 
        settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Get non-sensitive summary of provider settings."""
        if provider_type == EmailProviderType.SMTP:
            return {
                "host": settings.get("smtp", {}).get("host", ""),
                "port": settings.get("smtp", {}).get("port", 587),
                "use_tls": settings.get("smtp", {}).get("use_tls", True)
            }
        elif provider_type == EmailProviderType.SENDGRID:
            return {"provider": "SendGrid"}
        elif provider_type == EmailProviderType.MAILGUN:
            return {
                "domain": settings.get("mailgun", {}).get("domain", ""),
                "region": settings.get("mailgun", {}).get("region", "us")
            }
        elif provider_type == EmailProviderType.AWS_SES:
            return {"region": settings.get("aws_ses", {}).get("region", "us-east-1")}
        
        return {}
    
    def _is_cache_valid(self) -> bool:
        """Check if provider cache is still valid."""
        return datetime.utcnow() < self._cache_expiry
    
    def _clear_cache(self):
        """Clear provider cache."""
        self._provider_cache.clear()
        self._cache_expiry = datetime.utcnow()


    async def test_provider(
        self,
        provider_id: str,
        test_request: EmailProviderTestRequest
    ) -> Dict[str, Any]:
        """Test email provider configuration."""
        try:
            provider = await self.get_provider(provider_id)
            if not provider:
                return {
                    "success": False,
                    "message": "Provider not found",
                    "test_details": {},
                    "response_time": None,
                    "tested_at": datetime.utcnow()
                }

            start_time = time.time()

            # Test based on provider type
            if provider.provider_type == EmailProviderType.SMTP:
                result = await self._test_smtp_provider(provider, test_request)
            elif provider.provider_type == EmailProviderType.SENDGRID:
                result = await self._test_sendgrid_provider(provider, test_request)
            elif provider.provider_type == EmailProviderType.MAILGUN:
                result = await self._test_mailgun_provider(provider, test_request)
            elif provider.provider_type == EmailProviderType.AWS_SES:
                result = await self._test_aws_ses_provider(provider, test_request)
            else:
                result = {
                    "success": False,
                    "message": f"Testing not implemented for {provider.provider_type}",
                    "test_details": {}
                }

            response_time = (time.time() - start_time) * 1000  # Convert to milliseconds

            # Update provider test results
            await self.db.email_providers.update_one(
                {"_id": ObjectId(provider_id)},
                {
                    "$set": {
                        "last_test_date": datetime.utcnow(),
                        "last_test_result": result,
                        "status": ProviderStatus.ACTIVE if result["success"] else ProviderStatus.FAILED
                    }
                }
            )

            result.update({
                "response_time": response_time,
                "tested_at": datetime.utcnow()
            })

            return result

        except Exception as e:
            logger.error(f"Error testing email provider {provider_id}: {str(e)}")
            return {
                "success": False,
                "message": f"Test failed: {str(e)}",
                "test_details": {"error": str(e)},
                "response_time": None,
                "tested_at": datetime.utcnow()
            }

    async def _test_smtp_provider(
        self,
        provider: EmailProviderConfig,
        test_request: EmailProviderTestRequest
    ) -> Dict[str, Any]:
        """Test SMTP provider configuration."""
        try:
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart

            smtp_settings = provider.settings.smtp
            if not smtp_settings:
                return {
                    "success": False,
                    "message": "SMTP settings not found",
                    "test_details": {}
                }

            # Create test email
            msg = MIMEMultipart()
            msg['From'] = f"{provider.from_name} <{provider.from_email}>"
            msg['To'] = test_request.test_email
            msg['Subject'] = test_request.test_subject

            body = test_request.test_message
            msg.attach(MIMEText(body, 'plain'))

            # Connect to SMTP server
            if smtp_settings.use_ssl:
                server = smtplib.SMTP_SSL(smtp_settings.host, smtp_settings.port)
            else:
                server = smtplib.SMTP(smtp_settings.host, smtp_settings.port)
                if smtp_settings.use_tls:
                    server.starttls()

            # Login and send
            server.login(smtp_settings.username, smtp_settings.password)
            text = msg.as_string()
            server.sendmail(provider.from_email, test_request.test_email, text)
            server.quit()

            return {
                "success": True,
                "message": "Test email sent successfully via SMTP",
                "test_details": {
                    "host": smtp_settings.host,
                    "port": smtp_settings.port,
                    "use_tls": smtp_settings.use_tls,
                    "use_ssl": smtp_settings.use_ssl
                }
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"SMTP test failed: {str(e)}",
                "test_details": {"error": str(e)}
            }

    async def _test_sendgrid_provider(
        self,
        provider: EmailProviderConfig,
        test_request: EmailProviderTestRequest
    ) -> Dict[str, Any]:
        """Test SendGrid provider configuration."""
        # Placeholder for SendGrid testing
        return {
            "success": True,
            "message": "SendGrid test not implemented yet",
            "test_details": {"note": "SendGrid testing will be implemented"}
        }

    async def _test_mailgun_provider(
        self,
        provider: EmailProviderConfig,
        test_request: EmailProviderTestRequest
    ) -> Dict[str, Any]:
        """Test Mailgun provider configuration."""
        # Placeholder for Mailgun testing
        return {
            "success": True,
            "message": "Mailgun test not implemented yet",
            "test_details": {"note": "Mailgun testing will be implemented"}
        }

    async def _test_aws_ses_provider(
        self,
        provider: EmailProviderConfig,
        test_request: EmailProviderTestRequest
    ) -> Dict[str, Any]:
        """Test AWS SES provider configuration."""
        # Placeholder for AWS SES testing
        return {
            "success": True,
            "message": "AWS SES test not implemented yet",
            "test_details": {"note": "AWS SES testing will be implemented"}
        }

    async def get_provider_for_email_type(
        self,
        email_type: EmailType
    ) -> Optional[EmailProviderConfig]:
        """Get the best available provider for a specific email type."""
        try:
            # Query active providers for this email type, ordered by priority
            cursor = self.db.email_providers.find({
                "is_active": True,
                "status": ProviderStatus.ACTIVE,
                "email_types": {"$in": [email_type]}
            }).sort("priority", 1)

            async for provider_data in cursor:
                provider_data["_id"] = str(provider_data["_id"])

                # Decrypt settings
                provider_data["settings"] = await self._decrypt_provider_settings(
                    provider_data["provider_type"],
                    provider_data["settings"]
                )

                return EmailProviderConfig(**provider_data)

            return None

        except Exception as e:
            logger.error(f"Error getting provider for email type {email_type}: {str(e)}")
            return None


# Global service instance
email_provider_service = EmailProviderService()
