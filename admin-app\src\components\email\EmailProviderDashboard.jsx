// @since 2024-1-1 to 2025-25-7
import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  LinearProgress,
  Chip,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Email as EmailIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TestTube as TestIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

const EmailProviderDashboard = ({ 
  dashboard, 
  loading = false, 
  onRefresh, 
  onTestProvider 
}) => {
  const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`;
  };

  const formatNumber = (value) => {
    return value.toLocaleString();
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return <SuccessIcon color="success" />;
      case 'failed':
        return <ErrorIcon color="error" />;
      case 'testing':
        return <WarningIcon color="warning" />;
      default:
        return <WarningIcon color="warning" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'failed':
        return 'error';
      case 'testing':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getHealthColor = (isHealthy) => {
    return isHealthy ? 'success' : 'error';
  };

  if (loading && !dashboard) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!dashboard) {
    return (
      <Alert severity="info">
        No dashboard data available. Please check your email provider configurations.
      </Alert>
    );
  }

  return (
    <Box>
      {/* Overview Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <EmailIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Total Providers
                </Typography>
              </Box>
              <Typography variant="h3" component="div" color="primary">
                {dashboard.total_providers || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {dashboard.active_providers || 0} active, {dashboard.failed_providers || 0} failed
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TrendingUpIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Emails Sent Today
                </Typography>
              </Box>
              <Typography variant="h3" component="div" color="success.main">
                {formatNumber(dashboard.total_emails_sent_today || 0)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {dashboard.total_emails_failed_today || 0} failed
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <SuccessIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Delivery Rate
                </Typography>
              </Box>
              <Typography variant="h3" component="div" color="success.main">
                {formatPercentage(dashboard.overall_delivery_rate || 0)}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={dashboard.overall_delivery_rate || 0}
                color="success"
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TrendingDownIcon color="warning" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Bounce Rate
                </Typography>
              </Box>
              <Typography variant="h3" component="div" color="warning.main">
                {formatPercentage(dashboard.overall_bounce_rate || 0)}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={dashboard.overall_bounce_rate || 0}
                color="warning"
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Provider Health Status */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6" component="div">
              Provider Health Status
            </Typography>
            <Tooltip title="Refresh Data">
              <IconButton onClick={onRefresh} disabled={loading}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>

          {dashboard.providers && dashboard.providers.length > 0 ? (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Provider</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Health</TableCell>
                    <TableCell>Last Check</TableCell>
                    <TableCell>Uptime</TableCell>
                    <TableCell>Response Time</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {dashboard.providers.map((provider) => (
                    <TableRow key={provider.provider_id} hover>
                      <TableCell>
                        <Typography variant="subtitle2" fontWeight="medium">
                          {provider.provider_name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          ID: {provider.provider_id}
                        </Typography>
                      </TableCell>
                      
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {getStatusIcon(provider.status)}
                          <Chip
                            label={provider.status}
                            color={getStatusColor(provider.status)}
                            size="small"
                            variant="outlined"
                          />
                        </Box>
                      </TableCell>
                      
                      <TableCell>
                        <Chip
                          label={provider.is_healthy ? 'Healthy' : 'Unhealthy'}
                          color={getHealthColor(provider.is_healthy)}
                          size="small"
                          variant={provider.is_healthy ? 'filled' : 'outlined'}
                        />
                      </TableCell>
                      
                      <TableCell>
                        <Typography variant="body2">
                          {provider.last_check 
                            ? new Date(provider.last_check).toLocaleString()
                            : 'Never'
                          }
                        </Typography>
                      </TableCell>
                      
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body2">
                            {formatPercentage(provider.uptime_percentage || 0)}
                          </Typography>
                          <LinearProgress
                            variant="determinate"
                            value={provider.uptime_percentage || 0}
                            color={provider.uptime_percentage >= 95 ? 'success' : 'warning'}
                            sx={{ width: 60, height: 4 }}
                          />
                        </Box>
                      </TableCell>
                      
                      <TableCell>
                        <Typography variant="body2">
                          {provider.response_time 
                            ? `${provider.response_time.toFixed(0)}ms`
                            : 'N/A'
                          }
                        </Typography>
                      </TableCell>
                      
                      <TableCell align="right">
                        <Tooltip title="Test Provider">
                          <IconButton
                            size="small"
                            onClick={() => onTestProvider && onTestProvider(provider)}
                          >
                            <TestIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Alert severity="info">
              No email providers configured yet. Add your first provider to see health status.
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Performance Insights */}
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" component="div" gutterBottom>
                Performance Insights
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Overall System Health
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={dashboard.active_providers / Math.max(dashboard.total_providers, 1) * 100}
                  color="success"
                  sx={{ mt: 1, height: 8, borderRadius: 4 }}
                />
                <Typography variant="caption" color="text.secondary">
                  {dashboard.active_providers} of {dashboard.total_providers} providers active
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Email Success Rate
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={dashboard.overall_delivery_rate || 0}
                  color="primary"
                  sx={{ mt: 1, height: 8, borderRadius: 4 }}
                />
                <Typography variant="caption" color="text.secondary">
                  {formatPercentage(dashboard.overall_delivery_rate || 0)} delivery success
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" component="div" gutterBottom>
                Quick Actions
              </Typography>
              
              <Alert severity="info" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  Monitor your email providers regularly to ensure optimal delivery performance.
                </Typography>
              </Alert>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Typography variant="body2">
                  • Test all providers monthly
                </Typography>
                <Typography variant="body2">
                  • Monitor bounce rates below 5%
                </Typography>
                <Typography variant="body2">
                  • Keep delivery rates above 95%
                </Typography>
                <Typography variant="body2">
                  • Configure failover for critical emails
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default EmailProviderDashboard;
