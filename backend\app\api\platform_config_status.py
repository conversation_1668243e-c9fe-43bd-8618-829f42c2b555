"""
Platform Configuration Status API
Provides status endpoint for fallback mode when WebSockets are not available
@since 2024-1-1 to 2025-25-7
"""

from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import JSONResponse
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/platform-config", tags=["platform-config"])

# Simple in-memory cache for status
_status_cache = {
    "lastUpdate": datetime.utcnow().isoformat(),
    "platforms": {
        "facebook": {"status": "active", "lastCheck": datetime.utcnow().isoformat()},
        "twitter": {"status": "active", "lastCheck": datetime.utcnow().isoformat()},
        "linkedin": {"status": "active", "lastCheck": datetime.utcnow().isoformat()},
        "instagram": {"status": "active", "lastCheck": datetime.utcnow().isoformat()}
    },
    "configuration": {
        "version": "1.0.0",
        "features": {
            "realTimeUpdates": False,  # Disabled in fallback mode
            "webSocketSupport": False,
            "fallbackMode": True
        }
    }
}

@router.get("/status")
async def get_platform_status(request: Request):
    """
    Get platform configuration status for fallback mode
    
    This endpoint is used by the frontend fallback service when WebSockets
    are not available (e.g., on serverless platforms like Vercel).
    """
    try:
        # Check if this is a fallback mode request
        is_fallback = request.headers.get("X-Fallback-Mode") == "true"
        
        if is_fallback:
            logger.info("Serving platform status for fallback mode")
        
        # Update timestamp
        _status_cache["lastUpdate"] = datetime.utcnow().isoformat()
        
        # Add request metadata
        response_data = {
            **_status_cache,
            "requestTime": datetime.utcnow().isoformat(),
            "fallbackMode": is_fallback,
            "serverTime": datetime.utcnow().isoformat()
        }
        
        return JSONResponse(
            content=response_data,
            headers={
                "Cache-Control": "no-cache, no-store, must-revalidate",
                "Pragma": "no-cache",
                "Expires": "0",
                "X-Fallback-Response": "true" if is_fallback else "false"
            }
        )
        
    except Exception as e:
        logger.error(f"Error serving platform status: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve platform status"
        )

@router.get("/health")
async def platform_health_check():
    """
    Simple health check endpoint
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "platform-config"
    }

def update_platform_status(platform: str, status: str):
    """
    Update platform status (called by other services)
    """
    if platform in _status_cache["platforms"]:
        _status_cache["platforms"][platform]["status"] = status
        _status_cache["platforms"][platform]["lastCheck"] = datetime.utcnow().isoformat()
        _status_cache["lastUpdate"] = datetime.utcnow().isoformat()
        logger.info(f"Updated {platform} status to {status}")

def get_current_status():
    """
    Get current status (for internal use)
    """
    return _status_cache.copy()
