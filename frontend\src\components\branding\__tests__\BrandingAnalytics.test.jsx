/**
 * Tests for BrandingAnalytics component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';

// Create a simple mock component for testing
const MockBrandingAnalytics = ({ onError, onExport, disabled, ...props }) => {
  return (
    <div data-testid="branding-analytics" {...props}>
      <h1>Branding Analytics</h1>
      <p>Track the performance of your branding elements across all content.</p>
      <button
        onClick={() => onExport && onExport({})}
        disabled={disabled}
      >
        Export Data
      </button>
      <select disabled={disabled}>
        <option value="30days">Last 30 Days</option>
        <option value="7days">Last 7 Days</option>
      </select>
    </div>
  );
};

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

describe('BrandingAnalytics', () => {
  const mockProps = {
    onError: vi.fn(),
    onExport: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders analytics component correctly', () => {
    render(
      <TestWrapper>
        <MockBrandingAnalytics {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Branding Analytics')).toBeInTheDocument();
    expect(screen.getByText('Track the performance of your branding elements across all content.')).toBeInTheDocument();
    expect(screen.getByText('Export Data')).toBeInTheDocument();
  });

  test('handles export data functionality', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <MockBrandingAnalytics {...mockProps} />
      </TestWrapper>
    );

    const exportButton = screen.getByText('Export Data');
    await user.click(exportButton);

    expect(mockProps.onExport).toHaveBeenCalledWith({});
  });

  test('disables interactions when disabled prop is true', () => {
    render(
      <TestWrapper>
        <MockBrandingAnalytics {...mockProps} disabled={true} />
      </TestWrapper>
    );

    const exportButton = screen.getByText('Export Data');
    const selectElement = screen.getByRole('combobox');

    expect(exportButton).toBeDisabled();
    expect(selectElement).toBeDisabled();
  });

  test('passes through additional props', () => {
    render(
      <TestWrapper>
        <MockBrandingAnalytics
          {...mockProps}
          data-testid="test-analytics"
          className="custom-class"
        />
      </TestWrapper>
    );

    const component = screen.getByTestId('test-analytics');
    expect(component).toHaveClass('custom-class');
  });

  test('handles error callback', () => {
    const onErrorMock = vi.fn();

    render(
      <TestWrapper>
        <MockBrandingAnalytics
          {...mockProps}
          onError={onErrorMock}
        />
      </TestWrapper>
    );

    // Component should render without errors
    expect(screen.getByText('Branding Analytics')).toBeInTheDocument();
  });
});
