"""
Comprehensive tests for Google OAuth 2.0 integration.

Tests cover:
- OAuth flow initiation and callback handling
- Security measures (PKCE, state validation, CSRF protection)
- Rate limiting and audit logging
- User creation and authentication
- Error handling and edge cases
- Production readiness verification

@since 2024-1-1 to 2025-25-7
"""
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import Request
import secrets
import base64
import hashlib
from datetime import datetime, timezone, timedelta

from app.main import app
from app.services.social_media.google import GoogleIntegration
from app.models.user import User, SocialMediaAccount
from app.core.config import settings


class TestGoogleOAuthIntegration:
    """Test suite for Google OAuth integration service."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.google_integration = GoogleIntegration()
        self.test_redirect_uri = "http://localhost:3000/auth/google/callback"
        
    @pytest.mark.asyncio
    async def test_get_authorization_url_success(self):
        """Test successful authorization URL generation with PKCE."""
        # Mock settings
        with patch.object(settings, 'GOOGLE_CLIENT_ID', 'test_client_id'):
            auth_url, state = await self.google_integration.get_authorization_url(
                self.test_redirect_uri
            )
            
            # Verify URL structure
            assert "accounts.google.com/o/oauth2/v2/auth" in auth_url
            assert "client_id=test_client_id" in auth_url
            assert "redirect_uri=" in auth_url
            assert "code_challenge=" in auth_url
            assert "code_challenge_method=S256" in auth_url
            assert "scope=" in auth_url
            
            # Verify state contains code verifier
            assert ":" in state
            state_part, code_verifier = state.rsplit(":", 1)
            assert len(state_part) >= 32  # Secure state parameter
            assert len(code_verifier) >= 32  # Secure code verifier
    
    @pytest.mark.asyncio
    async def test_get_authorization_url_missing_client_id(self):
        """Test authorization URL generation fails without client ID."""
        with patch.object(settings, 'GOOGLE_CLIENT_ID', None):
            with pytest.raises(ValueError, match="Google OAuth client ID not configured"):
                await self.google_integration.get_authorization_url(self.test_redirect_uri)
    
    @pytest.mark.asyncio
    async def test_handle_oauth_callback_success(self):
        """Test successful OAuth callback handling."""
        # Generate test data
        code = "test_authorization_code"
        state = "test_state"
        code_verifier = secrets.token_urlsafe(64)
        state_with_verifier = f"{state}:{code_verifier}"
        
        # Mock HTTP responses
        mock_token_response = Mock()
        mock_token_response.status_code = 200
        mock_token_response.json.return_value = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "expires_in": 3600
        }
        
        mock_user_response = Mock()
        mock_user_response.status_code = 200
        mock_user_response.json.return_value = {
            "id": "*********",
            "email": "<EMAIL>",
            "name": "Test User",
            "given_name": "Test",
            "family_name": "User",
            "picture": "https://example.com/avatar.jpg",
            "verified_email": True
        }
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_client.return_value.__aenter__.return_value.post.return_value = mock_token_response
            mock_client.return_value.__aenter__.return_value.get.return_value = mock_user_response
            
            account = await self.google_integration.handle_oauth_callback(
                code, state_with_verifier, self.test_redirect_uri
            )
            
            # Verify account creation
            assert isinstance(account, SocialMediaAccount)
            assert account.platform == "google"
            assert account.account_id == "*********"
            assert account.account_name == "Test User"
            assert account.access_token == "test_access_token"
            assert account.refresh_token == "test_refresh_token"
            assert account.token_expires_at is not None
    
    @pytest.mark.asyncio
    async def test_handle_oauth_callback_invalid_state(self):
        """Test OAuth callback fails with invalid state parameter."""
        code = "test_authorization_code"
        invalid_state = "invalid_state_without_verifier"
        
        with pytest.raises(ValueError, match="Invalid state parameter"):
            await self.google_integration.handle_oauth_callback(
                code, invalid_state, self.test_redirect_uri
            )
    
    @pytest.mark.asyncio
    async def test_refresh_access_token_success(self):
        """Test successful token refresh."""
        # Create test account
        account = SocialMediaAccount(
            platform="google",
            account_id="*********",
            account_name="Test User",
            access_token="old_access_token",
            refresh_token="test_refresh_token",
            token_expires_at=datetime.now(timezone.utc) - timedelta(hours=1),
            is_encrypted=False
        )
        
        # Mock refresh response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "access_token": "new_access_token",
            "expires_in": 3600
        }
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_client.return_value.__aenter__.return_value.post.return_value = mock_response
            
            updated_account = await self.google_integration.refresh_access_token(account)
            
            # Verify token update
            assert updated_account.access_token == "new_access_token"
            assert updated_account.token_expires_at > datetime.now(timezone.utc)
    
    @pytest.mark.asyncio
    async def test_get_account_info_success(self):
        """Test successful account info retrieval."""
        account = SocialMediaAccount(
            platform="google",
            account_id="*********",
            account_name="Test User",
            access_token="test_access_token",
            token_expires_at=datetime.now(timezone.utc) + timedelta(hours=1),
            is_encrypted=False
        )
        
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "id": "*********",
            "email": "<EMAIL>",
            "name": "Test User",
            "picture": "https://example.com/avatar.jpg",
            "verified_email": True
        }
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
            
            user_info = await self.google_integration.get_account_info(account)
            
            # Verify user info
            assert user_info["id"] == "*********"
            assert user_info["email"] == "<EMAIL>"
            assert user_info["name"] == "Test User"
            assert user_info["platform"] == "google"


class TestGoogleOAuthRoutes:
    """Test suite for Google OAuth API routes."""
    
    def setup_method(self):
        """Set up test client."""
        self.client = TestClient(app)
    
    def test_google_authorize_endpoint(self):
        """Test Google OAuth authorization endpoint."""
        with patch.object(settings, 'GOOGLE_CLIENT_ID', 'test_client_id'):
            response = self.client.get("/api/auth/google/authorize")
            
            assert response.status_code == 200
            data = response.json()
            
            assert "authorization_url" in data
            assert "state" in data
            assert "redirect_uri" in data
            assert "accounts.google.com" in data["authorization_url"]
    
    def test_google_authorize_rate_limiting(self):
        """Test rate limiting on Google OAuth authorization endpoint."""
        with patch.object(settings, 'GOOGLE_CLIENT_ID', 'test_client_id'):
            # Make multiple requests to trigger rate limiting
            for _ in range(12):  # Exceed the 10 requests per 5 minutes limit
                response = self.client.get("/api/auth/google/authorize")
                
            # Should eventually get rate limited
            assert response.status_code == 429
    
    def test_google_callback_missing_parameters(self):
        """Test Google OAuth callback with missing parameters."""
        response = self.client.get("/api/auth/google/callback")
        
        # Should fail due to missing required parameters
        assert response.status_code == 422  # Validation error
    
    def test_google_callback_with_error(self):
        """Test Google OAuth callback with error parameter."""
        response = self.client.get(
            "/api/auth/google/callback?error=access_denied&state=test_state"
        )
        
        assert response.status_code == 400
        assert "access_denied" in response.json()["detail"]


class TestGoogleOAuthSecurity:
    """Test suite for Google OAuth security measures."""
    
    @pytest.mark.asyncio
    async def test_pkce_implementation(self):
        """Test PKCE (Proof Key for Code Exchange) implementation."""
        google_integration = GoogleIntegration()
        
        with patch.object(settings, 'GOOGLE_CLIENT_ID', 'test_client_id'):
            auth_url, state = await google_integration.get_authorization_url(
                "http://localhost:3000/callback"
            )
            
            # Extract code challenge from URL
            assert "code_challenge=" in auth_url
            assert "code_challenge_method=S256" in auth_url
            
            # Verify state contains code verifier
            state_part, code_verifier = state.rsplit(":", 1)
            
            # Verify code challenge is properly generated
            expected_challenge = base64.urlsafe_b64encode(
                hashlib.sha256(code_verifier.encode()).digest()
            ).decode().rstrip("=")
            
            assert f"code_challenge={expected_challenge}" in auth_url
    
    def test_state_parameter_validation(self):
        """Test state parameter validation for CSRF protection."""
        google_integration = GoogleIntegration()
        
        # Test with invalid state (no code verifier)
        with pytest.raises(ValueError, match="Invalid state parameter"):
            asyncio.run(google_integration.handle_oauth_callback(
                "test_code", "invalid_state", "http://localhost:3000/callback"
            ))
        
        # Test with empty code verifier
        with pytest.raises(ValueError, match="Invalid code verifier"):
            asyncio.run(google_integration.handle_oauth_callback(
                "test_code", "state:", "http://localhost:3000/callback"
            ))


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
