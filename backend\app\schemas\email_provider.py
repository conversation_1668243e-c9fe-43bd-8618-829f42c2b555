"""
Email Provider Configuration Schemas

This module defines the Pydantic schemas for email provider configuration API requests and responses.

@since 2024-1-1 to 2025-25-7
"""
from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field, validator
from app.models.email_provider import (
    EmailProviderType, EmailType, ProviderStatus,
    RateLimits, BounceHandling, EmailProviderSettings,
    SMTPSettings, SendGridSettings, MailgunSettings, AWSSESSettings
)


# Request Schemas

class EmailProviderCreateRequest(BaseModel):
    """Schema for creating a new email provider configuration."""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(default="", max_length=500)
    provider_type: EmailProviderType
    is_active: bool = Field(default=True)
    priority: int = Field(default=1, ge=1, le=100)
    
    from_email: str = Field(..., pattern=r'^[^@]+@[^@]+\.[^@]+$')
    from_name: str = Field(..., min_length=1, max_length=100)
    reply_to: Optional[str] = Field(default=None, pattern=r'^[^@]+@[^@]+\.[^@]+$')
    
    settings: EmailProviderSettings
    rate_limits: Optional[RateLimits] = None
    bounce_handling: Optional[BounceHandling] = None
    email_types: List[EmailType] = Field(default_factory=lambda: [EmailType.TRANSACTIONAL])


class EmailProviderUpdateRequest(BaseModel):
    """Schema for updating an email provider configuration."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    is_active: Optional[bool] = None
    priority: Optional[int] = Field(None, ge=1, le=100)
    
    from_email: Optional[str] = Field(None, pattern=r'^[^@]+@[^@]+\.[^@]+$')
    from_name: Optional[str] = Field(None, min_length=1, max_length=100)
    reply_to: Optional[str] = Field(None, pattern=r'^[^@]+@[^@]+\.[^@]+$')
    
    settings: Optional[EmailProviderSettings] = None
    rate_limits: Optional[RateLimits] = None
    bounce_handling: Optional[BounceHandling] = None
    email_types: Optional[List[EmailType]] = None


class EmailProviderTestRequest(BaseModel):
    """Schema for testing email provider configuration."""
    test_email: str = Field(..., pattern=r'^[^@]+@[^@]+\.[^@]+$')
    test_subject: str = Field(default="ACE Social Email Configuration Test")
    test_message: str = Field(default="This is a test email to verify your email provider configuration.")


class EmailProviderListRequest(BaseModel):
    """Schema for listing email providers with filters."""
    skip: int = Field(default=0, ge=0)
    limit: int = Field(default=20, ge=1, le=100)
    provider_type: Optional[EmailProviderType] = None
    email_type: Optional[EmailType] = None
    status: Optional[ProviderStatus] = None
    is_active: Optional[bool] = None
    search: Optional[str] = Field(None, max_length=255)


class EmailProviderStatsRequest(BaseModel):
    """Schema for requesting email provider statistics."""
    provider_ids: Optional[List[str]] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    email_type: Optional[EmailType] = None


# Response Schemas

class EmailProviderResponse(BaseModel):
    """Schema for email provider configuration response."""
    id: str
    name: str
    description: str
    provider_type: EmailProviderType
    is_active: bool
    priority: int
    
    from_email: str
    from_name: str
    reply_to: Optional[str]
    
    # Settings without sensitive data
    settings_summary: Dict[str, Any]
    rate_limits: RateLimits
    bounce_handling: BounceHandling
    email_types: List[EmailType]
    
    status: ProviderStatus
    last_test_date: Optional[datetime]
    last_test_result: Optional[Dict[str, Any]]
    
    success_count: int
    failure_count: int
    last_success_date: Optional[datetime]
    last_failure_date: Optional[datetime]
    
    created_at: datetime
    updated_at: datetime
    created_by: str
    updated_by: Optional[str]


class EmailProviderListResponse(BaseModel):
    """Schema for email provider list response."""
    providers: List[EmailProviderResponse]
    total: int
    skip: int
    limit: int


class EmailProviderTestResponse(BaseModel):
    """Schema for email provider test response."""
    success: bool
    message: str
    test_details: Dict[str, Any]
    response_time: Optional[float]  # in milliseconds
    tested_at: datetime


class EmailProviderStatsResponse(BaseModel):
    """Schema for email provider statistics response."""
    provider_id: str
    provider_name: str
    total_sent: int
    total_delivered: int
    total_bounced: int
    total_failed: int
    bounce_rate: float
    delivery_rate: float
    last_24h_sent: int
    last_24h_failed: int
    avg_response_time: float
    status: ProviderStatus
    last_updated: datetime


class EmailProviderHealthResponse(BaseModel):
    """Schema for email provider health check response."""
    provider_id: str
    provider_name: str
    status: ProviderStatus
    is_healthy: bool
    last_check: datetime
    response_time: Optional[float]
    error_message: Optional[str]
    uptime_percentage: float


class EmailProviderDashboardResponse(BaseModel):
    """Schema for email provider dashboard overview."""
    total_providers: int
    active_providers: int
    failed_providers: int
    total_emails_sent_today: int
    total_emails_failed_today: int
    overall_delivery_rate: float
    overall_bounce_rate: float
    providers: List[EmailProviderHealthResponse]


class EmailDeliveryLogResponse(BaseModel):
    """Schema for email delivery log response."""
    id: str
    provider_id: str
    provider_name: str
    email_type: EmailType
    recipient_email: str
    subject: str
    template_name: str
    status: str
    response_time: Optional[float]
    error_message: Optional[str]
    external_id: Optional[str]
    sent_at: datetime
    delivered_at: Optional[datetime]


class EmailDeliveryLogListResponse(BaseModel):
    """Schema for email delivery log list response."""
    logs: List[EmailDeliveryLogResponse]
    total: int
    skip: int
    limit: int


class EmailProviderFailoverResponse(BaseModel):
    """Schema for email provider failover configuration response."""
    id: str
    email_type: EmailType
    primary_provider_id: str
    primary_provider_name: str
    fallback_providers: List[Dict[str, str]]  # [{"id": "...", "name": "..."}]
    max_retries: int
    retry_delay: int
    circuit_breaker_threshold: int
    circuit_breaker_timeout: int
    is_active: bool
    created_at: datetime
    updated_at: datetime


class EmailProviderFailoverCreateRequest(BaseModel):
    """Schema for creating email provider failover configuration."""
    email_type: EmailType
    primary_provider_id: str
    fallback_providers: List[str]
    max_retries: int = Field(default=3, ge=1, le=10)
    retry_delay: int = Field(default=300, ge=60, le=3600)
    circuit_breaker_threshold: int = Field(default=5, ge=1, le=50)
    circuit_breaker_timeout: int = Field(default=300, ge=60, le=3600)
    is_active: bool = Field(default=True)


class EmailProviderFailoverUpdateRequest(BaseModel):
    """Schema for updating email provider failover configuration."""
    primary_provider_id: Optional[str] = None
    fallback_providers: Optional[List[str]] = None
    max_retries: Optional[int] = Field(None, ge=1, le=10)
    retry_delay: Optional[int] = Field(None, ge=60, le=3600)
    circuit_breaker_threshold: Optional[int] = Field(None, ge=1, le=50)
    circuit_breaker_timeout: Optional[int] = Field(None, ge=60, le=3600)
    is_active: Optional[bool] = None


# Utility Schemas

class EmailProviderValidationResponse(BaseModel):
    """Schema for email provider configuration validation."""
    is_valid: bool
    validation_errors: List[str]
    warnings: List[str]
    suggestions: List[str]


class EmailProviderImportRequest(BaseModel):
    """Schema for importing email provider configurations."""
    providers: List[EmailProviderCreateRequest]
    overwrite_existing: bool = Field(default=False)
    validate_only: bool = Field(default=False)


class EmailProviderImportResponse(BaseModel):
    """Schema for email provider import response."""
    imported_count: int
    skipped_count: int
    error_count: int
    errors: List[Dict[str, str]]
    imported_providers: List[str]  # Provider IDs


class EmailProviderExportResponse(BaseModel):
    """Schema for email provider export response."""
    providers: List[Dict[str, Any]]  # Sanitized provider configurations
    exported_count: int
    exported_at: datetime
