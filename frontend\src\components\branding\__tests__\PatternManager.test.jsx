// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import PatternManager from '../PatternManager';

// Mock the notification hook
const mockShowSuccessNotification = vi.fn();
const mockShowErrorNotification = vi.fn();

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showSuccessNotification: mockShowSuccessNotification,
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn(() => 'mock-pattern-url');

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('PatternManager', () => {
  const mockVisualElements = {
    patterns: [
      {
        id: 'pattern-1',
        name: 'Geometric',
        url: 'https://example.com/geometric.png',
        type: 'geometric',
        scale: 100,
        opacity: 30
      },
      {
        id: 'pattern-2',
        name: 'Dots',
        url: 'https://example.com/dots.png',
        type: 'dots',
        scale: 120,
        opacity: 50
      }
    ]
  };

  const mockProps = {
    visualElements: mockVisualElements,
    onChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders pattern manager correctly', () => {
    render(
      <TestWrapper>
        <PatternManager {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Pattern Library')).toBeInTheDocument();
    expect(screen.getByText(/Create and manage patterns that can be applied/)).toBeInTheDocument();
  });

  test('displays upload pattern button', () => {
    render(
      <TestWrapper>
        <PatternManager {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Upload Pattern')).toBeInTheDocument();
  });

  test('shows sample patterns in library', () => {
    render(
      <TestWrapper>
        <PatternManager {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Pattern Library')).toBeInTheDocument();
    // Sample patterns should be visible
    expect(screen.getAllByRole('button').length).toBeGreaterThan(0);
  });

  test('displays AI pattern generator', () => {
    render(
      <TestWrapper>
        <PatternManager {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('AI Pattern Generator')).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/e.g., Abstract geometric pattern/)).toBeInTheDocument();
    expect(screen.getByText('Generate')).toBeInTheDocument();
  });

  test('shows existing patterns', () => {
    render(
      <TestWrapper>
        <PatternManager {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Your Patterns')).toBeInTheDocument();
    expect(screen.getByText('Geometric')).toBeInTheDocument();
    expect(screen.getByText('Dots')).toBeInTheDocument();
  });

  test('handles pattern upload', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PatternManager {...mockProps} />
      </TestWrapper>
    );

    const file = new File(['pattern content'], 'pattern.png', { type: 'image/png' });
    const fileInput = screen.getByRole('button', { name: /upload pattern/i }).querySelector('input[type="file"]');
    
    await user.upload(fileInput, file);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualElements,
        patterns: expect.arrayContaining([
          expect.objectContaining({
            name: 'pattern',
            url: 'mock-pattern-url'
          })
        ])
      });
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Pattern uploaded successfully');
    });
  });

  test('handles adding sample pattern', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PatternManager {...mockProps} />
      </TestWrapper>
    );

    // Find and click on a sample pattern add button
    const addButtons = screen.getAllByRole('button');
    const samplePatternButton = addButtons.find(button => 
      button.querySelector('[data-testid="AddIcon"]')
    );
    
    if (samplePatternButton) {
      await user.click(samplePatternButton);

      await waitFor(() => {
        expect(mockProps.onChange).toHaveBeenCalled();
        expect(mockShowSuccessNotification).toHaveBeenCalledWith('Pattern added to your collection');
      });
    }
  });

  test('handles AI pattern generation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PatternManager {...mockProps} />
      </TestWrapper>
    );

    const textInput = screen.getByPlaceholderText(/e.g., Abstract geometric pattern/);
    const generateButton = screen.getByText('Generate');

    await user.type(textInput, 'Custom AI pattern');
    await user.click(generateButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualElements,
        patterns: expect.arrayContaining([
          expect.objectContaining({
            name: 'Custom AI pattern',
            type: 'ai-generated'
          })
        ])
      });
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('AI pattern generated');
    });
  });

  test('handles pattern selection', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PatternManager {...mockProps} />
      </TestWrapper>
    );

    // Click on an existing pattern
    const patternElement = screen.getByText('Geometric').closest('[role="img"]') || 
                          screen.getByText('Geometric').closest('div');
    
    if (patternElement) {
      await user.click(patternElement);

      // Should show pattern settings
      await waitFor(() => {
        expect(screen.getByText(/Pattern Settings:/)).toBeInTheDocument();
      });
    }
  });

  test('handles pattern removal', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PatternManager {...mockProps} />
      </TestWrapper>
    );

    // Find delete button for a pattern
    const deleteButtons = screen.getAllByRole('button');
    const deleteButton = deleteButtons.find(button => 
      button.querySelector('[data-testid="DeleteIcon"]')
    );
    
    if (deleteButton) {
      await user.click(deleteButton);

      await waitFor(() => {
        expect(mockProps.onChange).toHaveBeenCalledWith({
          ...mockVisualElements,
          patterns: expect.arrayContaining([
            expect.not.objectContaining({
              id: expect.any(String)
            })
          ])
        });
        expect(mockShowSuccessNotification).toHaveBeenCalledWith('Pattern removed');
      });
    }
  });

  test('handles pattern scale adjustment', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PatternManager {...mockProps} />
      </TestWrapper>
    );

    // First select a pattern to show settings
    const patternElement = screen.getByText('Geometric').closest('div');
    if (patternElement) {
      await user.click(patternElement);

      await waitFor(() => {
        const scaleSlider = screen.getByRole('slider', { name: /scale/i });
        if (scaleSlider) {
          fireEvent.change(scaleSlider, { target: { value: '150' } });
          
          const applyButton = screen.getByText('Apply Changes');
          user.click(applyButton);

          expect(mockProps.onChange).toHaveBeenCalled();
          expect(mockShowSuccessNotification).toHaveBeenCalledWith('Pattern updated');
        }
      });
    }
  });

  test('handles pattern opacity adjustment', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PatternManager {...mockProps} />
      </TestWrapper>
    );

    // First select a pattern to show settings
    const patternElement = screen.getByText('Geometric').closest('div');
    if (patternElement) {
      await user.click(patternElement);

      await waitFor(() => {
        const opacitySlider = screen.getByRole('slider', { name: /opacity/i });
        if (opacitySlider) {
          fireEvent.change(opacitySlider, { target: { value: '75' } });
          
          const applyButton = screen.getByText('Apply Changes');
          user.click(applyButton);

          expect(mockProps.onChange).toHaveBeenCalled();
          expect(mockShowSuccessNotification).toHaveBeenCalledWith('Pattern updated');
        }
      });
    }
  });

  test('prevents adding duplicate sample patterns', async () => {
    const user = userEvent.setup();
    
    // Mock props with existing sample pattern
    const propsWithExistingPattern = {
      ...mockProps,
      visualElements: {
        patterns: [
          {
            id: 'pattern-1',
            name: 'Geometric',
            url: 'https://www.transparenttextures.com/patterns/45-degree-fabric-light.png',
            type: 'geometric'
          }
        ]
      }
    };
    
    render(
      <TestWrapper>
        <PatternManager {...propsWithExistingPattern} />
      </TestWrapper>
    );

    // Try to add the same pattern again
    const addButtons = screen.getAllByRole('button');
    const samplePatternButton = addButtons.find(button => 
      button.querySelector('[data-testid="AddIcon"]')
    );
    
    if (samplePatternButton) {
      await user.click(samplePatternButton);

      await waitFor(() => {
        expect(mockShowErrorNotification).toHaveBeenCalledWith('This pattern is already in your collection');
      });
    }
  });

  test('shows empty state when no patterns exist', () => {
    const propsWithoutPatterns = {
      ...mockProps,
      visualElements: {
        patterns: []
      }
    };

    render(
      <TestWrapper>
        <PatternManager {...propsWithoutPatterns} />
      </TestWrapper>
    );

    expect(screen.getByText(/No patterns in your collection yet/)).toBeInTheDocument();
  });

  test('disables generate button when no pattern name is provided', () => {
    render(
      <TestWrapper>
        <PatternManager {...mockProps} />
      </TestWrapper>
    );

    const generateButton = screen.getByText('Generate');
    expect(generateButton).toBeDisabled();
  });

  test('enables generate button when pattern name is provided', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PatternManager {...mockProps} />
      </TestWrapper>
    );

    const textInput = screen.getByPlaceholderText(/e.g., Abstract geometric pattern/);
    const generateButton = screen.getByText('Generate');

    await user.type(textInput, 'Test pattern');

    expect(generateButton).not.toBeDisabled();
  });

  test('renders with default props when no visualElements provided', () => {
    render(
      <TestWrapper>
        <PatternManager onChange={vi.fn()} />
      </TestWrapper>
    );

    expect(screen.getByText('Pattern Library')).toBeInTheDocument();
    expect(screen.getByText(/No patterns in your collection yet/)).toBeInTheDocument();
  });

  test('handles file upload error gracefully', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PatternManager {...mockProps} />
      </TestWrapper>
    );

    // Simulate upload without file
    const fileInput = screen.getByRole('button', { name: /upload pattern/i }).querySelector('input[type="file"]');
    fireEvent.change(fileInput, { target: { files: [] } });

    // Should not call onChange or show success notification
    expect(mockProps.onChange).not.toHaveBeenCalled();
    expect(mockShowSuccessNotification).not.toHaveBeenCalled();
  });
});
