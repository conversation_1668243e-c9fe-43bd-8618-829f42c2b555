// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  Tabs,
  Tab,
  Alert,
  Snackbar,
  Fade,
  IconButton,
  Tooltip,
  CircularProgress,
  Chip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Skeleton
} from '@mui/material';
import {
  Add as AddIcon,
  Article as ArticleIcon,
  Dashboard as DashboardIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Preview as PreviewIcon,
  FileCopy as DuplicateIcon,
  Publish as PublishIcon,
  Analytics as AnalyticsIcon,
  Template as TemplateIcon,
  Search as SearchIcon,
  CloudOff as OfflineIcon,
  Category as CategoryIcon
} from '@mui/icons-material';

import StablePageWrapper from '../components/StablePageWrapper';
import ErrorBoundary from '../components/common/ErrorBoundary';
import KnowledgeBaseArticleList from '../components/knowledge-base/KnowledgeBaseArticleList';
import KnowledgeBaseArticleEditor from '../components/knowledge-base/KnowledgeBaseArticleEditor';
import KnowledgeBaseTemplateList from '../components/knowledge-base/KnowledgeBaseTemplateList';
import KnowledgeBaseTemplateEditor from '../components/knowledge-base/KnowledgeBaseTemplateEditor';
import KnowledgeBaseDashboard from '../components/knowledge-base/KnowledgeBaseDashboard';
import KnowledgeBaseSettings from '../components/knowledge-base/KnowledgeBaseSettings';
import { useKnowledgeBaseData } from '../hooks/useKnowledgeBaseData';

const KnowledgeBaseManagement = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [selectedItem, setSelectedItem] = useState(null);
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [editorMode, setEditorMode] = useState('create'); // 'create', 'edit', 'duplicate'
  const [editorType, setEditorType] = useState('article'); // 'article', 'template'
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [isRetrying, setIsRetrying] = useState(false);
  const [showOfflineMessage, setShowOfflineMessage] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);

  const {
    articles,
    templates,
    dashboard,
    loading,
    error,
    fetchArticles,
    fetchTemplates,
    fetchDashboard,
    createArticle,
    updateArticle,
    deleteArticle,
    duplicateArticle,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    searchArticles,
    bulkOperation,
    isOnline
  } = useKnowledgeBaseData();

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    setShowOfflineMessage(!isOnline);
  }, [isOnline]);

  const loadData = async () => {
    try {
      await Promise.all([
        fetchArticles(),
        fetchTemplates(),
        fetchDashboard()
      ]);
    } catch (error) {
      console.error('Error loading knowledge base data:', error);
      showSnackbar('Failed to load knowledge base data', 'error');
    }
  };

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const handleCreateArticle = () => {
    setSelectedItem(null);
    setEditorMode('create');
    setEditorType('article');
    setIsEditorOpen(true);
  };

  const handleCreateTemplate = () => {
    setSelectedItem(null);
    setEditorMode('create');
    setEditorType('template');
    setIsEditorOpen(true);
  };

  const handleEditItem = (item, type) => {
    setSelectedItem(item);
    setEditorMode('edit');
    setEditorType(type);
    setIsEditorOpen(true);
  };

  const handleDuplicateArticle = (article) => {
    setSelectedItem(article);
    setEditorMode('duplicate');
    setEditorType('article');
    setIsEditorOpen(true);
  };

  const handleDeleteItem = async (itemId, type) => {
    const itemName = type === 'article' ? 'article' : 'template';
    
    if (window.confirm(`Are you sure you want to delete this ${itemName}? This action cannot be undone.`)) {
      try {
        let success = false;
        
        if (type === 'article') {
          success = await deleteArticle(itemId);
        } else if (type === 'template') {
          success = await deleteTemplate(itemId);
        }
        
        if (success) {
          showSnackbar(`${itemName.charAt(0).toUpperCase() + itemName.slice(1)} deleted successfully`, 'success');
          await loadData();
        }
      } catch (error) {
        console.error(`Error deleting ${itemName}:`, error);
        showSnackbar(`Failed to delete ${itemName}`, 'error');
      }
    }
  };

  const handleEditorSubmit = async (itemData) => {
    try {
      let result = null;
      
      if (editorType === 'article') {
        if (editorMode === 'create') {
          result = await createArticle(itemData);
          showSnackbar('Knowledge base article created successfully', 'success');
        } else if (editorMode === 'edit') {
          result = await updateArticle(selectedItem.id, itemData);
          showSnackbar('Knowledge base article updated successfully', 'success');
        } else if (editorMode === 'duplicate') {
          result = await duplicateArticle(selectedItem.id, itemData);
          showSnackbar('Knowledge base article duplicated successfully', 'success');
        }
      } else if (editorType === 'template') {
        if (editorMode === 'create') {
          result = await createTemplate(itemData);
          showSnackbar('Article template created successfully', 'success');
        } else if (editorMode === 'edit') {
          result = await updateTemplate(selectedItem.id, itemData);
          showSnackbar('Article template updated successfully', 'success');
        }
      }
      
      setIsEditorOpen(false);
      setSelectedItem(null);
      await loadData();
    } catch (error) {
      console.error('Error saving item:', error);
      showSnackbar(
        `Failed to ${editorMode} ${editorType}`, 
        'error'
      );
    }
  };

  const handleBulkOperation = async (operation, itemIds, parameters = {}) => {
    try {
      const result = await bulkOperation(operation, itemIds, parameters);
      showSnackbar(
        `Bulk ${operation} completed: ${result.successful_items} successful, ${result.failed_items} failed`,
        result.failed_items > 0 ? 'warning' : 'success'
      );
      await loadData();
    } catch (error) {
      console.error('Error performing bulk operation:', error);
      showSnackbar(`Failed to perform bulk ${operation}`, 'error');
    }
  };

  const handleRetry = async () => {
    setIsRetrying(true);
    try {
      await loadData();
      showSnackbar('Data refreshed successfully', 'success');
    } catch (error) {
      showSnackbar('Failed to refresh data', 'error');
    } finally {
      setIsRetrying(false);
    }
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const tabLabels = [
    { label: 'Dashboard', icon: <DashboardIcon /> },
    { label: 'Articles', icon: <ArticleIcon /> },
    { label: 'Templates', icon: <TemplateIcon /> },
    { label: 'Analytics', icon: <AnalyticsIcon /> },
    { label: 'Settings', icon: <SettingsIcon /> }
  ];

  const getCreateButton = () => {
    switch (currentTab) {
      case 1: // Articles
        return (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateArticle}
          >
            Create Article
          </Button>
        );
      case 2: // Templates
        return (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateTemplate}
          >
            Create Template
          </Button>
        );
      default:
        return null;
    }
  };

  if (loading && !articles.length && !templates.length && !dashboard) {
    return (
      <StablePageWrapper>
        <Box sx={{ p: 3 }}>
          <Skeleton variant="text" width={300} height={40} />
          <Skeleton variant="rectangular" width="100%" height={200} sx={{ mt: 2 }} />
          <Grid container spacing={3} sx={{ mt: 2 }}>
            {[1, 2, 3].map((item) => (
              <Grid item xs={12} md={4} key={item}>
                <Skeleton variant="rectangular" height={150} />
              </Grid>
            ))}
          </Grid>
        </Box>
      </StablePageWrapper>
    );
  }

  return (
    <ErrorBoundary>
      <StablePageWrapper>
        <Box sx={{ p: 3 }}>
          {/* Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Box>
              <Typography variant="h4" component="h1" gutterBottom>
                Knowledge Base Management
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Create, manage, and organize help articles, documentation, and content templates
              </Typography>
            </Box>
            
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="Refresh Data">
                <IconButton 
                  onClick={handleRetry} 
                  disabled={isRetrying}
                  color="primary"
                >
                  {isRetrying ? <CircularProgress size={24} /> : <RefreshIcon />}
                </IconButton>
              </Tooltip>
              
              {getCreateButton()}
            </Box>
          </Box>

          {/* Offline Message */}
          <Fade in={showOfflineMessage}>
            <Alert 
              severity="warning" 
              icon={<OfflineIcon />}
              sx={{ mb: 2 }}
              action={
                <Button color="inherit" size="small" onClick={handleRetry}>
                  Retry
                </Button>
              }
            >
              You're currently offline. Some features may not be available.
            </Alert>
          </Fade>

          {/* Error Message */}
          {error && (
            <Alert 
              severity="error" 
              sx={{ mb: 2 }}
              action={
                <Button color="inherit" size="small" onClick={handleRetry}>
                  Retry
                </Button>
              }
            >
              {error}
            </Alert>
          )}

          {/* Tabs */}
          <Card sx={{ mb: 3 }}>
            <Tabs
              value={currentTab}
              onChange={handleTabChange}
              variant="fullWidth"
              sx={{ borderBottom: 1, borderColor: 'divider' }}
            >
              {tabLabels.map((tab, index) => (
                <Tab
                  key={index}
                  icon={tab.icon}
                  label={tab.label}
                  iconPosition="start"
                />
              ))}
            </Tabs>
          </Card>

          {/* Tab Content */}
          <Box sx={{ mt: 3 }}>
            {currentTab === 0 && (
              <KnowledgeBaseDashboard
                dashboard={dashboard}
                loading={loading}
                onRefresh={loadData}
              />
            )}
            
            {currentTab === 1 && (
              <KnowledgeBaseArticleList
                articles={articles}
                loading={loading}
                onEdit={(article) => handleEditItem(article, 'article')}
                onDelete={(articleId) => handleDeleteItem(articleId, 'article')}
                onDuplicate={handleDuplicateArticle}
                onBulkOperation={handleBulkOperation}
                onRefresh={loadData}
              />
            )}
            
            {currentTab === 2 && (
              <KnowledgeBaseTemplateList
                templates={templates}
                loading={loading}
                onEdit={(template) => handleEditItem(template, 'template')}
                onDelete={(templateId) => handleDeleteItem(templateId, 'template')}
                onRefresh={loadData}
              />
            )}
            
            {currentTab === 3 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Knowledge Base Analytics
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Comprehensive analytics for article performance, user engagement, and content effectiveness will be available here.
                </Typography>
              </Box>
            )}
            
            {currentTab === 4 && (
              <KnowledgeBaseSettings
                onRefresh={loadData}
              />
            )}
          </Box>

          {/* Editor Dialogs */}
          {editorType === 'article' && (
            <KnowledgeBaseArticleEditor
              open={isEditorOpen}
              mode={editorMode}
              article={selectedItem}
              templates={templates}
              onClose={() => {
                setIsEditorOpen(false);
                setSelectedItem(null);
              }}
              onSubmit={handleEditorSubmit}
            />
          )}

          {editorType === 'template' && (
            <KnowledgeBaseTemplateEditor
              open={isEditorOpen}
              mode={editorMode}
              template={selectedItem}
              onClose={() => {
                setIsEditorOpen(false);
                setSelectedItem(null);
              }}
              onSubmit={handleEditorSubmit}
            />
          )}

          {/* Snackbar */}
          <Snackbar
            open={snackbar.open}
            autoHideDuration={6000}
            onClose={handleCloseSnackbar}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          >
            <Alert 
              onClose={handleCloseSnackbar} 
              severity={snackbar.severity}
              variant="filled"
            >
              {snackbar.message}
            </Alert>
          </Snackbar>
        </Box>
      </StablePageWrapper>
    </ErrorBoundary>
  );
};

export default KnowledgeBaseManagement;
