/**
 * Tests for AudienceDemographics component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import AudienceDemographics from '../AudienceDemographics';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the hooks and API
vi.mock('../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showErrorNotification: vi.fn()
  }))
}));

vi.mock('../../api', () => ({
  default: {
    get: vi.fn()
  }
}));

// Mock Recharts components
vi.mock('recharts', () => ({
  ResponsiveContainer: ({ children }) => <div data-testid="responsive-container">{children}</div>,
  BarChart: ({ children }) => <div data-testid="bar-chart">{children}</div>,
  Bar: () => <div data-testid="bar" />,
  PieChart: ({ children }) => <div data-testid="pie-chart">{children}</div>,
  Pie: () => <div data-testid="pie" />,
  Cell: () => <div data-testid="cell" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  Legend: () => <div data-testid="legend" />
}));

describe('AudienceDemographics', () => {
  const mockApi = require('../../api').default;
  const mockNotification = vi.fn();

  const mockData = {
    total_audience: 50000,
    active_audience: 35000,
    engagement_rate: 8.5,
    audience_growth: 12.3,
    age_groups: [
      { range: '18-24', count: 12000 },
      { range: '25-34', count: 18000 },
      { range: '35-44', count: 15000 },
      { range: '45-54', count: 3000 },
      { range: '55+', count: 2000 }
    ],
    gender_distribution: {
      male: 28000,
      female: 20000,
      other: 2000
    },
    top_locations: [
      { name: 'United States', count: 20000 },
      { name: 'Canada', count: 8000 },
      { name: 'United Kingdom', count: 6000 },
      { name: 'Australia', count: 4000 },
      { name: 'Germany', count: 3000 }
    ],
    top_interests: [
      { name: 'Technology', count: 15000 },
      { name: 'Marketing', count: 12000 },
      { name: 'Business', count: 10000 },
      { name: 'Design', count: 8000 },
      { name: 'Social Media', count: 5000 }
    ]
  };

  const mockApiResponse = {
    data: {
      total_clicks: 50000,
      engagement_patterns: {
        average_engagement_rate: 8.5
      },
      growth_trends: {
        growth_rate: 12.3
      },
      demographics: {
        age: {
          '18-24': 12000,
          '25-34': 18000,
          '35-44': 15000,
          '45-54': 3000,
          '55+': 2000
        },
        gender: {
          male: 28000,
          female: 20000,
          other: 2000
        },
        location: {
          'United States': 20000,
          'Canada': 8000,
          'United Kingdom': 6000,
          'Australia': 4000,
          'Germany': 3000
        },
        industries: [
          { name: 'Technology', count: 15000 },
          { name: 'Marketing', count: 12000 },
          { name: 'Business', count: 10000 },
          { name: 'Design', count: 8000 },
          { name: 'Social Media', count: 5000 }
        ]
      }
    }
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockApi.get.mockResolvedValue(mockApiResponse);

    const { useNotification } = require('../../hooks/useNotification');
    useNotification.mockReturnValue({
      showErrorNotification: mockNotification
    });
  });

  test('renders audience demographics dashboard', () => {
    render(
      <TestWrapper>
        <AudienceDemographics data={mockData} />
      </TestWrapper>
    );

    expect(screen.getByText('Audience Demographics')).toBeInTheDocument();
    expect(screen.getByText('Age Distribution')).toBeInTheDocument();
    expect(screen.getByText('Gender Distribution')).toBeInTheDocument();
    expect(screen.getByText('Top Locations')).toBeInTheDocument();
    expect(screen.getByText('Top Interests')).toBeInTheDocument();
    expect(screen.getByText('Audience Overview')).toBeInTheDocument();
  });

  test('displays audience overview metrics correctly', () => {
    render(
      <TestWrapper>
        <AudienceDemographics data={mockData} />
      </TestWrapper>
    );

    expect(screen.getByText('50,000')).toBeInTheDocument(); // Total audience
    expect(screen.getByText('35,000')).toBeInTheDocument(); // Active audience
    expect(screen.getByText('8.5%')).toBeInTheDocument(); // Engagement rate
    expect(screen.getByText('12.3%')).toBeInTheDocument(); // Audience growth
  });

  test('shows loading state when loading prop is true', () => {
    render(
      <TestWrapper>
        <AudienceDemographics loading={true} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('displays charts correctly', () => {
    render(
      <TestWrapper>
        <AudienceDemographics data={mockData} />
      </TestWrapper>
    );

    expect(screen.getAllByTestId('bar-chart')).toHaveLength(3); // Age, locations, interests
    expect(screen.getByTestId('pie-chart')).toBeInTheDocument(); // Gender
  });

  test('handles refresh functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AudienceDemographics autoFetch={true} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/analytics/audience');
    });

    // Clear previous calls
    vi.clearAllMocks();

    // Click refresh button
    const refreshButton = screen.getByLabelText('Refresh audience demographics data');
    await user.click(refreshButton);

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/analytics/audience');
    });
  });

  test('fetches data automatically when autoFetch is true', async () => {
    render(
      <TestWrapper>
        <AudienceDemographics autoFetch={true} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/analytics/audience');
    });
  });

  test('does not fetch data when autoFetch is false', () => {
    render(
      <TestWrapper>
        <AudienceDemographics autoFetch={false} />
      </TestWrapper>
    );

    expect(mockApi.get).not.toHaveBeenCalled();
  });

  test('handles API error gracefully', async () => {
    mockApi.get.mockRejectedValue(new Error('API Error'));

    render(
      <TestWrapper>
        <AudienceDemographics autoFetch={true} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Failed to load audience demographics. Please try again.')).toBeInTheDocument();
      expect(mockNotification).toHaveBeenCalledWith('Failed to load audience demographics. Please try again.');
    });
  });

  test('shows no data message when data is null', () => {
    render(
      <TestWrapper>
        <AudienceDemographics data={null} loading={false} />
      </TestWrapper>
    );

    expect(screen.getByText('No audience data available')).toBeInTheDocument();
    expect(screen.getByText('Please check your data source or try refreshing the page.')).toBeInTheDocument();
  });

  test('works with external data prop', () => {
    render(
      <TestWrapper>
        <AudienceDemographics data={mockData} />
      </TestWrapper>
    );

    expect(screen.getByText('50,000')).toBeInTheDocument();
    expect(screen.getByText('Age Distribution')).toBeInTheDocument();
    expect(mockApi.get).not.toHaveBeenCalled(); // Should not fetch when external data provided
  });

  test('works with external loading prop', () => {
    render(
      <TestWrapper>
        <AudienceDemographics loading={true} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('calls onRefresh prop when provided', async () => {
    const mockOnRefresh = vi.fn();
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AudienceDemographics data={mockData} onRefresh={mockOnRefresh} />
      </TestWrapper>
    );

    const refreshButton = screen.getByLabelText('Refresh audience demographics data');
    await user.click(refreshButton);

    expect(mockOnRefresh).toHaveBeenCalled();
  });

  test('transforms API response correctly', async () => {
    render(
      <TestWrapper>
        <AudienceDemographics autoFetch={true} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('50,000')).toBeInTheDocument(); // total_clicks transformed to total_audience
      expect(screen.getByText('35,000')).toBeInTheDocument(); // calculated active_audience (70% of total)
      expect(screen.getByText('8.5%')).toBeInTheDocument(); // engagement rate
      expect(screen.getByText('12.3%')).toBeInTheDocument(); // growth rate
    });
  });

  test('handles missing data fields gracefully', async () => {
    const incompleteApiResponse = {
      data: {
        total_clicks: 1000,
        demographics: {}
      }
    };
    
    mockApi.get.mockResolvedValue(incompleteApiResponse);

    render(
      <TestWrapper>
        <AudienceDemographics autoFetch={true} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('1,000')).toBeInTheDocument(); // Should show total_clicks
      expect(screen.getByText('700')).toBeInTheDocument(); // Should calculate active audience
    });
  });

  test('displays custom tooltip correctly', () => {
    render(
      <TestWrapper>
        <AudienceDemographics data={mockData} />
      </TestWrapper>
    );

    // The CustomTooltip component should be rendered within charts
    expect(screen.getAllByTestId('bar-chart')).toHaveLength(3);
    expect(screen.getByTestId('pie-chart')).toBeInTheDocument();
  });

  test('updates data when external data prop changes', () => {
    const { rerender } = render(
      <TestWrapper>
        <AudienceDemographics data={mockData} />
      </TestWrapper>
    );

    expect(screen.getByText('50,000')).toBeInTheDocument();

    const newData = {
      ...mockData,
      total_audience: 75000
    };

    rerender(
      <TestWrapper>
        <AudienceDemographics data={newData} />
      </TestWrapper>
    );

    expect(screen.getByText('75,000')).toBeInTheDocument();
  });

  test('disables refresh button during loading', () => {
    render(
      <TestWrapper>
        <AudienceDemographics loading={true} />
      </TestWrapper>
    );

    const refreshButton = screen.getByLabelText('Refresh audience demographics data');
    expect(refreshButton).toBeDisabled();
  });

  test('handles empty arrays in data gracefully', () => {
    const emptyData = {
      ...mockData,
      age_groups: [],
      top_locations: [],
      top_interests: []
    };

    render(
      <TestWrapper>
        <AudienceDemographics data={emptyData} />
      </TestWrapper>
    );

    // Should still render the component structure without errors
    expect(screen.getByText('Age Distribution')).toBeInTheDocument();
    expect(screen.getByText('Top Locations')).toBeInTheDocument();
    expect(screen.getByText('Top Interests')).toBeInTheDocument();
  });
});
