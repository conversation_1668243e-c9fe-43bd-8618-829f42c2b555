# ACE Social Platform - Report Templates Documentation

## Overview

This document provides comprehensive production-ready documentation and templates for the ACE Social platform's download report functionality. All templates follow enterprise-grade standards and maintain consistency with the existing analytics_report.html template styling.

## Table of Contents

1. [Report Template Documentation](#report-template-documentation)
2. [Brand Guidelines for Exports](#brand-guidelines-for-exports)
3. [Format Specifications](#format-specifications)
4. [Sample Reports](#sample-reports)
5. [Implementation Guidelines](#implementation-guidelines)

---

## Report Template Documentation

### Supported Report Types

The ACE Social platform supports the following report types:

1. **Analytics Reports** - Content performance, engagement metrics, platform analytics
2. **Financial Reports** - Revenue summaries, churn analysis, LTV analysis, payment failures
3. **Competitor Analysis Reports** - Competitor metrics, content analysis, market insights
4. **Content Performance Reports** - Individual content metrics, performance trends
5. **ICP Metrics Reports** - Ideal Customer Profile analytics and insights

### Template Structure

All report templates follow a consistent structure:

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Meta tags and title -->
    <!-- ACE Social brand fonts and styles -->
</head>
<body>
    <div class="container">
        <!-- Header with ACE Social branding -->
        <div class="header">
            <img src="logo.svg" alt="ACE Social" class="logo">
            <h1>Report Title</h1>
        </div>
        
        <!-- Content area with report data -->
        <div class="content">
            <!-- Report content goes here -->
        </div>
        
        <!-- Footer with branding and links -->
        <div class="footer">
            <!-- Footer content -->
        </div>
    </div>
</body>
</html>
```

### Template Variables

All templates support the following standard variables:

#### Common Variables
- `{{report_title}}` - Report title
- `{{generated_at}}` - Report generation timestamp
- `{{user_name}}` - User's name
- `{{company_name}}` - User's company name
- `{{report_period_start}}` - Report period start date
- `{{report_period_end}}` - Report period end date
- `{{frontend_url}}` - Frontend application URL

#### Report-Specific Variables
Each report type has its own set of variables documented in the individual template files.

### Responsive Design Patterns

All templates include responsive design patterns:

```css
/* Mobile-first responsive design */
@media only screen and (max-width: 640px) {
    .container {
        width: 100%;
        border-radius: 16px;
        margin: 10px;
        max-width: calc(100% - 20px);
    }
    
    .content {
        padding: 30px 20px;
    }
}

@media only screen and (max-width: 400px) {
    .content {
        padding: 25px 15px;
    }
}
```

---

## Brand Guidelines for Exports

### ACE Social Brand Colors

The following color palette must be used consistently across all reports:

```css
:root {
    /* Primary Brand Colors */
    --ace-purple: #4E40C5;      /* Primary purple */
    --ace-dark: #15110E;        /* Dark background */
    --ace-yellow: #EBAE1B;      /* Accent yellow */
    --ace-white: #FFFFFF;       /* Pure white */
    
    /* Extended Palette */
    --ace-purple-light: #6C4BFA;
    --ace-purple-lighter: #8A72FF;
    --ace-purple-lightest: #B19FFF;
    
    /* Semantic Colors */
    --success-color: #4CAF50;
    --warning-color: #FF9800;
    --error-color: #F44336;
    --info-color: #2196F3;
    
    /* Neutral Colors */
    --text-primary: #1A1A2E;
    --text-secondary: #4A4A68;
    --text-muted: #AAAAAA;
    --background-light: #F0F4FF;
    --background-card: rgba(255, 255, 255, 0.85);
}
```

### Typography Hierarchy

All reports use the Inter font family with the following hierarchy:

```css
/* Font Import */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Typography Scale */
.report-title {
    font-size: 28px;
    font-weight: 700;
    letter-spacing: -0.5px;
    color: var(--ace-purple);
}

.section-title {
    font-size: 24px;
    font-weight: 600;
    letter-spacing: -0.5px;
    color: var(--text-primary);
}

.subsection-title {
    font-size: 20px;
    font-weight: 600;
    letter-spacing: -0.3px;
    color: var(--text-primary);
}

.body-text {
    font-size: 16px;
    font-weight: 400;
    line-height: 1.7;
    color: var(--text-secondary);
}

.caption-text {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-muted);
}
```

### Logo Placement and Sizing

```css
.logo {
    max-width: 180px;
    height: auto;
    margin-bottom: 15px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* Responsive logo sizing */
@media only screen and (max-width: 640px) {
    .logo {
        max-width: 140px;
    }
}
```

### Gradient and Shadow Standards

```css
/* Primary gradient */
.primary-gradient {
    background: linear-gradient(135deg, #6C4BFA 0%, #8A72FF 50%, #B19FFF 100%);
}

/* Card shadows */
.card-shadow {
    box-shadow: 
        0 4px 24px rgba(0, 0, 0, 0.08),
        0 1px 3px rgba(108, 75, 250, 0.1),
        0 1px 2px rgba(138, 114, 255, 0.1);
}

/* Elevated shadows */
.elevated-shadow {
    box-shadow: 
        0 6px 20px rgba(108, 75, 250, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.3) inset;
}
```

---

## Format Specifications

### CSV Export Standards

#### Header Format
All CSV exports must include properly formatted headers with consistent naming:

```csv
# Standard Analytics Report CSV Headers
"Report Title","Content Performance Analytics"
"Generated At","2024-01-15 14:30:00 UTC"
"Report Period","2024-01-01 to 2024-01-31"
"User","John Doe (<EMAIL>)"
"Company","Acme Corporation"
""
"Content Title","Platform","Content Type","Published Date","Impressions","Engagements","Engagement Rate","Clicks","Shares"
```

#### Data Formatting Rules

1. **Date Format**: ISO 8601 format (YYYY-MM-DD HH:MM:SS UTC)
2. **Numbers**: No thousands separators in raw data, use decimal points
3. **Percentages**: Store as decimal values (0.15 for 15%)
4. **Text Escaping**: Wrap in quotes if contains commas, quotes, or newlines
5. **Null Values**: Use empty string for missing data

#### CSV Escaping Implementation

```javascript
function escapeCSVValue(value) {
    if (value === null || value === undefined) {
        return '';
    }

    const stringValue = String(value);

    // Escape if contains comma, quote, or newline
    if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
        return `"${stringValue.replace(/"/g, '""')}"`;
    }

    return stringValue;
}

function formatCSVRow(data, columns) {
    return columns.map(col => {
        let value = data[col.key];

        // Handle nested properties
        if (col.key.includes('.')) {
            const keys = col.key.split('.');
            value = keys.reduce((obj, key) => obj?.[key], data);
        }

        // Format based on type
        if (col.type === 'date' && value) {
            value = new Date(value).toISOString().replace('T', ' ').replace('Z', ' UTC');
        } else if (col.type === 'currency' && value !== null && value !== undefined) {
            value = Number(value).toFixed(2);
        } else if (col.type === 'percentage' && value !== null && value !== undefined) {
            value = (Number(value) * 100).toFixed(2) + '%';
        } else if (col.type === 'boolean') {
            value = value ? 'Yes' : 'No';
        }

        return escapeCSVValue(value);
    }).join(',');
}
```

### PDF Layout Specifications

#### Page Setup
```javascript
const pdfConfig = {
    orientation: 'portrait', // or 'landscape' based on content width
    unit: 'mm',
    format: 'A4',
    margins: {
        top: 20,
        right: 15,
        bottom: 20,
        left: 15
    }
};
```

#### Multi-page Handling
```javascript
function generatePDFReport(data, template) {
    const pdf = new jsPDF(pdfConfig);

    // Add header to first page
    addPDFHeader(pdf, data.reportTitle, data.generatedAt);

    let currentY = 40; // Start position after header
    const pageHeight = pdf.internal.pageSize.height;
    const marginBottom = 20;

    data.sections.forEach(section => {
        // Check if section fits on current page
        const sectionHeight = calculateSectionHeight(section);

        if (currentY + sectionHeight > pageHeight - marginBottom) {
            pdf.addPage();
            addPDFHeader(pdf, data.reportTitle, data.generatedAt);
            currentY = 40;
        }

        currentY = addPDFSection(pdf, section, currentY);
    });

    // Add footer to all pages
    addPDFFooter(pdf);

    return pdf;
}
```

#### PDF Styling Standards
```javascript
const pdfStyles = {
    title: {
        fontSize: 20,
        fontStyle: 'bold',
        textColor: [78, 64, 197] // ACE Purple
    },
    sectionTitle: {
        fontSize: 16,
        fontStyle: 'bold',
        textColor: [26, 26, 46] // Text Primary
    },
    bodyText: {
        fontSize: 11,
        fontStyle: 'normal',
        textColor: [74, 74, 104] // Text Secondary
    },
    caption: {
        fontSize: 9,
        fontStyle: 'normal',
        textColor: [170, 170, 170] // Text Muted
    }
};
```

### Excel/XLSX Formatting

#### Workbook Structure
```javascript
const excelStructure = {
    worksheets: [
        {
            name: 'Summary',
            data: summaryData,
            formatting: 'summary'
        },
        {
            name: 'Detailed Data',
            data: detailedData,
            formatting: 'data'
        },
        {
            name: 'Charts',
            data: chartData,
            formatting: 'charts'
        }
    ]
};
```

#### Cell Formatting Standards
```javascript
const excelStyles = {
    header: {
        font: { bold: true, color: { rgb: '4E40C5' } },
        fill: { fgColor: { rgb: 'F0F4FF' } },
        border: {
            top: { style: 'thin', color: { rgb: '4E40C5' } },
            bottom: { style: 'thin', color: { rgb: '4E40C5' } }
        }
    },
    data: {
        font: { color: { rgb: '4A4A68' } },
        alignment: { horizontal: 'left', vertical: 'center' }
    },
    number: {
        numFmt: '#,##0.00'
    },
    percentage: {
        numFmt: '0.00%'
    },
    date: {
        numFmt: 'yyyy-mm-dd hh:mm:ss'
    }
};
```

### JSON Export Schemas

#### Standard Report Schema
```json
{
    "$schema": "https://json-schema.org/draft/2020-12/schema",
    "type": "object",
    "properties": {
        "report_metadata": {
            "type": "object",
            "properties": {
                "report_id": { "type": "string" },
                "report_type": { "type": "string" },
                "generated_at": { "type": "string", "format": "date-time" },
                "user_id": { "type": "string" },
                "company_name": { "type": "string" },
                "period_start": { "type": "string", "format": "date-time" },
                "period_end": { "type": "string", "format": "date-time" },
                "export_format": { "type": "string" },
                "version": { "type": "string", "default": "1.0" }
            },
            "required": ["report_id", "report_type", "generated_at", "user_id"]
        },
        "summary": {
            "type": "object",
            "properties": {
                "total_records": { "type": "integer" },
                "key_metrics": { "type": "object" },
                "performance_indicators": { "type": "array" }
            }
        },
        "data": {
            "type": "array",
            "items": { "type": "object" }
        },
        "charts": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "chart_type": { "type": "string" },
                    "title": { "type": "string" },
                    "data": { "type": "array" },
                    "config": { "type": "object" }
                }
            }
        }
    },
    "required": ["report_metadata", "data"]
}
```

#### Field Naming Conventions
- Use snake_case for all field names
- Include units in field names where applicable (e.g., `duration_seconds`, `amount_usd`)
- Use consistent date field suffixes (`_at` for timestamps, `_date` for dates)
- Prefix boolean fields with `is_`, `has_`, or `can_`

---

## Sample Reports

### Analytics Report Sample

#### HTML Email Version
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Content Performance Analytics - ACE Social</title>
    <style>
        /* Include base styles from analytics_report.html */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #6C4BFA 0%, #8A72FF 50%, #B19FFF 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow:
                0 4px 24px rgba(0, 0, 0, 0.08),
                0 1px 3px rgba(108, 75, 250, 0.1);
        }

        .header {
            text-align: center;
            padding: 40px 40px 20px;
            border-bottom: 1px solid rgba(108, 75, 250, 0.1);
        }

        .logo {
            max-width: 180px;
            height: auto;
            margin-bottom: 15px;
        }

        .content {
            padding: 40px;
        }

        .stat-box {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 24px;
            margin: 20px 0;
            border: 1px solid rgba(108, 75, 250, 0.1);
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #4E40C5;
            margin: 8px 0;
        }

        .performance-indicator.up {
            color: #4CAF50;
        }

        .performance-indicator.down {
            color: #F44336;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .button {
            display: inline-block;
            background: #4E40C5;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            margin: 20px 0;
        }

        .footer {
            text-align: center;
            padding: 30px 40px;
            border-top: 1px solid rgba(108, 75, 250, 0.1);
            color: #4A4A68;
            font-size: 14px;
        }

        @media only screen and (max-width: 640px) {
            .container {
                margin: 10px;
                border-radius: 16px;
            }
            .content {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="{{frontend_url}}/assets/logo.svg" alt="ACE Social" class="logo">
            <h1>Content Performance Analytics</h1>
        </div>

        <div class="content">
            <h2>Hello {{user_name}},</h2>

            <p>Here's your content performance report for {{report_period_start}} to {{report_period_end}}.</p>

            <div class="stat-box">
                <div class="stat-label">Overall Engagement Rate</div>
                <div class="stat-value">{{overall_engagement_rate}}%</div>
                <div>
                    {% if engagement_change > 0 %}
                    <span class="performance-indicator up">↑ {{engagement_change}}%</span>
                    {% elif engagement_change < 0 %}
                    <span class="performance-indicator down">↓ {{engagement_change|abs}}%</span>
                    {% else %}
                    <span class="performance-indicator neutral">No change</span>
                    {% endif %}
                    compared to previous period
                </div>
            </div>

            <div class="divider"></div>

            <h3>Key Performance Metrics</h3>

            <div class="stats-container">
                <div class="stat-item">
                    <div class="stat-box">
                        <div class="stat-label">Total Content Published</div>
                        <div class="stat-value">{{total_content_published}}</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-box">
                        <div class="stat-label">Total Impressions</div>
                        <div class="stat-value">{{total_impressions}}</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-box">
                        <div class="stat-label">Total Engagements</div>
                        <div class="stat-value">{{total_engagements}}</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-box">
                        <div class="stat-label">Click-Through Rate</div>
                        <div class="stat-value">{{click_through_rate}}%</div>
                    </div>
                </div>
            </div>

            <div class="divider"></div>

            <h3>Top Performing Content</h3>
            {% for content in top_performing_content %}
            <div class="info-box">
                <h4>{{content.title}}</h4>
                <p><strong>Platform:</strong> {{content.platform}}</p>
                <p><strong>Content Type:</strong> {{content.content_type}}</p>
                <p><strong>Engagement Rate:</strong> {{content.engagement_rate}}%</p>
                <p><strong>Published:</strong> {{content.published_date}}</p>
            </div>
            {% endfor %}

            <div class="divider"></div>

            <p>View your full analytics dashboard for more detailed insights:</p>

            <a href="{{analytics_url}}" class="button">View Full Analytics</a>
        </div>

        <div class="footer">
            <p>© {% now 'Y' %} ACE Social. All rights reserved.</p>
            <div class="footer-links">
                <a href="{{frontend_url}}/privacy-policy">Privacy Policy</a> •
                <a href="{{frontend_url}}/terms">Terms of Service</a> •
                <a href="{{frontend_url}}/contact">Contact Us</a>
            </div>
        </div>
    </div>
</body>
</html>
```
