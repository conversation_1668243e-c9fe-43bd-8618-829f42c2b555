// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import ShoppingCart from '../ShoppingCart';

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

// Mock cart data
const mockCartItems = [
  {
    id: '1',
    name: 'ACEO Pro Bundle',
    description: 'Professional content generation bundle',
    price: 99.99,
    quantity: 1
  },
  {
    id: '2',
    name: 'Campaign Analytics',
    description: 'Advanced analytics and reporting',
    price_monthly: 29.99,
    quantity: 2
  },
  {
    id: '3',
    name: 'Brand Voice Training',
    description: 'AI brand voice customization',
    price_per_unit: 49.99,
    quantity: 1
  }
];

describe('ShoppingCart', () => {
  const mockProps = {
    open: true,
    onClose: vi.fn(),
    cart: mockCartItems,
    onUpdateQuantity: vi.fn(),
    onRemoveItem: vi.fn(),
    onClearCart: vi.fn(),
    onCheckout: vi.fn(),
    loading: false
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders shopping cart when open', () => {
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Shopping Cart')).toBeInTheDocument();
    expect(screen.getByText('ACEO Pro Bundle')).toBeInTheDocument();
    expect(screen.getByText('Campaign Analytics')).toBeInTheDocument();
    expect(screen.getByText('Brand Voice Training')).toBeInTheDocument();
  });

  test('does not render when closed', () => {
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} open={false} />
      </TestWrapper>
    );

    expect(screen.queryByText('Shopping Cart')).not.toBeInTheDocument();
  });

  test('displays cart items correctly', () => {
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} />
      </TestWrapper>
    );

    // Check item names and descriptions
    expect(screen.getByText('ACEO Pro Bundle')).toBeInTheDocument();
    expect(screen.getByText('Professional content generation bundle')).toBeInTheDocument();
    expect(screen.getByText('Campaign Analytics')).toBeInTheDocument();
    expect(screen.getByText('Advanced analytics and reporting')).toBeInTheDocument();
    expect(screen.getByText('Brand Voice Training')).toBeInTheDocument();
    expect(screen.getByText('AI brand voice customization')).toBeInTheDocument();

    // Check prices
    expect(screen.getByText('$99.99')).toBeInTheDocument();
    expect(screen.getByText('$29.99/month')).toBeInTheDocument();
    expect(screen.getByText('$49.99 each')).toBeInTheDocument();
  });

  test('calculates total correctly', () => {
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} />
      </TestWrapper>
    );

    // Total should be: 99.99 + (29.99 * 2) + 49.99 = 209.96
    expect(screen.getByText('$209.96')).toBeInTheDocument();
  });

  test('handles quantity updates', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} />
      </TestWrapper>
    );

    // Find increase button for first item
    const increaseButtons = screen.getAllByLabelText('Increase quantity');
    await user.click(increaseButtons[0]);

    expect(mockProps.onUpdateQuantity).toHaveBeenCalledWith('1', 2);
  });

  test('handles quantity decrease', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} />
      </TestWrapper>
    );

    // Find decrease button for second item (quantity 2)
    const decreaseButtons = screen.getAllByLabelText('Decrease quantity');
    await user.click(decreaseButtons[1]);

    expect(mockProps.onUpdateQuantity).toHaveBeenCalledWith('2', 1);
  });

  test('handles item removal', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} />
      </TestWrapper>
    );

    const removeButtons = screen.getAllByLabelText('Remove item');
    await user.click(removeButtons[0]);

    expect(mockProps.onRemoveItem).toHaveBeenCalledWith('1');
  });

  test('handles cart clearing', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} />
      </TestWrapper>
    );

    const clearButton = screen.getByText('Clear Cart');
    await user.click(clearButton);

    expect(mockProps.onClearCart).toHaveBeenCalled();
  });

  test('handles checkout', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} />
      </TestWrapper>
    );

    const checkoutButton = screen.getByText('Proceed to Checkout');
    await user.click(checkoutButton);

    expect(mockProps.onCheckout).toHaveBeenCalled();
  });

  test('handles cart close', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} />
      </TestWrapper>
    );

    const closeButton = screen.getByLabelText('Close cart');
    await user.click(closeButton);

    expect(mockProps.onClose).toHaveBeenCalled();
  });

  test('shows empty cart message when cart is empty', () => {
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} cart={[]} />
      </TestWrapper>
    );

    expect(screen.getByText('Your cart is empty')).toBeInTheDocument();
    expect(screen.getByText('Add some items to get started!')).toBeInTheDocument();
  });

  test('disables checkout when cart is empty', () => {
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} cart={[]} />
      </TestWrapper>
    );

    const checkoutButton = screen.getByText('Proceed to Checkout');
    expect(checkoutButton).toBeDisabled();
  });

  test('shows loading state', () => {
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} loading={true} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('handles quantity input changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} />
      </TestWrapper>
    );

    // Find quantity input for first item
    const quantityInputs = screen.getAllByDisplayValue('1');
    await user.clear(quantityInputs[0]);
    await user.type(quantityInputs[0], '3');
    await user.tab(); // Trigger blur event

    expect(mockProps.onUpdateQuantity).toHaveBeenCalledWith('1', 3);
  });

  test('prevents quantity below 1', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} />
      </TestWrapper>
    );

    // Try to decrease quantity of item with quantity 1
    const decreaseButtons = screen.getAllByLabelText('Decrease quantity');
    await user.click(decreaseButtons[0]); // First item has quantity 1

    // Should not call onUpdateQuantity with 0
    expect(mockProps.onUpdateQuantity).not.toHaveBeenCalledWith('1', 0);
  });

  test('shows correct item count in header', () => {
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} />
      </TestWrapper>
    );

    // Total items: 1 + 2 + 1 = 4
    expect(screen.getByText('4 items')).toBeInTheDocument();
  });

  test('handles different price types correctly', () => {
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} />
      </TestWrapper>
    );

    // One-time price
    expect(screen.getByText('$99.99')).toBeInTheDocument();
    
    // Monthly price
    expect(screen.getByText('$29.99/month')).toBeInTheDocument();
    
    // Per-unit price
    expect(screen.getByText('$49.99 each')).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} />
      </TestWrapper>
    );

    // Check for proper ARIA labels
    expect(screen.getByLabelText('Close cart')).toBeInTheDocument();
    expect(screen.getAllByLabelText('Increase quantity')).toHaveLength(3);
    expect(screen.getAllByLabelText('Decrease quantity')).toHaveLength(3);
    expect(screen.getAllByLabelText('Remove item')).toHaveLength(3);
    
    // Check for proper roles
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByRole('list')).toBeInTheDocument();
  });

  test('shows security badge', () => {
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Secure Checkout')).toBeInTheDocument();
  });

  test('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} />
      </TestWrapper>
    );

    // Tab through interactive elements
    await user.tab();
    expect(screen.getByLabelText('Close cart')).toHaveFocus();
  });

  test('displays item quantities correctly', () => {
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} />
      </TestWrapper>
    );

    const quantityInputs = screen.getAllByRole('spinbutton');
    expect(quantityInputs[0]).toHaveValue(1); // First item
    expect(quantityInputs[1]).toHaveValue(2); // Second item
    expect(quantityInputs[2]).toHaveValue(1); // Third item
  });

  test('shows dividers between items', () => {
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} />
      </TestWrapper>
    );

    const dividers = screen.getAllByRole('separator');
    // Should have dividers between items (not after last item)
    expect(dividers.length).toBeGreaterThan(0);
  });

  test('handles cart with single item', () => {
    const singleItemCart = [mockCartItems[0]];
    
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} cart={singleItemCart} />
      </TestWrapper>
    );

    expect(screen.getByText('1 item')).toBeInTheDocument();
    expect(screen.getByText('$99.99')).toBeInTheDocument();
  });

  test('handles cart with no prices', () => {
    const noPriceCart = [{
      id: '1',
      name: 'Free Item',
      description: 'This item is free',
      quantity: 1
    }];
    
    render(
      <TestWrapper>
        <ShoppingCart {...mockProps} cart={noPriceCart} />
      </TestWrapper>
    );

    expect(screen.getByText('Free Item')).toBeInTheDocument();
    expect(screen.getByText('$0.00')).toBeInTheDocument();
  });
});
