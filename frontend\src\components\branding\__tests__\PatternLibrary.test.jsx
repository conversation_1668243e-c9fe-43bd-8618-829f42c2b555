// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import PatternLibrary from '../PatternLibrary';

// Mock the notification hook
const mockShowSuccessNotification = vi.fn();
const mockShowErrorNotification = vi.fn();

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showSuccessNotification: mockShowSuccessNotification,
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn(() => 'mock-pattern-url');

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('PatternLibrary', () => {
  const mockVisualElements = {
    patterns: [
      {
        id: 'pattern1',
        name: 'Dots',
        url: 'https://example.com/dots.png',
        opacity: 30,
        scale: 100
      },
      {
        id: 'pattern2',
        name: 'Lines',
        url: 'https://example.com/lines.png',
        opacity: 50,
        scale: 120
      }
    ],
    textures: [
      {
        id: 'texture1',
        name: 'Wood',
        url: 'https://example.com/wood.jpg',
        opacity: 40
      }
    ],
    shapes: {
      type: 'rounded',
      size: 'medium'
    }
  };

  const mockProps = {
    visualElements: mockVisualElements,
    onChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders pattern library correctly', () => {
    render(
      <TestWrapper>
        <PatternLibrary {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Pattern Library')).toBeInTheDocument();
    expect(screen.getByText(/Create a library of patterns, textures, and shape styles/)).toBeInTheDocument();
  });

  test('displays all tabs', () => {
    render(
      <TestWrapper>
        <PatternLibrary {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Patterns')).toBeInTheDocument();
    expect(screen.getByText('Textures')).toBeInTheDocument();
    expect(screen.getByText('Shapes')).toBeInTheDocument();
  });

  test('shows existing patterns in patterns tab', () => {
    render(
      <TestWrapper>
        <PatternLibrary {...mockProps} />
      </TestWrapper>
    );

    // Should start with Patterns tab active
    expect(screen.getByText('Dots')).toBeInTheDocument();
    expect(screen.getByText('Lines')).toBeInTheDocument();
  });

  test('handles tab navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PatternLibrary {...mockProps} />
      </TestWrapper>
    );

    // Click on Textures tab
    await user.click(screen.getByText('Textures'));
    expect(screen.getByText('Wood')).toBeInTheDocument();

    // Click on Shapes tab
    await user.click(screen.getByText('Shapes'));
    expect(screen.getByText('Shape Language')).toBeInTheDocument();
  });

  test('handles pattern upload', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PatternLibrary {...mockProps} />
      </TestWrapper>
    );

    const file = new File(['pattern content'], 'pattern.png', { type: 'image/png' });
    const fileInput = screen.getByRole('button', { name: /upload pattern/i }).querySelector('input[type="file"]');
    
    await user.upload(fileInput, file);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualElements,
        patterns: expect.arrayContaining([
          expect.objectContaining({
            name: 'pattern.png',
            url: 'mock-pattern-url'
          })
        ])
      });
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Pattern uploaded successfully');
    });
  });

  test('handles texture upload', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PatternLibrary {...mockProps} />
      </TestWrapper>
    );

    // Navigate to Textures tab
    await user.click(screen.getByText('Textures'));

    const file = new File(['texture content'], 'texture.jpg', { type: 'image/jpeg' });
    const fileInput = screen.getByRole('button', { name: /upload texture/i }).querySelector('input[type="file"]');
    
    await user.upload(fileInput, file);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualElements,
        textures: expect.arrayContaining([
          expect.objectContaining({
            name: 'texture.jpg',
            url: 'mock-pattern-url'
          })
        ])
      });
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Texture uploaded successfully');
    });
  });

  test('handles pattern deletion', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PatternLibrary {...mockProps} />
      </TestWrapper>
    );

    const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
    await user.click(deleteButtons[0]);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualElements,
        patterns: mockVisualElements.patterns.slice(1)
      });
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Pattern removed');
    });
  });

  test('handles texture deletion', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PatternLibrary {...mockProps} />
      </TestWrapper>
    );

    // Navigate to Textures tab
    await user.click(screen.getByText('Textures'));

    const deleteButton = screen.getByRole('button', { name: /delete/i });
    await user.click(deleteButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualElements,
        textures: []
      });
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Texture removed');
    });
  });

  test('handles pattern opacity change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PatternLibrary {...mockProps} />
      </TestWrapper>
    );

    const opacitySlider = screen.getByRole('slider', { name: /pattern opacity/i });
    
    // Change the slider value
    fireEvent.change(opacitySlider, { target: { value: '50' } });
    
    // Apply settings
    const applyButton = screen.getByText('Apply Pattern Settings');
    await user.click(applyButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualElements,
        patterns: mockVisualElements.patterns.map(pattern => ({
          ...pattern,
          opacity: 50
        }))
      });
    });
  });

  test('handles pattern scale change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PatternLibrary {...mockProps} />
      </TestWrapper>
    );

    const scaleSlider = screen.getByRole('slider', { name: /pattern scale/i });
    
    // Change the slider value
    fireEvent.change(scaleSlider, { target: { value: '150' } });
    
    // Apply settings
    const applyButton = screen.getByText('Apply Pattern Settings');
    await user.click(applyButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualElements,
        patterns: mockVisualElements.patterns.map(pattern => ({
          ...pattern,
          scale: 150
        }))
      });
    });
  });

  test('handles shape type change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PatternLibrary {...mockProps} />
      </TestWrapper>
    );

    // Navigate to Shapes tab
    await user.click(screen.getByText('Shapes'));

    const shapeTypeSelect = screen.getByRole('combobox');
    await user.click(shapeTypeSelect);
    
    const angularOption = screen.getByText('Angular');
    await user.click(angularOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualElements,
        shapes: {
          ...mockVisualElements.shapes,
          type: 'angular'
        }
      });
    });
  });

  test('handles shape size change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <PatternLibrary {...mockProps} />
      </TestWrapper>
    );

    // Navigate to Shapes tab
    await user.click(screen.getByText('Shapes'));

    const shapeSizeSelect = screen.getAllByRole('combobox')[1];
    await user.click(shapeSizeSelect);
    
    const largeOption = screen.getByText('Large');
    await user.click(largeOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualElements,
        shapes: {
          ...mockVisualElements.shapes,
          size: 'large'
        }
      });
    });
  });

  test('shows empty state when no patterns exist', () => {
    const propsWithoutPatterns = {
      ...mockProps,
      visualElements: {
        patterns: [],
        textures: [],
        shapes: { type: 'rounded', size: 'medium' }
      }
    };

    render(
      <TestWrapper>
        <PatternLibrary {...propsWithoutPatterns} />
      </TestWrapper>
    );

    expect(screen.getByText(/No patterns uploaded yet/)).toBeInTheDocument();
  });

  test('shows empty state when no textures exist', async () => {
    const user = userEvent.setup();
    
    const propsWithoutTextures = {
      ...mockProps,
      visualElements: {
        patterns: mockVisualElements.patterns,
        textures: [],
        shapes: { type: 'rounded', size: 'medium' }
      }
    };

    render(
      <TestWrapper>
        <PatternLibrary {...propsWithoutTextures} />
      </TestWrapper>
    );

    // Navigate to Textures tab
    await user.click(screen.getByText('Textures'));

    expect(screen.getByText(/No textures uploaded yet/)).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <PatternLibrary {...mockProps} />
      </TestWrapper>
    );

    // Check for proper labels and roles
    expect(screen.getByRole('slider', { name: /pattern opacity/i })).toBeInTheDocument();
    expect(screen.getByRole('slider', { name: /pattern scale/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /upload pattern/i })).toBeInTheDocument();
  });

  test('renders with default props when no visualElements provided', () => {
    render(
      <TestWrapper>
        <PatternLibrary onChange={vi.fn()} />
      </TestWrapper>
    );

    expect(screen.getByText('Pattern Library')).toBeInTheDocument();
    expect(screen.getByText(/No patterns uploaded yet/)).toBeInTheDocument();
  });

  test('handles file upload error gracefully', async () => {
    const user = userEvent.setup();
    
    // Mock console.error to avoid test output noise
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    render(
      <TestWrapper>
        <PatternLibrary {...mockProps} />
      </TestWrapper>
    );

    // Simulate upload without file
    const fileInput = screen.getByRole('button', { name: /upload pattern/i }).querySelector('input[type="file"]');
    fireEvent.change(fileInput, { target: { files: [] } });

    // Should not call onChange or show success notification
    expect(mockProps.onChange).not.toHaveBeenCalled();
    expect(mockShowSuccessNotification).not.toHaveBeenCalled();

    consoleSpy.mockRestore();
  });

  test('displays pattern preview correctly', () => {
    render(
      <TestWrapper>
        <PatternLibrary {...mockProps} />
      </TestWrapper>
    );

    // Check if pattern images are displayed
    const patternImages = screen.getAllByRole('img');
    expect(patternImages.length).toBeGreaterThan(0);
  });

  test('shows pattern settings controls', () => {
    render(
      <TestWrapper>
        <PatternLibrary {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Pattern Settings')).toBeInTheDocument();
    expect(screen.getByText('Pattern Opacity')).toBeInTheDocument();
    expect(screen.getByText('Pattern Scale')).toBeInTheDocument();
    expect(screen.getByText('Apply Pattern Settings')).toBeInTheDocument();
  });
});
