/**
 * Tests for AdvancedImageProcessing component
 @since 2024-1-1 to 2025-25-7
*/
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import AdvancedImageProcessing from '../AdvancedImageProcessing';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock hooks
vi.mock('../../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showSuccessNotification: vi.fn(),
    showErrorNotification: vi.fn()
  }))
}));

vi.mock('../../../hooks/useBranding', () => ({
  default: vi.fn(() => ({
    brandingData: {
      colorSystem: { 
        primary: '#4E40C5',
        secondary: '#EBAE1B',
        accent: '#15110E'
      },
      logo_url: 'https://example.com/logo.png'
    }
  }))
}));

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn(() => 'blob:mock-url');
global.URL.revokeObjectURL = vi.fn();

describe('AdvancedImageProcessing', () => {
  const mockProps = {
    onImageProcessed: vi.fn(),
    onImageSaved: vi.fn(),
    onError: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders advanced image processing component', () => {
    render(
      <TestWrapper>
        <AdvancedImageProcessing {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Advanced Image Processing')).toBeInTheDocument();
    expect(screen.getByText('Apply sophisticated image processing to enhance your brand visuals.')).toBeInTheDocument();
    expect(screen.getByText('Image Preview')).toBeInTheDocument();
  });

  test('loads initial image when provided', () => {
    render(
      <TestWrapper>
        <AdvancedImageProcessing {...mockProps} initialImage="https://example.com/test.jpg" />
      </TestWrapper>
    );

    const image = screen.getByAltText('Preview');
    expect(image).toHaveAttribute('src', 'https://example.com/test.jpg');
  });

  test('handles image upload', async () => {
    const user = userEvent.setup();
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    
    render(
      <TestWrapper>
        <AdvancedImageProcessing {...mockProps} />
      </TestWrapper>
    );

    const uploadInput = screen.getByLabelText('Upload image file').querySelector('input[type="file"]');
    await user.upload(uploadInput, file);

    await waitFor(() => {
      expect(mockProps.onImageProcessed).toHaveBeenCalledWith(
        expect.objectContaining({
          url: 'blob:mock-url',
          file: file,
          settings: expect.any(Object)
        })
      );
    });
  });

  test('adjusts brightness slider', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AdvancedImageProcessing {...mockProps} />
      </TestWrapper>
    );

    // Switch to Adjustments tab
    const adjustmentsTab = screen.getByText('Adjustments');
    await user.click(adjustmentsTab);

    const brightnessSlider = screen.getByLabelText('brightness-slider');
    expect(brightnessSlider).toBeInTheDocument();
    expect(screen.getByText('100%')).toBeInTheDocument(); // Default brightness
  });

  test('applies filter presets', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AdvancedImageProcessing {...mockProps} />
      </TestWrapper>
    );

    // Switch to Filters tab
    const filtersTab = screen.getByText('Filters');
    await user.click(filtersTab);

    expect(screen.getByText('Filter Presets')).toBeInTheDocument();
    expect(screen.getByText('Original')).toBeInTheDocument();
    expect(screen.getByText('Brand')).toBeInTheDocument();
    expect(screen.getByText('Vibrant')).toBeInTheDocument();

    // Click on Brand filter
    const brandFilter = screen.getByText('Brand');
    await user.click(brandFilter);

    // Should apply the filter
    const image = screen.getByAltText('Preview');
    expect(image).toHaveStyle({ filter: expect.stringContaining('saturate') });
  });

  test('toggles color overlay', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AdvancedImageProcessing {...mockProps} />
      </TestWrapper>
    );

    // Switch to Overlay tab
    const overlayTab = screen.getByText('Overlay');
    await user.click(overlayTab);

    const overlaySwitch = screen.getByLabelText('Use Color Overlay');
    await user.click(overlaySwitch);

    expect(overlaySwitch).toBeChecked();
  });

  test('toggles logo overlay', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AdvancedImageProcessing {...mockProps} />
      </TestWrapper>
    );

    // Switch to Branding tab
    const brandingTab = screen.getByText('Branding');
    await user.click(brandingTab);

    const logoSwitch = screen.getByLabelText('Add Logo');
    await user.click(logoSwitch);

    expect(logoSwitch).toBeChecked();
  });

  test('handles undo/redo functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AdvancedImageProcessing {...mockProps} />
      </TestWrapper>
    );

    const undoButton = screen.getByLabelText('Undo');
    const redoButton = screen.getByLabelText('Redo');

    // Initially, undo should be disabled (no history)
    expect(undoButton).toBeDisabled();
    expect(redoButton).toBeDisabled();

    // Make a change to create history
    const filtersTab = screen.getByText('Filters');
    await user.click(filtersTab);
    
    const brandFilter = screen.getByText('Brand');
    await user.click(brandFilter);

    // Now undo should be enabled
    expect(undoButton).not.toBeDisabled();
  });

  test('saves processed image', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AdvancedImageProcessing {...mockProps} />
      </TestWrapper>
    );

    const saveButton = screen.getByLabelText('Save processed image');
    await user.click(saveButton);

    await waitFor(() => {
      expect(mockProps.onImageSaved).toHaveBeenCalledWith(
        expect.objectContaining({
          imageUrl: expect.any(String),
          settings: expect.any(Object),
          filterStyle: expect.any(String)
        })
      );
    });

    expect(screen.getByText('Image processing settings saved to your brand profile')).toBeInTheDocument();
  });

  test('handles API errors gracefully', async () => {
    const user = userEvent.setup();
    const mockOnImageSaved = vi.fn().mockRejectedValue(new Error('API Error'));
    
    render(
      <TestWrapper>
        <AdvancedImageProcessing {...mockProps} onImageSaved={mockOnImageSaved} />
      </TestWrapper>
    );

    const saveButton = screen.getByLabelText('Save processed image');
    await user.click(saveButton);

    await waitFor(() => {
      expect(mockProps.onError).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  test('disables controls when disabled prop is true', () => {
    render(
      <TestWrapper>
        <AdvancedImageProcessing {...mockProps} disabled={true} />
      </TestWrapper>
    );

    const uploadButton = screen.getByLabelText('Upload image file');
    const saveButton = screen.getByLabelText('Save processed image');

    expect(uploadButton).toBeDisabled();
    expect(saveButton).toBeDisabled();
  });

  test('shows loading state during save', async () => {
    const user = userEvent.setup();
    const mockOnImageSaved = vi.fn(() => new Promise(() => {})); // Never resolves
    
    render(
      <TestWrapper>
        <AdvancedImageProcessing {...mockProps} onImageSaved={mockOnImageSaved} />
      </TestWrapper>
    );

    const saveButton = screen.getByLabelText('Save processed image');
    await user.click(saveButton);

    expect(screen.getByText('Saving...')).toBeInTheDocument();
    expect(saveButton).toBeDisabled();
  });

  test('displays brand colors in overlay section', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AdvancedImageProcessing {...mockProps} />
      </TestWrapper>
    );

    // Switch to Overlay tab
    const overlayTab = screen.getByText('Overlay');
    await user.click(overlayTab);

    expect(screen.getByText('Overlay Color')).toBeInTheDocument();
    
    // Should show brand color swatches
    const colorSwatches = screen.getAllByRole('button');
    const colorSwatchCount = colorSwatches.filter(button => 
      button.style.backgroundColor || button.getAttribute('style')?.includes('background')
    ).length;
    expect(colorSwatchCount).toBeGreaterThan(0);
  });

  test('shows logo position options when logo is enabled', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AdvancedImageProcessing {...mockProps} />
      </TestWrapper>
    );

    // Switch to Branding tab
    const brandingTab = screen.getByText('Branding');
    await user.click(brandingTab);

    // Enable logo
    const logoSwitch = screen.getByLabelText('Add Logo');
    await user.click(logoSwitch);

    // Should show position dropdown
    expect(screen.getByLabelText('Logo Position')).toBeInTheDocument();
    
    // Click to open dropdown
    const positionSelect = screen.getByLabelText('Logo Position');
    await user.click(positionSelect);

    expect(screen.getByText('Top Left')).toBeInTheDocument();
    expect(screen.getByText('Bottom Right')).toBeInTheDocument();
  });

  test('shows warning when no logo is available', async () => {
    const user = userEvent.setup();
    const useBranding = await import('../../../hooks/useBranding');
    
    // Mock no logo
    useBranding.default.mockReturnValue({
      brandingData: {
        colorSystem: { primary: '#4E40C5' }
        // No logo_url
      }
    });
    
    render(
      <TestWrapper>
        <AdvancedImageProcessing {...mockProps} />
      </TestWrapper>
    );

    // Switch to Branding tab
    const brandingTab = screen.getByText('Branding');
    await user.click(brandingTab);

    expect(screen.getByText('No logo found. Please upload a logo in the Logo & Assets section.')).toBeInTheDocument();
  });

  test('applies correct accessibility attributes', () => {
    render(
      <TestWrapper>
        <AdvancedImageProcessing {...mockProps} />
      </TestWrapper>
    );

    const uploadButton = screen.getByLabelText('Upload image file');
    const saveButton = screen.getByLabelText('Save processed image');
    const undoButton = screen.getByLabelText('Undo');
    const redoButton = screen.getByLabelText('Redo');

    expect(uploadButton).toHaveAttribute('aria-label', 'Upload image file');
    expect(saveButton).toHaveAttribute('aria-label', 'Save processed image');
    expect(undoButton).toBeInTheDocument();
    expect(redoButton).toBeInTheDocument();
  });

  test('passes through additional props', () => {
    render(
      <TestWrapper>
        <AdvancedImageProcessing 
          {...mockProps} 
          data-testid="test-image-processing"
          className="custom-class"
        />
      </TestWrapper>
    );

    const component = screen.getByTestId('test-image-processing');
    expect(component).toHaveClass('custom-class');
  });

  test('handles tab navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AdvancedImageProcessing {...mockProps} />
      </TestWrapper>
    );

    // Test all tabs
    const adjustmentsTab = screen.getByText('Adjustments');
    const filtersTab = screen.getByText('Filters');
    const overlayTab = screen.getByText('Overlay');
    const brandingTab = screen.getByText('Branding');

    await user.click(filtersTab);
    expect(screen.getByText('Filter Presets')).toBeInTheDocument();

    await user.click(overlayTab);
    expect(screen.getByText('Use Color Overlay')).toBeInTheDocument();

    await user.click(brandingTab);
    expect(screen.getByText('Add Logo')).toBeInTheDocument();

    await user.click(adjustmentsTab);
    expect(screen.getByText('Basic Adjustments')).toBeInTheDocument();
  });
});
