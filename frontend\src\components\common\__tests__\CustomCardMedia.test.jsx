// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import CustomCardMedia from '../CustomCardMedia';

// Mock navigator.share and clipboard
Object.assign(navigator, {
  share: vi.fn().mockResolvedValue(undefined),
  clipboard: {
    writeText: vi.fn().mockResolvedValue(undefined),
  },
});

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      error: {
        main: '#F44336',
      },
      grey: {
        100: '#F5F5F5',
        500: '#9E9E9E',
      },
      common: {
        black: '#000000',
        white: '#FFFFFF',
      },
    },
    spacing: (factor) => `${8 * factor}px`,
    transitions: {
      create: () => 'all 0.3s ease',
      duration: {
        short: 250,
        standard: 300,
      },
    },
    breakpoints: {
      down: (key) => `@media (max-width:${key === 'sm' ? 600 : 960}px)`,
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('CustomCardMedia', () => {
  const mockProps = {
    src: 'https://example.com/image.jpg',
    alt: 'Test image'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders image media correctly', () => {
    render(
      <TestWrapper>
        <CustomCardMedia {...mockProps} />
      </TestWrapper>
    );

    const image = screen.getByRole('img');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', 'https://example.com/image.jpg');
    expect(image).toHaveAttribute('alt', 'Test image');
  });

  test('renders video media correctly', () => {
    render(
      <TestWrapper>
        <CustomCardMedia 
          src="https://example.com/video.mp4"
          type="video"
          alt="Test video"
        />
      </TestWrapper>
    );

    const video = screen.getByLabelText('Test video');
    expect(video).toBeInTheDocument();
    expect(video).toHaveAttribute('src', 'https://example.com/video.mp4');
  });

  test('shows loading state correctly', () => {
    render(
      <TestWrapper>
        <CustomCardMedia loading={true} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(screen.getByText('Loading media...')).toBeInTheDocument();
  });

  test('shows error state correctly', () => {
    const error = 'Failed to load media';
    
    render(
      <TestWrapper>
        <CustomCardMedia error={error} />
      </TestWrapper>
    );

    expect(screen.getByText('Media Error')).toBeInTheDocument();
    expect(screen.getByText(error)).toBeInTheDocument();
  });

  test('handles retry functionality', async () => {
    const user = userEvent.setup();
    const onRetry = vi.fn();
    
    render(
      <TestWrapper>
        <CustomCardMedia 
          error="Failed to load"
          retryable={true}
          onRetry={onRetry}
        />
      </TestWrapper>
    );

    const retryButton = screen.getByText('Retry');
    await user.click(retryButton);

    expect(onRetry).toHaveBeenCalled();
  });

  test('handles media click when interactive', async () => {
    const user = userEvent.setup();
    const onClick = vi.fn();
    
    render(
      <TestWrapper>
        <CustomCardMedia 
          {...mockProps} 
          interactive={true}
          onClick={onClick}
        />
      </TestWrapper>
    );

    const image = screen.getByRole('img');
    await user.click(image);

    expect(onClick).toHaveBeenCalled();
  });

  test('applies different aspect ratios', () => {
    render(
      <TestWrapper>
        <CustomCardMedia {...mockProps} aspectRatio="16/9" />
      </TestWrapper>
    );

    const container = screen.getByRole('img').parentElement;
    expect(container).toBeInTheDocument();
  });

  test('applies different object fit values', () => {
    render(
      <TestWrapper>
        <CustomCardMedia {...mockProps} objectFit="contain" />
      </TestWrapper>
    );

    const image = screen.getByRole('img');
    expect(image).toBeInTheDocument();
  });

  test('shows placeholder during loading', () => {
    render(
      <TestWrapper>
        <CustomCardMedia 
          loading={true}
          placeholder="https://example.com/placeholder.jpg"
        />
      </TestWrapper>
    );

    const placeholder = screen.getByAltText('Loading...');
    expect(placeholder).toBeInTheDocument();
    expect(placeholder).toHaveAttribute('src', 'https://example.com/placeholder.jpg');
  });

  test('shows fallback image on error', () => {
    render(
      <TestWrapper>
        <CustomCardMedia 
          error="Failed to load"
          fallbackSrc="https://example.com/fallback.jpg"
        />
      </TestWrapper>
    );

    const fallback = screen.getByAltText('Fallback');
    expect(fallback).toBeInTheDocument();
    expect(fallback).toHaveAttribute('src', 'https://example.com/fallback.jpg');
  });

  test('handles video controls', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CustomCardMedia 
          src="https://example.com/video.mp4"
          type="video"
          showControls={true}
        />
      </TestWrapper>
    );

    const playButton = screen.getByLabelText('Play');
    expect(playButton).toBeInTheDocument();

    await user.click(playButton);
    // Video play/pause functionality would be tested with proper video mock
  });

  test('handles zoom functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CustomCardMedia 
          {...mockProps}
          type="video"
          enableZoom={true}
          showControls={true}
        />
      </TestWrapper>
    );

    const zoomInButton = screen.getByLabelText('Zoom in');
    const zoomOutButton = screen.getByLabelText('Zoom out');

    expect(zoomInButton).toBeInTheDocument();
    expect(zoomOutButton).toBeInTheDocument();

    await user.click(zoomInButton);
    // Zoom functionality would be tested with proper state tracking
  });

  test('handles download functionality', async () => {
    const user = userEvent.setup();
    
    // Mock fetch for download
    global.fetch = vi.fn().mockResolvedValue({
      blob: () => Promise.resolve(new Blob(['test'], { type: 'image/jpeg' }))
    });

    // Mock URL.createObjectURL
    global.URL.createObjectURL = vi.fn().mockReturnValue('blob:test-url');
    global.URL.revokeObjectURL = vi.fn();

    render(
      <TestWrapper>
        <CustomCardMedia 
          {...mockProps}
          type="video"
          enableDownload={true}
          showControls={true}
        />
      </TestWrapper>
    );

    const downloadButton = screen.getByLabelText('Download');
    await user.click(downloadButton);

    expect(global.fetch).toHaveBeenCalledWith('https://example.com/image.jpg');
  });

  test('handles share functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CustomCardMedia 
          {...mockProps}
          type="video"
          enableShare={true}
          showControls={true}
        />
      </TestWrapper>
    );

    const shareButton = screen.getByLabelText('Share');
    await user.click(shareButton);

    expect(navigator.share).toHaveBeenCalledWith({
      title: 'Test image',
      url: 'https://example.com/image.jpg'
    });
  });

  test('handles gallery navigation', async () => {
    const user = userEvent.setup();
    const onGalleryChange = vi.fn();
    
    const gallery = [
      { src: 'https://example.com/image1.jpg', alt: 'Image 1' },
      { src: 'https://example.com/image2.jpg', alt: 'Image 2' },
      { src: 'https://example.com/image3.jpg', alt: 'Image 3' }
    ];

    render(
      <TestWrapper>
        <CustomCardMedia 
          gallery={gallery}
          currentIndex={0}
          onGalleryChange={onGalleryChange}
          enableGalleryNavigation={true}
        />
      </TestWrapper>
    );

    const nextButton = screen.getByLabelText('Next image');
    await user.click(nextButton);

    expect(onGalleryChange).toHaveBeenCalledWith(1);

    const prevButton = screen.getByLabelText('Previous image');
    await user.click(prevButton);

    expect(onGalleryChange).toHaveBeenCalledWith(2); // Should wrap around
  });

  test('handles gallery indicators', async () => {
    const user = userEvent.setup();
    const onGalleryChange = vi.fn();
    
    const gallery = [
      { src: 'https://example.com/image1.jpg', alt: 'Image 1' },
      { src: 'https://example.com/image2.jpg', alt: 'Image 2' },
      { src: 'https://example.com/image3.jpg', alt: 'Image 3' }
    ];

    render(
      <TestWrapper>
        <CustomCardMedia 
          gallery={gallery}
          currentIndex={0}
          onGalleryChange={onGalleryChange}
          enableGalleryNavigation={true}
        />
      </TestWrapper>
    );

    const indicator = screen.getByLabelText('Go to image 2');
    await user.click(indicator);

    expect(onGalleryChange).toHaveBeenCalledWith(1);
  });

  test('handles fullscreen functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CustomCardMedia 
          {...mockProps}
          type="video"
          enableFullscreen={true}
          showControls={true}
        />
      </TestWrapper>
    );

    const fullscreenButton = screen.getByLabelText('Fullscreen');
    await user.click(fullscreenButton);

    // Should open fullscreen dialog
    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });
  });

  test('handles analytics tracking', async () => {
    const user = userEvent.setup();
    const onAnalytics = vi.fn();
    
    render(
      <TestWrapper>
        <CustomCardMedia 
          {...mockProps} 
          interactive={true}
          onClick={vi.fn()}
          enableAnalytics={true}
          onAnalytics={onAnalytics}
        />
      </TestWrapper>
    );

    const image = screen.getByRole('img');
    await user.click(image);

    expect(onAnalytics).toHaveBeenCalledWith(
      expect.objectContaining({
        component: 'CustomCardMedia',
        action: 'media_click'
      })
    );
  });

  test('shows metadata chips', () => {
    const metadata = {
      size: '1.2MB',
      format: 'JPEG',
      dimensions: '1920x1080'
    };

    render(
      <TestWrapper>
        <CustomCardMedia {...mockProps} metadata={metadata} />
      </TestWrapper>
    );

    expect(screen.getByText('size: 1.2MB')).toBeInTheDocument();
    expect(screen.getByText('format: JPEG')).toBeInTheDocument();
    expect(screen.getByText('dimensions: 1920x1080')).toBeInTheDocument();
  });

  test('shows watermark', () => {
    render(
      <TestWrapper>
        <CustomCardMedia {...mockProps} watermark="© 2024 Test" />
      </TestWrapper>
    );

    expect(screen.getByText('© 2024 Test')).toBeInTheDocument();
  });

  test('applies rounded corners', () => {
    render(
      <TestWrapper>
        <CustomCardMedia {...mockProps} rounded={true} />
      </TestWrapper>
    );

    const container = screen.getByRole('img').parentElement;
    expect(container).toBeInTheDocument();
  });

  test('applies custom height', () => {
    render(
      <TestWrapper>
        <CustomCardMedia {...mockProps} height="200px" />
      </TestWrapper>
    );

    const container = screen.getByRole('img').parentElement;
    expect(container).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <CustomCardMedia 
          {...mockProps} 
          ariaLabel="Custom media"
          role="figure"
        />
      </TestWrapper>
    );

    const media = screen.getByRole('img');
    expect(media).toHaveAttribute('aria-label', 'Custom media');
  });

  test('applies custom styles', () => {
    const customSx = { backgroundColor: 'red' };
    
    render(
      <TestWrapper>
        <CustomCardMedia {...mockProps} sx={customSx} />
      </TestWrapper>
    );

    const container = screen.getByRole('img').parentElement;
    expect(container).toBeInTheDocument();
  });

  test('handles lazy loading', () => {
    render(
      <TestWrapper>
        <CustomCardMedia {...mockProps} lazy={true} />
      </TestWrapper>
    );

    const image = screen.getByRole('img');
    expect(image).toHaveAttribute('loading', 'lazy');
  });

  test('handles eager loading', () => {
    render(
      <TestWrapper>
        <CustomCardMedia {...mockProps} lazy={false} />
      </TestWrapper>
    );

    const image = screen.getByRole('img');
    expect(image).toHaveAttribute('loading', 'eager');
  });
});
