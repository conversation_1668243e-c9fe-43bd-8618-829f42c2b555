/**
 * Token Encryption Service for Frontend Token Management
 * 
 * Provides secure encryption/decryption for authentication tokens using Web Crypto API
 * with AES-256-GCM encryption, following ACE Social security patterns.
 * 
 * @version 1.0.0
 * <AUTHOR> Social Platform Team
 * @since 2024-01-01
 * 
 * @example
 * ```javascript
 * import { TokenEncryptionService } from '../utils/TokenEncryptionService';
 * 
 * const encryption = new TokenEncryptionService();
 * 
 * // Encrypt token
 * const encrypted = await encryption.encryptToken('jwt_token_here');
 * 
 * // Decrypt token
 * const decrypted = await encryption.decryptToken(encrypted);
 * ```
 @since 2024-1-1 to 2025-25-7
*/

/**
 * Token Encryption Service for Frontend Authentication
 * 
 * Provides enterprise-grade encryption for authentication tokens using AES-256-GCM
 * encryption with secure key derivation and integrity validation.
 * 
 * Features:
 * - AES-256-GCM encryption for token content
 * - Secure key derivation from device characteristics
 * - Integrity validation to detect tampering
 * - Graceful fallback to plain text if encryption fails
 * - Integration with existing ACE Social security infrastructure
 */
export class TokenEncryptionService {
  /**
   * Initialize the encryption service
   * 
   * @param {Object} options - Configuration options
   * @param {boolean} options.enabled - Whether encryption is enabled
   * @param {string} options.algorithm - Encryption algorithm (default: AES-256-GCM)
   * @param {number} options.keyDerivationIterations - PBKDF2 iterations (default: 100000)
   */
  constructor(options = {}) {
    this.enabled = options.enabled !== false; // Default to enabled
    this.algorithm = options.algorithm || 'AES-GCM';
    this.keyLength = 256;
    this.keyDerivationIterations = options.keyDerivationIterations || 100000;
    this.ivLength = 12; // 96 bits for GCM
    this.tagLength = 16; // 128 bits for GCM
    
    // Cache for derived keys
    this.keyCache = new Map();
    this.keyCacheTTL = 300000; // 5 minutes
    
    // Initialize if Web Crypto API is available
    this.isSupported = this._checkWebCryptoSupport();
    
    if (!this.isSupported) {
      console.warn('[TokenEncryptionService] Web Crypto API not supported - encryption disabled');
      this.enabled = false;
    }
  }

  /**
   * Encrypt a token with device-specific key derivation
   * 
   * @param {string} token - Token to encrypt
   * @param {Object} options - Encryption options
   * @param {string} options.deviceFingerprint - Device fingerprint for key derivation
   * @returns {Promise<Object>} Encrypted token data
   */
  async encryptToken(token, options = {}) {
    if (!this.enabled || !token) {
      return { encrypted: false, data: token };
    }

    try {
      // Derive encryption key from device characteristics
      const encryptionKey = await this._deriveEncryptionKey(options.deviceFingerprint);
      
      // Generate random IV
      const iv = crypto.getRandomValues(new Uint8Array(this.ivLength));
      
      // Encrypt token
      const encoder = new TextEncoder();
      const tokenData = encoder.encode(token);
      
      const encryptedData = await crypto.subtle.encrypt(
        {
          name: this.algorithm,
          iv: iv,
          tagLength: this.tagLength * 8 // Convert to bits
        },
        encryptionKey,
        tokenData
      );
      
      // Combine IV and encrypted data
      const combined = new Uint8Array(iv.length + encryptedData.byteLength);
      combined.set(iv);
      combined.set(new Uint8Array(encryptedData), iv.length);
      
      // Convert to base64 for storage
      const base64Data = btoa(String.fromCharCode(...combined));
      
      return {
        encrypted: true,
        data: base64Data,
        algorithm: this.algorithm,
        keyDerivation: 'device-fingerprint',
        timestamp: Date.now()
      };
      
    } catch (error) {
      console.error('[TokenEncryptionService] Encryption failed:', error);
      // Fallback to plain text
      return { encrypted: false, data: token };
    }
  }

  /**
   * Decrypt a token with device-specific key derivation
   * 
   * @param {Object} encryptedData - Encrypted token data
   * @param {Object} options - Decryption options
   * @param {string} options.deviceFingerprint - Device fingerprint for key derivation
   * @returns {Promise<string|null>} Decrypted token or null if failed
   */
  async decryptToken(encryptedData, options = {}) {
    if (!this.enabled || !encryptedData) {
      return null;
    }

    // Handle plain text tokens
    if (!encryptedData.encrypted) {
      return encryptedData.data;
    }

    try {
      // Derive decryption key
      const decryptionKey = await this._deriveEncryptionKey(options.deviceFingerprint);
      
      // Convert from base64
      const combined = new Uint8Array(
        atob(encryptedData.data).split('').map(char => char.charCodeAt(0))
      );
      
      // Extract IV and encrypted data
      const iv = combined.slice(0, this.ivLength);
      const encrypted = combined.slice(this.ivLength);
      
      // Decrypt data
      const decryptedData = await crypto.subtle.decrypt(
        {
          name: this.algorithm,
          iv: iv,
          tagLength: this.tagLength * 8
        },
        decryptionKey,
        encrypted
      );
      
      // Convert back to string
      const decoder = new TextDecoder();
      return decoder.decode(decryptedData);
      
    } catch (error) {
      console.error('[TokenEncryptionService] Decryption failed:', error);
      return null;
    }
  }

  /**
   * Validate token integrity
   * 
   * @param {Object} encryptedData - Encrypted token data
   * @returns {boolean} Whether token integrity is valid
   */
  validateIntegrity(encryptedData) {
    if (!encryptedData || typeof encryptedData !== 'object') {
      return false;
    }

    // Check required fields
    const requiredFields = ['encrypted', 'data'];
    for (const field of requiredFields) {
      if (!(field in encryptedData)) {
        return false;
      }
    }

    // Validate encrypted data format
    if (encryptedData.encrypted) {
      try {
        // Validate base64 format
        atob(encryptedData.data);
        return true;
      } catch {
        return false;
      }
    }

    return true;
  }

  /**
   * Check if Web Crypto API is supported
   * 
   * @private
   * @returns {boolean} Whether Web Crypto API is supported
   */
  _checkWebCryptoSupport() {
    return (
      typeof window !== 'undefined' &&
      window.crypto &&
      window.crypto.subtle &&
      typeof window.crypto.subtle.encrypt === 'function' &&
      typeof window.crypto.subtle.decrypt === 'function' &&
      typeof window.crypto.subtle.importKey === 'function' &&
      typeof window.crypto.subtle.deriveKey === 'function'
    );
  }

  /**
   * Derive encryption key from device fingerprint
   * 
   * @private
   * @param {string} deviceFingerprint - Device fingerprint
   * @returns {Promise<CryptoKey>} Derived encryption key
   */
  async _deriveEncryptionKey(deviceFingerprint) {
    const keyMaterial = deviceFingerprint || 'default-key-material';
    const cacheKey = `key_${keyMaterial}`;
    
    // Check cache first
    if (this.keyCache.has(cacheKey)) {
      const cached = this.keyCache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.keyCacheTTL) {
        return cached.key;
      }
      this.keyCache.delete(cacheKey);
    }
    
    try {
      // Import key material
      const keyMaterialBuffer = new TextEncoder().encode(keyMaterial);
      const importedKey = await crypto.subtle.importKey(
        'raw',
        keyMaterialBuffer,
        'PBKDF2',
        false,
        ['deriveKey']
      );
      
      // Derive key using PBKDF2
      const salt = new TextEncoder().encode('ace-social-token-salt');
      const derivedKey = await crypto.subtle.deriveKey(
        {
          name: 'PBKDF2',
          salt: salt,
          iterations: this.keyDerivationIterations,
          hash: 'SHA-256'
        },
        importedKey,
        {
          name: this.algorithm,
          length: this.keyLength
        },
        false,
        ['encrypt', 'decrypt']
      );
      
      // Cache the derived key
      this.keyCache.set(cacheKey, {
        key: derivedKey,
        timestamp: Date.now()
      });
      
      return derivedKey;
      
    } catch (error) {
      console.error('[TokenEncryptionService] Key derivation failed:', error);
      throw error;
    }
  }

  /**
   * Clear key cache
   */
  clearKeyCache() {
    this.keyCache.clear();
  }

  /**
   * Get service status
   * 
   * @returns {Object} Service status information
   */
  getStatus() {
    return {
      enabled: this.enabled,
      supported: this.isSupported,
      algorithm: this.algorithm,
      keyLength: this.keyLength,
      cacheSize: this.keyCache.size
    };
  }
}

// Export singleton instance
export const tokenEncryption = new TokenEncryptionService({
  enabled: process.env.NODE_ENV === 'production' || process.env.REACT_APP_TOKEN_ENCRYPTION_ENABLED === 'true'
});

export default TokenEncryptionService;
