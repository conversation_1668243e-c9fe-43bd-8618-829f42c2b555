/**
 * Enhanced OpenAI Insights Service - Enterprise-grade AI integration for social media recommendations
 * 
 * Features:
 * - OpenAI API integration with GPT-4 for intelligent insights
 * - Subscription-based prompt engineering and response quality
 * - Rate limiting and quota management
 * - Comprehensive error handling and fallback mechanisms
 * - Response caching and performance optimization
 * - Analytics tracking and monitoring
 * - Security and data privacy compliance
 @since 2024-1-1 to 2025-25-7
*/

import { logger } from '../utils/logger';
import { aiInsightsCacheService } from './aiInsightsCacheService';

// OpenAI API configuration
const OPENAI_CONFIG = {
  apiUrl: process.env.REACT_APP_OPENAI_API_URL || 'https://api.openai.com/v1',
  model: 'gpt-4-turbo-preview',
  maxTokens: 2000,
  temperature: 0.7,
  timeout: 30000, // 30 seconds
  retryAttempts: 3,
  retryDelay: 1000 // 1 second
};

// Rate limiting configuration
const RATE_LIMITS = {
  creator: {
    requestsPerHour: 10,
    requestsPerDay: 50,
    maxTokensPerRequest: 1000
  },
  accelerator: {
    requestsPerHour: 30,
    requestsPerDay: 200,
    maxTokensPerRequest: 1500
  },
  dominator: {
    requestsPerHour: 100,
    requestsPerDay: 1000,
    maxTokensPerRequest: 2000
  }
};

// Subscription-based prompt templates
const PROMPT_TEMPLATES = {
  creator: {
    systemPrompt: `You are an AI social media advisor for ACE Social platform users. Provide basic, actionable insights based on the provided analytics data. Focus on simple, clear recommendations that a content creator can easily implement. Keep responses concise and practical.`,
    maxRecommendations: 3,
    complexity: 'basic'
  },
  accelerator: {
    systemPrompt: `You are an advanced AI social media strategist for ACE Social platform. Analyze the comprehensive analytics data including competitive insights and provide strategic recommendations. Include data-driven insights, competitive analysis, and advanced optimization strategies. Provide detailed explanations with confidence scores.`,
    maxRecommendations: 5,
    complexity: 'advanced'
  },
  dominator: {
    systemPrompt: `You are a premium AI social media intelligence advisor for ACE Social platform. Leverage all available data including predictive analytics, competitive intelligence, and advanced metrics to provide expert-level strategic recommendations. Include predictive insights, market opportunities, and sophisticated optimization strategies with detailed analysis and implementation roadmaps.`,
    maxRecommendations: 8,
    complexity: 'premium'
  }
};

/**
 * OpenAI Insights Service Class
 */
class OpenAIInsightsService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 10 * 60 * 1000; // 10 minutes
    this.rateLimitTracker = new Map();
    this.requestQueue = [];
    this.isProcessing = false;
    
    // Performance and usage metrics
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      rateLimitedRequests: 0,
      averageResponseTime: 0,
      totalTokensUsed: 0,
      cacheHits: 0,
      cacheMisses: 0
    };
  }

  /**
   * Generate AI insights and recommendations
   * @param {Object} aggregatedData - Comprehensive metrics data
   * @param {Object} subscription - User subscription information
   * @param {Object} options - Configuration options
   * @returns {Promise<Object>} AI-generated insights and recommendations
   */
  async generateInsights(aggregatedData, subscription, options = {}) {
    const startTime = Date.now();
    const planId = subscription?.plan_id || 'creator';
    
    try {
      this.metrics.totalRequests++;
      
      // Check rate limits
      if (!this._checkRateLimit(subscription.user_id, planId)) {
        this.metrics.rateLimitedRequests++;
        throw new Error('Rate limit exceeded. Please try again later.');
      }
      
      // Check enhanced cache first
      const cacheKey = this._generateCacheKey(aggregatedData, planId, options);
      const cachedInsights = await aiInsightsCacheService.get(cacheKey, {
        validateFreshness: true,
        useMemory: true,
        useLocalStorage: true,
        useSessionStorage: false // Don't use session storage for AI insights
      });

      if (cachedInsights) {
        this.metrics.cacheHits++;
        logger.debug('AI insights served from enhanced cache', { cacheKey, planId });
        return cachedInsights;
      }

      this.metrics.cacheMisses++;
      
      // Generate AI insights
      const insights = await this._generateAIInsights(aggregatedData, planId, options);
      
      // Cache the result using enhanced cache service
      await aiInsightsCacheService.set(cacheKey, insights, {
        useMemory: true,
        useLocalStorage: true,
        useSessionStorage: false,
        ttl: planId === 'dominator' ? 5 * 60 * 1000 : // 5 minutes for dominator
             planId === 'accelerator' ? 10 * 60 * 1000 : // 10 minutes for accelerator
             15 * 60 * 1000 // 15 minutes for creator
      });
      
      // Update rate limit tracker
      this._updateRateLimit(subscription.user_id, planId);
      
      const responseTime = Date.now() - startTime;
      this._updateMetrics(responseTime, true, insights.tokensUsed || 0);
      
      logger.info('AI insights generated successfully', {
        planId,
        responseTime,
        tokensUsed: insights.tokensUsed,
        recommendationsCount: insights.recommendations?.length || 0
      });
      
      return insights;
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this._updateMetrics(responseTime, false, 0);
      
      logger.error('Failed to generate AI insights', error);
      
      // Return fallback insights on error
      return this._generateFallbackInsights(aggregatedData, planId);
    }
  }

  /**
   * Generate AI insights using OpenAI API
   * @private
   */
  async _generateAIInsights(aggregatedData, planId, options) {
    const template = PROMPT_TEMPLATES[planId] || PROMPT_TEMPLATES.creator;
    const rateLimit = RATE_LIMITS[planId] || RATE_LIMITS.creator;
    
    // Prepare structured data for AI analysis
    const structuredData = this._prepareDataForAI(aggregatedData, template.complexity);
    
    // Create the prompt
    const prompt = this._createPrompt(structuredData, template, options);
    
    // Make OpenAI API request
    const response = await this._makeOpenAIRequest(prompt, {
      maxTokens: Math.min(rateLimit.maxTokensPerRequest, OPENAI_CONFIG.maxTokens),
      temperature: OPENAI_CONFIG.temperature
    });
    
    // Parse and structure the response
    const insights = this._parseAIResponse(response, template, aggregatedData);
    
    return insights;
  }

  /**
   * Prepare data for AI analysis based on complexity level
   * @private
   */
  _prepareDataForAI(aggregatedData, complexity) {
    const baseData = {
      performance: aggregatedData.performance,
      content: {
        distribution: aggregatedData.content.distribution,
        topPerforming: aggregatedData.content.performance.slice(0, 5),
        optimalTimes: aggregatedData.content.optimalTimes
      },
      audience: {
        demographics: aggregatedData.audience.demographics,
        behavior: aggregatedData.audience.behavior,
        growth: aggregatedData.audience.growth
      },
      sentiment: aggregatedData.sentiment.overall,
      temporal: aggregatedData.temporal.patterns
    };

    // Add advanced data for higher complexity levels
    if (complexity === 'advanced' || complexity === 'premium') {
      baseData.competitive = aggregatedData.competitive;
      baseData.sentimentDetails = aggregatedData.sentiment;
    }

    // Add predictive data for premium level
    if (complexity === 'premium') {
      baseData.predictive = aggregatedData.predictive;
      baseData.fullTemporal = aggregatedData.temporal;
    }

    return baseData;
  }

  /**
   * Create AI prompt based on template and data
   * @private
   */
  _createPrompt(structuredData, template, options) {
    const dataContext = JSON.stringify(structuredData, null, 2);
    
    const userPrompt = `
Analyze the following social media analytics data and provide ${template.maxRecommendations} actionable insights and recommendations:

ANALYTICS DATA:
${dataContext}

Please provide recommendations in the following JSON format:
{
  "insights": [
    {
      "id": "unique-id",
      "title": "Insight Title",
      "description": "Detailed description",
      "category": "content|timing|audience|engagement|competitive",
      "type": "positive|negative|neutral|opportunity|warning",
      "confidence": 0.85,
      "impact": "high|medium|low",
      "actionable": true,
      "actions": [
        {
          "title": "Action Title",
          "description": "Action description",
          "priority": "high|medium|low",
          "effort": "low|medium|high",
          "timeline": "immediate|short-term|long-term"
        }
      ],
      "metrics": {
        "currentValue": "current metric value",
        "expectedImprovement": "expected improvement",
        "dataSource": "source of the insight"
      }
    }
  ],
  "summary": {
    "overallPerformance": "assessment",
    "keyOpportunities": ["opportunity1", "opportunity2"],
    "priorityActions": ["action1", "action2"],
    "nextSteps": "recommended next steps"
  }
}

Focus on practical, data-driven recommendations that align with the user's subscription level capabilities.
`;

    return [
      { role: 'system', content: template.systemPrompt },
      { role: 'user', content: userPrompt }
    ];
  }

  /**
   * Make OpenAI API request with retry logic
   * @private
   */
  async _makeOpenAIRequest(messages, options) {
    const requestConfig = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.REACT_APP_OPENAI_API_KEY}`
      },
      body: JSON.stringify({
        model: OPENAI_CONFIG.model,
        messages,
        max_tokens: options.maxTokens,
        temperature: options.temperature,
        response_format: { type: 'json_object' }
      })
    };

    let lastError;
    
    for (let attempt = 1; attempt <= OPENAI_CONFIG.retryAttempts; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), OPENAI_CONFIG.timeout);
        
        const response = await fetch(`${OPENAI_CONFIG.apiUrl}/chat/completions`, {
          ...requestConfig,
          signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        if (!response.ok) {
          throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        return data;
        
      } catch (error) {
        lastError = error;
        logger.warn(`OpenAI API request attempt ${attempt} failed`, error);
        
        if (attempt < OPENAI_CONFIG.retryAttempts) {
          await new Promise(resolve => setTimeout(resolve, OPENAI_CONFIG.retryDelay * attempt));
        }
      }
    }
    
    throw lastError;
  }

  /**
   * Parse AI response and structure insights
   * @private
   */
  _parseAIResponse(response, template, originalData) {
    try {
      const content = response.choices[0]?.message?.content;
      const tokensUsed = response.usage?.total_tokens || 0;
      
      if (!content) {
        throw new Error('No content in OpenAI response');
      }
      
      const parsedInsights = JSON.parse(content);
      
      // Validate and enhance the response
      const enhancedInsights = {
        insights: this._validateInsights(parsedInsights.insights || [], template),
        summary: parsedInsights.summary || {},
        metadata: {
          planId: template.complexity,
          tokensUsed,
          generatedAt: new Date().toISOString(),
          dataSourcesCount: Object.keys(originalData).length,
          confidence: this._calculateOverallConfidence(parsedInsights.insights || [])
        },
        tokensUsed
      };
      
      return enhancedInsights;
      
    } catch (error) {
      logger.error('Failed to parse AI response', error);
      throw new Error('Invalid AI response format');
    }
  }

  /**
   * Validate and limit insights based on subscription
   * @private
   */
  _validateInsights(insights, template) {
    return insights
      .slice(0, template.maxRecommendations)
      .map((insight, index) => ({
        id: insight.id || `insight-${index + 1}`,
        title: insight.title || 'Untitled Insight',
        description: insight.description || '',
        category: insight.category || 'general',
        type: insight.type || 'neutral',
        confidence: Math.min(Math.max(insight.confidence || 0.5, 0), 1),
        impact: insight.impact || 'medium',
        actionable: insight.actionable !== false,
        actions: insight.actions || [],
        metrics: insight.metrics || {}
      }));
  }

  /**
   * Calculate overall confidence score
   * @private
   */
  _calculateOverallConfidence(insights) {
    if (!insights.length) return 0;

    const totalConfidence = insights.reduce((sum, insight) => sum + (insight.confidence || 0), 0);
    return totalConfidence / insights.length;
  }

  /**
   * Generate fallback insights when AI service fails
   * @private
   */
  _generateFallbackInsights(aggregatedData, planId) {
    const template = PROMPT_TEMPLATES[planId] || PROMPT_TEMPLATES.creator;

    const fallbackInsights = [
      {
        id: 'engagement-analysis',
        title: 'Engagement Performance Analysis',
        description: `Your current engagement rate is ${(aggregatedData.performance?.engagement?.rate || 0).toFixed(2)}%. ${
          aggregatedData.performance?.engagement?.trend === 'up' ? 'This shows positive growth!' :
          aggregatedData.performance?.engagement?.trend === 'down' ? 'Consider reviewing your content strategy.' :
          'Your engagement is stable.'
        }`,
        category: 'engagement',
        type: aggregatedData.performance?.engagement?.trend === 'up' ? 'positive' :
              aggregatedData.performance?.engagement?.trend === 'down' ? 'negative' : 'neutral',
        confidence: 0.8,
        impact: 'high',
        actionable: true,
        actions: [
          {
            title: 'Review Top Performing Content',
            description: 'Analyze your best-performing posts to understand what resonates with your audience',
            priority: 'high',
            effort: 'low',
            timeline: 'immediate'
          }
        ],
        metrics: {
          currentValue: `${(aggregatedData.performance?.engagement?.rate || 0).toFixed(2)}%`,
          expectedImprovement: '10-15%',
          dataSource: 'engagement_analytics'
        }
      },
      {
        id: 'content-optimization',
        title: 'Content Type Optimization',
        description: `Your ${aggregatedData.performance?.contentPerformance?.bestPerformingType || 'image'} content performs best. Consider creating more of this content type.`,
        category: 'content',
        type: 'opportunity',
        confidence: 0.75,
        impact: 'medium',
        actionable: true,
        actions: [
          {
            title: 'Increase Best-Performing Content Type',
            description: `Focus on creating more ${aggregatedData.performance?.contentPerformance?.bestPerformingType || 'image'} content`,
            priority: 'medium',
            effort: 'medium',
            timeline: 'short-term'
          }
        ],
        metrics: {
          currentValue: aggregatedData.performance?.contentPerformance?.bestPerformingType || 'unknown',
          expectedImprovement: '20-25%',
          dataSource: 'content_analytics'
        }
      }
    ];

    // Add competitive insights for higher tier plans
    if (template.complexity !== 'basic' && aggregatedData.competitive) {
      fallbackInsights.push({
        id: 'competitive-opportunity',
        title: 'Competitive Positioning',
        description: 'Based on competitive analysis, there are opportunities to improve your market position.',
        category: 'competitive',
        type: 'opportunity',
        confidence: 0.7,
        impact: 'high',
        actionable: true,
        actions: [
          {
            title: 'Analyze Competitor Strategies',
            description: 'Review competitor content and engagement strategies for optimization opportunities',
            priority: 'medium',
            effort: 'high',
            timeline: 'long-term'
          }
        ],
        metrics: {
          currentValue: 'Competitive analysis available',
          expectedImprovement: '15-20%',
          dataSource: 'competitive_intelligence'
        }
      });
    }

    return {
      insights: fallbackInsights.slice(0, template.maxRecommendations),
      summary: {
        overallPerformance: 'Analysis based on available data',
        keyOpportunities: ['Content optimization', 'Engagement improvement'],
        priorityActions: ['Review top content', 'Optimize posting strategy'],
        nextSteps: 'Focus on data-driven content creation and audience engagement'
      },
      metadata: {
        planId: template.complexity,
        tokensUsed: 0,
        generatedAt: new Date().toISOString(),
        dataSourcesCount: Object.keys(aggregatedData).length,
        confidence: 0.75,
        fallback: true
      },
      tokensUsed: 0
    };
  }

  /**
   * Check rate limits for user and plan
   * @private
   */
  _checkRateLimit(userId, planId) {
    const limits = RATE_LIMITS[planId] || RATE_LIMITS.creator;
    const now = Date.now();
    const userKey = `${userId}_${planId}`;

    if (!this.rateLimitTracker.has(userKey)) {
      this.rateLimitTracker.set(userKey, {
        hourlyRequests: [],
        dailyRequests: []
      });
    }

    const tracker = this.rateLimitTracker.get(userKey);

    // Clean old requests
    const oneHourAgo = now - (60 * 60 * 1000);
    const oneDayAgo = now - (24 * 60 * 60 * 1000);

    tracker.hourlyRequests = tracker.hourlyRequests.filter(time => time > oneHourAgo);
    tracker.dailyRequests = tracker.dailyRequests.filter(time => time > oneDayAgo);

    // Check limits
    return tracker.hourlyRequests.length < limits.requestsPerHour &&
           tracker.dailyRequests.length < limits.requestsPerDay;
  }

  /**
   * Update rate limit tracker
   * @private
   */
  _updateRateLimit(userId, planId) {
    const userKey = `${userId}_${planId}`;
    const tracker = this.rateLimitTracker.get(userKey);
    const now = Date.now();

    if (tracker) {
      tracker.hourlyRequests.push(now);
      tracker.dailyRequests.push(now);
    }
  }

  /**
   * Generate cache key
   * @private
   */
  _generateCacheKey(aggregatedData, planId, options) {
    const dataHash = this._hashData(aggregatedData);
    const optionsHash = JSON.stringify(options);
    return `ai_insights_${planId}_${dataHash}_${btoa(optionsHash)}`;
  }

  /**
   * Simple hash function for data
   * @private
   */
  _hashData(data) {
    const str = JSON.stringify(data);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Get data from cache
   * @private
   */
  _getFromCache(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  /**
   * Set data in cache
   * @private
   */
  _setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Update performance metrics
   * @private
   */
  _updateMetrics(responseTime, success, tokensUsed) {
    if (success) {
      this.metrics.successfulRequests++;
    } else {
      this.metrics.failedRequests++;
    }

    this.metrics.totalTokensUsed += tokensUsed;

    // Update average response time
    const totalRequests = this.metrics.successfulRequests + this.metrics.failedRequests;
    this.metrics.averageResponseTime =
      (this.metrics.averageResponseTime * (totalRequests - 1) + responseTime) / totalRequests;
  }

  /**
   * Get service metrics and usage statistics
   */
  getMetrics() {
    const cacheStats = aiInsightsCacheService.getStats();
    return {
      ...this.metrics,
      cache: cacheStats
    };
  }

  /**
   * Get rate limit status for user
   */
  getRateLimitStatus(userId, planId) {
    const limits = RATE_LIMITS[planId] || RATE_LIMITS.creator;
    const userKey = `${userId}_${planId}`;
    const tracker = this.rateLimitTracker.get(userKey);

    if (!tracker) {
      return {
        hourlyRemaining: limits.requestsPerHour,
        dailyRemaining: limits.requestsPerDay,
        resetTime: null
      };
    }

    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);
    const oneDayAgo = now - (24 * 60 * 60 * 1000);

    const recentHourly = tracker.hourlyRequests.filter(time => time > oneHourAgo);
    const recentDaily = tracker.dailyRequests.filter(time => time > oneDayAgo);

    return {
      hourlyRemaining: Math.max(0, limits.requestsPerHour - recentHourly.length),
      dailyRemaining: Math.max(0, limits.requestsPerDay - recentDaily.length),
      resetTime: recentHourly.length > 0 ? new Date(Math.max(...recentHourly) + (60 * 60 * 1000)) : null
    };
  }

  /**
   * Clear cache and reset metrics
   */
  async clearCache() {
    this.cache.clear();
    await aiInsightsCacheService.clearAll();
    logger.info('OpenAI insights cache cleared');
  }

  /**
   * Reset rate limits for testing
   */
  resetRateLimits() {
    this.rateLimitTracker.clear();
    logger.info('Rate limits reset');
  }
}

// Export singleton instance
export const openAIInsightsService = new OpenAIInsightsService();
export default openAIInsightsService;
