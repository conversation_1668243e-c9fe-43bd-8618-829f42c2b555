// @since 2024-1-1 to 2025-25-7
import { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  Grid,
  Button,
  Alert,
  Avatar,
  LinearProgress,
  Checkbox,
  Toolbar,
  CircularProgress,
  Skeleton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  Snackbar
} from '@mui/material';
import {
  MoreVert as MoreIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Preview as PreviewIcon,
  FileCopy as DuplicateIcon,
  Publish as PublishIcon,
  Archive as ArchiveIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Article as ArticleIcon,
  Visibility as ViewIcon,
  ThumbUp as HelpfulIcon,
  Schedule as ScheduleIcon,
  CheckCircle as PublishedIcon,
  Draft as DraftIcon,
  Warning as ReviewIcon
} from '@mui/icons-material';

const KnowledgeBaseArticleList = ({
  articles = [],
  loading = false,
  onEdit,
  onDelete,
  onDuplicate,
  onBulkOperation,
  onRefresh
}) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedArticle, setSelectedArticle] = useState(null);
  const [selectedArticles, setSelectedArticles] = useState([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [bulkDeleteDialogOpen, setBulkDeleteDialogOpen] = useState(false);
  const [operationLoading, setOperationLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  // Filter articles based on search and filters
  const filteredArticles = articles.filter(article => {
    const matchesSearch = !searchTerm || 
      article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.summary?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.content.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = categoryFilter === 'all' || article.category === categoryFilter;
    const matchesStatus = statusFilter === 'all' || article.status === statusFilter;
    const matchesType = typeFilter === 'all' || article.article_type === typeFilter;
    
    return matchesSearch && matchesCategory && matchesStatus && matchesType;
  });

  // Paginated articles
  const paginatedArticles = filteredArticles.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  const handleChangePage = (_, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleMenuOpen = (event, article) => {
    setAnchorEl(event.currentTarget);
    setSelectedArticle(article);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedArticle(null);
  };

  const handleEdit = () => {
    if (selectedArticle && onEdit) {
      onEdit(selectedArticle);
    }
    handleMenuClose();
  };

  const handleDelete = () => {
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (selectedArticle && onDelete) {
      setOperationLoading(true);
      try {
        await onDelete(selectedArticle.id);
        showSnackbar('Article deleted successfully', 'success');
      } catch (error) {
        console.error('Error deleting article:', error);
        showSnackbar('Failed to delete article', 'error');
      } finally {
        setOperationLoading(false);
        setDeleteDialogOpen(false);
        handleMenuClose();
      }
    }
  };

  const handleDuplicate = async () => {
    if (selectedArticle && onDuplicate) {
      setOperationLoading(true);
      try {
        await onDuplicate(selectedArticle);
        showSnackbar('Article duplicated successfully', 'success');
      } catch (error) {
        console.error('Error duplicating article:', error);
        showSnackbar('Failed to duplicate article', 'error');
      } finally {
        setOperationLoading(false);
        handleMenuClose();
      }
    }
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const handleSelectArticle = (articleId) => {
    setSelectedArticles(prev => {
      if (prev.includes(articleId)) {
        const newSelection = prev.filter(id => id !== articleId);
        setShowBulkActions(newSelection.length > 0);
        return newSelection;
      } else {
        const newSelection = [...prev, articleId];
        setShowBulkActions(newSelection.length > 0);
        return newSelection;
      }
    });
  };

  const handleSelectAll = () => {
    if (selectedArticles.length === paginatedArticles.length) {
      setSelectedArticles([]);
      setShowBulkActions(false);
    } else {
      const allIds = paginatedArticles.map(article => article.id);
      setSelectedArticles(allIds);
      setShowBulkActions(true);
    }
  };

  const handleBulkAction = async (action) => {
    if (selectedArticles.length === 0) return;

    // Show confirmation for destructive actions
    if (action === 'delete') {
      setBulkDeleteDialogOpen(true);
      return;
    }

    await performBulkAction(action);
  };

  const performBulkAction = async (action) => {
    setOperationLoading(true);
    try {
      await onBulkOperation(action, selectedArticles);
      setSelectedArticles([]);
      setShowBulkActions(false);

      const actionPastTense = {
        'publish': 'published',
        'archive': 'archived',
        'delete': 'deleted',
        'update_category': 'updated'
      };

      showSnackbar(
        `${selectedArticles.length} article(s) ${actionPastTense[action] || 'updated'} successfully`,
        'success'
      );
    } catch (error) {
      console.error('Bulk operation failed:', error);
      showSnackbar(`Failed to ${action} selected articles`, 'error');
    } finally {
      setOperationLoading(false);
      setBulkDeleteDialogOpen(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'published':
        return <PublishedIcon color="success" />;
      case 'draft':
        return <DraftIcon color="default" />;
      case 'archived':
        return <ArchiveIcon color="warning" />;
      case 'scheduled':
        return <ScheduleIcon color="info" />;
      case 'under_review':
        return <ReviewIcon color="primary" />;
      default:
        return <ArticleIcon color="default" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'published':
        return 'success';
      case 'draft':
        return 'default';
      case 'archived':
        return 'warning';
      case 'scheduled':
        return 'info';
      case 'under_review':
        return 'primary';
      default:
        return 'default';
    }
  };

  const getCategoryColor = (category) => {
    const colors = {
      'faq': 'primary',
      'tutorials': 'secondary',
      'api_documentation': 'info',
      'user_guides': 'success',
      'troubleshooting': 'warning',
      'announcements': 'error'
    };
    return colors[category] || 'default';
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString();
  };

  const truncateText = (text, maxLength = 100) => {
    if (!text) return '';
    return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
  };

  if (articles.length === 0 && !loading) {
    return (
      <Card>
        <CardContent>
          <Alert severity="info">
            No knowledge base articles found. Click &ldquo;Create Article&rdquo; to create your first article.
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (loading && articles.length === 0) {
    return (
      <Card>
        <CardContent>
          {/* Loading Skeletons */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={3}>
              <Skeleton variant="rectangular" height={56} />
            </Grid>
            <Grid item xs={12} md={2}>
              <Skeleton variant="rectangular" height={56} />
            </Grid>
            <Grid item xs={12} md={2}>
              <Skeleton variant="rectangular" height={56} />
            </Grid>
            <Grid item xs={12} md={2}>
              <Skeleton variant="rectangular" height={56} />
            </Grid>
            <Grid item xs={12} md={3}>
              <Skeleton variant="rectangular" height={56} />
            </Grid>
          </Grid>

          <Skeleton variant="text" width={200} height={20} sx={{ mb: 2 }} />

          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox">
                    <Skeleton variant="rectangular" width={20} height={20} />
                  </TableCell>
                  <TableCell><Skeleton variant="text" /></TableCell>
                  <TableCell><Skeleton variant="text" /></TableCell>
                  <TableCell><Skeleton variant="text" /></TableCell>
                  <TableCell><Skeleton variant="text" /></TableCell>
                  <TableCell><Skeleton variant="text" /></TableCell>
                  <TableCell><Skeleton variant="text" /></TableCell>
                  <TableCell><Skeleton variant="text" /></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {[...Array(5)].map((_, index) => (
                  <TableRow key={index}>
                    <TableCell padding="checkbox">
                      <Skeleton variant="rectangular" width={20} height={20} />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Skeleton variant="circular" width={40} height={40} />
                        <Box sx={{ flex: 1 }}>
                          <Skeleton variant="text" width="80%" />
                          <Skeleton variant="text" width="60%" />
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell><Skeleton variant="rectangular" width={80} height={24} /></TableCell>
                    <TableCell><Skeleton variant="rectangular" width={80} height={24} /></TableCell>
                    <TableCell><Skeleton variant="rectangular" width={120} height={40} /></TableCell>
                    <TableCell><Skeleton variant="text" /></TableCell>
                    <TableCell><Skeleton variant="text" /></TableCell>
                    <TableCell><Skeleton variant="circular" width={24} height={24} /></TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        {/* Bulk Actions Toolbar */}
        {showBulkActions && (
          <Toolbar sx={{ mb: 2, bgcolor: 'action.selected', borderRadius: 1 }}>
            <Typography variant="subtitle1" sx={{ flex: 1 }}>
              {selectedArticles.length} article(s) selected
            </Typography>
            <Button
              size="small"
              startIcon={<PublishIcon />}
              onClick={() => handleBulkAction('publish')}
              sx={{ mr: 1 }}
            >
              Publish
            </Button>
            <Button
              size="small"
              startIcon={<ArchiveIcon />}
              onClick={() => handleBulkAction('archive')}
              sx={{ mr: 1 }}
            >
              Archive
            </Button>
            <Button
              size="small"
              startIcon={<DeleteIcon />}
              onClick={() => handleBulkAction('delete')}
              color="error"
            >
              Delete
            </Button>
          </Toolbar>
        )}

        {/* Filters */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              placeholder="Search articles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Category</InputLabel>
              <Select
                value={categoryFilter}
                label="Category"
                onChange={(e) => setCategoryFilter(e.target.value)}
              >
                <MenuItem value="all">All Categories</MenuItem>
                <MenuItem value="faq">FAQ</MenuItem>
                <MenuItem value="tutorials">Tutorials</MenuItem>
                <MenuItem value="api_documentation">API Docs</MenuItem>
                <MenuItem value="user_guides">User Guides</MenuItem>
                <MenuItem value="troubleshooting">Troubleshooting</MenuItem>
                <MenuItem value="announcements">Announcements</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                label="Status"
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <MenuItem value="all">All Statuses</MenuItem>
                <MenuItem value="published">Published</MenuItem>
                <MenuItem value="draft">Draft</MenuItem>
                <MenuItem value="archived">Archived</MenuItem>
                <MenuItem value="scheduled">Scheduled</MenuItem>
                <MenuItem value="under_review">Under Review</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Type</InputLabel>
              <Select
                value={typeFilter}
                label="Type"
                onChange={(e) => setTypeFilter(e.target.value)}
              >
                <MenuItem value="all">All Types</MenuItem>
                <MenuItem value="help_article">Help Article</MenuItem>
                <MenuItem value="tutorial">Tutorial</MenuItem>
                <MenuItem value="faq">FAQ</MenuItem>
                <MenuItem value="technical_doc">Technical Doc</MenuItem>
                <MenuItem value="announcement">Announcement</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<FilterIcon />}
              onClick={onRefresh}
              sx={{ height: '56px' }}
            >
              Refresh
            </Button>
          </Grid>
        </Grid>

        {/* Results Summary */}
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Showing {filteredArticles.length} of {articles.length} articles
        </Typography>

        {/* Table */}
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={selectedArticles.length > 0 && selectedArticles.length < paginatedArticles.length}
                    checked={paginatedArticles.length > 0 && selectedArticles.length === paginatedArticles.length}
                    onChange={handleSelectAll}
                  />
                </TableCell>
                <TableCell>Article</TableCell>
                <TableCell>Category</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Performance</TableCell>
                <TableCell>Author</TableCell>
                <TableCell>Last Updated</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedArticles.map((article) => (
                <TableRow key={article.id} hover>
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={selectedArticles.includes(article.id)}
                      onChange={() => handleSelectArticle(article.id)}
                    />
                  </TableCell>
                  
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar sx={{ bgcolor: getCategoryColor(article.category) + '.main' }}>
                        <ArticleIcon />
                      </Avatar>
                      <Box>
                        <Typography variant="subtitle2" fontWeight="medium">
                          {article.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {truncateText(article.summary, 80)}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                          {article.is_featured && (
                            <Chip label="Featured" size="small" color="primary" variant="outlined" />
                          )}
                          {article.is_internal && (
                            <Chip label="Internal" size="small" color="warning" variant="outlined" />
                          )}
                        </Box>
                      </Box>
                    </Box>
                  </TableCell>
                  
                  <TableCell>
                    <Chip
                      label={article.category.replace('_', ' ')}
                      color={getCategoryColor(article.category)}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {getStatusIcon(article.status)}
                      <Chip
                        label={article.status.replace('_', ' ')}
                        color={getStatusColor(article.status)}
                        size="small"
                        variant="outlined"
                      />
                    </Box>
                  </TableCell>
                  
                  <TableCell>
                    <Box sx={{ minWidth: 120 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                        <ViewIcon fontSize="small" color="action" />
                        <Typography variant="caption">
                          {article.view_count || 0} views
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <HelpfulIcon fontSize="small" color="action" />
                        <Typography variant="caption">
                          {((article.helpfulness_score || 0) * 100).toFixed(0)}% helpful
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={(article.helpfulness_score || 0) * 100}
                        sx={{ height: 4, borderRadius: 2, mt: 0.5 }}
                      />
                    </Box>
                  </TableCell>
                  
                  <TableCell>
                    <Typography variant="body2">
                      {article.author_name || 'Unknown'}
                    </Typography>
                  </TableCell>
                  
                  <TableCell>
                    <Typography variant="body2">
                      {formatDate(article.updated_at)}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      v{article.version || 1}
                    </Typography>
                  </TableCell>
                  
                  <TableCell align="right">
                    <Tooltip title="More actions">
                      <IconButton
                        onClick={(e) => handleMenuOpen(e, article)}
                        size="small"
                      >
                        <MoreIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={filteredArticles.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />

        {/* Action Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={handleEdit}>
            <ListItemIcon>
              <EditIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Edit Article</ListItemText>
          </MenuItem>
          
          <MenuItem onClick={handleDuplicate}>
            <ListItemIcon>
              <DuplicateIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Duplicate Article</ListItemText>
          </MenuItem>
          
          <MenuItem onClick={() => window.open(`/knowledge-base/articles/${selectedArticle?.id}`, '_blank')}>
            <ListItemIcon>
              <PreviewIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Preview Article</ListItemText>
          </MenuItem>
          
          <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
            <ListItemIcon>
              <DeleteIcon fontSize="small" color="error" />
            </ListItemIcon>
            <ListItemText>Delete Article</ListItemText>
          </MenuItem>
        </Menu>

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={deleteDialogOpen}
          onClose={() => setDeleteDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Confirm Delete</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete the article &ldquo;{selectedArticle?.title}&rdquo;?
              This action cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleConfirmDelete}
              color="error"
              variant="contained"
              disabled={operationLoading}
            >
              {operationLoading ? <CircularProgress size={20} /> : 'Delete'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Bulk Delete Confirmation Dialog */}
        <Dialog
          open={bulkDeleteDialogOpen}
          onClose={() => setBulkDeleteDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Confirm Bulk Delete</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete {selectedArticles.length} selected articles?
              This action cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setBulkDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => performBulkAction('delete')}
              color="error"
              variant="contained"
              disabled={operationLoading}
            >
              {operationLoading ? <CircularProgress size={20} /> : 'Delete All'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            variant="filled"
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </CardContent>
    </Card>
  );
};

export default KnowledgeBaseArticleList;
