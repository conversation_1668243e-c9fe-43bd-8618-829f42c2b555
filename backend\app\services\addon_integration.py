"""
Integration service that ties together all add-on system components.
Provides a unified interface for add-on operations across the ACEO platform.
@since 2024-1-1 to 2025-25-7
"""
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone

from app.models.user import User
from app.services.addon_catalog import get_addon_catalog, get_addon_by_id, validate_addon_purchase
from app.services.addon_usage_tracking import track_addon_usage, get_user_addon_status, usage_tracker
from app.services.addon_marketing import get_addon_recommendations, check_addon_promotions, send_usage_upsell
from app.services.feature_access_enhanced import check_feature_access_with_addons, get_user_feature_limits
from app.services.addon_error_handler import handle_addon_error, AddonErrorType
from app.services.addon_production_readiness import (
    production_manager,
    monitor_addon_performance,
    validate_addon_security,
    ABTestVariant
)
from app.core.monitoring import record_addon_metrics

logger = logging.getLogger(__name__)


class AddonIntegrationService:
    """
    Unified service for all add-on operations.
    Provides a single interface that coordinates between all add-on components.
    """
    
    def __init__(self):
        self.is_initialized = False
        self.feature_mappings = {
            "content_regeneration": "regeneration_credits",
            "image_generation": "image_generation",
            "sentiment_analysis": "sentiment_analysis",
            "auto_replies": "auto_replies",
            "team_collaboration": "team_seats",
            "priority_support": "priority_support",
            "white_label": "white_label_access",
            "advanced_analytics": "advanced_analytics"
        }
    
    async def initialize(self):
        """Initialize the add-on integration service."""
        try:
            # Setup default A/B tests
            await self._setup_default_ab_tests()
            
            # Initialize feature flags
            await self._setup_feature_flags()
            
            # Setup monitoring
            await self._setup_monitoring()
            
            self.is_initialized = True
            logger.info("Add-on integration service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize add-on integration service: {str(e)}")
            raise
    
    @monitor_addon_performance("get_user_addon_dashboard")
    async def get_user_addon_dashboard(self, user: User) -> Dict[str, Any]:
        """
        Get comprehensive add-on dashboard data for a user.
        
        Returns:
            Complete dashboard with catalog, user add-ons, recommendations, and usage data
        """
        try:
            # Get all data in parallel
            import asyncio
            
            catalog_task = get_addon_catalog(user)
            user_addons_task = get_user_addon_status(user.id)
            recommendations_task = get_addon_recommendations(user, "dashboard")
            feature_limits_task = get_user_feature_limits(user)
            
            catalog, user_addons, recommendations, feature_limits = await asyncio.gather(
                catalog_task, user_addons_task, recommendations_task, feature_limits_task
            )
            
            # Check for promotions
            promotion_context = {
                "triggers": ["dashboard_view"],
                "user_plan": user.subscription.plan_id if user.subscription else "creator",
                "days_since_signup": (datetime.now(timezone.utc) - user.created_at).days
            }
            
            promotions = await check_addon_promotions(user, promotion_context)
            
            # Get usage alerts
            usage_alerts = []
            for addon in user_addons:
                if addon["usage_percentage"] >= 75:
                    usage_alerts.append({
                        "addon_id": addon["addon_id"],
                        "addon_name": addon["name"],
                        "usage_percentage": addon["usage_percentage"],
                        "alert_level": "critical" if addon["usage_percentage"] >= 95 else "warning"
                    })
            
            # Calculate potential savings
            potential_savings = await self._calculate_potential_savings(user, catalog)
            
            dashboard = {
                "user_info": {
                    "plan": user.subscription.plan_id if user.subscription else "creator",
                    "total_addons": len(user_addons),
                    "active_addons": len([a for a in user_addons if a["status"] == "active"])
                },
                "catalog": catalog,
                "user_addons": user_addons,
                "recommendations": recommendations,
                "promotions": promotions,
                "feature_limits": feature_limits,
                "usage_alerts": usage_alerts,
                "potential_savings": potential_savings,
                "dashboard_generated_at": datetime.now(timezone.utc).isoformat()
            }
            
            record_addon_metrics("dashboard_viewed", user.subscription.plan_id if user.subscription else "creator")
            
            return dashboard
            
        except Exception as e:
            logger.error(f"Error getting user addon dashboard for user {user.id}: {str(e)}")
            await handle_addon_error(
                AddonErrorType.EXTERNAL_SERVICE_ERROR,
                hash(str(user.id)),
                operation="get_user_addon_dashboard",
                details={"error": str(e)}
            )
            raise
    
    @validate_addon_security("feature_access")
    @monitor_addon_performance("check_and_consume_feature")
    async def check_and_consume_feature(self, user: User, feature: str, 
                                      amount: int = 1, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Check feature access and consume usage if allowed.
        
        This is the main entry point for feature usage across the platform.
        """
        try:
            # Map feature to usage type
            usage_type = self.feature_mappings.get(feature)
            if not usage_type:
                return {
                    "allowed": False,
                    "error": f"Unknown feature: {feature}",
                    "suggested_action": "Contact support"
                }
            
            # Check access first
            access_result = await check_feature_access_with_addons(user, feature, amount)
            
            if not access_result["has_access"]:
                # Feature blocked - check for upsell opportunities
                if access_result["result"] == "blocked_usage_limit":
                    # Send upsell notification
                    limits = access_result.get("limits", {})
                    await send_usage_upsell(
                        hash(str(user.id)),
                        usage_type,
                        limits.get("current_usage", 0),
                        limits.get("total_limit", 0)
                    )
                
                return {
                    "allowed": False,
                    "reason": access_result["message"],
                    "suggested_action": access_result["suggested_action"],
                    "limits": access_result.get("limits"),
                    "upgrade_options": access_result.get("limits", {}).get("suggested_addons", [])
                }
            
            # Access allowed - track usage
            usage_result = await track_addon_usage(user.id, usage_type, amount, metadata)
            
            if not usage_result.get("success"):
                # Usage tracking failed - handle gracefully
                await handle_addon_error(
                    AddonErrorType.USAGE_CONFLICT,
                    hash(str(user.id)),
                    operation="track_usage",
                    details={"usage_type": usage_type, "amount": amount, "error": usage_result.get("error")}
                )
                
                return {
                    "allowed": False,
                    "reason": "Usage tracking failed",
                    "suggested_action": "Try again in a moment"
                }
            
            return {
                "allowed": True,
                "credits_remaining": usage_result.get("credits_remaining"),
                "usage_percentage": usage_result.get("usage_percentage"),
                "limits": access_result.get("limits")
            }
            
        except Exception as e:
            logger.error(f"Error in check_and_consume_feature for user {user.id}, feature {feature}: {str(e)}")
            await handle_addon_error(
                AddonErrorType.EXTERNAL_SERVICE_ERROR,
                hash(str(user.id)),
                operation="check_and_consume_feature",
                details={"feature": feature, "amount": amount, "error": str(e)}
            )
            
            return {
                "allowed": False,
                "reason": "Internal error",
                "suggested_action": "Try again later"
            }
    
    @monitor_addon_performance("purchase_addon")
    async def purchase_addon(self, user: User, addon_id: str, variant: str = "basic") -> Dict[str, Any]:
        """
        Complete add-on purchase flow with validation and error handling.
        """
        try:
            # Validate purchase
            validation = await validate_addon_purchase(user, addon_id, variant)
            
            if not validation["valid"]:
                return {
                    "success": False,
                    "error": validation["error"],
                    "suggested_action": "Check add-on requirements"
                }
            
            # Security validation
            security_result = await production_manager.validate_security(
                "addon_purchase",
                hash(str(user.id)),
                {
                    "addon_id": addon_id,
                    "variant": variant,
                    "amount": validation["pricing"]["price"],
                    "user_plan": user.subscription.plan_id if user.subscription else "creator"
                }
            )
            
            if not security_result.is_valid:
                return {
                    "success": False,
                    "error": "Security validation failed",
                    "suggested_action": "Contact support if this persists"
                }
            
            # Process purchase (this would integrate with billing service)
            # For now, simulate successful purchase
            purchase_result = {
                "success": True,
                "transaction_id": f"addon_{user.id}_{addon_id}_{int(datetime.now().timestamp())}",
                "amount": validation["pricing"]["price"],
                "addon_id": addon_id,
                "variant": variant
            }
            
            # Record metrics
            record_addon_metrics(
                "addon_purchased",
                user.subscription.plan_id if user.subscription else "creator",
                1
            )
            
            return purchase_result
            
        except Exception as e:
            logger.error(f"Error purchasing addon {addon_id} for user {user.id}: {str(e)}")
            await handle_addon_error(
                AddonErrorType.PAYMENT_FAILED,
                hash(str(user.id)),
                addon_id=addon_id,
                operation="purchase_addon",
                details={"variant": variant, "error": str(e)}
            )
            
            return {
                "success": False,
                "error": "Purchase failed",
                "suggested_action": "Please try again or contact support"
            }
    
    async def get_smart_recommendations(self, user: User, context: str = "dashboard") -> List[Dict[str, Any]]:
        """Get AI-powered smart recommendations for add-ons."""
        try:
            # Check if smart recommendations are enabled
            if not await production_manager.is_feature_enabled("smart_recommendations", hash(str(user.id))):
                # Fallback to basic recommendations
                return await get_addon_recommendations(user, context)
            
            # Get enhanced recommendations with AI
            recommendations = await get_addon_recommendations(user, context)
            
            # Enhance with usage predictions and personalization
            enhanced_recommendations = []
            
            for rec in recommendations:
                addon = rec["addon"]
                
                # Add usage prediction
                usage_prediction = await self._predict_addon_value(user, addon)
                
                # Add personalization score
                personalization_score = await self._calculate_personalization_score(user, addon, context)
                
                enhanced_rec = {
                    **rec,
                    "usage_prediction": usage_prediction,
                    "personalization_score": personalization_score,
                    "confidence_level": "high" if personalization_score > 0.8 else "medium"
                }
                
                enhanced_recommendations.append(enhanced_rec)
            
            # Sort by enhanced scoring
            enhanced_recommendations.sort(
                key=lambda x: x["personalization_score"] * x["score"], 
                reverse=True
            )
            
            return enhanced_recommendations[:5]  # Top 5
            
        except Exception as e:
            logger.error(f"Error getting smart recommendations for user {user.id}: {str(e)}")
            # Fallback to basic recommendations
            return await get_addon_recommendations(user, context)
    
    async def _setup_default_ab_tests(self):
        """Setup default A/B tests for the add-on system."""
        try:
            # Pricing test
            await production_manager.setup_ab_test("addon_pricing_v1", [
                ABTestVariant("control", 0.5, {"pricing_strategy": "current"}, is_control=True),
                ABTestVariant("discount", 0.5, {"pricing_strategy": "10_percent_off"})
            ])
            
            # Recommendation algorithm test
            await production_manager.setup_ab_test("recommendation_algorithm", [
                ABTestVariant("basic", 0.5, {"algorithm": "basic"}, is_control=True),
                ABTestVariant("ai_enhanced", 0.5, {"algorithm": "ai_enhanced"})
            ])
            
        except Exception as e:
            logger.error(f"Error setting up default A/B tests: {str(e)}")
    
    async def _setup_feature_flags(self):
        """Setup default feature flags."""
        try:
            # Enable features gradually
            await production_manager.set_feature_flag("addon_marketplace_v2", True, {"percentage": 50})
            await production_manager.set_feature_flag("smart_recommendations", True, {"plans": ["accelerator", "dominator"]})
            await production_manager.set_feature_flag("usage_predictions", False)  # Not ready yet
            
        except Exception as e:
            logger.error(f"Error setting up feature flags: {str(e)}")
    
    async def _setup_monitoring(self):
        """Setup monitoring and health checks."""
        try:
            # This would setup periodic health checks
            logger.info("Monitoring setup completed")
            
        except Exception as e:
            logger.error(f"Error setting up monitoring: {str(e)}")
    
    async def _calculate_potential_savings(self, user: User, catalog: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate potential savings from add-on bundles."""
        # Simplified calculation - would be more sophisticated in production
        total_individual_cost = sum(
            addon.get("pricing", {}).get("basic", {}).get("price", 0) 
            for addon in catalog[:3]  # Top 3 recommendations
        )
        
        bundle_discount = 0.15  # 15% bundle discount
        potential_savings = total_individual_cost * bundle_discount
        
        return {
            "individual_cost": total_individual_cost,
            "bundle_cost": total_individual_cost * (1 - bundle_discount),
            "savings_amount": potential_savings,
            "savings_percentage": bundle_discount * 100
        }
    
    async def _predict_addon_value(self, user: User, addon: Dict[str, Any]) -> Dict[str, Any]:
        """Predict the value an add-on would provide to the user."""
        # Simplified prediction - would use ML in production
        return {
            "predicted_monthly_usage": 50,  # Placeholder
            "estimated_time_savings": "2 hours",
            "roi_estimate": "250%"
        }
    
    async def _calculate_personalization_score(self, user: User, addon: Dict[str, Any], context: str) -> float:
        """Calculate personalization score for an add-on."""
        # Simplified scoring - would be more sophisticated in production
        base_score = 0.5
        
        # Boost score based on user plan
        user_plan = user.subscription.plan_id if user.subscription else "creator"
        if addon.get("required_plan") == user_plan:
            base_score += 0.2
        
        # Boost score based on context
        if context == "dashboard" and addon.get("is_popular"):
            base_score += 0.1
        
        return min(base_score, 1.0)


# Global integration service instance
addon_integration = AddonIntegrationService()


# Convenience functions for easy integration
async def get_addon_dashboard(user: User) -> Dict[str, Any]:
    """Get complete add-on dashboard for user."""
    return await addon_integration.get_user_addon_dashboard(user)


async def use_feature(user: User, feature: str, amount: int = 1, 
                     metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Use a platform feature with add-on integration."""
    return await addon_integration.check_and_consume_feature(user, feature, amount, metadata)


async def buy_addon(user: User, addon_id: str, variant: str = "basic") -> Dict[str, Any]:
    """Purchase an add-on."""
    return await addon_integration.purchase_addon(user, addon_id, variant)


async def get_recommendations(user: User, context: str = "dashboard") -> List[Dict[str, Any]]:
    """Get personalized add-on recommendations."""
    return await addon_integration.get_smart_recommendations(user, context)


# Initialize the service on import
import asyncio

async def _initialize_service():
    """Initialize the add-on integration service."""
    try:
        await addon_integration.initialize()
    except Exception as e:
        logger.error(f"Failed to initialize add-on integration service: {str(e)}")

# Schedule initialization
# Note: In a real application, this would be called during app startup
# asyncio.create_task(_initialize_service())
