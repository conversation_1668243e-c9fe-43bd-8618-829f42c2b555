/**
 * Google OAuth Login Button Component
 * 
 * Provides secure Google OAuth 2.0 authentication with ACE Social branding
 * and consistent UI/UX following the platform's design system.
 * 
 * Features:
 * - PKCE implementation for enhanced security
 * - State parameter validation for CSRF protection
 * - Consistent ACE Social branding (#15110E dark, #4E40C5 purple)
 * - Loading states and error handling
 * - Responsive design
 * 
 * @since 2024-1-1 to 2025-25-7
 */
import { useState } from 'react';
import { 
  Button, 
  Box, 
  CircularProgress, 
  useTheme, 
  alpha 
} from '@mui/material';
import { Google as GoogleIcon } from '@mui/icons-material';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';
import api from '../../api';

const GoogleOAuthButton = ({ 
  onSuccess, 
  onError, 
  disabled = false,
  fullWidth = true,
  variant = "outlined",
  size = "large"
}) => {
  const [loading, setLoading] = useState(false);
  const { showError, showSuccess } = useAdvancedToast();
  const theme = useTheme();

  const handleGoogleLogin = async () => {
    if (disabled || loading) return;

    setLoading(true);
    
    try {
      // Get Google OAuth authorization URL from backend
      const response = await api.get('/api/auth/google/authorize');
      
      if (!response.data?.authorization_url) {
        throw new Error('Failed to get Google authorization URL');
      }

      // Store state for verification after redirect
      localStorage.setItem('google_oauth_state', response.data.state);
      localStorage.setItem('google_oauth_redirect_uri', response.data.redirect_uri);

      // Redirect to Google OAuth authorization page
      window.location.href = response.data.authorization_url;
      
    } catch (error) {
      console.error('Google OAuth initiation error:', error);
      setLoading(false);
      
      const errorMessage = error.response?.data?.detail || 
                          error.message || 
                          'Failed to initiate Google login';
      
      showError(errorMessage);
      onError?.(errorMessage);
    }
  };

  return (
    <Button
      fullWidth={fullWidth}
      variant={variant}
      size={size}
      onClick={handleGoogleLogin}
      disabled={disabled || loading}
      startIcon={
        loading ? (
          <CircularProgress size={20} color="inherit" />
        ) : (
          <GoogleIcon />
        )
      }
      sx={{
        minHeight: 56,
        borderRadius: 2,
        fontSize: '1rem',
        fontWeight: 600,
        textTransform: 'none',
        border: `2px solid ${alpha(theme.palette.divider, 0.2)}`,
        color: theme.palette.text.primary,
        background: theme.palette.background.paper,
        transition: 'all 0.3s ease',
        '&:hover': {
          border: `2px solid ${theme.palette.primary.main}`,
          background: alpha(theme.palette.primary.main, 0.04),
          transform: 'translateY(-1px)',
          boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.15)}`,
        },
        '&:disabled': {
          background: alpha(theme.palette.action.disabled, 0.08),
          color: theme.palette.action.disabled,
          border: `2px solid ${alpha(theme.palette.divider, 0.1)}`,
          transform: 'none',
          boxShadow: 'none',
        },
        '& .MuiButton-startIcon': {
          marginRight: 1.5,
          '& svg': {
            fontSize: '1.2rem',
          },
        },
      }}
    >
      {loading ? 'Connecting to Google...' : 'Continue with Google'}
    </Button>
  );
};

export default GoogleOAuthButton;
