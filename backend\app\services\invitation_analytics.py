"""
Service for team invitation analytics and metrics tracking.
@since 2024-1-1 to 2025-25-7
"""
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone, timedelta
import logging
from bson import ObjectId

from app.db.mongodb import get_database

logger = logging.getLogger(__name__)

class InvitationAnalyticsService:
    """Service for tracking and analyzing team invitation metrics."""
    
    def __init__(self):
        self.collection_name = "invitation_analytics"
    
    async def track_invitation_sent(
        self,
        team_id: str,
        inviter_id: str,
        recipient_email: str,
        invitation_token: str,
        correlation_id: Optional[str] = None
    ) -> bool:
        """
        Track when an invitation is sent.
        
        Args:
            team_id: ID of the team
            inviter_id: ID of the user sending invitation
            recipient_email: Email of the recipient
            invitation_token: Invitation token
            correlation_id: Optional correlation ID for tracking
            
        Returns:
            True if tracked successfully
        """
        try:
            db = await get_database()
            
            analytics_data = {
                "event_type": "invitation_sent",
                "team_id": ObjectId(team_id),
                "inviter_id": ObjectId(inviter_id),
                "recipient_email": recipient_email.lower(),
                "invitation_token": invitation_token,
                "timestamp": datetime.now(timezone.utc),
                "correlation_id": correlation_id,
                "metadata": {
                    "sent_at": datetime.now(timezone.utc).isoformat(),
                    "status": "sent"
                }
            }
            
            await db[self.collection_name].insert_one(analytics_data)
            return True
            
        except Exception as e:
            logger.error(f"Error tracking invitation sent: {str(e)}")
            return False
    
    async def track_invitation_opened(
        self,
        invitation_token: str,
        client_ip: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> bool:
        """
        Track when an invitation email is opened/clicked.
        
        Args:
            invitation_token: Invitation token
            client_ip: Client IP address
            user_agent: User agent string
            
        Returns:
            True if tracked successfully
        """
        try:
            db = await get_database()
            
            analytics_data = {
                "event_type": "invitation_opened",
                "invitation_token": invitation_token,
                "timestamp": datetime.now(timezone.utc),
                "client_ip": client_ip,
                "user_agent": user_agent[:200] if user_agent else None,
                "metadata": {
                    "opened_at": datetime.now(timezone.utc).isoformat()
                }
            }
            
            await db[self.collection_name].insert_one(analytics_data)
            return True
            
        except Exception as e:
            logger.error(f"Error tracking invitation opened: {str(e)}")
            return False
    
    async def track_invitation_accepted(
        self,
        invitation_token: str,
        user_id: str,
        team_id: str,
        registration_type: str = "existing_user",  # "existing_user" or "new_user"
        correlation_id: Optional[str] = None
    ) -> bool:
        """
        Track when an invitation is accepted.
        
        Args:
            invitation_token: Invitation token
            user_id: ID of the user accepting
            team_id: ID of the team
            registration_type: Type of registration (existing_user or new_user)
            correlation_id: Optional correlation ID
            
        Returns:
            True if tracked successfully
        """
        try:
            db = await get_database()
            
            analytics_data = {
                "event_type": "invitation_accepted",
                "invitation_token": invitation_token,
                "user_id": ObjectId(user_id),
                "team_id": ObjectId(team_id),
                "registration_type": registration_type,
                "timestamp": datetime.now(timezone.utc),
                "correlation_id": correlation_id,
                "metadata": {
                    "accepted_at": datetime.now(timezone.utc).isoformat(),
                    "conversion_type": registration_type
                }
            }
            
            await db[self.collection_name].insert_one(analytics_data)
            return True
            
        except Exception as e:
            logger.error(f"Error tracking invitation accepted: {str(e)}")
            return False
    
    async def track_invitation_rejected(
        self,
        invitation_token: str,
        correlation_id: Optional[str] = None
    ) -> bool:
        """
        Track when an invitation is rejected.
        
        Args:
            invitation_token: Invitation token
            correlation_id: Optional correlation ID
            
        Returns:
            True if tracked successfully
        """
        try:
            db = await get_database()
            
            analytics_data = {
                "event_type": "invitation_rejected",
                "invitation_token": invitation_token,
                "timestamp": datetime.now(timezone.utc),
                "correlation_id": correlation_id,
                "metadata": {
                    "rejected_at": datetime.now(timezone.utc).isoformat()
                }
            }
            
            await db[self.collection_name].insert_one(analytics_data)
            return True
            
        except Exception as e:
            logger.error(f"Error tracking invitation rejected: {str(e)}")
            return False
    
    async def get_team_invitation_metrics(
        self,
        team_id: str,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        Get invitation metrics for a specific team.
        
        Args:
            team_id: ID of the team
            days: Number of days to look back
            
        Returns:
            Dictionary containing metrics
        """
        try:
            db = await get_database()
            team_object_id = ObjectId(team_id)
            start_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            # Aggregation pipeline for metrics
            pipeline = [
                {
                    "$match": {
                        "team_id": team_object_id,
                        "timestamp": {"$gte": start_date}
                    }
                },
                {
                    "$group": {
                        "_id": "$event_type",
                        "count": {"$sum": 1},
                        "latest": {"$max": "$timestamp"}
                    }
                }
            ]
            
            cursor = db[self.collection_name].aggregate(pipeline)
            results = await cursor.to_list(length=None)
            
            # Process results
            metrics = {
                "invitations_sent": 0,
                "invitations_opened": 0,
                "invitations_accepted": 0,
                "invitations_rejected": 0,
                "conversion_rate": 0.0,
                "open_rate": 0.0,
                "period_days": days,
                "last_activity": None
            }
            
            for result in results:
                event_type = result["_id"]
                count = result["count"]
                
                if event_type == "invitation_sent":
                    metrics["invitations_sent"] = count
                elif event_type == "invitation_opened":
                    metrics["invitations_opened"] = count
                elif event_type == "invitation_accepted":
                    metrics["invitations_accepted"] = count
                elif event_type == "invitation_rejected":
                    metrics["invitations_rejected"] = count
                
                # Track latest activity
                if not metrics["last_activity"] or result["latest"] > metrics["last_activity"]:
                    metrics["last_activity"] = result["latest"]
            
            # Calculate rates
            if metrics["invitations_sent"] > 0:
                metrics["conversion_rate"] = (metrics["invitations_accepted"] / metrics["invitations_sent"]) * 100
                metrics["open_rate"] = (metrics["invitations_opened"] / metrics["invitations_sent"]) * 100
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting team invitation metrics: {str(e)}")
            return {}
    
    async def get_platform_invitation_metrics(
        self,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        Get platform-wide invitation metrics.
        
        Args:
            days: Number of days to look back
            
        Returns:
            Dictionary containing platform metrics
        """
        try:
            db = await get_database()
            start_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            # Platform-wide aggregation
            pipeline = [
                {
                    "$match": {
                        "timestamp": {"$gte": start_date}
                    }
                },
                {
                    "$group": {
                        "_id": {
                            "event_type": "$event_type",
                            "registration_type": "$registration_type"
                        },
                        "count": {"$sum": 1}
                    }
                }
            ]
            
            cursor = db[self.collection_name].aggregate(pipeline)
            results = await cursor.to_list(length=None)
            
            # Process platform metrics
            metrics = {
                "total_invitations_sent": 0,
                "total_invitations_accepted": 0,
                "new_user_conversions": 0,
                "existing_user_conversions": 0,
                "overall_conversion_rate": 0.0,
                "new_user_conversion_rate": 0.0,
                "period_days": days
            }
            
            for result in results:
                event_type = result["_id"]["event_type"]
                registration_type = result["_id"].get("registration_type")
                count = result["count"]
                
                if event_type == "invitation_sent":
                    metrics["total_invitations_sent"] += count
                elif event_type == "invitation_accepted":
                    metrics["total_invitations_accepted"] += count
                    
                    if registration_type == "new_user":
                        metrics["new_user_conversions"] += count
                    elif registration_type == "existing_user":
                        metrics["existing_user_conversions"] += count
            
            # Calculate conversion rates
            if metrics["total_invitations_sent"] > 0:
                metrics["overall_conversion_rate"] = (metrics["total_invitations_accepted"] / metrics["total_invitations_sent"]) * 100
                metrics["new_user_conversion_rate"] = (metrics["new_user_conversions"] / metrics["total_invitations_sent"]) * 100
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting platform invitation metrics: {str(e)}")
            return {}

# Global instance
invitation_analytics = InvitationAnalyticsService()
